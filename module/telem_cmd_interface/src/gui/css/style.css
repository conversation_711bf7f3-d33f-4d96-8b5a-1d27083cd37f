* {
    box-sizing: border-box
}

body {
    overflow-x: hidden;
    height: 100%;
    margin: 0;
    font-family: Arial, Helvetica, sans-serif;
    background-color: #121212;
}

.light-mode {
    background-color: #c2c2c2;
    color: #7f7f7f;
}

.topnav {
    width: auto;
    height: auto;
    overflow: hidden;
    background-color: #7f7f7f;
}

.topnav a {
    float: left;
    color: #f2f2f2;
    text-align: center;
    padding: 14px 16px;
    text-decoration: none;
    font-size: 30px;
}

.configDropdown {
    float: left;
    overflow: hidden;
}

.configDropdown .configDropBtn {
    color: #f2f2f2;
    background-color: inherit;
    font-family: inherit;
    text-align: center;
    padding: 14px 16px;
    font-size: 30px;
    border: none;
    outline: none;
}

.topnav a:hover, .configDropdown:hover, .configDropBtn {
    background-color: #ddd;
    color: black;
}

.configContents{
    display: none;
    position: absolute;
    background-color: #7f7f7f;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    min-width: 160px;
    z-index: 1;
}

.configContents a{
    float: none;
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
}

.topnav a.active {
    background-color: #04AA6D;
    color: black;
}

.configContents a:hover {
    background-color: #ddd;
}

.configDropdown:hover .configContents {
    display: block;
}

.box {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 1200px;
    padding: 20px;
    margin: 25px;
    background-color: #222222;
    border-radius: 25px;
}

.ingressLeftBox {
    display: flex;
    flex-direction: column;
    width: 70%;
}

.cardBox {
    position: relative;
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(3, 170px);
    grid-gap: 20px;
}

.tab {
    overflow: hidden;
    background-color: #f1f1f1;
    width: 100%;
}

.tab button {
    background-color: #404040;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    transition: 0.3s;
    font-size: 2em;
    color: white;
    font-weight: 500;
}

.tab button:hover {
    background-color: #ddd;
}

.tab button.active {
    background-color: black;
}

.card {
    position: relative;
    background: #404040;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: none;
    padding: 20px;
}

.card .label {
    color: white;
}

.card .data {
    position: relative;
    font-size: 2em;
    color: white;
    font-weight: 500;
}


.visualizations {
    position: relative;
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-gap: 20px;
}

.rateGraph {
    position: relative;
    background: #404040;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: none;
}

.onOffIndicator {
    position: relative;
    padding: 20px;
    background-color: red;
    border-radius: 50%;
    display: inline-block;
}

.details {
    padding-top: 100px;
    position: relative;
    padding: 20px;
    display: flex;
}

.details table {
    border-collapse: separate;
}

.details table tbody tr {
    color: white;
    font-weight: 100;
    vertical-align: middle;
}


.details .statsCard {
    position: relative;
    background: #404040;
    padding: 20px;
}

.details .statsCard table tr td {
    padding: 7px 3px;
    color: white;
}


.details .configParamStats {
    position: relative;
    background: #404040;
    padding: 20px 20px;
    border-spacing: 0 20px;
}

.cardTitle {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    color: white;
}

.engine_table {
    table-layout: fixed;
    background-color: #7f7f7f;
    color: white;
    font-size: 20px;
    text-align: left;
    width: 1150px;
}

.engine_table th {
    background-color: #04AA6D;
    color: white;
}

.engine_table td {
    color: white;
}

.engine_table tr:nth-child(even) {
    background-color: #404040;
}

.engine_table tr td:nth-child(even) {
    text-align: center;
}

.hidden {
    display: none;
}

/*config page*/

.configBody {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: left;
    border-radius: 10%;
}

.configBodyTables {
    width: 66%;
    display: flex;
    flex-direction: column;
    justify-content: left;
    margin-top: 25px;
    margin-left: 20px;
    padding: 20px;
    background-color: #404040;
    color: white;
    font-size: 20px;
    text-align: left;
    border-top: none;
    border-radius: 25px;
}

.configBox {
    text-align: left;
}

.configBox table {
    border-collapse: collapse;
    color: white;
    font-size: 1.2em;
    font-family: sans-serif;
    border: 1px solid;
    table-layout: fixed;
}

.styled-table thead tr {
    background-color: #009879;
    color: #ffffff;
    text-align: left;
}

.styled-table th {
    padding: 20px 15px;
}

.styled-table td {
    padding: 20px 15px;
    border: 1px solid;
    word-wrap: break-word;
}

caption {
    font-size: 40px;
    font-weight: bold;
}

/*BPSec page*/
.bpsecBody{
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: visible;
}

.bpsecTitle{
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 15px;
}

.bpSecConfigTitle{
    padding: 20px;
}

.bpsecButtons{
    display: block;
    margin-left: auto;
    margin-right: 10px;
    transition-duration: .4s;
}

.bpSecAddPolicy, .bpSecAddSecurityEvent{
    width: 100px;
    height: 50px;
    background-color: #c2c2c2;
    color: black;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 15px;
}

.bpSecAddPolicy:hover, .bpSecAddSecurityEvent:hover {
    cursor: pointer;
    background-color: #7f7f7f;
}

.bpSecPoliciesTitle{
    display: block;
    margin-left: 10px;
    margin-right: auto;
}

.bpSecAddPolicyForm, .bpsecAddSecurityEventForm {
    position: relative;
    text-align: center;
    width: 100%;
}

.bpsecPolicies{
    display: flex;
    flex-direction: column;
    text-align: center;
    margin-left: 25px;
}

.formPopup, .eventFormPopup {
    display: none;
    height: auto;
    position: fixed;
    background-color: #7f7f7f;
    color: white;
    left: 45%;
    top: 5%;
    transform: translate(-50%, 5%);
    border: 3px solid #999999;
    z-index: 9;
    width: 450px;
    justify-content: center;
}

.formLayout {
    display: flex;
    flex-direction: column;
}

.popupTitle{
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    margin: 0;
    margin-bottom: 5px;
}

.policyQuitBtn{
    width: 20px;
    height: 20px;
    color: black;
    display: block;
    margin-top: 15px;
    margin-left: auto;
    margin-right: 5px;
    border: none;
    font-size: 16px;
    font-weight: bold;
    text-decoration: none;
    background-color: transparent;
    transition: .4s;
}

.policyQuitBtn:hover{
    cursor: pointer;
    font-size: 20px;
    font-weight: bolder;
}

.bpSecPolicyElements{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.input{
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    padding: 2px;
}

.bpSecList{
    width: 60%;
}

.bpSecEditPolicies{
    width: auto;
    height: auto;
    color: black;
    display: block;
    border: none;
    font-size: 13px;
    font-weight: bold;
    text-decoration: none;
    transition: .4s;   
    margin: 10px 15px 10px auto;
}

.bpSecEditPolicies:hover{
    cursor: pointer;
}

