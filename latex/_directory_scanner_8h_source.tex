\doxysection{Directory\+Scanner.\+h}
\hypertarget{_directory_scanner_8h_source}{}\label{_directory_scanner_8h_source}\index{common/util/include/DirectoryScanner.h@{common/util/include/DirectoryScanner.h}}
\mbox{\hyperlink{_directory_scanner_8h}{Go to the documentation of this file.}}
\begin{DoxyCode}{0}
\DoxyCodeLine{00001\ }
\DoxyCodeLine{00020\ }
\DoxyCodeLine{00021\ \textcolor{preprocessor}{\#ifndef\ \_DIRECTORY\_SCANNER\_H}}
\DoxyCodeLine{00022\ \textcolor{preprocessor}{\#define\ \_DIRECTORY\_SCANNER\_H\ 1}}
\DoxyCodeLine{00023\ }
\DoxyCodeLine{00024\ \textcolor{preprocessor}{\#include\ <string>}}
\DoxyCodeLine{00025\ \textcolor{preprocessor}{\#include\ <boost/filesystem/path.hpp>}}
\DoxyCodeLine{00026\ \textcolor{preprocessor}{\#include\ <boost/asio.hpp>}}
\DoxyCodeLine{00027\ \textcolor{preprocessor}{\#include\ <boost/thread.hpp>}}
\DoxyCodeLine{00028\ \textcolor{preprocessor}{\#include\ <memory>}}
\DoxyCodeLine{00029\ \textcolor{preprocessor}{\#include\ <list>}}
\DoxyCodeLine{00030\ \textcolor{preprocessor}{\#include\ <set>}}
\DoxyCodeLine{00031\ \textcolor{preprocessor}{\#include\ <map>}}
\DoxyCodeLine{00032\ \textcolor{preprocessor}{\#include\ <queue>}}
\DoxyCodeLine{00033\ \textcolor{preprocessor}{\#include\ "{}dir\_monitor/dir\_monitor.hpp"{}}}
\DoxyCodeLine{00034\ \textcolor{preprocessor}{\#include\ "{}hdtn\_util\_export.h"{}}}
\DoxyCodeLine{00035\ }
\DoxyCodeLine{00036\ \textcolor{keyword}{class\ }DirectoryScanner\ \{}
\DoxyCodeLine{00037\ \textcolor{keyword}{private}:}
\DoxyCodeLine{00038\ \ \ \ \ DirectoryScanner()\ =\ \textcolor{keyword}{delete};}
\DoxyCodeLine{00039\ \textcolor{keyword}{public}:}
\DoxyCodeLine{00040\ \ \ \ \ \textcolor{keyword}{typedef}\ std::list<boost::filesystem::path>\ path\_list\_t;}
\DoxyCodeLine{00041\ \ \ \ \ \textcolor{keyword}{typedef}\ std::set<boost::filesystem::path>\ path\_set\_t;}
\DoxyCodeLine{00042\ \ \ \ \ \textcolor{keyword}{typedef}\ std::pair<uintmax\_t,\ unsigned\ int>\ filesize\_queuecount\_pair\_t;}
\DoxyCodeLine{00043\ \ \ \ \ \textcolor{keyword}{typedef}\ std::map<boost::filesystem::path,\ filesize\_queuecount\_pair\_t>\ path\_to\_size\_map\_t;}
\DoxyCodeLine{00044\ \ \ \ \ \textcolor{keyword}{typedef}\ std::pair<boost::posix\_time::ptime,\ path\_to\_size\_map\_t::iterator>\ ptime\_plus\_mapit\_pair\_t;}
\DoxyCodeLine{00045\ \ \ \ \ \textcolor{keyword}{typedef}\ std::queue<ptime\_plus\_mapit\_pair\_t>\ timer\_queue\_t;}
\DoxyCodeLine{00046\ \ \ \ \ HDTN\_UTIL\_EXPORT\ DirectoryScanner(\textcolor{keyword}{const}\ boost::filesystem::path\ \&\ rootFileOrFolderPath,}
\DoxyCodeLine{00047\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{bool}\ includeExistingFiles,\ \textcolor{keywordtype}{bool}\ includeNewFiles,\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ recurseDirectoriesDepth,}
\DoxyCodeLine{00048\ \ \ \ \ \ \ \ \ boost::asio::io\_service\ \&\ ioServiceRef,\ \textcolor{keyword}{const}\ uint64\_t\ recheckFileSizeDurationMilliseconds);}
\DoxyCodeLine{00049\ \ \ \ \ HDTN\_UTIL\_EXPORT\ \string~DirectoryScanner();}
\DoxyCodeLine{00050\ \ \ \ \ HDTN\_UTIL\_EXPORT\ std::size\_t\ GetNumberOfFilesToSend()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00051\ \ \ \ \ HDTN\_UTIL\_EXPORT\ std::size\_t\ GetNumberOfCurrentlyMonitoredDirectories()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00052\ \ \ \ \ HDTN\_UTIL\_EXPORT\ \textcolor{keywordtype}{bool}\ GetNextFilePath(boost::filesystem::path\&\ nextFilePathAbsolute,\ boost::filesystem::path\&\ nextFilePathRelative);}
\DoxyCodeLine{00053\ \ \ \ \ HDTN\_UTIL\_EXPORT\ \textcolor{keywordtype}{bool}\ GetNextFilePathTimeout(boost::filesystem::path\&\ nextFilePathAbsolute,}
\DoxyCodeLine{00054\ \ \ \ \ \ \ \ \ boost::filesystem::path\&\ nextFilePathRelative,\ \textcolor{keyword}{const}\ boost::posix\_time::time\_duration\ \&\ timeout);}
\DoxyCodeLine{00055\ \ \ \ \ HDTN\_UTIL\_EXPORT\ \textcolor{keyword}{friend}\ std::ostream\&\ operator<<(std::ostream\&\ os,\ \textcolor{keyword}{const}\ path\_list\_t\&\ o);}
\DoxyCodeLine{00056\ \ \ \ \ HDTN\_UTIL\_EXPORT\ \textcolor{keyword}{friend}\ std::ostream\&\ operator<<(std::ostream\&\ os,\ \textcolor{keyword}{const}\ path\_set\_t\&\ o);}
\DoxyCodeLine{00057\ \ \ \ \ HDTN\_UTIL\_EXPORT\ \textcolor{keyword}{const}\ path\_list\_t\ \&\ GetListOfFilesAbsolute()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00058\ \ \ \ \ HDTN\_UTIL\_EXPORT\ path\_list\_t\ GetListOfFilesRelativeCopy()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00059\ \ \ \ \ HDTN\_UTIL\_EXPORT\ \textcolor{keyword}{const}\ path\_set\_t\&\ GetSetOfMonitoredDirectoriesAbsolute()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00060\ \ \ \ \ HDTN\_UTIL\_EXPORT\ path\_set\_t\ GetSetOfMonitoredDirectoriesRelativeCopy()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00061\ \textcolor{keyword}{private}:}
\DoxyCodeLine{00062\ \ \ \ \ HDTN\_UTIL\_NO\_EXPORT\ \textcolor{keywordtype}{bool}\ GetNextFilePath\_NotThreadSafe(boost::filesystem::path\&\ nextFilePathAbsolute,\ boost::filesystem::path\&\ nextFilePathRelative);}
\DoxyCodeLine{00063\ \ \ \ \ HDTN\_UTIL\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ Reload();}
\DoxyCodeLine{00064\ \ \ \ \ HDTN\_UTIL\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ Clear();}
\DoxyCodeLine{00065\ \ \ \ \ HDTN\_UTIL\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ OnDirectoryChangeEvent(\textcolor{keyword}{const}\ boost::system::error\_code\&\ ec,\ \textcolor{keyword}{const}\ \mbox{\hyperlink{structboost_1_1asio_1_1dir__monitor__event}{boost::asio::dir\_monitor\_event}}\&\ ev);}
\DoxyCodeLine{00066\ \ \ \ \ HDTN\_UTIL\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ OnRecheckFileSize\_TimerExpired(\textcolor{keyword}{const}\ boost::system::error\_code\&\ e);}
\DoxyCodeLine{00067\ \ \ \ \ HDTN\_UTIL\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ TryAddNewFile(\textcolor{keyword}{const}\ boost::filesystem::path\&\ p);}
\DoxyCodeLine{00068\ \ \ \ \ HDTN\_UTIL\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ IterateDirectories(\textcolor{keyword}{const}\ boost::filesystem::path\&\ rootDirectory,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ startingRecursiveDepthIndex,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ addFiles);}
\DoxyCodeLine{00069\ \textcolor{keyword}{private}:}
\DoxyCodeLine{00070\ \ \ \ \ boost::mutex\ m\_pathsOfFilesListMutex;}
\DoxyCodeLine{00071\ \ \ \ \ boost::condition\_variable\ m\_pathsOfFilesListCv;}
\DoxyCodeLine{00072\ \ \ \ \ path\_list\_t\ m\_pathsOfFilesList;}
\DoxyCodeLine{00073\ \ \ \ \ path\_list\_t::iterator\ m\_currentFilePathIterator;}
\DoxyCodeLine{00074\ \ \ \ \ \textcolor{keyword}{const}\ boost::filesystem::path\ m\_rootFileOrFolderPath;}
\DoxyCodeLine{00075\ \ \ \ \ boost::filesystem::path\ m\_relativeToPath;}
\DoxyCodeLine{00076\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ m\_includeExistingFiles;}
\DoxyCodeLine{00077\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ m\_includeNewFiles;}
\DoxyCodeLine{00078\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ m\_recurseDirectoriesDepth;}
\DoxyCodeLine{00079\ \ \ \ \ boost::asio::dir\_monitor\ m\_dirMonitor;}
\DoxyCodeLine{00080\ \ \ \ \ boost::asio::deadline\_timer\ m\_timerNewFileComplete;}
\DoxyCodeLine{00081\ \ \ \ \ \textcolor{keyword}{const}\ boost::posix\_time::time\_duration\ m\_timeDurationToRecheckFileSize;}
\DoxyCodeLine{00082\ \ \ \ \ path\_set\_t\ m\_currentlyMonitoredDirectoryPaths;}
\DoxyCodeLine{00083\ \ \ \ \ path\_to\_size\_map\_t\ m\_currentlyPendingFilesToAddMap;}
\DoxyCodeLine{00084\ \ \ \ \ path\_set\_t\ m\_newFilePathsAddedSet;}
\DoxyCodeLine{00085\ \ \ \ \ timer\_queue\_t\ m\_currentlyPendingFilesToAddTimerQueue;}
\DoxyCodeLine{00086\ \};}
\DoxyCodeLine{00087\ }
\DoxyCodeLine{00088\ \textcolor{preprocessor}{\#endif\ }\textcolor{comment}{//\_DIRECTORY\_SCANNER\_H}}

\end{DoxyCode}
