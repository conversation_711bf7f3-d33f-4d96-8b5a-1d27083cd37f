\doxysection{common/bpcodec/src/codec/\+Custody\+Transfer\+Manager.cpp File Reference}
\hypertarget{_custody_transfer_manager_8cpp}{}\label{_custody_transfer_manager_8cpp}\index{common/bpcodec/src/codec/CustodyTransferManager.cpp@{common/bpcodec/src/codec/CustodyTransferManager.cpp}}
{\ttfamily \#include "{}codec/\+Custody\+Transfer\+Manager.\+h"{}}\newline
{\ttfamily \#include $<$iostream$>$}\newline
{\ttfamily \#include "{}Timestamp\+Util.\+h"{}}\newline
{\ttfamily \#include "{}Uri.\+h"{}}\newline
{\ttfamily \#include $<$boost/make\+\_\+unique.\+hpp$>$}\newline


\doxysubsection{Detailed Description}
\begin{DoxyAuthor}{Author}
Brian <PERSON> \href{mailto:<EMAIL>}{\texttt{ brian.\+j.\+tomko@nasa.\+gov}}
\end{DoxyAuthor}
\begin{DoxyCopyright}{Copyright}
Copyright (c) 2021 United States Government as represented by the National Aeronautics and Space Administration. No copyright is claimed in the United States under Title 17, U.\+S.\+Code. All Other Rights Reserved.
\end{DoxyCopyright}
\hypertarget{import__installation_2src_2test__main_8cpp_LICENSE}{}\doxysubsection{\texorpdfstring{LICENSE}{LICENSE}}\label{import__installation_2src_2test__main_8cpp_LICENSE}
Released under the NASA Open Source Agreement (NOSA) See LICENSE.\+md in the source root directory for more information. 