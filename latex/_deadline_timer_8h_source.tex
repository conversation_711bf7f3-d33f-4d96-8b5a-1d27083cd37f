\doxysection{Deadline\+Timer.\+h}
\hypertarget{_deadline_timer_8h_source}{}\label{_deadline_timer_8h_source}\index{common/util/include/DeadlineTimer.h@{common/util/include/DeadlineTimer.h}}
\mbox{\hyperlink{_deadline_timer_8h}{Go to the documentation of this file.}}
\begin{DoxyCode}{0}
\DoxyCodeLine{00001\ }
\DoxyCodeLine{00017\ }
\DoxyCodeLine{00018\ \textcolor{preprocessor}{\#ifndef\ \_DEADLINE\_TIMER\_H}}
\DoxyCodeLine{00019\ \textcolor{preprocessor}{\#define\ \_DEADLINE\_TIMER\_H\ 1}}
\DoxyCodeLine{00020\ }
\DoxyCodeLine{00021\ \textcolor{preprocessor}{\#include\ <boost/asio.hpp>}}
\DoxyCodeLine{00022\ \textcolor{preprocessor}{\#include\ <atomic>}}
\DoxyCodeLine{00023\ \textcolor{preprocessor}{\#include\ "{}hdtn\_util\_export.h"{}}}
\DoxyCodeLine{00024\ }
\DoxyCodeLine{00025\ \textcolor{keyword}{class\ }DeadlineTimer}
\DoxyCodeLine{00026\ \{}
\DoxyCodeLine{00027\ \ \ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{00028\ \ \ \ \ \ \ \ \ HDTN\_UTIL\_EXPORT\ DeadlineTimer(\textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ intervalMs);}
\DoxyCodeLine{00029\ \ \ \ \ \ \ \ \ HDTN\_UTIL\_EXPORT\ \textcolor{keywordtype}{bool}\ SleepUntilNextInterval();}
\DoxyCodeLine{00030\ \ \ \ \ \ \ \ \ HDTN\_UTIL\_EXPORT\ \textcolor{keywordtype}{void}\ Cancel();}
\DoxyCodeLine{00031\ \ \ \ \ \ \ \ \ HDTN\_UTIL\_EXPORT\ \textcolor{keywordtype}{void}\ Disable()\ \textcolor{keyword}{noexcept};}
\DoxyCodeLine{00032\ }
\DoxyCodeLine{00033\ \ \ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{00034\ \ \ \ \ \ \ \ \ DeadlineTimer()\ =\ \textcolor{keyword}{delete};}
\DoxyCodeLine{00035\ \ \ \ \ \ \ \ \ boost::asio::io\_service\ m\_ioService;}
\DoxyCodeLine{00036\ \ \ \ \ \ \ \ \ boost::posix\_time::time\_duration\ m\_sleepValTimeDuration;}
\DoxyCodeLine{00037\ \ \ \ \ \ \ \ \ boost::asio::deadline\_timer\ m\_deadlineTimer;}
\DoxyCodeLine{00038\ \ \ \ \ \ \ \ \ std::atomic<bool>\ m\_enabled;}
\DoxyCodeLine{00039\ \};}
\DoxyCodeLine{00040\ }
\DoxyCodeLine{00041\ \textcolor{preprocessor}{\#endif\ }\textcolor{comment}{//\ \_DEADLINE\_TIMER\_H}}

\end{DoxyCode}
