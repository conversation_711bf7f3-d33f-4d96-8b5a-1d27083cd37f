\doxysection{security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t Struct Reference}
\hypertarget{structsecurity__failure__event__sets__t}{}\label{structsecurity__failure__event__sets__t}\index{security\_failure\_event\_sets\_t@{security\_failure\_event\_sets\_t}}
Inheritance diagram for security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{structsecurity__failure__event__sets__t}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{structsecurity__failure__event__sets__t_a9b0787cc45df854cd8eb56e794039193}\label{structsecurity__failure__event__sets__t_a9b0787cc45df854cd8eb56e794039193} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries operator==} (const \mbox{\hyperlink{structsecurity__failure__event__sets__t}{security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t}} \&o) const
\item 
\Hypertarget{structsecurity__failure__event__sets__t_aae5aa74284a760a42f3617cdcd1b1f2a}\label{structsecurity__failure__event__sets__t_aae5aa74284a760a42f3617cdcd1b1f2a} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries operator$<$} (const \mbox{\hyperlink{structsecurity__failure__event__sets__t}{security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t}} \&o) const
\item 
\Hypertarget{structsecurity__failure__event__sets__t_adefa2e7c148c14736090c7c342a1d624}\label{structsecurity__failure__event__sets__t_adefa2e7c148c14736090c7c342a1d624} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT {\bfseries security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t} (const \mbox{\hyperlink{structsecurity__failure__event__sets__t}{security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t}} \&o)
\item 
\Hypertarget{structsecurity__failure__event__sets__t_aae682d73e0121d693dad50944704ed8e}\label{structsecurity__failure__event__sets__t_aae682d73e0121d693dad50944704ed8e} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT {\bfseries security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t} (\mbox{\hyperlink{structsecurity__failure__event__sets__t}{security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t}} \&\&o) noexcept
\item 
\Hypertarget{structsecurity__failure__event__sets__t_a657fb6f895dfb970c113aec655553820}\label{structsecurity__failure__event__sets__t_a657fb6f895dfb970c113aec655553820} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT \mbox{\hyperlink{structsecurity__failure__event__sets__t}{security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t}} \& {\bfseries operator=} (const \mbox{\hyperlink{structsecurity__failure__event__sets__t}{security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t}} \&o)
\item 
\Hypertarget{structsecurity__failure__event__sets__t_a7e5e17c3f31bec48ccaa60bacfa6c7ab}\label{structsecurity__failure__event__sets__t_a7e5e17c3f31bec48ccaa60bacfa6c7ab} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT \mbox{\hyperlink{structsecurity__failure__event__sets__t}{security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t}} \& {\bfseries operator=} (\mbox{\hyperlink{structsecurity__failure__event__sets__t}{security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t}} \&\&o) noexcept
\item 
virtual CONFIG\+\_\+\+LIB\+\_\+\+EXPORT boost\+::property\+\_\+tree\+::ptree \mbox{\hyperlink{structsecurity__failure__event__sets__t_aeef84e974372611f3a10352d69aa7e0e}{Get\+New\+Property\+Tree}} () const override
\item 
virtual CONFIG\+\_\+\+LIB\+\_\+\+EXPORT bool \mbox{\hyperlink{structsecurity__failure__event__sets__t_a3586d584439beaafdf4de19b389dd517}{Set\+Values\+From\+Property\+Tree}} (const boost\+::property\+\_\+tree\+::ptree \&pt) override
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}}
\begin{DoxyCompactItemize}
\item 
std\+::string {\bfseries To\+Json} (bool pretty=true) const
\item 
bool {\bfseries To\+Json\+File} (const boost\+::filesystem\+::path \&file\+Path, bool pretty=true) const
\item 
std\+::string {\bfseries To\+Xml} () const
\item 
bool {\bfseries To\+Xml\+File} (const std\+::string \&file\+Name, char indent\+Character=\textquotesingle{} \textquotesingle{}, int indent\+Count=2) const
\item 
bool {\bfseries Set\+Values\+From\+Json} (const std\+::string \&json\+String)
\item 
bool {\bfseries Set\+Values\+From\+Json\+Char\+Array} (const char \texorpdfstring{$\ast$}{*}data, const std\+::size\+\_\+t size)
\end{DoxyCompactItemize}
\doxysubsubsection*{Public Attributes}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{structsecurity__failure__event__sets__t_ae72699823a3f1641d8e1ac954f730de3}\label{structsecurity__failure__event__sets__t_ae72699823a3f1641d8e1ac954f730de3} 
std\+::string {\bfseries m\+\_\+name}
\item 
\Hypertarget{structsecurity__failure__event__sets__t_a46db66ff995121e691cc279dc201be33}\label{structsecurity__failure__event__sets__t_a46db66ff995121e691cc279dc201be33} 
std\+::string {\bfseries m\+\_\+description}
\item 
\Hypertarget{structsecurity__failure__event__sets__t_a25c6bd767ab8a2cf6a188cd71fae5be2}\label{structsecurity__failure__event__sets__t_a25c6bd767ab8a2cf6a188cd71fae5be2} 
security\+\_\+operation\+\_\+event\+\_\+plus\+\_\+actions\+\_\+pairs\+\_\+vec\+\_\+t {\bfseries m\+\_\+security\+Operation\+Events\+Vec}
\item 
\Hypertarget{structsecurity__failure__event__sets__t_ac7c23dcb0a9a929f27ef78eae4a81cc3}\label{structsecurity__failure__event__sets__t_ac7c23dcb0a9a929f27ef78eae4a81cc3} 
event\+\_\+type\+\_\+to\+\_\+event\+\_\+set\+\_\+ptr\+\_\+lut\+\_\+t {\bfseries m\+\_\+event\+Type\+To\+Event\+Set\+Ptr\+Lut}
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Static Public Member Functions inherited from \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}}
\begin{DoxyCompactItemize}
\item 
static bool {\bfseries Load\+Text\+File\+Into\+String} (const boost\+::filesystem\+::path \&file\+Path, std\+::string \&file\+Contents\+As\+String)
\item 
static void {\bfseries Get\+All\+Json\+Keys} (const std\+::string \&json\+Text, std\+::set$<$ std\+::string $>$ \&json\+Keys\+No\+Quotes\+Set\+To\+Append)
\item 
static void {\bfseries Get\+All\+Json\+Keys\+Line\+By\+Line} (std\+::istream \&stream, std\+::set$<$ std\+::string $>$ \&json\+Keys\+No\+Quotes\+Set\+To\+Append)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+File\+Path} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, const boost\+::filesystem\+::path \&original\+User\+Json\+File\+Path, std\+::string \&returned\+Error\+Message)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+String} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, const std\+::string \&original\+User\+Json\+String, std\+::string \&returned\+Error\+Message)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+Stream} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, std\+::istream \&original\+User\+Json\+Stream, std\+::string \&returned\+Error\+Message)
\item 
static std\+::string {\bfseries Pt\+To\+Json\+String} (const boost\+::property\+\_\+tree\+::ptree \&pt, bool pretty=true)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+Char\+Array} (char \texorpdfstring{$\ast$}{*}data, const std\+::size\+\_\+t size, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+Stream} (std\+::istream \&json\+Stream, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+String} (const std\+::string \&json\+Str, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+File\+Path} (const boost\+::filesystem\+::path \&json\+File\+Path, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static std\+::string {\bfseries Pt\+To\+Xml\+String} (const boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static boost\+::property\+\_\+tree\+::ptree {\bfseries Get\+Property\+Tree\+From\+Xml\+String} (const std\+::string \&json\+Str)
\item 
static boost\+::property\+\_\+tree\+::ptree {\bfseries Get\+Property\+Tree\+From\+Xml\+File} (const std\+::string \&xml\+File\+Name)
\end{DoxyCompactItemize}


\doxysubsection{Member Function Documentation}
\Hypertarget{structsecurity__failure__event__sets__t_aeef84e974372611f3a10352d69aa7e0e}\index{security\_failure\_event\_sets\_t@{security\_failure\_event\_sets\_t}!GetNewPropertyTree@{GetNewPropertyTree}}
\index{GetNewPropertyTree@{GetNewPropertyTree}!security\_failure\_event\_sets\_t@{security\_failure\_event\_sets\_t}}
\doxysubsubsection{\texorpdfstring{GetNewPropertyTree()}{GetNewPropertyTree()}}
{\footnotesize\ttfamily \label{structsecurity__failure__event__sets__t_aeef84e974372611f3a10352d69aa7e0e} 
boost\+::property\+\_\+tree\+::ptree security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t\+::\+Get\+New\+Property\+Tree (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption}) const\hspace{0.3cm}{\ttfamily [override]}, {\ttfamily [virtual]}}



Implements \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}.

\Hypertarget{structsecurity__failure__event__sets__t_a3586d584439beaafdf4de19b389dd517}\index{security\_failure\_event\_sets\_t@{security\_failure\_event\_sets\_t}!SetValuesFromPropertyTree@{SetValuesFromPropertyTree}}
\index{SetValuesFromPropertyTree@{SetValuesFromPropertyTree}!security\_failure\_event\_sets\_t@{security\_failure\_event\_sets\_t}}
\doxysubsubsection{\texorpdfstring{SetValuesFromPropertyTree()}{SetValuesFromPropertyTree()}}
{\footnotesize\ttfamily \label{structsecurity__failure__event__sets__t_a3586d584439beaafdf4de19b389dd517} 
bool security\+\_\+failure\+\_\+event\+\_\+sets\+\_\+t\+::\+Set\+Values\+From\+Property\+Tree (\begin{DoxyParamCaption}\item[{const boost\+::property\+\_\+tree\+::ptree \&}]{pt}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [override]}, {\ttfamily [virtual]}}



Implements \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}.



The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
common/config/include/\mbox{\hyperlink{_bp_sec_config_8h}{Bp\+Sec\+Config.\+h}}\item 
common/config/src/\mbox{\hyperlink{_bp_sec_config_8cpp}{Bp\+Sec\+Config.\+cpp}}\end{DoxyCompactItemize}
