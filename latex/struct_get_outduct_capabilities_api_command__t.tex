\doxysection{Get\+Outduct\+Capabilities\+Api\+Command\+\_\+t Struct Reference}
\hypertarget{struct_get_outduct_capabilities_api_command__t}{}\label{struct_get_outduct_capabilities_api_command__t}\index{GetOutductCapabilitiesApiCommand\_t@{GetOutductCapabilitiesApiCommand\_t}}
Inheritance diagram for Get\+Outduct\+Capabilities\+Api\+Command\+\_\+t\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{struct_get_outduct_capabilities_api_command__t}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT \mbox{\hyperlink{struct_get_outduct_capabilities_api_command__t_a880110baabe839e54f698d9aa6dac224}{Get\+Outduct\+Capabilities\+Api\+Command\+\_\+t}} ()
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}}}
\begin{DoxyCompactItemize}
\item 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{Api\+Command\+\_\+t}} ()
\item 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT bool {\bfseries operator==} (const \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}} \&o) const
\item 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT bool {\bfseries operator!=} (const \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}} \&o) const
\item 
virtual TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT boost\+::property\+\_\+tree\+::ptree \mbox{\hyperlink{struct_api_command__t_ab28f4b4980e74c59e770c28c3a3cc83a}{Get\+New\+Property\+Tree}} () const override
\item 
virtual TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT bool \mbox{\hyperlink{struct_api_command__t_ace0b0d45056be361a39ca9eeb968ed55}{Set\+Values\+From\+Property\+Tree}} (const boost\+::property\+\_\+tree\+::ptree \&pt) override
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}}
\begin{DoxyCompactItemize}
\item 
std\+::string {\bfseries To\+Json} (bool pretty=true) const
\item 
bool {\bfseries To\+Json\+File} (const boost\+::filesystem\+::path \&file\+Path, bool pretty=true) const
\item 
std\+::string {\bfseries To\+Xml} () const
\item 
bool {\bfseries To\+Xml\+File} (const std\+::string \&file\+Name, char indent\+Character=\textquotesingle{} \textquotesingle{}, int indent\+Count=2) const
\item 
bool {\bfseries Set\+Values\+From\+Json} (const std\+::string \&json\+String)
\item 
bool {\bfseries Set\+Values\+From\+Json\+Char\+Array} (const char \texorpdfstring{$\ast$}{*}data, const std\+::size\+\_\+t size)
\end{DoxyCompactItemize}
\doxysubsubsection*{Static Public Attributes}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{struct_get_outduct_capabilities_api_command__t_ae4d5cac457c35f7948c08bc73fa2c82c}\label{struct_get_outduct_capabilities_api_command__t_ae4d5cac457c35f7948c08bc73fa2c82c} 
static TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT const std\+::string {\bfseries name} = "{}get\+\_\+outduct\+\_\+capabilities"{}
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Static Public Member Functions inherited from \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}}}
\begin{DoxyCompactItemize}
\item 
static TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT std\+::shared\+\_\+ptr$<$ \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}} $>$ {\bfseries Create\+From\+Json} (const std\+::string \&json\+Str)
\end{DoxyCompactItemize}
\doxysubsection*{Static Public Member Functions inherited from \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}}
\begin{DoxyCompactItemize}
\item 
static bool {\bfseries Load\+Text\+File\+Into\+String} (const boost\+::filesystem\+::path \&file\+Path, std\+::string \&file\+Contents\+As\+String)
\item 
static void {\bfseries Get\+All\+Json\+Keys} (const std\+::string \&json\+Text, std\+::set$<$ std\+::string $>$ \&json\+Keys\+No\+Quotes\+Set\+To\+Append)
\item 
static void {\bfseries Get\+All\+Json\+Keys\+Line\+By\+Line} (std\+::istream \&stream, std\+::set$<$ std\+::string $>$ \&json\+Keys\+No\+Quotes\+Set\+To\+Append)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+File\+Path} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, const boost\+::filesystem\+::path \&original\+User\+Json\+File\+Path, std\+::string \&returned\+Error\+Message)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+String} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, const std\+::string \&original\+User\+Json\+String, std\+::string \&returned\+Error\+Message)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+Stream} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, std\+::istream \&original\+User\+Json\+Stream, std\+::string \&returned\+Error\+Message)
\item 
static std\+::string {\bfseries Pt\+To\+Json\+String} (const boost\+::property\+\_\+tree\+::ptree \&pt, bool pretty=true)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+Char\+Array} (char \texorpdfstring{$\ast$}{*}data, const std\+::size\+\_\+t size, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+Stream} (std\+::istream \&json\+Stream, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+String} (const std\+::string \&json\+Str, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+File\+Path} (const boost\+::filesystem\+::path \&json\+File\+Path, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static std\+::string {\bfseries Pt\+To\+Xml\+String} (const boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static boost\+::property\+\_\+tree\+::ptree {\bfseries Get\+Property\+Tree\+From\+Xml\+String} (const std\+::string \&json\+Str)
\item 
static boost\+::property\+\_\+tree\+::ptree {\bfseries Get\+Property\+Tree\+From\+Xml\+File} (const std\+::string \&xml\+File\+Name)
\end{DoxyCompactItemize}
\doxysubsection*{Public Attributes inherited from \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}}}
\begin{DoxyCompactItemize}
\item 
std\+::string {\bfseries m\+\_\+api\+Call}
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{struct_get_outduct_capabilities_api_command__t_a880110baabe839e54f698d9aa6dac224}\index{GetOutductCapabilitiesApiCommand\_t@{GetOutductCapabilitiesApiCommand\_t}!GetOutductCapabilitiesApiCommand\_t@{GetOutductCapabilitiesApiCommand\_t}}
\index{GetOutductCapabilitiesApiCommand\_t@{GetOutductCapabilitiesApiCommand\_t}!GetOutductCapabilitiesApiCommand\_t@{GetOutductCapabilitiesApiCommand\_t}}
\doxysubsubsection{\texorpdfstring{GetOutductCapabilitiesApiCommand\_t()}{GetOutductCapabilitiesApiCommand\_t()}}
{\footnotesize\ttfamily \label{struct_get_outduct_capabilities_api_command__t_a880110baabe839e54f698d9aa6dac224} 
Get\+Outduct\+Capabilities\+Api\+Command\+\_\+t\+::\+Get\+Outduct\+Capabilities\+Api\+Command\+\_\+t (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\doxylink{struct_get_outduct_capabilities_api_command__t}{Get\+Outduct\+Capabilities\+Api\+Command\+\_\+t} 

The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
common/telemetry\+\_\+definitions/include/\mbox{\hyperlink{_telemetry_definitions_8h}{Telemetry\+Definitions.\+h}}\item 
common/telemetry\+\_\+definitions/src/Telemetry\+Definitions.\+cpp\end{DoxyCompactItemize}
