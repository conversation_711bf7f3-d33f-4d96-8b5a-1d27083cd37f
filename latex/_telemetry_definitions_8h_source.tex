\doxysection{Telemetry\+Definitions.\+h}
\hypertarget{_telemetry_definitions_8h_source}{}\label{_telemetry_definitions_8h_source}\index{common/telemetry\_definitions/include/TelemetryDefinitions.h@{common/telemetry\_definitions/include/TelemetryDefinitions.h}}
\mbox{\hyperlink{_telemetry_definitions_8h}{Go to the documentation of this file.}}
\begin{DoxyCode}{0}
\DoxyCodeLine{00001\ }
\DoxyCodeLine{00019\ }
\DoxyCodeLine{00020\ \textcolor{preprocessor}{\#ifndef\ HDTN\_TELEMETRY\_H}}
\DoxyCodeLine{00021\ \textcolor{preprocessor}{\#define\ HDTN\_TELEMETRY\_H\ 1}}
\DoxyCodeLine{00022\ }
\DoxyCodeLine{00023\ \textcolor{preprocessor}{\#include\ <cstdint>}}
\DoxyCodeLine{00024\ \textcolor{preprocessor}{\#include\ <map>}}
\DoxyCodeLine{00025\ \textcolor{preprocessor}{\#include\ <list>}}
\DoxyCodeLine{00026\ \textcolor{preprocessor}{\#include\ <vector>}}
\DoxyCodeLine{00027\ }
\DoxyCodeLine{00028\ \textcolor{preprocessor}{\#include\ <zmq.hpp>}}
\DoxyCodeLine{00029\ }
\DoxyCodeLine{00030\ \textcolor{preprocessor}{\#include\ "{}telemetry\_definitions\_export.h"{}}}
\DoxyCodeLine{00031\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_cbhe_8h}{codec/Cbhe.h}}"{}}}
\DoxyCodeLine{00032\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_json_serializable_8h}{JsonSerializable.h}}"{}}}
\DoxyCodeLine{00033\ }
\DoxyCodeLine{00034\ \textcolor{preprocessor}{\#ifndef\ CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS}}
\DoxyCodeLine{00035\ \textcolor{preprocessor}{\#\ \ ifdef\ \_WIN32}}
\DoxyCodeLine{00036\ \textcolor{preprocessor}{\#\ \ \ \ define\ CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS}}
\DoxyCodeLine{00037\ \textcolor{preprocessor}{\#\ \ else}}
\DoxyCodeLine{00038\ \textcolor{preprocessor}{\#\ \ \ \ define\ CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ TELEMETRY\_DEFINITIONS\_EXPORT}}
\DoxyCodeLine{00039\ \textcolor{preprocessor}{\#\ \ endif}}
\DoxyCodeLine{00040\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00041\ }
\DoxyCodeLine{00042\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ StorageTelemetry\_t\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00043\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ StorageTelemetry\_t();}
\DoxyCodeLine{00044\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \string~StorageTelemetry\_t();}
\DoxyCodeLine{00045\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ StorageTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00046\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ StorageTelemetry\_t\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00047\ }
\DoxyCodeLine{00048\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00049\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00050\ }
\DoxyCodeLine{00051\ \ \ \ \ uint64\_t\ m\_timestampMilliseconds;}
\DoxyCodeLine{00052\ }
\DoxyCodeLine{00053\ \ \ \ \ \textcolor{comment}{//from\ ZmqStorageInterface}}
\DoxyCodeLine{00054\ \ \ \ \ uint64\_t\ m\_totalBundlesErasedFromStorageNoCustodyTransfer;}
\DoxyCodeLine{00055\ \ \ \ \ uint64\_t\ m\_totalBundlesErasedFromStorageWithCustodyTransfer;}
\DoxyCodeLine{00056\ \ \ \ \ uint64\_t\ m\_totalBundlesErasedFromStorageBecauseExpired;}
\DoxyCodeLine{00057\ \ \ \ \ uint64\_t\ m\_totalBundlesRewrittenToStorageFromFailedEgressSend;}
\DoxyCodeLine{00058\ \ \ \ \ uint64\_t\ m\_totalBundlesSentToEgressFromStorageReadFromDisk;}
\DoxyCodeLine{00059\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSentToEgressFromStorageReadFromDisk;}
\DoxyCodeLine{00060\ \ \ \ \ uint64\_t\ m\_totalBundlesSentToEgressFromStorageForwardCutThrough;}
\DoxyCodeLine{00061\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSentToEgressFromStorageForwardCutThrough;}
\DoxyCodeLine{00062\ \ \ \ \ uint64\_t\ m\_numRfc5050CustodyTransfers;}
\DoxyCodeLine{00063\ \ \ \ \ uint64\_t\ m\_numAcsCustodyTransfers;}
\DoxyCodeLine{00064\ \ \ \ \ uint64\_t\ m\_numAcsPacketsReceived;}
\DoxyCodeLine{00065\ \ \ \ \ }
\DoxyCodeLine{00066\ \ \ \ \ \textcolor{comment}{//from\ BundleStorageCatalog}}
\DoxyCodeLine{00067\ \ \ \ \ uint64\_t\ m\_numBundlesOnDisk;}
\DoxyCodeLine{00068\ \ \ \ \ uint64\_t\ m\_numBundleBytesOnDisk;}
\DoxyCodeLine{00069\ \ \ \ \ uint64\_t\ m\_totalBundleWriteOperationsToDisk;}
\DoxyCodeLine{00070\ \ \ \ \ uint64\_t\ m\_totalBundleByteWriteOperationsToDisk;}
\DoxyCodeLine{00071\ \ \ \ \ uint64\_t\ m\_totalBundleEraseOperationsFromDisk;}
\DoxyCodeLine{00072\ \ \ \ \ uint64\_t\ m\_totalBundleByteEraseOperationsFromDisk;}
\DoxyCodeLine{00073\ }
\DoxyCodeLine{00074\ \ \ \ \ \textcolor{comment}{//from\ BundleStorageManagerBase's\ MemoryManager}}
\DoxyCodeLine{00075\ \ \ \ \ uint64\_t\ m\_usedSpaceBytes;}
\DoxyCodeLine{00076\ \ \ \ \ uint64\_t\ m\_freeSpaceBytes;}
\DoxyCodeLine{00077\ \};}
\DoxyCodeLine{00078\ }
\DoxyCodeLine{00079\ }
\DoxyCodeLine{00080\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ StorageExpiringBeforeThresholdTelemetry\_t\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00081\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ StorageExpiringBeforeThresholdTelemetry\_t();}
\DoxyCodeLine{00082\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \string~StorageExpiringBeforeThresholdTelemetry\_t();}
\DoxyCodeLine{00083\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ StorageExpiringBeforeThresholdTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00084\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ StorageExpiringBeforeThresholdTelemetry\_t\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00085\ }
\DoxyCodeLine{00086\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00087\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00088\ }
\DoxyCodeLine{00089\ \ \ \ \ uint64\_t\ priority;}
\DoxyCodeLine{00090\ \ \ \ \ uint64\_t\ thresholdSecondsSinceStartOfYear2000;}
\DoxyCodeLine{00091\ \ \ \ \ \textcolor{keyword}{typedef}\ std::pair<uint64\_t,\ uint64\_t>\ bundle\_count\_plus\_bundle\_bytes\_pair\_t;}
\DoxyCodeLine{00092\ \ \ \ \ std::map<uint64\_t,\ bundle\_count\_plus\_bundle\_bytes\_pair\_t>\ mapNodeIdToExpiringBeforeThresholdCount;}
\DoxyCodeLine{00093\ \};}
\DoxyCodeLine{00094\ }
\DoxyCodeLine{00095\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ OutductCapabilityTelemetry\_t\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00096\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ OutductCapabilityTelemetry\_t();}
\DoxyCodeLine{00097\ }
\DoxyCodeLine{00098\ \ \ \ \ }
\DoxyCodeLine{00099\ \ \ \ \ uint64\_t\ outductArrayIndex;\ \textcolor{comment}{//outductUuid}}
\DoxyCodeLine{00100\ \ \ \ \ uint64\_t\ maxBundlesInPipeline;}
\DoxyCodeLine{00101\ \ \ \ \ uint64\_t\ maxBundleSizeBytesInPipeline;}
\DoxyCodeLine{00102\ \ \ \ \ uint64\_t\ nextHopNodeId;}
\DoxyCodeLine{00103\ \ \ \ \ \textcolor{keywordtype}{bool}\ assumedInitiallyDown;}
\DoxyCodeLine{00104\ \ \ \ \ std::list<cbhe\_eid\_t>\ finalDestinationEidList;}
\DoxyCodeLine{00105\ \ \ \ \ std::list<uint64\_t>\ finalDestinationNodeIdList;}
\DoxyCodeLine{00106\ }
\DoxyCodeLine{00107\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ OutductCapabilityTelemetry\_t(\textcolor{keyword}{const}\ OutductCapabilityTelemetry\_t\&\ o);\ \textcolor{comment}{//a\ copy\ constructor:\ X(const\ X\&)}}
\DoxyCodeLine{00108\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ OutductCapabilityTelemetry\_t(OutductCapabilityTelemetry\_t\&\&\ o)\ \textcolor{keyword}{noexcept};\ \textcolor{comment}{//a\ move\ constructor:\ X(X\&\&)}}
\DoxyCodeLine{00109\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ OutductCapabilityTelemetry\_t\&\ operator=(\textcolor{keyword}{const}\ OutductCapabilityTelemetry\_t\&\ o);\ \textcolor{comment}{//a\ copy\ assignment:\ operator=(const\ X\&)}}
\DoxyCodeLine{00110\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ OutductCapabilityTelemetry\_t\&\ operator=(OutductCapabilityTelemetry\_t\&\&\ o)\ \textcolor{keyword}{noexcept};\ \textcolor{comment}{//a\ move\ assignment:\ operator=(X\&\&)}}
\DoxyCodeLine{00111\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ OutductCapabilityTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00112\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ OutductCapabilityTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ !=}}
\DoxyCodeLine{00113\ }
\DoxyCodeLine{00114\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00115\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00116\ \};}
\DoxyCodeLine{00117\ }
\DoxyCodeLine{00118\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ AllOutductCapabilitiesTelemetry\_t\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00119\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ AllOutductCapabilitiesTelemetry\_t();}
\DoxyCodeLine{00120\ }
\DoxyCodeLine{00121\ \ \ \ \ std::list<OutductCapabilityTelemetry\_t>\ outductCapabilityTelemetryList;}
\DoxyCodeLine{00122\ }
\DoxyCodeLine{00123\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ AllOutductCapabilitiesTelemetry\_t(\textcolor{keyword}{const}\ AllOutductCapabilitiesTelemetry\_t\&\ o);\ \textcolor{comment}{//a\ copy\ constructor:\ X(const\ X\&)}}
\DoxyCodeLine{00124\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ AllOutductCapabilitiesTelemetry\_t(AllOutductCapabilitiesTelemetry\_t\&\&\ o)\ \textcolor{keyword}{noexcept};\ \textcolor{comment}{//a\ move\ constructor:\ X(X\&\&)}}
\DoxyCodeLine{00125\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ AllOutductCapabilitiesTelemetry\_t\&\ operator=(\textcolor{keyword}{const}\ AllOutductCapabilitiesTelemetry\_t\&\ o);\ \textcolor{comment}{//a\ copy\ assignment:\ operator=(const\ X\&)}}
\DoxyCodeLine{00126\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ AllOutductCapabilitiesTelemetry\_t\&\ operator=(AllOutductCapabilitiesTelemetry\_t\&\&\ o)\ \textcolor{keyword}{noexcept};\ \textcolor{comment}{//a\ move\ assignment:\ operator=(X\&\&)}}
\DoxyCodeLine{00127\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ AllOutductCapabilitiesTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00128\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ AllOutductCapabilitiesTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ !=}}
\DoxyCodeLine{00129\ }
\DoxyCodeLine{00130\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00131\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00132\ \};}
\DoxyCodeLine{00133\ }
\DoxyCodeLine{00134\ }
\DoxyCodeLine{00135\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ InductConnectionTelemetry\_t\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00136\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ InductConnectionTelemetry\_t();}
\DoxyCodeLine{00137\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~InductConnectionTelemetry\_t();}
\DoxyCodeLine{00138\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00139\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00140\ }
\DoxyCodeLine{00141\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00142\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00143\ }
\DoxyCodeLine{00144\ \ \ \ \ std::string\ m\_connectionName;}
\DoxyCodeLine{00145\ \ \ \ \ std::string\ m\_inputName;}
\DoxyCodeLine{00146\ \ \ \ \ uint64\_t\ m\_totalBundlesReceived;}
\DoxyCodeLine{00147\ \ \ \ \ uint64\_t\ m\_totalBundleBytesReceived;}
\DoxyCodeLine{00148\ \};}
\DoxyCodeLine{00149\ }
\DoxyCodeLine{00150\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ StcpInductConnectionTelemetry\_t\ :\ \textcolor{keyword}{public}\ InductConnectionTelemetry\_t\ \{}
\DoxyCodeLine{00151\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ StcpInductConnectionTelemetry\_t();}
\DoxyCodeLine{00152\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~StcpInductConnectionTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00153\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00154\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00155\ }
\DoxyCodeLine{00156\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00157\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00158\ }
\DoxyCodeLine{00159\ \ \ \ \ uint64\_t\ m\_totalStcpBytesReceived;}
\DoxyCodeLine{00160\ \};}
\DoxyCodeLine{00161\ }
\DoxyCodeLine{00162\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ UdpInductConnectionTelemetry\_t\ :\ \textcolor{keyword}{public}\ InductConnectionTelemetry\_t\ \{}
\DoxyCodeLine{00163\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ UdpInductConnectionTelemetry\_t();}
\DoxyCodeLine{00164\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~UdpInductConnectionTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00165\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00166\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00167\ }
\DoxyCodeLine{00168\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00169\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00170\ }
\DoxyCodeLine{00171\ \ \ \ \ uint64\_t\ m\_countCircularBufferOverruns;}
\DoxyCodeLine{00172\ \};}
\DoxyCodeLine{00173\ }
\DoxyCodeLine{00174\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ TcpclV3InductConnectionTelemetry\_t\ :\ \textcolor{keyword}{public}\ InductConnectionTelemetry\_t\ \{}
\DoxyCodeLine{00175\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ TcpclV3InductConnectionTelemetry\_t();}
\DoxyCodeLine{00176\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~TcpclV3InductConnectionTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00177\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00178\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00179\ }
\DoxyCodeLine{00180\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00181\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00182\ }
\DoxyCodeLine{00183\ \ \ \ \ uint64\_t\ m\_totalIncomingFragmentsAcked;}
\DoxyCodeLine{00184\ \ \ \ \ uint64\_t\ m\_totalOutgoingFragmentsSent;}
\DoxyCodeLine{00185\ \ \ \ \ \textcolor{comment}{//bidirectionality\ (identical\ to\ OutductTelemetry\_t)}}
\DoxyCodeLine{00186\ \ \ \ \ uint64\_t\ m\_totalBundlesSentAndAcked;}
\DoxyCodeLine{00187\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSentAndAcked;}
\DoxyCodeLine{00188\ \ \ \ \ uint64\_t\ m\_totalBundlesSent;}
\DoxyCodeLine{00189\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSent;}
\DoxyCodeLine{00190\ \ \ \ \ uint64\_t\ m\_totalBundlesFailedToSend;}
\DoxyCodeLine{00191\ \};}
\DoxyCodeLine{00192\ }
\DoxyCodeLine{00193\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ TcpclV4InductConnectionTelemetry\_t\ :\ \textcolor{keyword}{public}\ InductConnectionTelemetry\_t\ \{}
\DoxyCodeLine{00194\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ TcpclV4InductConnectionTelemetry\_t();}
\DoxyCodeLine{00195\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~TcpclV4InductConnectionTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00196\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00197\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00198\ }
\DoxyCodeLine{00199\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00200\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00201\ }
\DoxyCodeLine{00202\ \ \ \ \ uint64\_t\ m\_totalIncomingFragmentsAcked;}
\DoxyCodeLine{00203\ \ \ \ \ uint64\_t\ m\_totalOutgoingFragmentsSent;}
\DoxyCodeLine{00204\ \ \ \ \ \textcolor{comment}{//bidirectionality\ (identical\ to\ OutductTelemetry\_t)}}
\DoxyCodeLine{00205\ \ \ \ \ uint64\_t\ m\_totalBundlesSentAndAcked;}
\DoxyCodeLine{00206\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSentAndAcked;}
\DoxyCodeLine{00207\ \ \ \ \ uint64\_t\ m\_totalBundlesSent;}
\DoxyCodeLine{00208\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSent;}
\DoxyCodeLine{00209\ \ \ \ \ uint64\_t\ m\_totalBundlesFailedToSend;}
\DoxyCodeLine{00210\ \};}
\DoxyCodeLine{00211\ }
\DoxyCodeLine{00212\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ SlipOverUartInductConnectionTelemetry\_t\ :\ \textcolor{keyword}{public}\ InductConnectionTelemetry\_t\ \{}
\DoxyCodeLine{00213\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ SlipOverUartInductConnectionTelemetry\_t();}
\DoxyCodeLine{00214\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~SlipOverUartInductConnectionTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00215\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00216\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00217\ }
\DoxyCodeLine{00218\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00219\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00220\ }
\DoxyCodeLine{00221\ \ \ \ \ uint64\_t\ m\_totalSlipBytesSent;}
\DoxyCodeLine{00222\ \ \ \ \ uint64\_t\ m\_totalSlipBytesReceived;}
\DoxyCodeLine{00223\ \ \ \ \ uint64\_t\ m\_totalReceivedChunks;}
\DoxyCodeLine{00224\ \ \ \ \ uint64\_t\ m\_largestReceivedBytesPerChunk;}
\DoxyCodeLine{00225\ \ \ \ \ uint64\_t\ m\_averageReceivedBytesPerChunk;}
\DoxyCodeLine{00226\ \ \ \ \ \textcolor{comment}{//bidirectionality\ (identical\ to\ OutductTelemetry\_t)}}
\DoxyCodeLine{00227\ \ \ \ \ uint64\_t\ m\_totalBundlesSentAndAcked;}
\DoxyCodeLine{00228\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSentAndAcked;}
\DoxyCodeLine{00229\ \ \ \ \ uint64\_t\ m\_totalBundlesSent;}
\DoxyCodeLine{00230\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSent;}
\DoxyCodeLine{00231\ \ \ \ \ uint64\_t\ m\_totalBundlesFailedToSend;}
\DoxyCodeLine{00232\ \};}
\DoxyCodeLine{00233\ }
\DoxyCodeLine{00234\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ BpOverEncapLocalStreamInductConnectionTelemetry\_t\ :\ \textcolor{keyword}{public}\ InductConnectionTelemetry\_t\ \{}
\DoxyCodeLine{00235\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ BpOverEncapLocalStreamInductConnectionTelemetry\_t();}
\DoxyCodeLine{00236\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~BpOverEncapLocalStreamInductConnectionTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00237\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00238\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00239\ }
\DoxyCodeLine{00240\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00241\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00242\ }
\DoxyCodeLine{00243\ \ \ \ \ uint64\_t\ m\_totalEncapHeaderBytesSent;}
\DoxyCodeLine{00244\ \ \ \ \ uint64\_t\ m\_totalEncapHeaderBytesReceived;}
\DoxyCodeLine{00245\ \ \ \ \ uint64\_t\ m\_largestEncapHeaderSizeBytesReceived;}
\DoxyCodeLine{00246\ \ \ \ \ uint64\_t\ m\_smallestEncapHeaderSizeBytesReceived;}
\DoxyCodeLine{00247\ \ \ \ \ uint64\_t\ m\_averageEncapHeaderSizeBytesReceived;}
\DoxyCodeLine{00248\ \ \ \ \ \textcolor{comment}{//bidirectionality\ (identical\ to\ OutductTelemetry\_t)}}
\DoxyCodeLine{00249\ \ \ \ \ uint64\_t\ m\_totalBundlesSentAndAcked;}
\DoxyCodeLine{00250\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSentAndAcked;}
\DoxyCodeLine{00251\ \ \ \ \ uint64\_t\ m\_totalBundlesSent;}
\DoxyCodeLine{00252\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSent;}
\DoxyCodeLine{00253\ \ \ \ \ uint64\_t\ m\_totalBundlesFailedToSend;}
\DoxyCodeLine{00254\ \};}
\DoxyCodeLine{00255\ }
\DoxyCodeLine{00256\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ LtpInductConnectionTelemetry\_t\ :\ \textcolor{keyword}{public}\ InductConnectionTelemetry\_t\ \{}
\DoxyCodeLine{00257\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ LtpInductConnectionTelemetry\_t();}
\DoxyCodeLine{00258\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~LtpInductConnectionTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00259\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00260\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ InductConnectionTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00261\ }
\DoxyCodeLine{00262\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00263\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00264\ }
\DoxyCodeLine{00265\ \ \ \ \ \textcolor{comment}{//session\ receiver\ stats}}
\DoxyCodeLine{00266\ \ \ \ \ uint64\_t\ m\_numReportSegmentTimerExpiredCallbacks;}
\DoxyCodeLine{00267\ \ \ \ \ uint64\_t\ m\_numReportSegmentsUnableToBeIssued;}
\DoxyCodeLine{00268\ \ \ \ \ uint64\_t\ m\_numReportSegmentsTooLargeAndNeedingSplit;}
\DoxyCodeLine{00269\ \ \ \ \ uint64\_t\ m\_numReportSegmentsCreatedViaSplit;}
\DoxyCodeLine{00270\ \ \ \ \ uint64\_t\ m\_numGapsFilledByOutOfOrderDataSegments;}
\DoxyCodeLine{00271\ \ \ \ \ uint64\_t\ m\_numDelayedFullyClaimedPrimaryReportSegmentsSent;}
\DoxyCodeLine{00272\ \ \ \ \ uint64\_t\ m\_numDelayedFullyClaimedSecondaryReportSegmentsSent;}
\DoxyCodeLine{00273\ \ \ \ \ uint64\_t\ m\_numDelayedPartiallyClaimedPrimaryReportSegmentsSent;}
\DoxyCodeLine{00274\ \ \ \ \ uint64\_t\ m\_numDelayedPartiallyClaimedSecondaryReportSegmentsSent;}
\DoxyCodeLine{00275\ \ \ \ \ uint64\_t\ m\_totalCancelSegmentsStarted;}
\DoxyCodeLine{00276\ \ \ \ \ uint64\_t\ m\_totalCancelSegmentSendRetries;}
\DoxyCodeLine{00277\ \ \ \ \ uint64\_t\ m\_totalCancelSegmentsFailedToSend;}
\DoxyCodeLine{00278\ \ \ \ \ uint64\_t\ m\_totalCancelSegmentsAcknowledged;}
\DoxyCodeLine{00279\ \ \ \ \ uint64\_t\ m\_numRxSessionsCancelledBySender;}
\DoxyCodeLine{00280\ \ \ \ \ uint64\_t\ m\_numStagnantRxSessionsDeleted;}
\DoxyCodeLine{00281\ }
\DoxyCodeLine{00282\ \ \ \ \ \textcolor{comment}{//ltp\ udp\ engine}}
\DoxyCodeLine{00283\ \ \ \ \ uint64\_t\ m\_countUdpPacketsSent;}
\DoxyCodeLine{00284\ \ \ \ \ uint64\_t\ m\_countRxUdpCircularBufferOverruns;}
\DoxyCodeLine{00285\ \ \ \ \ uint64\_t\ m\_countTxUdpPacketsLimitedByRate;}
\DoxyCodeLine{00286\ \ \ \ \ }
\DoxyCodeLine{00287\ \};}
\DoxyCodeLine{00288\ }
\DoxyCodeLine{00289\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ InductTelemetry\_t\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00290\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ InductTelemetry\_t();}
\DoxyCodeLine{00291\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ InductTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00292\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ InductTelemetry\_t\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00293\ }
\DoxyCodeLine{00294\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00295\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00296\ }
\DoxyCodeLine{00297\ \ \ \ \ std::string\ m\_convergenceLayer;}
\DoxyCodeLine{00298\ \ \ \ \ std::list<std::unique\_ptr<InductConnectionTelemetry\_t>\ >\ m\_listInductConnections;}
\DoxyCodeLine{00299\ \};}
\DoxyCodeLine{00300\ }
\DoxyCodeLine{00301\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ AllInductTelemetry\_t\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00302\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ AllInductTelemetry\_t();}
\DoxyCodeLine{00303\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ AllInductTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00304\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ AllInductTelemetry\_t\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00305\ }
\DoxyCodeLine{00306\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00307\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00308\ \ \ \ \ uint64\_t\ m\_timestampMilliseconds;}
\DoxyCodeLine{00309\ \ \ \ \ \textcolor{comment}{//ingress\ specific}}
\DoxyCodeLine{00310\ \ \ \ \ uint64\_t\ m\_bundleCountEgress;}
\DoxyCodeLine{00311\ \ \ \ \ uint64\_t\ m\_bundleCountStorage;}
\DoxyCodeLine{00312\ \ \ \ \ uint64\_t\ m\_bundleByteCountEgress;}
\DoxyCodeLine{00313\ \ \ \ \ uint64\_t\ m\_bundleByteCountStorage;}
\DoxyCodeLine{00314\ \ \ \ \ \textcolor{comment}{//inducts\ specific}}
\DoxyCodeLine{00315\ \ \ \ \ std::list<InductTelemetry\_t>\ m\_listAllInducts;}
\DoxyCodeLine{00316\ \};}
\DoxyCodeLine{00317\ }
\DoxyCodeLine{00318\ }
\DoxyCodeLine{00319\ }
\DoxyCodeLine{00320\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ OutductTelemetry\_t\ :\ \textcolor{keyword}{public}\ JsonSerializable}
\DoxyCodeLine{00321\ \{}
\DoxyCodeLine{00322\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ OutductTelemetry\_t();}
\DoxyCodeLine{00323\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~OutductTelemetry\_t();}
\DoxyCodeLine{00324\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00325\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00326\ }
\DoxyCodeLine{00327\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00328\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00329\ }
\DoxyCodeLine{00330\ \ \ \ \ std::string\ m\_convergenceLayer;}
\DoxyCodeLine{00331\ \ \ \ \ uint64\_t\ m\_totalBundlesAcked;}
\DoxyCodeLine{00332\ \ \ \ \ uint64\_t\ m\_totalBundleBytesAcked;}
\DoxyCodeLine{00333\ \ \ \ \ uint64\_t\ m\_totalBundlesSent;}
\DoxyCodeLine{00334\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSent;}
\DoxyCodeLine{00335\ \ \ \ \ uint64\_t\ m\_totalBundlesFailedToSend;}
\DoxyCodeLine{00336\ \ \ \ \ \textcolor{keywordtype}{bool}\ m\_linkIsUpPhysically;}
\DoxyCodeLine{00337\ \ \ \ \ \textcolor{keywordtype}{bool}\ m\_linkIsUpPerTimeSchedule;}
\DoxyCodeLine{00338\ }
\DoxyCodeLine{00339\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ uint64\_t\ GetTotalBundlesQueued()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00340\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ uint64\_t\ GetTotalBundleBytesQueued()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00341\ \};}
\DoxyCodeLine{00342\ }
\DoxyCodeLine{00343\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ StcpOutductTelemetry\_t\ :\ \textcolor{keyword}{public}\ OutductTelemetry\_t\ \{}
\DoxyCodeLine{00344\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ StcpOutductTelemetry\_t();}
\DoxyCodeLine{00345\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~StcpOutductTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00346\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00347\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00348\ }
\DoxyCodeLine{00349\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00350\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00351\ }
\DoxyCodeLine{00352\ \ \ \ \ uint64\_t\ m\_totalStcpBytesSent;}
\DoxyCodeLine{00353\ \ \ \ \ uint64\_t\ m\_numTcpReconnectAttempts;}
\DoxyCodeLine{00354\ \};}
\DoxyCodeLine{00355\ }
\DoxyCodeLine{00356\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ LtpOutductTelemetry\_t\ :\ \textcolor{keyword}{public}\ OutductTelemetry\_t\ \{}
\DoxyCodeLine{00357\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ LtpOutductTelemetry\_t();}
\DoxyCodeLine{00358\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~LtpOutductTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00359\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00360\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00361\ }
\DoxyCodeLine{00362\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00363\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00364\ }
\DoxyCodeLine{00365\ \ \ \ \ \textcolor{comment}{//ltp\ engine\ session\ sender\ stats}}
\DoxyCodeLine{00366\ \ \ \ \ uint64\_t\ m\_numCheckpointsExpired;}
\DoxyCodeLine{00367\ \ \ \ \ uint64\_t\ m\_numDiscretionaryCheckpointsNotResent;}
\DoxyCodeLine{00368\ \ \ \ \ uint64\_t\ m\_numDeletedFullyClaimedPendingReports;}
\DoxyCodeLine{00369\ \ \ \ \ uint64\_t\ m\_totalCancelSegmentsStarted;}
\DoxyCodeLine{00370\ \ \ \ \ uint64\_t\ m\_totalCancelSegmentSendRetries;}
\DoxyCodeLine{00371\ \ \ \ \ uint64\_t\ m\_totalCancelSegmentsFailedToSend;}
\DoxyCodeLine{00372\ \ \ \ \ uint64\_t\ m\_totalCancelSegmentsAcknowledged;}
\DoxyCodeLine{00373\ \ \ \ \ uint64\_t\ m\_totalPingsStarted;}
\DoxyCodeLine{00374\ \ \ \ \ uint64\_t\ m\_totalPingRetries;}
\DoxyCodeLine{00375\ \ \ \ \ uint64\_t\ m\_totalPingsFailedToSend;}
\DoxyCodeLine{00376\ \ \ \ \ uint64\_t\ m\_totalPingsAcknowledged;}
\DoxyCodeLine{00377\ \ \ \ \ uint64\_t\ m\_numTxSessionsReturnedToStorage;}
\DoxyCodeLine{00378\ \ \ \ \ uint64\_t\ m\_numTxSessionsCancelledByReceiver;}
\DoxyCodeLine{00379\ }
\DoxyCodeLine{00380\ \ \ \ \ \textcolor{comment}{//ltp\ udp\ engine}}
\DoxyCodeLine{00381\ \ \ \ \ uint64\_t\ m\_countUdpPacketsSent;}
\DoxyCodeLine{00382\ \ \ \ \ uint64\_t\ m\_countRxUdpCircularBufferOverruns;}
\DoxyCodeLine{00383\ \ \ \ \ uint64\_t\ m\_countTxUdpPacketsLimitedByRate;}
\DoxyCodeLine{00384\ \};}
\DoxyCodeLine{00385\ }
\DoxyCodeLine{00386\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ TcpclV3OutductTelemetry\_t\ :\ \textcolor{keyword}{public}\ OutductTelemetry\_t\ \{}
\DoxyCodeLine{00387\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ TcpclV3OutductTelemetry\_t();}
\DoxyCodeLine{00388\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~TcpclV3OutductTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00389\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00390\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00391\ }
\DoxyCodeLine{00392\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00393\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00394\ }
\DoxyCodeLine{00395\ \ \ \ \ uint64\_t\ m\_totalFragmentsAcked;}
\DoxyCodeLine{00396\ \ \ \ \ uint64\_t\ m\_totalFragmentsSent;}
\DoxyCodeLine{00397\ \ \ \ \ \textcolor{comment}{//bidirectionality\ (identical\ to\ InductConnectionTelemetry\_t)}}
\DoxyCodeLine{00398\ \ \ \ \ uint64\_t\ m\_totalBundlesReceived;}
\DoxyCodeLine{00399\ \ \ \ \ uint64\_t\ m\_totalBundleBytesReceived;}
\DoxyCodeLine{00400\ \ \ \ \ uint64\_t\ m\_numTcpReconnectAttempts;}
\DoxyCodeLine{00401\ \};}
\DoxyCodeLine{00402\ }
\DoxyCodeLine{00403\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ TcpclV4OutductTelemetry\_t\ :\ \textcolor{keyword}{public}\ OutductTelemetry\_t\ \{}
\DoxyCodeLine{00404\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ TcpclV4OutductTelemetry\_t();}
\DoxyCodeLine{00405\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~TcpclV4OutductTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00406\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00407\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00408\ }
\DoxyCodeLine{00409\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00410\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00411\ }
\DoxyCodeLine{00412\ \ \ \ \ uint64\_t\ m\_totalFragmentsAcked;}
\DoxyCodeLine{00413\ \ \ \ \ uint64\_t\ m\_totalFragmentsSent;}
\DoxyCodeLine{00414\ \ \ \ \ \textcolor{comment}{//bidirectionality\ (identical\ to\ InductConnectionTelemetry\_t)}}
\DoxyCodeLine{00415\ \ \ \ \ uint64\_t\ m\_totalBundlesReceived;}
\DoxyCodeLine{00416\ \ \ \ \ uint64\_t\ m\_totalBundleBytesReceived;}
\DoxyCodeLine{00417\ \ \ \ \ uint64\_t\ m\_numTcpReconnectAttempts;}
\DoxyCodeLine{00418\ \};}
\DoxyCodeLine{00419\ }
\DoxyCodeLine{00420\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ SlipOverUartOutductTelemetry\_t\ :\ \textcolor{keyword}{public}\ OutductTelemetry\_t\ \{}
\DoxyCodeLine{00421\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ SlipOverUartOutductTelemetry\_t();}
\DoxyCodeLine{00422\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~SlipOverUartOutductTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00423\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00424\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00425\ }
\DoxyCodeLine{00426\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00427\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00428\ }
\DoxyCodeLine{00429\ \ \ \ \ uint64\_t\ m\_totalSlipBytesSent;}
\DoxyCodeLine{00430\ \ \ \ \ uint64\_t\ m\_totalSlipBytesReceived;}
\DoxyCodeLine{00431\ \ \ \ \ uint64\_t\ m\_totalReceivedChunks;}
\DoxyCodeLine{00432\ \ \ \ \ uint64\_t\ m\_largestReceivedBytesPerChunk;}
\DoxyCodeLine{00433\ \ \ \ \ uint64\_t\ m\_averageReceivedBytesPerChunk;}
\DoxyCodeLine{00434\ \ \ \ \ \textcolor{comment}{//bidirectionality\ (identical\ to\ InductConnectionTelemetry\_t)}}
\DoxyCodeLine{00435\ \ \ \ \ uint64\_t\ m\_totalBundlesReceived;}
\DoxyCodeLine{00436\ \ \ \ \ uint64\_t\ m\_totalBundleBytesReceived;}
\DoxyCodeLine{00437\ \};}
\DoxyCodeLine{00438\ }
\DoxyCodeLine{00439\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ BpOverEncapLocalStreamOutductTelemetry\_t\ :\ \textcolor{keyword}{public}\ OutductTelemetry\_t\ \{}
\DoxyCodeLine{00440\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ BpOverEncapLocalStreamOutductTelemetry\_t();}
\DoxyCodeLine{00441\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~BpOverEncapLocalStreamOutductTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00442\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00443\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00444\ }
\DoxyCodeLine{00445\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00446\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00447\ }
\DoxyCodeLine{00448\ \ \ \ \ uint64\_t\ m\_totalEncapHeaderBytesSent;}
\DoxyCodeLine{00449\ \ \ \ \ uint64\_t\ m\_totalEncapHeaderBytesReceived;}
\DoxyCodeLine{00450\ \ \ \ \ uint64\_t\ m\_largestEncapHeaderSizeBytesSent;}
\DoxyCodeLine{00451\ \ \ \ \ uint64\_t\ m\_smallestEncapHeaderSizeBytesSent;}
\DoxyCodeLine{00452\ \ \ \ \ uint64\_t\ m\_averageEncapHeaderSizeBytesSent;}
\DoxyCodeLine{00453\ \ \ \ \ \textcolor{comment}{//bidirectionality\ (identical\ to\ InductConnectionTelemetry\_t)}}
\DoxyCodeLine{00454\ \ \ \ \ uint64\_t\ m\_totalBundlesReceived;}
\DoxyCodeLine{00455\ \ \ \ \ uint64\_t\ m\_totalBundleBytesReceived;}
\DoxyCodeLine{00456\ \};}
\DoxyCodeLine{00457\ }
\DoxyCodeLine{00458\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ UdpOutductTelemetry\_t\ :\ \textcolor{keyword}{public}\ OutductTelemetry\_t\ \{}
\DoxyCodeLine{00459\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ UdpOutductTelemetry\_t();}
\DoxyCodeLine{00460\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~UdpOutductTelemetry\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00461\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00462\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ OutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00463\ }
\DoxyCodeLine{00464\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00465\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00466\ }
\DoxyCodeLine{00467\ \ \ \ \ uint64\_t\ m\_totalPacketsSent;}
\DoxyCodeLine{00468\ \ \ \ \ uint64\_t\ m\_totalPacketBytesSent;}
\DoxyCodeLine{00469\ \ \ \ \ uint64\_t\ m\_totalPacketsDequeuedForSend;}
\DoxyCodeLine{00470\ \ \ \ \ uint64\_t\ m\_totalPacketBytesDequeuedForSend;}
\DoxyCodeLine{00471\ \ \ \ \ uint64\_t\ m\_totalPacketsLimitedByRate;}
\DoxyCodeLine{00472\ \};}
\DoxyCodeLine{00473\ }
\DoxyCodeLine{00474\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ AllOutductTelemetry\_t\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00475\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ AllOutductTelemetry\_t();}
\DoxyCodeLine{00476\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ AllOutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const};\ \textcolor{comment}{//operator\ ==}}
\DoxyCodeLine{00477\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ AllOutductTelemetry\_t\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00478\ }
\DoxyCodeLine{00479\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00480\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00481\ \ \ \ \ uint64\_t\ m\_timestampMilliseconds;}
\DoxyCodeLine{00482\ \ \ \ \ uint64\_t\ m\_totalBundlesGivenToOutducts;}
\DoxyCodeLine{00483\ \ \ \ \ uint64\_t\ m\_totalBundleBytesGivenToOutducts;}
\DoxyCodeLine{00484\ \ \ \ \ uint64\_t\ m\_totalTcpclBundlesReceived;}
\DoxyCodeLine{00485\ \ \ \ \ uint64\_t\ m\_totalTcpclBundleBytesReceived;}
\DoxyCodeLine{00486\ \ \ \ \ uint64\_t\ m\_totalStorageToIngressOpportunisticBundles;}
\DoxyCodeLine{00487\ \ \ \ \ uint64\_t\ m\_totalStorageToIngressOpportunisticBundleBytes;}
\DoxyCodeLine{00488\ \ \ \ \ uint64\_t\ m\_totalBundlesSuccessfullySent;}
\DoxyCodeLine{00489\ \ \ \ \ uint64\_t\ m\_totalBundleBytesSuccessfullySent;}
\DoxyCodeLine{00490\ \ \ \ \ std::list<std::unique\_ptr<OutductTelemetry\_t>\ >\ m\_listAllOutducts;}
\DoxyCodeLine{00491\ \};}
\DoxyCodeLine{00492\ }
\DoxyCodeLine{00493\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00494\ \ \ \ \ std::string\ m\_apiCall;}
\DoxyCodeLine{00495\ }
\DoxyCodeLine{00496\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}();}
\DoxyCodeLine{00497\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{\string~ApiCommand\_t}}();}
\DoxyCodeLine{00498\ }
\DoxyCodeLine{00499\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00500\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00501\ }
\DoxyCodeLine{00502\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00503\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00504\ }
\DoxyCodeLine{00505\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ std::shared\_ptr<ApiCommand\_t>\ CreateFromJson(\textcolor{keyword}{const}\ std::string\&\ jsonStr);}
\DoxyCodeLine{00506\ \};}
\DoxyCodeLine{00507\ }
\DoxyCodeLine{00508\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_get_storage_api_command__t_ae1ebd0ecea435a3bfdb0be87c8a3dc1e}{GetStorageApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00509\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_get_storage_api_command__t_ae1ebd0ecea435a3bfdb0be87c8a3dc1e}{GetStorageApiCommand\_t}}();}
\DoxyCodeLine{00510\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \mbox{\hyperlink{struct_get_storage_api_command__t_ae1ebd0ecea435a3bfdb0be87c8a3dc1e}{\string~GetStorageApiCommand\_t}}()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00511\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ \mbox{\hyperlink{struct_get_storage_api_command__t_a9a9681d2e45a33049337f6c720911ac0}{name}};}
\DoxyCodeLine{00512\ \};}
\DoxyCodeLine{00513\ }
\DoxyCodeLine{00514\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_get_outducts_api_command__t_ac8d855c7c576cc773bcbd5b9edccd4d9}{GetOutductsApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00515\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_get_outducts_api_command__t_ac8d855c7c576cc773bcbd5b9edccd4d9}{GetOutductsApiCommand\_t}}();}
\DoxyCodeLine{00516\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \mbox{\hyperlink{struct_get_outducts_api_command__t_ac8d855c7c576cc773bcbd5b9edccd4d9}{\string~GetOutductsApiCommand\_t}}()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00517\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00518\ \};}
\DoxyCodeLine{00519\ }
\DoxyCodeLine{00520\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_get_outduct_capabilities_api_command__t_a880110baabe839e54f698d9aa6dac224}{GetOutductCapabilitiesApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00521\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_get_outduct_capabilities_api_command__t_a880110baabe839e54f698d9aa6dac224}{GetOutductCapabilitiesApiCommand\_t}}();}
\DoxyCodeLine{00522\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \mbox{\hyperlink{struct_get_outduct_capabilities_api_command__t_a880110baabe839e54f698d9aa6dac224}{\string~GetOutductCapabilitiesApiCommand\_t}}()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00523\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00524\ \};}
\DoxyCodeLine{00525\ }
\DoxyCodeLine{00526\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_get_inducts_api_command__t_a9b5cd72de5818a9cba0ede1c69e94e1d}{GetInductsApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00527\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_get_inducts_api_command__t_a9b5cd72de5818a9cba0ede1c69e94e1d}{GetInductsApiCommand\_t}}();}
\DoxyCodeLine{00528\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_get_inducts_api_command__t_a9b5cd72de5818a9cba0ede1c69e94e1d}{\string~GetInductsApiCommand\_t}}();}
\DoxyCodeLine{00529\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00530\ \};}
\DoxyCodeLine{00531\ }
\DoxyCodeLine{00532\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_ping_api_command__t_a6b59dd907433d32437cc99ee041ed430}{PingApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00533\ \ \ \ \ uint64\_t\ m\_nodeId;}
\DoxyCodeLine{00534\ \ \ \ \ uint64\_t\ m\_pingServiceNumber;}
\DoxyCodeLine{00535\ \ \ \ \ uint64\_t\ m\_bpVersion;}
\DoxyCodeLine{00536\ }
\DoxyCodeLine{00537\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_ping_api_command__t_a6b59dd907433d32437cc99ee041ed430}{PingApiCommand\_t}}();}
\DoxyCodeLine{00538\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \mbox{\hyperlink{struct_ping_api_command__t_a6b59dd907433d32437cc99ee041ed430}{\string~PingApiCommand\_t}}()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00539\ }
\DoxyCodeLine{00540\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00541\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00542\ }
\DoxyCodeLine{00543\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00544\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00545\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00546\ \};}
\DoxyCodeLine{00547\ }
\DoxyCodeLine{00548\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_upload_contact_plan_api_command__t_a4b8974e67fd03325b1fe00e5a1d208ff}{UploadContactPlanApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00549\ \ \ \ \ std::string\ m\_contactPlanJson;}
\DoxyCodeLine{00550\ }
\DoxyCodeLine{00551\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_upload_contact_plan_api_command__t_a4b8974e67fd03325b1fe00e5a1d208ff}{UploadContactPlanApiCommand\_t}}();}
\DoxyCodeLine{00552\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \mbox{\hyperlink{struct_upload_contact_plan_api_command__t_a4b8974e67fd03325b1fe00e5a1d208ff}{\string~UploadContactPlanApiCommand\_t}}()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00553\ }
\DoxyCodeLine{00554\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00555\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00556\ }
\DoxyCodeLine{00557\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00558\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00559\ }
\DoxyCodeLine{00560\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00561\ \};}
\DoxyCodeLine{00562\ }
\DoxyCodeLine{00563\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_get_expiring_storage_api_command__t_abdaa3e5d2da44b4c1a9514158cffebee}{GetExpiringStorageApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00564\ \ \ \ \ uint64\_t\ m\_priority;}
\DoxyCodeLine{00565\ \ \ \ \ uint64\_t\ m\_thresholdSecondsFromNow;}
\DoxyCodeLine{00566\ }
\DoxyCodeLine{00567\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_get_expiring_storage_api_command__t_abdaa3e5d2da44b4c1a9514158cffebee}{GetExpiringStorageApiCommand\_t}}();}
\DoxyCodeLine{00568\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \mbox{\hyperlink{struct_get_expiring_storage_api_command__t_abdaa3e5d2da44b4c1a9514158cffebee}{\string~GetExpiringStorageApiCommand\_t}}()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00569\ }
\DoxyCodeLine{00570\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00571\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00572\ }
\DoxyCodeLine{00573\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00574\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00575\ }
\DoxyCodeLine{00576\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00577\ \};}
\DoxyCodeLine{00578\ }
\DoxyCodeLine{00579\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_update_bp_sec_api_command__t_ae181f7526b42aab7718896052fb3d669}{UpdateBpSecApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00580\ \ \ \ \ std::string\ m\_bpSecJson;}
\DoxyCodeLine{00581\ }
\DoxyCodeLine{00582\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_update_bp_sec_api_command__t_ae181f7526b42aab7718896052fb3d669}{UpdateBpSecApiCommand\_t}}();}
\DoxyCodeLine{00583\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \mbox{\hyperlink{struct_update_bp_sec_api_command__t_ae181f7526b42aab7718896052fb3d669}{\string~UpdateBpSecApiCommand\_t}}()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00584\ }
\DoxyCodeLine{00585\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00586\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00587\ }
\DoxyCodeLine{00588\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00589\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00590\ }
\DoxyCodeLine{00591\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00592\ \};}
\DoxyCodeLine{00593\ }
\DoxyCodeLine{00594\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_get_bp_sec_api_command__t_a09c48c967450978055c25d0d56a2c33f}{GetBpSecApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00595\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_get_bp_sec_api_command__t_a09c48c967450978055c25d0d56a2c33f}{GetBpSecApiCommand\_t}}();}
\DoxyCodeLine{00596\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \mbox{\hyperlink{struct_get_bp_sec_api_command__t_a09c48c967450978055c25d0d56a2c33f}{\string~GetBpSecApiCommand\_t}}()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00597\ }
\DoxyCodeLine{00598\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00599\ \};}
\DoxyCodeLine{00600\ }
\DoxyCodeLine{00601\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_set_max_send_rate_api_command__t_a6e5d493101e9d2cbb823649a6cee1c1f}{SetMaxSendRateApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00602\ \ \ \ \ uint64\_t\ m\_rateBitsPerSec;}
\DoxyCodeLine{00603\ \ \ \ \ uint64\_t\ m\_outduct;}
\DoxyCodeLine{00604\ }
\DoxyCodeLine{00605\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_set_max_send_rate_api_command__t_a6e5d493101e9d2cbb823649a6cee1c1f}{SetMaxSendRateApiCommand\_t}}();}
\DoxyCodeLine{00606\ }
\DoxyCodeLine{00607\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00608\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00609\ }
\DoxyCodeLine{00610\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00611\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00612\ }
\DoxyCodeLine{00613\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00614\ \};}
\DoxyCodeLine{00615\ }
\DoxyCodeLine{00616\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_get_hdtn_config_api_command__t_aea13a47b9380790e45839750b5d2d874}{GetHdtnConfigApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00617\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_get_hdtn_config_api_command__t_aea13a47b9380790e45839750b5d2d874}{GetHdtnConfigApiCommand\_t}}();}
\DoxyCodeLine{00618\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00619\ \};}
\DoxyCodeLine{00620\ }
\DoxyCodeLine{00621\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ GetHdtnVersionApiCommand\_t\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00622\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ GetHdtnVersionApiCommand\_t();}
\DoxyCodeLine{00623\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \string~GetHdtnVersionApiCommand\_t()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00624\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00625\ \};}
\DoxyCodeLine{00626\ }
\DoxyCodeLine{00627\ }
\DoxyCodeLine{00628\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_set_link_down_api_command__t_a3e89b858dd05519a0e2206254813f5e0}{SetLinkDownApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00629\ \ \ \ \ uint64\_t\ m\_index;}
\DoxyCodeLine{00630\ }
\DoxyCodeLine{00631\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_set_link_down_api_command__t_a3e89b858dd05519a0e2206254813f5e0}{SetLinkDownApiCommand\_t}}();}
\DoxyCodeLine{00632\ }
\DoxyCodeLine{00633\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00634\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00635\ }
\DoxyCodeLine{00636\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00637\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00638\ }
\DoxyCodeLine{00639\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00640\ \};}
\DoxyCodeLine{00641\ }
\DoxyCodeLine{00642\ }
\DoxyCodeLine{00643\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_set_link_up_api_command__t_add030f0e174bd793d011e6e15aa2885d}{SetLinkUpApiCommand\_t}}\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\ \{}
\DoxyCodeLine{00644\ \ \ \ \ uint64\_t\ m\_index;}
\DoxyCodeLine{00645\ }
\DoxyCodeLine{00646\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_set_link_up_api_command__t_add030f0e174bd793d011e6e15aa2885d}{SetLinkUpApiCommand\_t}}();}
\DoxyCodeLine{00647\ }
\DoxyCodeLine{00648\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00649\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{ApiCommand\_t}}\&\ o)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00650\ }
\DoxyCodeLine{00651\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00652\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00653\ }
\DoxyCodeLine{00654\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keyword}{const}\ std::string\ name;}
\DoxyCodeLine{00655\ \};}
\DoxyCodeLine{00656\ }
\DoxyCodeLine{00657\ \textcolor{keyword}{struct\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ \mbox{\hyperlink{struct_api_resp__t_aa23cbcda5cace96e01f1f02b31889537}{ApiResp\_t}}\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00658\ \ \ \ \ \textcolor{keywordtype}{bool}\ m\_success;}
\DoxyCodeLine{00659\ \ \ \ \ std::string\ m\_message;}
\DoxyCodeLine{00660\ }
\DoxyCodeLine{00661\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{struct_api_resp__t_aa23cbcda5cace96e01f1f02b31889537}{ApiResp\_t}}();}
\DoxyCodeLine{00662\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \mbox{\hyperlink{struct_api_resp__t_aa23cbcda5cace96e01f1f02b31889537}{\string~ApiResp\_t}}();}
\DoxyCodeLine{00663\ }
\DoxyCodeLine{00664\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00665\ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00666\ \};}
\DoxyCodeLine{00667\ }
\DoxyCodeLine{00673\ \textcolor{keyword}{static}\ \textcolor{keyword}{constexpr}\ uint8\_t\ ZMQ\_CONNECTION\_ID\_LEN\ =\ 5;}
\DoxyCodeLine{00674\ }
\DoxyCodeLine{00675\ \textcolor{keyword}{class\ }CLASS\_VISIBILITY\_TELEMETRY\_DEFINITIONS\ ZmqConnectionId\_t\ \{}
\DoxyCodeLine{00676\ \ \ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{00677\ \ \ \ \ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ ZmqConnectionId\_t();}
\DoxyCodeLine{00678\ }
\DoxyCodeLine{00679\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ This\ constructor\ generates\ custom\ ZMQ\ connection\ IDs.\ It\ accepts\ a\ single\ byte\ (uint8\_t)}}
\DoxyCodeLine{00680\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ and\ assigns\ it\ to\ the\ last\ byte\ of\ the\ ID,\ while\ prepending\ all\ other\ bytes\ with\ 0's.}}
\DoxyCodeLine{00681\ \ \ \ \ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ ZmqConnectionId\_t(\textcolor{keyword}{const}\ uint8\_t\ val);}
\DoxyCodeLine{00682\ }
\DoxyCodeLine{00683\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ Convert\ ZmqConnectionId\ to\ a\ zmq::message\_t}}
\DoxyCodeLine{00684\ \ \ \ \ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \mbox{\hyperlink{classzmq_1_1message__t}{zmq::message\_t}}\ Msg();}
\DoxyCodeLine{00685\ }
\DoxyCodeLine{00686\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ Compare\ two\ ZmqConnectionId\ objects}}
\DoxyCodeLine{00687\ \ \ \ \ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ ZmqConnectionId\_t\&\ other)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00688\ }
\DoxyCodeLine{00689\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ Compare\ ZmqConnectionId\ object\ with\ a\ zmq\ message}}
\DoxyCodeLine{00690\ \ \ \ \ \ \ \ \ TELEMETRY\_DEFINITIONS\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ \mbox{\hyperlink{classzmq_1_1message__t}{zmq::message\_t}}\&\ msg)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00691\ }
\DoxyCodeLine{00692\ \ \ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{00693\ \ \ \ \ \ \ \ \ std::array<uint8\_t,\ ZMQ\_CONNECTION\_ID\_LEN>\ m\_id;}
\DoxyCodeLine{00694\ \};}
\DoxyCodeLine{00695\ }
\DoxyCodeLine{00700\ \textcolor{keyword}{static}\ \mbox{\hyperlink{class_zmq_connection_id__t}{ZmqConnectionId\_t}}\ TELEM\_REQ\_CONN\_ID\ =\ \mbox{\hyperlink{class_zmq_connection_id__t}{ZmqConnectionId\_t}}(1);}
\DoxyCodeLine{00701\ \textcolor{keyword}{static}\ \mbox{\hyperlink{class_zmq_connection_id__t}{ZmqConnectionId\_t}}\ GUI\_REQ\_CONN\_ID\ =\ \mbox{\hyperlink{class_zmq_connection_id__t}{ZmqConnectionId\_t}}(2);}
\DoxyCodeLine{00702\ }
\DoxyCodeLine{00703\ \textcolor{preprocessor}{\#endif\ }\textcolor{comment}{//\ HDTN\_TELEMETRY\_H}}

\end{DoxyCode}
