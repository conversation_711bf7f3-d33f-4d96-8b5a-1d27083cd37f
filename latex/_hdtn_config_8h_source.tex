\doxysection{Hdtn\+Config.\+h}
\hypertarget{_hdtn_config_8h_source}{}\label{_hdtn_config_8h_source}\index{common/config/include/HdtnConfig.h@{common/config/include/HdtnConfig.h}}
\mbox{\hyperlink{_hdtn_config_8h}{Go to the documentation of this file.}}
\begin{DoxyCode}{0}
\DoxyCodeLine{00001\ }
\DoxyCodeLine{00019\ }
\DoxyCodeLine{00020\ \textcolor{preprocessor}{\#ifndef\ HDTN\_CONFIG\_H}}
\DoxyCodeLine{00021\ \textcolor{preprocessor}{\#define\ HDTN\_CONFIG\_H\ 1}}
\DoxyCodeLine{00022\ }
\DoxyCodeLine{00023\ \textcolor{preprocessor}{\#include\ <string>}}
\DoxyCodeLine{00024\ \textcolor{preprocessor}{\#include\ <memory>}}
\DoxyCodeLine{00025\ \textcolor{preprocessor}{\#include\ <boost/integer.hpp>}}
\DoxyCodeLine{00026\ \textcolor{preprocessor}{\#include\ <set>}}
\DoxyCodeLine{00027\ \textcolor{preprocessor}{\#include\ <vector>}}
\DoxyCodeLine{00028\ \textcolor{preprocessor}{\#include\ <utility>}}
\DoxyCodeLine{00029\ \textcolor{preprocessor}{\#include\ <tuple>}}
\DoxyCodeLine{00030\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_json_serializable_8h}{JsonSerializable.h}}"{}}}
\DoxyCodeLine{00031\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_inducts_config_8h}{InductsConfig.h}}"{}}}
\DoxyCodeLine{00032\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_outducts_config_8h}{OutductsConfig.h}}"{}}}
\DoxyCodeLine{00033\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_storage_config_8h}{StorageConfig.h}}"{}}}
\DoxyCodeLine{00034\ \textcolor{preprocessor}{\#include\ "{}config\_lib\_export.h"{}}}
\DoxyCodeLine{00035\ }
\DoxyCodeLine{00036\ \textcolor{keyword}{class\ }\mbox{\hyperlink{class_hdtn_config}{HdtnConfig}};}
\DoxyCodeLine{00037\ \textcolor{keyword}{typedef}\ std::shared\_ptr<HdtnConfig>\ HdtnConfig\_ptr;}
\DoxyCodeLine{00038\ }
\DoxyCodeLine{00039\ \textcolor{keyword}{class\ }HdtnConfig\ :\ \textcolor{keyword}{public}\ JsonSerializable\ \{}
\DoxyCodeLine{00040\ }
\DoxyCodeLine{00041\ }
\DoxyCodeLine{00042\ \textcolor{keyword}{public}:}
\DoxyCodeLine{00043\ \ \ \ \ CONFIG\_LIB\_EXPORT\ HdtnConfig();}
\DoxyCodeLine{00044\ \ \ \ \ CONFIG\_LIB\_EXPORT\ \string~HdtnConfig();}
\DoxyCodeLine{00045\ }
\DoxyCodeLine{00046\ \ \ \ \ \textcolor{comment}{//a\ copy\ constructor:\ X(const\ X\&)}}
\DoxyCodeLine{00047\ \ \ \ \ CONFIG\_LIB\_EXPORT\ HdtnConfig(\textcolor{keyword}{const}\ HdtnConfig\&\ o);}
\DoxyCodeLine{00048\ }
\DoxyCodeLine{00049\ \ \ \ \ \textcolor{comment}{//a\ move\ constructor:\ X(X\&\&)}}
\DoxyCodeLine{00050\ \ \ \ \ CONFIG\_LIB\_EXPORT\ HdtnConfig(HdtnConfig\&\&\ o)\ \textcolor{keyword}{noexcept};}
\DoxyCodeLine{00051\ }
\DoxyCodeLine{00052\ \ \ \ \ \textcolor{comment}{//a\ copy\ assignment:\ operator=(const\ X\&)}}
\DoxyCodeLine{00053\ \ \ \ \ CONFIG\_LIB\_EXPORT\ HdtnConfig\&\ operator=(\textcolor{keyword}{const}\ HdtnConfig\&\ o);}
\DoxyCodeLine{00054\ }
\DoxyCodeLine{00055\ \ \ \ \ \textcolor{comment}{//a\ move\ assignment:\ operator=(X\&\&)}}
\DoxyCodeLine{00056\ \ \ \ \ CONFIG\_LIB\_EXPORT\ HdtnConfig\&\ operator=(HdtnConfig\&\&\ o)\ \textcolor{keyword}{noexcept};}
\DoxyCodeLine{00057\ }
\DoxyCodeLine{00058\ \ \ \ \ CONFIG\_LIB\_EXPORT\ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ HdtnConfig\ \&\ other)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00059\ }
\DoxyCodeLine{00060\ \ \ \ \ CONFIG\_LIB\_EXPORT\ \textcolor{keyword}{static}\ HdtnConfig\_ptr\ CreateFromPtree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\ \&\ pt);}
\DoxyCodeLine{00061\ \ \ \ \ CONFIG\_LIB\_EXPORT\ \textcolor{keyword}{static}\ HdtnConfig\_ptr\ CreateFromJson(\textcolor{keyword}{const}\ std::string\ \&\ jsonString,\ \textcolor{keywordtype}{bool}\ verifyNoUnusedJsonKeys\ =\ \textcolor{keyword}{true});}
\DoxyCodeLine{00062\ \ \ \ \ CONFIG\_LIB\_EXPORT\ \textcolor{keyword}{static}\ HdtnConfig\_ptr\ CreateFromJsonFilePath(\textcolor{keyword}{const}\ boost::filesystem::path\&\ jsonFilePath,\ \textcolor{keywordtype}{bool}\ verifyNoUnusedJsonKeys\ =\ \textcolor{keyword}{true});}
\DoxyCodeLine{00063\ \ \ \ \ CONFIG\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ boost::property\_tree::ptree\ GetNewPropertyTree()\ \textcolor{keyword}{const\ override};}
\DoxyCodeLine{00064\ \ \ \ \ CONFIG\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{bool}\ SetValuesFromPropertyTree(\textcolor{keyword}{const}\ boost::property\_tree::ptree\ \&\ pt)\ \textcolor{keyword}{override};}
\DoxyCodeLine{00065\ }
\DoxyCodeLine{00066\ \textcolor{keyword}{public}:}
\DoxyCodeLine{00067\ }
\DoxyCodeLine{00068\ \ \ \ \ std::string\ m\_hdtnConfigName;}
\DoxyCodeLine{00069\ \ \ \ \ \textcolor{keywordtype}{bool}\ m\_userInterfaceOn;}
\DoxyCodeLine{00070\ \ \ \ \ std::string\ m\_mySchemeName;}
\DoxyCodeLine{00071\ \ \ \ \ uint64\_t\ m\_myNodeId;}
\DoxyCodeLine{00072\ \ \ \ \ uint64\_t\ m\_myBpEchoServiceId;}
\DoxyCodeLine{00073\ \ \ \ \ std::string\ m\_myCustodialSsp;}
\DoxyCodeLine{00074\ \ \ \ \ uint64\_t\ m\_myCustodialServiceId;}
\DoxyCodeLine{00075\ \ \ \ \ uint64\_t\ m\_myRouterServiceId;}
\DoxyCodeLine{00076\ \ \ \ \ \textcolor{keywordtype}{bool}\ m\_isAcsAware;}
\DoxyCodeLine{00077\ \ \ \ \ uint64\_t\ m\_acsMaxFillsPerAcsPacket;}
\DoxyCodeLine{00078\ \ \ \ \ uint64\_t\ m\_acsSendPeriodMilliseconds;}
\DoxyCodeLine{00079\ \ \ \ \ uint64\_t\ m\_retransmitBundleAfterNoCustodySignalMilliseconds;}
\DoxyCodeLine{00080\ \ \ \ \ uint64\_t\ m\_maxBundleSizeBytes;}
\DoxyCodeLine{00081\ \ \ \ \ uint64\_t\ m\_maxIngressBundleWaitOnEgressMilliseconds;}
\DoxyCodeLine{00082\ \ \ \ \ \textcolor{keywordtype}{bool}\ m\_bufferRxToStorageOnLinkUpSaturation;}
\DoxyCodeLine{00083\ \ \ \ \ uint64\_t\ m\_maxLtpReceiveUdpPacketSizeBytes;}
\DoxyCodeLine{00084\ }
\DoxyCodeLine{00085\ \ \ \ \ uint64\_t\ m\_neighborDepletedStorageDelaySeconds;}
\DoxyCodeLine{00086\ \ \ \ \ uint64\_t\ m\_fragmentBundlesLargerThanBytes;}
\DoxyCodeLine{00087\ \ \ \ \ \textcolor{keywordtype}{bool}\ m\_enforceBundlePriority;}
\DoxyCodeLine{00088\ }
\DoxyCodeLine{00089\ \ \ \ \ \textcolor{comment}{//pub-\/sub\ from\ router\ to\ all\ modules\ (defined\ in\ HdtnConfig\ as\ the\ TCP\ socket\ is\ used\ by\ hdtn-\/one-\/process)}}
\DoxyCodeLine{00090\ \ \ \ \ uint16\_t\ m\_zmqBoundRouterPubSubPortPath;}
\DoxyCodeLine{00091\ \ \ \ \ uint16\_t\ m\_zmqBoundTelemApiPortPath;}
\DoxyCodeLine{00092\ }
\DoxyCodeLine{00093\ \ \ \ \ \mbox{\hyperlink{class_inducts_config}{InductsConfig}}\ m\_inductsConfig;}
\DoxyCodeLine{00094\ \ \ \ \ \mbox{\hyperlink{class_outducts_config}{OutductsConfig}}\ m\_outductsConfig;}
\DoxyCodeLine{00095\ \ \ \ \ \mbox{\hyperlink{class_storage_config}{StorageConfig}}\ m\_storageConfig;}
\DoxyCodeLine{00096\ \};}
\DoxyCodeLine{00097\ }
\DoxyCodeLine{00098\ \textcolor{preprocessor}{\#endif\ }\textcolor{comment}{//\ HDTN\_CONFIG\_H}}
\DoxyCodeLine{00099\ }

\end{DoxyCode}
