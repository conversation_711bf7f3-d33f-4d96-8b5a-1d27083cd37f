\doxysection{Binary\+Conversions.\+h}
\hypertarget{_binary_conversions_8h_source}{}\label{_binary_conversions_8h_source}\index{common/util/include/BinaryConversions.h@{common/util/include/BinaryConversions.h}}
\mbox{\hyperlink{_binary_conversions_8h}{Go to the documentation of this file.}}
\begin{DoxyCode}{0}
\DoxyCodeLine{00001\ }
\DoxyCodeLine{00020\ }
\DoxyCodeLine{00021\ \textcolor{preprocessor}{\#ifndef\ \_BINARY\_CONVERSIONS\_H}}
\DoxyCodeLine{00022\ \textcolor{preprocessor}{\#define\ \_BINARY\_CONVERSIONS\_H\ 1}}
\DoxyCodeLine{00023\ \textcolor{preprocessor}{\#include\ <stdlib.h>}}
\DoxyCodeLine{00024\ \textcolor{preprocessor}{\#include\ <stdint.h>}}
\DoxyCodeLine{00025\ \textcolor{preprocessor}{\#include\ <vector>}}
\DoxyCodeLine{00026\ \textcolor{preprocessor}{\#include\ <string>}}
\DoxyCodeLine{00027\ \textcolor{preprocessor}{\#include\ <boost/asio/buffer.hpp>}}
\DoxyCodeLine{00028\ \textcolor{preprocessor}{\#include\ <boost/version.hpp>}}
\DoxyCodeLine{00029\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_padded_vector_uint8_8h}{PaddedVectorUint8.h}}"{}}}
\DoxyCodeLine{00030\ \textcolor{preprocessor}{\#include\ "{}hdtn\_util\_export.h"{}}}
\DoxyCodeLine{00031\ }
\DoxyCodeLine{00032\ \textcolor{keyword}{class\ }HDTN\_UTIL\_EXPORT\ \mbox{\hyperlink{class_binary_conversions}{BinaryConversions}}\ \{}
\DoxyCodeLine{00033\ \textcolor{keyword}{public}:}
\DoxyCodeLine{00034\ \textcolor{preprocessor}{\#if\ (BOOST\_VERSION\ >=\ 106600)}}
\DoxyCodeLine{00035\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keywordtype}{void}\ DecodeBase64(\textcolor{keyword}{const}\ std::string\ \&\ strBase64,\ std::vector<uint8\_t>\ \&\ binaryDecodedMessage);}
\DoxyCodeLine{00036\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keywordtype}{void}\ EncodeBase64(\textcolor{keyword}{const}\ std::vector<uint8\_t>\ \&\ binaryMessage,\ std::string\ \&\ strBase64);}
\DoxyCodeLine{00037\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00038\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keywordtype}{void}\ BytesToHexString(\textcolor{keyword}{const}\ std::vector<uint8\_t>\ \&\ bytes,\ std::string\ \&\ hexString);}
\DoxyCodeLine{00039\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keywordtype}{void}\ BytesToHexString(\textcolor{keyword}{const}\ padded\_vector\_uint8\_t\&\ bytes,\ std::string\&\ hexString);}
\DoxyCodeLine{00040\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keywordtype}{void}\ BytesToHexString(\textcolor{keyword}{const}\ \textcolor{keywordtype}{void}*\ data,\ std::size\_t\ size,\ std::string\&\ hexString);}
\DoxyCodeLine{00041\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keywordtype}{void}\ BytesToHexString(\textcolor{keyword}{const}\ std::vector<boost::asio::const\_buffer>\&\ bytes,\ std::string\&\ hexString);}
\DoxyCodeLine{00042\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keywordtype}{void}\ BytesToHexString(\textcolor{keyword}{const}\ boost::asio::const\_buffer\&\ bytes,\ std::string\&\ hexString);}
\DoxyCodeLine{00043\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keywordtype}{bool}\ HexStringToBytes(\textcolor{keyword}{const}\ std::string\&\ hexString,\ padded\_vector\_uint8\_t\&\ bytes);}
\DoxyCodeLine{00044\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keywordtype}{bool}\ HexStringToBytes(\textcolor{keyword}{const}\ std::string\ \&\ hexString,\ std::vector<uint8\_t>\ \&\ bytes);}
\DoxyCodeLine{00045\ \};}
\DoxyCodeLine{00046\ \textcolor{preprocessor}{\#endif\ \ \ \ \ \ }\textcolor{comment}{//\ \_BINARY\_CONVERSIONS\_H\ }}

\end{DoxyCode}
