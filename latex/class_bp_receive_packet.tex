\doxysection{Bp\+Receive\+Packet Class Reference}
\hypertarget{class_bp_receive_packet}{}\label{class_bp_receive_packet}\index{BpReceivePacket@{BpReceivePacket}}
Inheritance diagram for Bp\+Receive\+Packet\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_bp_receive_packet}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_bp_receive_packet_a131a2e09a8b7764c6a5ef94ad436d81e}\label{class_bp_receive_packet_a131a2e09a8b7764c6a5ef94ad436d81e} 
bool {\bfseries socket\+Init} (Outducts\+Config\+\_\+ptr \&outducts\+Config\+Ptr, const \mbox{\hyperlink{structcbhe__eid__t}{cbhe\+\_\+eid\+\_\+t}} \&my\+Eid, const uint64\+\_\+t max\+Bundle\+Size\+Bytes)
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_bp_sink_pattern}{Bp\+Sink\+Pattern}}}
\begin{DoxyCompactItemize}
\item 
BP\+\_\+\+APP\+\_\+\+PATTERNS\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Stop} ()
\item 
BP\+\_\+\+APP\+\_\+\+PATTERNS\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Init} (Inducts\+Config\+\_\+ptr \&inducts\+Config\+Ptr, Outducts\+Config\+\_\+ptr \&outducts\+Config\+Ptr, const boost\+::filesystem\+::path \&bp\+Sec\+Config\+File\+Path, bool is\+Acs\+Aware, const \mbox{\hyperlink{structcbhe__eid__t}{cbhe\+\_\+eid\+\_\+t}} \&my\+Eid, uint32\+\_\+t processing\+Lag\+Ms, const uint64\+\_\+t max\+Bundle\+Size\+Bytes, const uint64\+\_\+t my\+Bp\+Echo\+Service\+Id=2047)
\item 
BP\+\_\+\+APP\+\_\+\+PATTERNS\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Log\+Stats} (\mbox{\hyperlink{struct_primary_block}{Primary\+Block}} \&primary\+Block, bool is\+Bp\+Version6)
\end{DoxyCompactItemize}
\doxysubsubsection*{Protected Member Functions}
\begin{DoxyCompactItemize}
\item 
virtual bool \mbox{\hyperlink{class_bp_receive_packet_a1a9fad68ea68beba234519da13b1b63c}{Process\+Payload}} (const uint8\+\_\+t \texorpdfstring{$\ast$}{*}data, const uint64\+\_\+t size) override
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Public Attributes inherited from \mbox{\hyperlink{class_bp_sink_pattern}{Bp\+Sink\+Pattern}}}
\begin{DoxyCompactItemize}
\item 
uint64\+\_\+t {\bfseries m\+\_\+total\+Payload\+Bytes\+Rx}
\item 
uint64\+\_\+t {\bfseries m\+\_\+total\+Bundle\+Bytes\+Rx}
\item 
uint64\+\_\+t {\bfseries m\+\_\+total\+Bundles\+Version6\+Rx}
\item 
uint64\+\_\+t {\bfseries m\+\_\+total\+Bundles\+Version7\+Rx}
\item 
uint64\+\_\+t {\bfseries m\+\_\+last\+Payload\+Bytes\+Rx}
\item 
uint64\+\_\+t {\bfseries m\+\_\+last\+Bundle\+Bytes\+Rx}
\item 
uint64\+\_\+t {\bfseries m\+\_\+last\+Bundles\+Rx}
\item 
boost\+::posix\+\_\+time\+::ptime {\bfseries m\+\_\+last\+Ptime}
\item 
\mbox{\hyperlink{structcbhe__eid__t}{cbhe\+\_\+eid\+\_\+t}} {\bfseries m\+\_\+last\+Previous\+Node}
\item 
std\+::vector$<$ uint64\+\_\+t $>$ {\bfseries m\+\_\+hop\+Counts}
\item 
uint64\+\_\+t {\bfseries m\+\_\+bpv7\+Priority}
\end{DoxyCompactItemize}


\doxysubsection{Member Function Documentation}
\Hypertarget{class_bp_receive_packet_a1a9fad68ea68beba234519da13b1b63c}\index{BpReceivePacket@{BpReceivePacket}!ProcessPayload@{ProcessPayload}}
\index{ProcessPayload@{ProcessPayload}!BpReceivePacket@{BpReceivePacket}}
\doxysubsubsection{\texorpdfstring{ProcessPayload()}{ProcessPayload()}}
{\footnotesize\ttfamily \label{class_bp_receive_packet_a1a9fad68ea68beba234519da13b1b63c} 
bool Bp\+Receive\+Packet\+::\+Process\+Payload (\begin{DoxyParamCaption}\item[{const uint8\+\_\+t \texorpdfstring{$\ast$}{*}}]{data}{, }\item[{const uint64\+\_\+t}]{size}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [override]}, {\ttfamily [protected]}, {\ttfamily [virtual]}}



Implements \mbox{\hyperlink{class_bp_sink_pattern}{Bp\+Sink\+Pattern}}.



The documentation for this class was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
common/bpcodec/apps/bpreceivepacket/include/Bp\+Receive\+Packet.\+h\item 
common/bpcodec/apps/bpreceivepacket/src/Bp\+Receive\+Packet.\+cpp\end{DoxyCompactItemize}
