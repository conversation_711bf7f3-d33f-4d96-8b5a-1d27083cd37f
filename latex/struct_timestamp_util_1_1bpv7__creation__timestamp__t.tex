\doxysection{Timestamp\+Util\+::bpv7\+\_\+creation\+\_\+timestamp\+\_\+t Struct Reference}
\hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t}\index{TimestampUtil::bpv7\_creation\_timestamp\_t@{TimestampUtil::bpv7\_creation\_timestamp\_t}}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a79e84bd0fbf5e579f661960b10365b04}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a79e84bd0fbf5e579f661960b10365b04} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT {\bfseries bpv7\+\_\+creation\+\_\+timestamp\+\_\+t} (uint64\+\_\+t param\+Milliseconds\+Since\+Start\+Of\+Year2000, uint32\+\_\+t param\+Sequence\+Number)
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_ae874330ac63b57aee607b17b6de34306}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_ae874330ac63b57aee607b17b6de34306} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT {\bfseries bpv7\+\_\+creation\+\_\+timestamp\+\_\+t} (const \mbox{\hyperlink{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{bpv7\+\_\+creation\+\_\+timestamp\+\_\+t}} \&o)
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a7ca0d181255ba05416ba7bf316531893}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a7ca0d181255ba05416ba7bf316531893} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT {\bfseries bpv7\+\_\+creation\+\_\+timestamp\+\_\+t} (\mbox{\hyperlink{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{bpv7\+\_\+creation\+\_\+timestamp\+\_\+t}} \&\&o)
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_ae831cb9db2eed832133b8f2d4e5109e1}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_ae831cb9db2eed832133b8f2d4e5109e1} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT \mbox{\hyperlink{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{bpv7\+\_\+creation\+\_\+timestamp\+\_\+t}} \& {\bfseries operator=} (const \mbox{\hyperlink{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{bpv7\+\_\+creation\+\_\+timestamp\+\_\+t}} \&o)
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_af4df0c0af8f06d2e4222314db8e8c60d}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_af4df0c0af8f06d2e4222314db8e8c60d} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT \mbox{\hyperlink{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{bpv7\+\_\+creation\+\_\+timestamp\+\_\+t}} \& {\bfseries operator=} (\mbox{\hyperlink{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{bpv7\+\_\+creation\+\_\+timestamp\+\_\+t}} \&\&o)
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_aafad258eb8b0069be80efa789f49872a}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_aafad258eb8b0069be80efa789f49872a} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT bool {\bfseries operator==} (const \mbox{\hyperlink{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{bpv7\+\_\+creation\+\_\+timestamp\+\_\+t}} \&o) const
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a01ce5fc72ca660745ce3e82f5fb165a8}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a01ce5fc72ca660745ce3e82f5fb165a8} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT bool {\bfseries operator!=} (const \mbox{\hyperlink{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{bpv7\+\_\+creation\+\_\+timestamp\+\_\+t}} \&o) const
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_acf88e4e454b6af387ca46736fcb5416b}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_acf88e4e454b6af387ca46736fcb5416b} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT bool {\bfseries operator$<$} (const \mbox{\hyperlink{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{bpv7\+\_\+creation\+\_\+timestamp\+\_\+t}} \&o) const
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_ab7285433e27dcc5e765697b180484fbb}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_ab7285433e27dcc5e765697b180484fbb} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT uint64\+\_\+t {\bfseries Serialize\+Bpv7} (uint8\+\_\+t \texorpdfstring{$\ast$}{*}serialization) const
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a0766433f2f5f832cb1cbb7cef072d8ff}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a0766433f2f5f832cb1cbb7cef072d8ff} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT uint64\+\_\+t {\bfseries Serialize\+Bpv7} (uint8\+\_\+t \texorpdfstring{$\ast$}{*}serialization, uint64\+\_\+t buffer\+Size) const
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a6f291bf551f028033863905a55b138d7}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a6f291bf551f028033863905a55b138d7} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT uint64\+\_\+t {\bfseries Get\+Serialization\+Size} () const
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a9e4525446ee41aa2df72295be0a2636c}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a9e4525446ee41aa2df72295be0a2636c} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT bool {\bfseries Deserialize\+Bpv7} (const uint8\+\_\+t \texorpdfstring{$\ast$}{*}serialization, uint8\+\_\+t \texorpdfstring{$\ast$}{*}num\+Bytes\+Taken\+To\+Decode, uint64\+\_\+t buffer\+Size)
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_afcaee37e78760f3044b85b20d070305d}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_afcaee37e78760f3044b85b20d070305d} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT void {\bfseries Set\+Zero} ()
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a0777cab669efaad080e2ce8dd082c635}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a0777cab669efaad080e2ce8dd082c635} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT boost\+::posix\+\_\+time\+::ptime {\bfseries Get\+Ptime} () const
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a42a5101c7ecc76ef248286a8a3c9a244}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a42a5101c7ecc76ef248286a8a3c9a244} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT void {\bfseries Set\+From\+Ptime} (const boost\+::posix\+\_\+time\+::ptime \&posix\+Time\+Value)
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a38017bd761307730568a7c9b3b10b3c9}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a38017bd761307730568a7c9b3b10b3c9} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT std\+::string {\bfseries Get\+Utc\+Timestamp\+String} (bool for\+File\+Name) const
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a0f624721074822f2cd6deb387fc24d4e}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a0f624721074822f2cd6deb387fc24d4e} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT void {\bfseries Set\+Time\+From\+Now} ()
\end{DoxyCompactItemize}
\doxysubsubsection*{Public Attributes}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a8395a03fca28c59b45a44b1067a39ff7}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a8395a03fca28c59b45a44b1067a39ff7} 
uint64\+\_\+t {\bfseries milliseconds\+Since\+Start\+Of\+Year2000}
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a935711417d49e0da59df30409105a926}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a935711417d49e0da59df30409105a926} 
uint64\+\_\+t {\bfseries sequence\+Number}
\end{DoxyCompactItemize}
\doxysubsubsection*{Static Public Attributes}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a5bc3a12f9a8a3af4416af911299b08d0}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a5bc3a12f9a8a3af4416af911299b08d0} 
static constexpr unsigned int {\bfseries MAX\+\_\+\+BUFFER\+\_\+\+SIZE} = 18
\end{DoxyCompactItemize}
\doxysubsubsection*{Friends}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a99d142e32a598ba81d8121f903bdd3b7}\label{struct_timestamp_util_1_1bpv7__creation__timestamp__t_a99d142e32a598ba81d8121f903bdd3b7} 
HDTN\+\_\+\+UTIL\+\_\+\+EXPORT friend std\+::ostream \& {\bfseries operator$<$$<$} (std\+::ostream \&os, const \mbox{\hyperlink{struct_timestamp_util_1_1bpv7__creation__timestamp__t}{bpv7\+\_\+creation\+\_\+timestamp\+\_\+t}} \&o)
\end{DoxyCompactItemize}


The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
common/util/include/\mbox{\hyperlink{_timestamp_util_8h}{Timestamp\+Util.\+h}}\item 
common/util/src/\mbox{\hyperlink{_timestamp_util_8cpp}{Timestamp\+Util.\+cpp}}\end{DoxyCompactItemize}
