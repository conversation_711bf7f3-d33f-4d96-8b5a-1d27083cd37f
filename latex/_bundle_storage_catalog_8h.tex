\doxysection{module/storage/include/\+Bundle\+Storage\+Catalog.h File Reference}
\hypertarget{_bundle_storage_catalog_8h}{}\label{_bundle_storage_catalog_8h}\index{module/storage/include/BundleStorageCatalog.h@{module/storage/include/BundleStorageCatalog.h}}
{\ttfamily \#include $<$cstdint$>$}\newline
{\ttfamily \#include $<$map$>$}\newline
{\ttfamily \#include "{}Forward\+List\+Queue.\+h"{}}\newline
{\ttfamily \#include $<$array$>$}\newline
{\ttfamily \#include $<$vector$>$}\newline
{\ttfamily \#include $<$utility$>$}\newline
{\ttfamily \#include $<$string$>$}\newline
{\ttfamily \#include "{}Memory\+Manager\+Tree\+Array.\+h"{}}\newline
{\ttfamily \#include "{}codec/\+Primary\+Block.\+h"{}}\newline
{\ttfamily \#include "{}Hash\+Map16\+Bit\+Fixed\+Size.\+h"{}}\newline
{\ttfamily \#include $<$boost/bimap.\+hpp$>$}\newline
{\ttfamily \#include $<$boost/date\+\_\+time.\+hpp$>$}\newline
{\ttfamily \#include "{}Catalog\+Entry.\+h"{}}\newline
{\ttfamily \#include "{}Telemetry\+Definitions.\+h"{}}\newline
{\ttfamily \#include "{}storage\+\_\+lib\+\_\+export.\+h"{}}\newline
\doxysubsubsection*{Classes}
\begin{DoxyCompactItemize}
\item 
class \mbox{\hyperlink{class_bundle_storage_catalog}{Bundle\+Storage\+Catalog}}
\end{DoxyCompactItemize}
\doxysubsubsection*{Typedefs}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{_bundle_storage_catalog_8h_adb850648f56c873d0e396f44dbde114f}\label{_bundle_storage_catalog_8h_adb850648f56c873d0e396f44dbde114f} 
typedef \mbox{\hyperlink{class_forward_list_queue}{Forward\+List\+Queue}}$<$ uint64\+\_\+t $>$ {\bfseries custids\+\_\+flist\+\_\+queue\+\_\+t}
\item 
\Hypertarget{_bundle_storage_catalog_8h_af5ec5585efe09e13d3443aee759cf578}\label{_bundle_storage_catalog_8h_af5ec5585efe09e13d3443aee759cf578} 
typedef std\+::map$<$ uint64\+\_\+t, \mbox{\hyperlink{class_forward_list_queue}{custids\+\_\+flist\+\_\+queue\+\_\+t}} $>$ {\bfseries expirations\+\_\+to\+\_\+custids\+\_\+map\+\_\+t}
\item 
\Hypertarget{_bundle_storage_catalog_8h_a2e20b0d44db68416d0a399d439de2f69}\label{_bundle_storage_catalog_8h_a2e20b0d44db68416d0a399d439de2f69} 
typedef std\+::array$<$ expirations\+\_\+to\+\_\+custids\+\_\+map\+\_\+t, NUMBER\+\_\+\+OF\+\_\+\+PRIORITIES $>$ {\bfseries priorities\+\_\+to\+\_\+expirations\+\_\+array\+\_\+t}
\item 
\Hypertarget{_bundle_storage_catalog_8h_a5f19b603c947a65bcba658696fbd2017}\label{_bundle_storage_catalog_8h_a5f19b603c947a65bcba658696fbd2017} 
typedef std\+::map$<$ \mbox{\hyperlink{structcbhe__eid__t}{cbhe\+\_\+eid\+\_\+t}}, priorities\+\_\+to\+\_\+expirations\+\_\+array\+\_\+t $>$ {\bfseries dest\+\_\+eid\+\_\+to\+\_\+priorities\+\_\+map\+\_\+t}
\item 
\Hypertarget{_bundle_storage_catalog_8h_ab76520f1799b8a0c92e374d3d877e8f5}\label{_bundle_storage_catalog_8h_ab76520f1799b8a0c92e374d3d877e8f5} 
typedef \mbox{\hyperlink{class_hash_map16_bit_fixed_size}{Hash\+Map16\+Bit\+Fixed\+Size}}$<$ \mbox{\hyperlink{structcbhe__bundle__uuid__t}{cbhe\+\_\+bundle\+\_\+uuid\+\_\+t}}, uint64\+\_\+t $>$ {\bfseries uuid\+\_\+to\+\_\+custid\+\_\+hashmap\+\_\+t}
\item 
\Hypertarget{_bundle_storage_catalog_8h_a5ec40f360d201a9828c7d259877ee52b}\label{_bundle_storage_catalog_8h_a5ec40f360d201a9828c7d259877ee52b} 
typedef \mbox{\hyperlink{class_hash_map16_bit_fixed_size}{Hash\+Map16\+Bit\+Fixed\+Size}}$<$ \mbox{\hyperlink{structcbhe__bundle__uuid__nofragment__t}{cbhe\+\_\+bundle\+\_\+uuid\+\_\+nofragment\+\_\+t}}, uint64\+\_\+t $>$ {\bfseries uuidnofrag\+\_\+to\+\_\+custid\+\_\+hashmap\+\_\+t}
\item 
\Hypertarget{_bundle_storage_catalog_8h_a81aa16be05565cc8fa6e820a17658c6d}\label{_bundle_storage_catalog_8h_a81aa16be05565cc8fa6e820a17658c6d} 
typedef \mbox{\hyperlink{class_hash_map16_bit_fixed_size}{Hash\+Map16\+Bit\+Fixed\+Size}}$<$ uint64\+\_\+t, \mbox{\hyperlink{structcatalog__entry__t}{catalog\+\_\+entry\+\_\+t}} $>$ {\bfseries custid\+\_\+to\+\_\+catalog\+\_\+entry\+\_\+hashmap\+\_\+t}
\item 
\Hypertarget{_bundle_storage_catalog_8h_a79118a105564919258f0e6a0741cb15e}\label{_bundle_storage_catalog_8h_a79118a105564919258f0e6a0741cb15e} 
typedef boost\+::bimap$<$ uint64\+\_\+t, boost\+::posix\+\_\+time\+::ptime $>$ {\bfseries custid\+\_\+to\+\_\+custody\+\_\+xfer\+\_\+expiry\+\_\+bimap\+\_\+t}
\end{DoxyCompactItemize}


\doxysubsection{Detailed Description}
\begin{DoxyAuthor}{Author}
Brian Tomko \href{mailto:<EMAIL>}{\texttt{ brian.\+j.\+tomko@nasa.\+gov}}
\end{DoxyAuthor}
\begin{DoxyCopyright}{Copyright}
Copyright (c) 2021 United States Government as represented by the National Aeronautics and Space Administration. No copyright is claimed in the United States under Title 17, U.\+S.\+Code. All Other Rights Reserved.
\end{DoxyCopyright}
\hypertarget{import__installation_2src_2test__main_8cpp_LICENSE}{}\doxysubsection{\texorpdfstring{LICENSE}{LICENSE}}\label{import__installation_2src_2test__main_8cpp_LICENSE}
Released under the NASA Open Source Agreement (NOSA) See LICENSE.\+md in the source root directory for more information.\hypertarget{import__installation_2src_2test__main_8cpp_DESCRIPTION}{}\doxysubsection{\texorpdfstring{DESCRIPTION}{DESCRIPTION}}\label{import__installation_2src_2test__main_8cpp_DESCRIPTION}
This \doxylink{class_bundle_storage_catalog}{Bundle\+Storage\+Catalog} class defines methods for storing key information about bundles in memory, such as bundle expiration, how a bundle is stored/fragmented across disk(s), etc. 