\doxysection{common/bpcodec/test/\+Test\+Bpsec\+Default\+Security\+Contexts.cpp File Reference}
\hypertarget{_test_bpsec_default_security_contexts_8cpp}{}\label{_test_bpsec_default_security_contexts_8cpp}\index{common/bpcodec/test/TestBpsecDefaultSecurityContexts.cpp@{common/bpcodec/test/TestBpsecDefaultSecurityContexts.cpp}}
{\ttfamily \#include $<$boost/test/unit\+\_\+test.\+hpp$>$}\newline
{\ttfamily \#include $<$boost/thread.\+hpp$>$}\newline
{\ttfamily \#include "{}codec/\+Bundle\+View\+V7.\+h"{}}\newline
{\ttfamily \#include $<$iostream$>$}\newline
{\ttfamily \#include $<$string$>$}\newline
{\ttfamily \#include $<$inttypes.\+h$>$}\newline
{\ttfamily \#include $<$vector$>$}\newline
{\ttfamily \#include "{}Uri.\+h"{}}\newline
{\ttfamily \#include $<$boost/make\+\_\+unique.\+hpp$>$}\newline
{\ttfamily \#include "{}Padded\+Vector\+Uint8.\+h"{}}\newline
{\ttfamily \#include "{}Binary\+Conversions.\+h"{}}\newline
{\ttfamily \#include $<$boost/algorithm/string.\+hpp$>$}\newline
\doxysubsubsection*{Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{_test_bpsec_default_security_contexts_8cpp_a40a390e98674f51ac3a7dd9b977d6e58}\label{_test_bpsec_default_security_contexts_8cpp_a40a390e98674f51ac3a7dd9b977d6e58} 
{\bfseries BOOST\+\_\+\+AUTO\+\_\+\+TEST\+\_\+\+CASE} (Test\+Bpsec\+Default\+Security\+Contexts\+Simple\+Integrity\+Test\+Case)
\item 
\Hypertarget{_test_bpsec_default_security_contexts_8cpp_ac2f775ccdb29799c66535d0b0447777a}\label{_test_bpsec_default_security_contexts_8cpp_ac2f775ccdb29799c66535d0b0447777a} 
{\bfseries BOOST\+\_\+\+AUTO\+\_\+\+TEST\+\_\+\+CASE} (Test\+Bpsec\+Default\+Security\+Contexts\+Simple\+Confidentiality\+With\+Key\+Wrap\+Test\+Case)
\item 
\Hypertarget{_test_bpsec_default_security_contexts_8cpp_abc37f5148137f8b191e0c92cc4fcae0d}\label{_test_bpsec_default_security_contexts_8cpp_abc37f5148137f8b191e0c92cc4fcae0d} 
{\bfseries BOOST\+\_\+\+AUTO\+\_\+\+TEST\+\_\+\+CASE} (Test\+Bpsec\+Default\+Security\+Contexts\+Security\+Blocks\+From\+Multiple\+Sources\+Test\+Case)
\item 
\Hypertarget{_test_bpsec_default_security_contexts_8cpp_a1280c75c16b9b59334b820279199c00f}\label{_test_bpsec_default_security_contexts_8cpp_a1280c75c16b9b59334b820279199c00f} 
{\bfseries BOOST\+\_\+\+AUTO\+\_\+\+TEST\+\_\+\+CASE} (Test\+Bpsec\+Default\+Security\+Contexts\+Security\+Blocks\+With\+Full\+Scope\+Test\+Case)
\end{DoxyCompactItemize}


\doxysubsection{Detailed Description}
\begin{DoxyAuthor}{Author}
Brian Tomko \href{mailto:<EMAIL>}{\texttt{ brian.\+j.\+tomko@nasa.\+gov}}
\end{DoxyAuthor}
\begin{DoxyCopyright}{Copyright}
Copyright (c) 2021 United States Government as represented by the National Aeronautics and Space Administration. No copyright is claimed in the United States under Title 17, U.\+S.\+Code. All Other Rights Reserved.
\end{DoxyCopyright}
\hypertarget{import__installation_2src_2test__main_8cpp_LICENSE}{}\doxysubsection{\texorpdfstring{LICENSE}{LICENSE}}\label{import__installation_2src_2test__main_8cpp_LICENSE}
Released under the NASA Open Source Agreement (NOSA) See LICENSE.\+md in the source root directory for more information. 