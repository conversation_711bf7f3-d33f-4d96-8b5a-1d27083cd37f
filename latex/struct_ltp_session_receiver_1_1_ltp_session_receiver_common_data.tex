\doxysection{Ltp\+Session\+Receiver\+::Ltp\+Session\+Receiver\+Common\+Data Struct Reference}
\hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data}{}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data}\index{LtpSessionReceiver::LtpSessionReceiverCommonData@{LtpSessionReceiver::LtpSessionReceiverCommonData}}


Receiver common data, referenced across all receivers associated with the same \doxylink{class_ltp_engine}{Ltp\+Engine}.  




{\ttfamily \#include $<$Ltp\+Session\+Receiver.\+h$>$}

Inheritance diagram for Ltp\+Session\+Receiver\+::Ltp\+Session\+Receiver\+Common\+Data\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_af4e0cd32c4f0916e6c950e307776e71d}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_af4e0cd32c4f0916e6c950e307776e71d} 
LTP\+\_\+\+LIB\+\_\+\+EXPORT {\bfseries Ltp\+Session\+Receiver\+Common\+Data} (const uint64\+\_\+t client\+Service\+Id, uint64\+\_\+t max\+Reception\+Claims, uint64\+\_\+t estimated\+Bytes\+To\+Receive, uint64\+\_\+t max\+Red\+Rx\+Bytes, uint32\+\_\+t \&max\+Retries\+Per\+Serial\+Number\+Ref, \mbox{\hyperlink{class_ltp_timer_manager}{Ltp\+Timer\+Manager}}$<$ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp\+::session\+\_\+id\+\_\+t}}, \mbox{\hyperlink{struct_ltp_1_1hash__session__id__t}{Ltp\+::hash\+\_\+session\+\_\+id\+\_\+t}} $>$ \&time\+Manager\+Of\+Report\+Serial\+Numbers\+Ref, const \mbox{\hyperlink{class_ltp_timer_manager}{Ltp\+Timer\+Manager}}$<$ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp\+::session\+\_\+id\+\_\+t}}, \mbox{\hyperlink{struct_ltp_1_1hash__session__id__t}{Ltp\+::hash\+\_\+session\+\_\+id\+\_\+t}} $>$\+::\+Ltp\+Timer\+Expired\+Callback\+\_\+t \&rsn\+Timer\+Expired\+Callback\+Ref, \mbox{\hyperlink{class_ltp_timer_manager}{Ltp\+Timer\+Manager}}$<$ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp\+::session\+\_\+id\+\_\+t}}, \mbox{\hyperlink{struct_ltp_1_1hash__session__id__t}{Ltp\+::hash\+\_\+session\+\_\+id\+\_\+t}} $>$ \&time\+Manager\+Of\+Sending\+Delayed\+Reception\+Reports\+Ref, const \mbox{\hyperlink{class_ltp_timer_manager}{Ltp\+Timer\+Manager}}$<$ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp\+::session\+\_\+id\+\_\+t}}, \mbox{\hyperlink{struct_ltp_1_1hash__session__id__t}{Ltp\+::hash\+\_\+session\+\_\+id\+\_\+t}} $>$\+::\+Ltp\+Timer\+Expired\+Callback\+\_\+t \&delayed\+Reception\+Report\+Timer\+Expired\+Callback\+Ref, const Notify\+Engine\+That\+This\+Receiver\+Needs\+Deleted\+Callback\+\_\+t \&notify\+Engine\+That\+This\+Receiver\+Needs\+Deleted\+Callback\+Ref, const Notify\+Engine\+That\+This\+Receivers\+Timers\+Has\+Producible\+Data\+Function\+\_\+t \&notify\+Engine\+That\+This\+Receivers\+Timers\+Has\+Producible\+Data\+Function\+Ref, const Notify\+Engine\+That\+This\+Receiver\+Completed\+Deferred\+Operation\+Function\+\_\+t \&notify\+Engine\+That\+This\+Receiver\+Completed\+Deferred\+Operation\+Function\+Ref, const Red\+Part\+Reception\+Callback\+\_\+t \&red\+Part\+Reception\+Callback\+Ref, const Green\+Part\+Segment\+Arrival\+Callback\+\_\+t \&green\+Part\+Segment\+Arrival\+Callback\+Ref, std\+::unique\+\_\+ptr$<$ \mbox{\hyperlink{class_memory_in_files}{Memory\+In\+Files}} $>$ \&memory\+In\+Files\+Ptr\+Ref, \mbox{\hyperlink{class_user_data_recycler}{Ltp\+Session\+Receiver\+Recycler}} \&ltp\+Session\+Receiver\+Recycler\+Ref, const boost\+::posix\+\_\+time\+::ptime \&now\+Time\+Ref)
\begin{DoxyCompactList}\small\item\em Start all stat counters from 0. \end{DoxyCompactList}\end{DoxyCompactItemize}
\doxysubsubsection*{Public Attributes}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_af97aa196a6d6187747343a7c393855c4}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_af97aa196a6d6187747343a7c393855c4} 
const uint64\+\_\+t {\bfseries m\+\_\+client\+Service\+Id}
\begin{DoxyCompactList}\small\item\em Local client service ID. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_af6d048b526246e8bccabd5771399a65f}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_af6d048b526246e8bccabd5771399a65f} 
uint64\+\_\+t {\bfseries m\+\_\+max\+Reception\+Claims}
\begin{DoxyCompactList}\small\item\em Maximum number of reception claims per report segment. \end{DoxyCompactList}\item 
uint64\+\_\+t \mbox{\hyperlink{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a18d92080fa9894127f5a72733861f64d}{m\+\_\+estimated\+Bytes\+To\+Receive}}
\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a66db5bc8206de4b6cc95b1e08c4257c0}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a66db5bc8206de4b6cc95b1e08c4257c0} 
uint64\+\_\+t {\bfseries m\+\_\+max\+Red\+Rx\+Bytes}
\begin{DoxyCompactList}\small\item\em Maximum number of red data allowed in bytes per red data part. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_ae39d2b693c499643d9c5d3752967a6c2}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_ae39d2b693c499643d9c5d3752967a6c2} 
uint32\+\_\+t \& {\bfseries m\+\_\+max\+Retries\+Per\+Serial\+Number\+Ref}
\begin{DoxyCompactList}\small\item\em Maximum retries allowed per report. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a06289fadeee4d28e855043c6930ada27}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a06289fadeee4d28e855043c6930ada27} 
\mbox{\hyperlink{class_ltp_timer_manager}{Ltp\+Timer\+Manager}}$<$ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp\+::session\+\_\+id\+\_\+t}}, \mbox{\hyperlink{struct_ltp_1_1hash__session__id__t}{Ltp\+::hash\+\_\+session\+\_\+id\+\_\+t}} $>$ \& {\bfseries m\+\_\+time\+Manager\+Of\+Report\+Serial\+Numbers\+Ref}
\begin{DoxyCompactList}\small\item\em Report retransmission timer manager, timer mapped by session ID, hashed by session ID. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a8f47b2550abff9fba0f57ae86b938a57}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a8f47b2550abff9fba0f57ae86b938a57} 
const \mbox{\hyperlink{class_ltp_timer_manager}{Ltp\+Timer\+Manager}}$<$ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp\+::session\+\_\+id\+\_\+t}}, \mbox{\hyperlink{struct_ltp_1_1hash__session__id__t}{Ltp\+::hash\+\_\+session\+\_\+id\+\_\+t}} $>$\+::\+Ltp\+Timer\+Expired\+Callback\+\_\+t \& {\bfseries m\+\_\+rsn\+Timer\+Expired\+Callback\+Ref}
\begin{DoxyCompactList}\small\item\em Report retransmission timer expiry callback. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a3d57d7f398ec4dfce7fe3c81904db4e4}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a3d57d7f398ec4dfce7fe3c81904db4e4} 
\mbox{\hyperlink{class_ltp_timer_manager}{Ltp\+Timer\+Manager}}$<$ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp\+::session\+\_\+id\+\_\+t}}, \mbox{\hyperlink{struct_ltp_1_1hash__session__id__t}{Ltp\+::hash\+\_\+session\+\_\+id\+\_\+t}} $>$ \& {\bfseries m\+\_\+time\+Manager\+Of\+Sending\+Delayed\+Reception\+Reports\+Ref}
\begin{DoxyCompactList}\small\item\em Pending checkpoint delayed report transmission timer manager, timer mapped by session ID, hashed by session ID. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_aceedac641528b76b08fbee669b6d28aa}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_aceedac641528b76b08fbee669b6d28aa} 
const \mbox{\hyperlink{class_ltp_timer_manager}{Ltp\+Timer\+Manager}}$<$ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp\+::session\+\_\+id\+\_\+t}}, \mbox{\hyperlink{struct_ltp_1_1hash__session__id__t}{Ltp\+::hash\+\_\+session\+\_\+id\+\_\+t}} $>$\+::\+Ltp\+Timer\+Expired\+Callback\+\_\+t \& {\bfseries m\+\_\+delayed\+Reception\+Report\+Timer\+Expired\+Callback\+Ref}
\begin{DoxyCompactList}\small\item\em Pending checkpoint delayed report transmission timer expiry callback. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a0a9a3b811283ae1d5719c85f2725986b}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a0a9a3b811283ae1d5719c85f2725986b} 
const Notify\+Engine\+That\+This\+Receiver\+Needs\+Deleted\+Callback\+\_\+t \& {\bfseries m\+\_\+notify\+Engine\+That\+This\+Receiver\+Needs\+Deleted\+Callback\+Ref}
\begin{DoxyCompactList}\small\item\em \doxylink{class_ltp_engine}{Ltp\+Engine} this receiver should be queued for deletion notice function. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_aac37138393121b6d4dd8267fc250854a}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_aac37138393121b6d4dd8267fc250854a} 
const Notify\+Engine\+That\+This\+Receivers\+Timers\+Has\+Producible\+Data\+Function\+\_\+t \& {\bfseries m\+\_\+notify\+Engine\+That\+This\+Receivers\+Timers\+Has\+Producible\+Data\+Function\+Ref}
\begin{DoxyCompactList}\small\item\em \doxylink{class_ltp_engine}{Ltp\+Engine} this receiver has data to send notice function. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a81f0a235d53d5ae37c7e677f5729e403}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a81f0a235d53d5ae37c7e677f5729e403} 
const Notify\+Engine\+That\+This\+Receiver\+Completed\+Deferred\+Operation\+Function\+\_\+t \& {\bfseries m\+\_\+notify\+Engine\+That\+This\+Receiver\+Completed\+Deferred\+Operation\+Function\+Ref}
\begin{DoxyCompactList}\small\item\em \doxylink{class_ltp_engine}{Ltp\+Engine} this receiver has completed a deferred disk operation notice function. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_aca115f0e9317d5fc8aac16c0cfc1f8a5}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_aca115f0e9317d5fc8aac16c0cfc1f8a5} 
const Red\+Part\+Reception\+Callback\+\_\+t \& {\bfseries m\+\_\+red\+Part\+Reception\+Callback\+Ref}
\begin{DoxyCompactList}\small\item\em Red data part reception callback, invoked only on full red data part reception. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a12531d7b93e6acf3add67be87f33880b}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a12531d7b93e6acf3add67be87f33880b} 
const Green\+Part\+Segment\+Arrival\+Callback\+\_\+t \& {\bfseries m\+\_\+green\+Part\+Segment\+Arrival\+Callback\+Ref}
\begin{DoxyCompactList}\small\item\em Green data segment reception callback, invoked for any partial segment. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_afd48103b3a62983e9297b05bf69d0737}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_afd48103b3a62983e9297b05bf69d0737} 
std\+::unique\+\_\+ptr$<$ \mbox{\hyperlink{class_memory_in_files}{Memory\+In\+Files}} $>$ \& {\bfseries m\+\_\+memory\+In\+Files\+Ptr\+Ref}
\begin{DoxyCompactList}\small\item\em Disk memory manager. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_ac5442b489a966e7e294755610234cb8c}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_ac5442b489a966e7e294755610234cb8c} 
\mbox{\hyperlink{class_user_data_recycler}{Ltp\+Session\+Receiver\+Recycler}} \& {\bfseries m\+\_\+ltp\+Session\+Receiver\+Recycler\+Ref}
\begin{DoxyCompactList}\small\item\em Recycled data structure manager. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a01372a09a8b6683ee9c58f864c424bd0}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a01372a09a8b6683ee9c58f864c424bd0} 
const boost\+::posix\+\_\+time\+::ptime \& {\bfseries m\+\_\+now\+Time\+Ref}
\begin{DoxyCompactList}\small\item\em Now time (updated periodically from housekeeping) so timestamp need not make system calls to get the time. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a2a8bb30abcc7dd117b779d8151a455a8}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a2a8bb30abcc7dd117b779d8151a455a8} 
std\+::atomic$<$ uint64\+\_\+t $>$ {\bfseries m\+\_\+num\+Report\+Segment\+Timer\+Expired\+Callbacks}
\begin{DoxyCompactList}\small\item\em Total number of report segment timer expiry callback invocations. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a1c21fb6473a195693b69f03bdb14fd87}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a1c21fb6473a195693b69f03bdb14fd87} 
std\+::atomic$<$ uint64\+\_\+t $>$ {\bfseries m\+\_\+num\+Report\+Segments\+Unable\+To\+Be\+Issued}
\begin{DoxyCompactList}\small\item\em Total number of report segments unable to be issued. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a97291ef070d18d28ad0df92a9dd67dae}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a97291ef070d18d28ad0df92a9dd67dae} 
std\+::atomic$<$ uint64\+\_\+t $>$ {\bfseries m\+\_\+num\+Report\+Segments\+Too\+Large\+And\+Needing\+Split}
\begin{DoxyCompactList}\small\item\em Total number of reports too large needing-\/fragmented (when report claims \texorpdfstring{$>$}{>} m\+\_\+max\+Reception\+Claims) \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_ae1af7792486a95fccc8c09e8e321cdfa}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_ae1af7792486a95fccc8c09e8e321cdfa} 
std\+::atomic$<$ uint64\+\_\+t $>$ {\bfseries m\+\_\+num\+Report\+Segments\+Created\+Via\+Split}
\begin{DoxyCompactList}\small\item\em Total number of report segments produced from too large needing-\/fragmented reports. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_add88ae6f4e8066bd365d01b806e765fd}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_add88ae6f4e8066bd365d01b806e765fd} 
std\+::atomic$<$ uint64\+\_\+t $>$ {\bfseries m\+\_\+num\+Gaps\+Filled\+By\+Out\+Of\+Order\+Data\+Segments}
\begin{DoxyCompactList}\small\item\em Total number of gaps filled by out-\/of-\/order data segments. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a464337f0cef843923e5a6fd1b2339ff9}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a464337f0cef843923e5a6fd1b2339ff9} 
std\+::atomic$<$ uint64\+\_\+t $>$ {\bfseries m\+\_\+num\+Delayed\+Fully\+Claimed\+Primary\+Report\+Segments\+Sent}
\begin{DoxyCompactList}\small\item\em Total number of whole primary report segments sent (only reports when no gaps) \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_ae979472caac546d98ab1ef4e4d2ca7f9}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_ae979472caac546d98ab1ef4e4d2ca7f9} 
std\+::atomic$<$ uint64\+\_\+t $>$ {\bfseries m\+\_\+num\+Delayed\+Fully\+Claimed\+Secondary\+Report\+Segments\+Sent}
\begin{DoxyCompactList}\small\item\em Total number of whole secondary report segments sent (only reports when no gaps) \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a5a66a42223381a8b13d13817974adb22}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a5a66a42223381a8b13d13817974adb22} 
std\+::atomic$<$ uint64\+\_\+t $>$ {\bfseries m\+\_\+num\+Delayed\+Partially\+Claimed\+Primary\+Report\+Segments\+Sent}
\begin{DoxyCompactList}\small\item\em Total number of out-\/of-\/order partial primary report segments. \end{DoxyCompactList}\item 
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_ac46b65c7b41c646dbfe8c986ecaf9b9f}\label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_ac46b65c7b41c646dbfe8c986ecaf9b9f} 
std\+::atomic$<$ uint64\+\_\+t $>$ {\bfseries m\+\_\+num\+Delayed\+Partially\+Claimed\+Secondary\+Report\+Segments\+Sent}
\begin{DoxyCompactList}\small\item\em Total number of out-\/of-\/order partial secondary report segments. \end{DoxyCompactList}\end{DoxyCompactItemize}


\doxysubsection{Detailed Description}
Receiver common data, referenced across all receivers associated with the same \doxylink{class_ltp_engine}{Ltp\+Engine}. 

\doxysubsection{Member Data Documentation}
\Hypertarget{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a18d92080fa9894127f5a72733861f64d}\index{LtpSessionReceiver::LtpSessionReceiverCommonData@{LtpSessionReceiver::LtpSessionReceiverCommonData}!m\_estimatedBytesToReceive@{m\_estimatedBytesToReceive}}
\index{m\_estimatedBytesToReceive@{m\_estimatedBytesToReceive}!LtpSessionReceiver::LtpSessionReceiverCommonData@{LtpSessionReceiver::LtpSessionReceiverCommonData}}
\doxysubsubsection{\texorpdfstring{m\_estimatedBytesToReceive}{m\_estimatedBytesToReceive}}
{\footnotesize\ttfamily \label{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data_a18d92080fa9894127f5a72733861f64d} 
uint64\+\_\+t Ltp\+Session\+Receiver\+::\+Ltp\+Session\+Receiver\+Common\+Data\+::m\+\_\+estimated\+Bytes\+To\+Receive}

Estimated maximum number of bytes to reverse space for (both in-\/memory and for disk storage). This is a soft cap to lessen instances of reallocation, the actual space will be expanded if needed. 

The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
common/ltp/include/\mbox{\hyperlink{_ltp_session_receiver_8h}{Ltp\+Session\+Receiver.\+h}}\item 
common/ltp/src/\mbox{\hyperlink{_ltp_session_receiver_8cpp}{Ltp\+Session\+Receiver.\+cpp}}\end{DoxyCompactItemize}
