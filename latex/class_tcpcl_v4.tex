\doxysection{Tcpcl\+V4 Class Reference}
\hypertarget{class_tcpcl_v4}{}\label{class_tcpcl_v4}\index{TcpclV4@{TcpclV4}}
\doxysubsubsection*{Classes}
\begin{DoxyCompactItemize}
\item 
struct \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__ack__t}{tcpclv4\+\_\+ack\+\_\+t}}
\item 
struct \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extension__t}{tcpclv4\+\_\+extension\+\_\+t}}
\item 
struct \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extensions__t}{tcpclv4\+\_\+extensions\+\_\+t}}
\end{DoxyCompactItemize}
\doxysubsubsection*{Public Types}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_tcpcl_v4_a0ff74dc873d93f733084d5e24ed2f95b}\label{class_tcpcl_v4_a0ff74dc873d93f733084d5e24ed2f95b} 
typedef boost\+::function$<$ void(padded\+\_\+vector\+\_\+uint8\+\_\+t \&data\+Segment\+Data\+Vec, bool is\+Start\+Flag, bool is\+End\+Flag, uint64\+\_\+t transfer\+Id, const \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extensions__t}{tcpclv4\+\_\+extensions\+\_\+t}} \&transfer\+Extensions)$>$ {\bfseries Data\+Segment\+Contents\+Read\+Callback\+\_\+t}
\item 
\Hypertarget{class_tcpcl_v4_aae2fb0bb38d56e469ce38a1d83cb4dc0}\label{class_tcpcl_v4_aae2fb0bb38d56e469ce38a1d83cb4dc0} 
typedef boost\+::function$<$ void(bool remote\+Has\+Enabled\+Tls\+Security)$>$ {\bfseries Contact\+Header\+Read\+Callback\+\_\+t}
\item 
\Hypertarget{class_tcpcl_v4_a7b354f0c5e604d045395b6370f424fd3}\label{class_tcpcl_v4_a7b354f0c5e604d045395b6370f424fd3} 
typedef boost\+::function$<$ void(uint16\+\_\+t keep\+Alive\+Interval\+Seconds, uint64\+\_\+t segment\+Mru, uint64\+\_\+t transfer\+Mru, const std\+::string \&remote\+Node\+Eid\+Uri, const \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extensions__t}{tcpclv4\+\_\+extensions\+\_\+t}} \&session\+Extensions)$>$ {\bfseries Session\+Init\+Callback\+\_\+t}
\item 
\Hypertarget{class_tcpcl_v4_a24a1aa74f9085cceb4b457c4638c4b70}\label{class_tcpcl_v4_a24a1aa74f9085cceb4b457c4638c4b70} 
typedef boost\+::function$<$ void(const \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__ack__t}{tcpclv4\+\_\+ack\+\_\+t}} \&ack)$>$ {\bfseries Ack\+Segment\+Read\+Callback\+\_\+t}
\item 
\Hypertarget{class_tcpcl_v4_a686820028875d7378a0b4de8d03e9831}\label{class_tcpcl_v4_a686820028875d7378a0b4de8d03e9831} 
typedef boost\+::function$<$ void(TCPCLV4\+\_\+\+MESSAGE\+\_\+\+REJECT\+\_\+\+REASON\+\_\+\+CODES refusal\+Code, uint8\+\_\+t rejected\+Message\+Header)$>$ {\bfseries Message\+Reject\+Callback\+\_\+t}
\item 
\Hypertarget{class_tcpcl_v4_a726cabf477e9e160a3e4b57da0eb2e77}\label{class_tcpcl_v4_a726cabf477e9e160a3e4b57da0eb2e77} 
typedef boost\+::function$<$ void(TCPCLV4\+\_\+\+TRANSFER\+\_\+\+REFUSE\+\_\+\+REASON\+\_\+\+CODES refusal\+Code, uint64\+\_\+t transfer\+Id)$>$ {\bfseries Bundle\+Refusal\+Callback\+\_\+t}
\item 
\Hypertarget{class_tcpcl_v4_aef3aa3e12c5d0c4606566c1fd7a88f08}\label{class_tcpcl_v4_aef3aa3e12c5d0c4606566c1fd7a88f08} 
typedef boost\+::function$<$ void()$>$ {\bfseries Keep\+Alive\+Callback\+\_\+t}
\item 
\Hypertarget{class_tcpcl_v4_a6570489b0997ade9679287df9f25f198}\label{class_tcpcl_v4_a6570489b0997ade9679287df9f25f198} 
typedef boost\+::function$<$ void(TCPCLV4\+\_\+\+SESSION\+\_\+\+TERMINATION\+\_\+\+REASON\+\_\+\+CODES termination\+Reason\+Code, bool is\+Ack\+Of\+An\+Earlier\+Session\+Termination\+Message)$>$ {\bfseries Session\+Termination\+Message\+Callback\+\_\+t}
\end{DoxyCompactItemize}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_tcpcl_v4_aa61c53b9fbdfa034b40ff3a8d85fed29}\label{class_tcpcl_v4_aa61c53b9fbdfa034b40ff3a8d85fed29} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Set\+Data\+Segment\+Contents\+Read\+Callback} (const Data\+Segment\+Contents\+Read\+Callback\+\_\+t \&callback)
\item 
\Hypertarget{class_tcpcl_v4_a29f98823e3e3c48d5679f66d83a20d39}\label{class_tcpcl_v4_a29f98823e3e3c48d5679f66d83a20d39} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Set\+Contact\+Header\+Read\+Callback} (const Contact\+Header\+Read\+Callback\+\_\+t \&callback)
\item 
\Hypertarget{class_tcpcl_v4_a82e68f0fb3944e04b86c353e4ce1fed4}\label{class_tcpcl_v4_a82e68f0fb3944e04b86c353e4ce1fed4} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Set\+Session\+Init\+Read\+Callback} (const Session\+Init\+Callback\+\_\+t \&callback)
\item 
\Hypertarget{class_tcpcl_v4_a8a81ae077173768a59b8498269f9e1c9}\label{class_tcpcl_v4_a8a81ae077173768a59b8498269f9e1c9} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Set\+Ack\+Segment\+Read\+Callback} (const Ack\+Segment\+Read\+Callback\+\_\+t \&callback)
\item 
\Hypertarget{class_tcpcl_v4_acf03fde6e2bcb246079acb230d3db3b2}\label{class_tcpcl_v4_acf03fde6e2bcb246079acb230d3db3b2} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Set\+Bundle\+Refusal\+Callback} (const Bundle\+Refusal\+Callback\+\_\+t \&callback)
\item 
\Hypertarget{class_tcpcl_v4_a05f545d96c6b24ff548d11115ed99113}\label{class_tcpcl_v4_a05f545d96c6b24ff548d11115ed99113} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Set\+Message\+Reject\+Callback} (const Message\+Reject\+Callback\+\_\+t \&callback)
\item 
\Hypertarget{class_tcpcl_v4_afc55c6077f7c3aa1d77f86e2b8597c25}\label{class_tcpcl_v4_afc55c6077f7c3aa1d77f86e2b8597c25} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Set\+Keep\+Alive\+Callback} (const Keep\+Alive\+Callback\+\_\+t \&callback)
\item 
\Hypertarget{class_tcpcl_v4_a3f49564edb57b37749c0dfdaf66e3d03}\label{class_tcpcl_v4_a3f49564edb57b37749c0dfdaf66e3d03} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Set\+Session\+Termination\+Message\+Callback} (const Session\+Termination\+Message\+Callback\+\_\+t \&callback)
\item 
\Hypertarget{class_tcpcl_v4_a6f4b9e4c4c17f25e8342ca60c281bdff}\label{class_tcpcl_v4_a6f4b9e4c4c17f25e8342ca60c281bdff} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Set\+Max\+Receive\+Bundle\+Size\+Bytes} (const uint64\+\_\+t max\+Rx\+Bundle\+Size\+Bytes)
\item 
\Hypertarget{class_tcpcl_v4_a2af92c81eb5a6eba899cecd9fd404f37}\label{class_tcpcl_v4_a2af92c81eb5a6eba899cecd9fd404f37} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Init\+Rx} ()
\item 
\Hypertarget{class_tcpcl_v4_affbd4c3250a51c4c77ed29837fe90a48}\label{class_tcpcl_v4_affbd4c3250a51c4c77ed29837fe90a48} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Handle\+Received\+Chars} (const uint8\+\_\+t \texorpdfstring{$\ast$}{*}rx\+Vals, std\+::size\+\_\+t num\+Chars)
\item 
\Hypertarget{class_tcpcl_v4_a4e3582a9df5d811973c0984704d3fdc0}\label{class_tcpcl_v4_a4e3582a9df5d811973c0984704d3fdc0} 
TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Handle\+Received\+Char} (const uint8\+\_\+t rx\+Val)
\end{DoxyCompactItemize}
\doxysubsubsection*{Static Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_tcpcl_v4_a489a3bc679351724cdf2034ec3fcc062}\label{class_tcpcl_v4_a489a3bc679351724cdf2034ec3fcc062} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Generate\+Contact\+Header} (std\+::vector$<$ uint8\+\_\+t $>$ \&hdr, bool remote\+Has\+Enabled\+Tls\+Security)
\item 
\Hypertarget{class_tcpcl_v4_a592b76667094c32a6f08724e0605bd0b}\label{class_tcpcl_v4_a592b76667094c32a6f08724e0605bd0b} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Session\+Init\+Message} (std\+::vector$<$ uint8\+\_\+t $>$ \&msg, uint16\+\_\+t keep\+Alive\+Interval\+Seconds, uint64\+\_\+t segment\+Mru, uint64\+\_\+t transfer\+Mru, const std\+::string \&my\+Node\+Eid\+Uri, const \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extensions__t}{tcpclv4\+\_\+extensions\+\_\+t}} \&session\+Extensions)
\item 
\Hypertarget{class_tcpcl_v4_a439ddcae5a67f1e97ba3835f25bea313}\label{class_tcpcl_v4_a439ddcae5a67f1e97ba3835f25bea313} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Non\+Fragmented\+Data\+Segment} (std\+::vector$<$ uint8\+\_\+t $>$ \&data\+Segment, uint64\+\_\+t transfer\+Id, const uint8\+\_\+t \texorpdfstring{$\ast$}{*}contents, uint64\+\_\+t size\+Contents)
\item 
\Hypertarget{class_tcpcl_v4_af14f0595440c019679baedeae1d9a5cb}\label{class_tcpcl_v4_af14f0595440c019679baedeae1d9a5cb} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Non\+Fragmented\+Data\+Segment} (std\+::vector$<$ uint8\+\_\+t $>$ \&data\+Segment, uint64\+\_\+t transfer\+Id, const uint8\+\_\+t \texorpdfstring{$\ast$}{*}contents, uint64\+\_\+t size\+Contents, const \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extensions__t}{tcpclv4\+\_\+extensions\+\_\+t}} \&transfer\+Extensions)
\item 
\Hypertarget{class_tcpcl_v4_aa311a1a1e53524da115e7d4280286190}\label{class_tcpcl_v4_aa311a1a1e53524da115e7d4280286190} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Start\+Data\+Segment} (std\+::vector$<$ uint8\+\_\+t $>$ \&data\+Segment, bool is\+End\+Segment, uint64\+\_\+t transfer\+Id, const uint8\+\_\+t \texorpdfstring{$\ast$}{*}contents, uint64\+\_\+t size\+Contents, const \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extensions__t}{tcpclv4\+\_\+extensions\+\_\+t}} \&transfer\+Extensions)
\item 
\Hypertarget{class_tcpcl_v4_a9ff56755228de5fccab79f6150862577}\label{class_tcpcl_v4_a9ff56755228de5fccab79f6150862577} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Fragmented\+Start\+Data\+Segment\+With\+Length\+Extension} (std\+::vector$<$ uint8\+\_\+t $>$ \&data\+Segment, uint64\+\_\+t transfer\+Id, const uint8\+\_\+t \texorpdfstring{$\ast$}{*}contents, uint64\+\_\+t size\+Contents, uint64\+\_\+t total\+Bundle\+Length\+To\+Be\+Sent)
\item 
\Hypertarget{class_tcpcl_v4_af5b124d9c90dd77f0700c695f231af8e}\label{class_tcpcl_v4_af5b124d9c90dd77f0700c695f231af8e} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Non\+Start\+Data\+Segment} (std\+::vector$<$ uint8\+\_\+t $>$ \&data\+Segment, bool is\+End\+Segment, uint64\+\_\+t transfer\+Id, const uint8\+\_\+t \texorpdfstring{$\ast$}{*}contents, uint64\+\_\+t size\+Contents)
\item 
\Hypertarget{class_tcpcl_v4_a82efcc6c7c90ab0ec0b2cd4549094bee}\label{class_tcpcl_v4_a82efcc6c7c90ab0ec0b2cd4549094bee} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Non\+Fragmented\+Data\+Segment\+Header\+Only} (std\+::vector$<$ uint8\+\_\+t $>$ \&data\+Segment\+Header\+Data\+Vec, uint64\+\_\+t transfer\+Id, uint64\+\_\+t size\+Contents)
\item 
\Hypertarget{class_tcpcl_v4_a76f52d4f24723af0525b72369d9dc05b}\label{class_tcpcl_v4_a76f52d4f24723af0525b72369d9dc05b} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Non\+Fragmented\+Data\+Segment\+Header\+Only} (std\+::vector$<$ uint8\+\_\+t $>$ \&data\+Segment\+Header\+Data\+Vec, uint64\+\_\+t transfer\+Id, uint64\+\_\+t size\+Contents, const \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extensions__t}{tcpclv4\+\_\+extensions\+\_\+t}} \&transfer\+Extensions)
\item 
\Hypertarget{class_tcpcl_v4_a0d774b30813408ace95af33c27d7089a}\label{class_tcpcl_v4_a0d774b30813408ace95af33c27d7089a} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Start\+Data\+Segment\+Header\+Only} (std\+::vector$<$ uint8\+\_\+t $>$ \&data\+Segment\+Header\+Data\+Vec, bool is\+End\+Segment, uint64\+\_\+t transfer\+Id, uint64\+\_\+t size\+Contents, const \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extensions__t}{tcpclv4\+\_\+extensions\+\_\+t}} \&transfer\+Extensions)
\item 
\Hypertarget{class_tcpcl_v4_a32b998fb30646a15267850c7773d1d10}\label{class_tcpcl_v4_a32b998fb30646a15267850c7773d1d10} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Fragmented\+Start\+Data\+Segment\+With\+Length\+Extension\+Header\+Only} (std\+::vector$<$ uint8\+\_\+t $>$ \&data\+Segment\+Header\+Data\+Vec, uint64\+\_\+t transfer\+Id, uint64\+\_\+t size\+Contents, uint64\+\_\+t total\+Bundle\+Length\+To\+Be\+Sent)
\item 
\Hypertarget{class_tcpcl_v4_a4e45ee87f150ffaf9069af70a4ff7846}\label{class_tcpcl_v4_a4e45ee87f150ffaf9069af70a4ff7846} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Non\+Start\+Data\+Segment\+Header\+Only} (std\+::vector$<$ uint8\+\_\+t $>$ \&data\+Segment\+Header\+Data\+Vec, bool is\+End\+Segment, uint64\+\_\+t transfer\+Id, uint64\+\_\+t size\+Contents)
\item 
\Hypertarget{class_tcpcl_v4_a2ee0861ad93ee1fcd64ed7cdff051d6a}\label{class_tcpcl_v4_a2ee0861ad93ee1fcd64ed7cdff051d6a} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Ack\+Segment} (std\+::vector$<$ uint8\+\_\+t $>$ \&ack\+Segment, const \mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__ack__t}{tcpclv4\+\_\+ack\+\_\+t}} \&ack)
\item 
\Hypertarget{class_tcpcl_v4_af45f95e306781cc173d80d34b6c4a6d0}\label{class_tcpcl_v4_af45f95e306781cc173d80d34b6c4a6d0} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Generate\+Ack\+Segment} (std\+::vector$<$ uint8\+\_\+t $>$ \&ack\+Segment, bool is\+Start\+Segment, bool is\+End\+Segment, uint64\+\_\+t transfer\+Id, uint64\+\_\+t total\+Bytes\+Acknowledged)
\item 
\Hypertarget{class_tcpcl_v4_a30f7180ed88509dfad47ce097646b031}\label{class_tcpcl_v4_a30f7180ed88509dfad47ce097646b031} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Generate\+Bundle\+Refusal} (std\+::vector$<$ uint8\+\_\+t $>$ \&refusal\+Message, TCPCLV4\+\_\+\+TRANSFER\+\_\+\+REFUSE\+\_\+\+REASON\+\_\+\+CODES refusal\+Code, uint64\+\_\+t transfer\+Id)
\item 
\Hypertarget{class_tcpcl_v4_a8c6364d0aca8d798d94b30177d40161a}\label{class_tcpcl_v4_a8c6364d0aca8d798d94b30177d40161a} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Generate\+Message\+Rejection} (std\+::vector$<$ uint8\+\_\+t $>$ \&rejection\+Message, TCPCLV4\+\_\+\+MESSAGE\+\_\+\+REJECT\+\_\+\+REASON\+\_\+\+CODES rejection\+Code, uint8\+\_\+t rejected\+Message\+Header)
\item 
\Hypertarget{class_tcpcl_v4_aa23b606c1ff52b7d30b4d7685046fcf0}\label{class_tcpcl_v4_aa23b606c1ff52b7d30b4d7685046fcf0} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Generate\+Keep\+Alive\+Message} (std\+::vector$<$ uint8\+\_\+t $>$ \&keep\+Alive\+Message)
\item 
\Hypertarget{class_tcpcl_v4_ac56cd016e86931003f7a1c222473c873}\label{class_tcpcl_v4_ac56cd016e86931003f7a1c222473c873} 
static TCPCL\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Generate\+Session\+Termination\+Message} (std\+::vector$<$ uint8\+\_\+t $>$ \&session\+Termination\+Message, TCPCLV4\+\_\+\+SESSION\+\_\+\+TERMINATION\+\_\+\+REASON\+\_\+\+CODES session\+Termination\+Reason\+Code, bool is\+Ack\+Of\+An\+Earlier\+Session\+Termination\+Message)
\end{DoxyCompactItemize}
\doxysubsubsection*{Public Attributes}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_tcpcl_v4_aa5cc266586150f9de639a703d251a4a9}\label{class_tcpcl_v4_aa5cc266586150f9de639a703d251a4a9} 
uint64\+\_\+t {\bfseries M\+\_\+\+MAX\+\_\+\+RX\+\_\+\+BUNDLE\+\_\+\+SIZE\+\_\+\+BYTES}
\item 
\Hypertarget{class_tcpcl_v4_af2f9ad04c74a97da9b09ed4a87216831}\label{class_tcpcl_v4_af2f9ad04c74a97da9b09ed4a87216831} 
TCPCLV4\+\_\+\+MAIN\+\_\+\+RX\+\_\+\+STATE {\bfseries m\+\_\+main\+Rx\+State}
\item 
\Hypertarget{class_tcpcl_v4_abff243fc22e339fc9f9ef98c37bf071b}\label{class_tcpcl_v4_abff243fc22e339fc9f9ef98c37bf071b} 
TCPCLV4\+\_\+\+CONTACT\+\_\+\+HEADER\+\_\+\+RX\+\_\+\+STATE {\bfseries m\+\_\+contact\+Header\+Rx\+State}
\item 
\Hypertarget{class_tcpcl_v4_a9aaa89e26ee262b3b34bbcf91af3d829}\label{class_tcpcl_v4_a9aaa89e26ee262b3b34bbcf91af3d829} 
TCPCLV4\+\_\+\+DATA\+\_\+\+SEGMENT\+\_\+\+RX\+\_\+\+STATE {\bfseries m\+\_\+data\+Segment\+Rx\+State}
\item 
\Hypertarget{class_tcpcl_v4_a45b4519fbda6f8e65bbb659986c25ada}\label{class_tcpcl_v4_a45b4519fbda6f8e65bbb659986c25ada} 
TCPCLV4\+\_\+\+DATA\+\_\+\+ACK\+\_\+\+RX\+\_\+\+STATE {\bfseries m\+\_\+data\+Ack\+Rx\+State}
\item 
\Hypertarget{class_tcpcl_v4_ae370ce01b69edb8e2db30030dceaccda}\label{class_tcpcl_v4_ae370ce01b69edb8e2db30030dceaccda} 
TCPCLV4\+\_\+\+MESSAGE\+\_\+\+REJECT\+\_\+\+RX\+\_\+\+STATE {\bfseries m\+\_\+message\+Reject\+Rx\+State}
\item 
\Hypertarget{class_tcpcl_v4_a8192d3081c545bedfdac47dcf29c8c50}\label{class_tcpcl_v4_a8192d3081c545bedfdac47dcf29c8c50} 
TCPCLV4\+\_\+\+TRANSFER\+\_\+\+REFUSAL\+\_\+\+RX\+\_\+\+STATE {\bfseries m\+\_\+transfer\+Refusal\+Rx\+State}
\item 
\Hypertarget{class_tcpcl_v4_af0aa459d8c2074b2acb9b02867f4f95a}\label{class_tcpcl_v4_af0aa459d8c2074b2acb9b02867f4f95a} 
TCPCLV4\+\_\+\+SESSION\+\_\+\+TERMINATION\+\_\+\+RX\+\_\+\+STATE {\bfseries m\+\_\+session\+Termination\+Rx\+State}
\item 
\Hypertarget{class_tcpcl_v4_a50d6c9d3749ebd797dee9045a7a06fbe}\label{class_tcpcl_v4_a50d6c9d3749ebd797dee9045a7a06fbe} 
TCPCLV4\+\_\+\+SESSION\+\_\+\+INIT\+\_\+\+RX\+\_\+\+STATE {\bfseries m\+\_\+session\+Init\+Rx\+State}
\item 
\Hypertarget{class_tcpcl_v4_a9180b76588340618d69e297e1d63f5bd}\label{class_tcpcl_v4_a9180b76588340618d69e297e1d63f5bd} 
bool {\bfseries m\+\_\+remote\+Has\+Enabled\+Tls\+Security}
\item 
\Hypertarget{class_tcpcl_v4_a6d80f35d776b03ce985595764a5c9841}\label{class_tcpcl_v4_a6d80f35d776b03ce985595764a5c9841} 
TCPCLV4\+\_\+\+MESSAGE\+\_\+\+TYPE\+\_\+\+BYTE\+\_\+\+CODES {\bfseries m\+\_\+message\+Type\+Byte}
\item 
\Hypertarget{class_tcpcl_v4_a4a4d7869bfb944b78631af77a32c104f}\label{class_tcpcl_v4_a4a4d7869bfb944b78631af77a32c104f} 
uint16\+\_\+t {\bfseries m\+\_\+keep\+Alive\+Interval}
\item 
\Hypertarget{class_tcpcl_v4_aa217ccab77effad3272414d08e532bfc}\label{class_tcpcl_v4_aa217ccab77effad3272414d08e532bfc} 
uint64\+\_\+t {\bfseries m\+\_\+segment\+Mru}
\item 
\Hypertarget{class_tcpcl_v4_a80543ad51d6a2d230f7ad8cd876bde3d}\label{class_tcpcl_v4_a80543ad51d6a2d230f7ad8cd876bde3d} 
uint64\+\_\+t {\bfseries m\+\_\+transfer\+Mru}
\item 
\Hypertarget{class_tcpcl_v4_a4a6ff8bb061858feb4668ab801b69ce4}\label{class_tcpcl_v4_a4a6ff8bb061858feb4668ab801b69ce4} 
uint16\+\_\+t {\bfseries m\+\_\+remote\+Node\+Uri\+Length}
\item 
\Hypertarget{class_tcpcl_v4_a1f7baf566142edc28d4e0752237a568c}\label{class_tcpcl_v4_a1f7baf566142edc28d4e0752237a568c} 
std\+::string {\bfseries m\+\_\+remote\+Node\+Uri\+Str}
\item 
\Hypertarget{class_tcpcl_v4_a949531148716050ec085ddbafc946050}\label{class_tcpcl_v4_a949531148716050ec085ddbafc946050} 
uint32\+\_\+t {\bfseries m\+\_\+session\+Extension\+Items\+Length\+Bytes}
\item 
\Hypertarget{class_tcpcl_v4_a67dcfe6ef25b16ad06647247b4c9a83e}\label{class_tcpcl_v4_a67dcfe6ef25b16ad06647247b4c9a83e} 
uint32\+\_\+t {\bfseries m\+\_\+current\+Count\+Of\+Session\+Extension\+Encoded\+Bytes}
\item 
\Hypertarget{class_tcpcl_v4_a78ca87b484d88e87c07201b44856f95c}\label{class_tcpcl_v4_a78ca87b484d88e87c07201b44856f95c} 
\mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extensions__t}{tcpclv4\+\_\+extensions\+\_\+t}} {\bfseries m\+\_\+session\+Extensions}
\item 
\Hypertarget{class_tcpcl_v4_aafbb4e89db3ea7d50bc494783414c357}\label{class_tcpcl_v4_aafbb4e89db3ea7d50bc494783414c357} 
uint16\+\_\+t {\bfseries m\+\_\+current\+Session\+Extension\+Length}
\item 
\Hypertarget{class_tcpcl_v4_aa49a19529965d6afbc114e5d22a3db80}\label{class_tcpcl_v4_aa49a19529965d6afbc114e5d22a3db80} 
uint8\+\_\+t {\bfseries m\+\_\+read\+Value\+Byte\+Index}
\item 
\Hypertarget{class_tcpcl_v4_a74752afd64e0f798527a31c00b7444b6}\label{class_tcpcl_v4_a74752afd64e0f798527a31c00b7444b6} 
uint8\+\_\+t {\bfseries m\+\_\+message\+Flags}
\item 
\Hypertarget{class_tcpcl_v4_a4f13d8a04b26acc8a1dd1de43956ccce}\label{class_tcpcl_v4_a4f13d8a04b26acc8a1dd1de43956ccce} 
bool {\bfseries m\+\_\+data\+Segment\+Start\+Flag}
\item 
\Hypertarget{class_tcpcl_v4_a3b176fde5a0696634b7ea9adb49a396e}\label{class_tcpcl_v4_a3b176fde5a0696634b7ea9adb49a396e} 
bool {\bfseries m\+\_\+data\+Segment\+End\+Flag}
\item 
\Hypertarget{class_tcpcl_v4_a86a4fea1d9e191d6bf3bbdf6282a418f}\label{class_tcpcl_v4_a86a4fea1d9e191d6bf3bbdf6282a418f} 
uint64\+\_\+t {\bfseries m\+\_\+transfer\+Id}
\item 
\Hypertarget{class_tcpcl_v4_a1b065e00c9cfddb11830db389801b757}\label{class_tcpcl_v4_a1b065e00c9cfddb11830db389801b757} 
uint32\+\_\+t {\bfseries m\+\_\+transfer\+Extension\+Items\+Length\+Bytes}
\item 
\Hypertarget{class_tcpcl_v4_ad54ca5f859fadc5f795f0a98f5f050c3}\label{class_tcpcl_v4_ad54ca5f859fadc5f795f0a98f5f050c3} 
uint32\+\_\+t {\bfseries m\+\_\+current\+Count\+Of\+Transfer\+Extension\+Encoded\+Bytes}
\item 
\Hypertarget{class_tcpcl_v4_a1b524af25ba9f0fe9692906f0d8c714d}\label{class_tcpcl_v4_a1b524af25ba9f0fe9692906f0d8c714d} 
\mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__extensions__t}{tcpclv4\+\_\+extensions\+\_\+t}} {\bfseries m\+\_\+transfer\+Extensions}
\item 
\Hypertarget{class_tcpcl_v4_a5d570b71c72056556558888cc48bdb85}\label{class_tcpcl_v4_a5d570b71c72056556558888cc48bdb85} 
uint16\+\_\+t {\bfseries m\+\_\+current\+Transfer\+Extension\+Length}
\item 
\Hypertarget{class_tcpcl_v4_a83d8dba8646833675c85da6fa051242d}\label{class_tcpcl_v4_a83d8dba8646833675c85da6fa051242d} 
uint64\+\_\+t {\bfseries m\+\_\+data\+Segment\+Length}
\item 
\Hypertarget{class_tcpcl_v4_a645e41c20dd5e5dde8b117a8f6141b83}\label{class_tcpcl_v4_a645e41c20dd5e5dde8b117a8f6141b83} 
padded\+\_\+vector\+\_\+uint8\+\_\+t {\bfseries m\+\_\+data\+Segment\+Data\+Vec}
\item 
\Hypertarget{class_tcpcl_v4_a973219e7dff05f61859edf0ca8116a27}\label{class_tcpcl_v4_a973219e7dff05f61859edf0ca8116a27} 
uint8\+\_\+t {\bfseries m\+\_\+ack\+Flags}
\item 
\Hypertarget{class_tcpcl_v4_a63784be1d336966d602f7ca98020af52}\label{class_tcpcl_v4_a63784be1d336966d602f7ca98020af52} 
\mbox{\hyperlink{struct_tcpcl_v4_1_1tcpclv4__ack__t}{tcpclv4\+\_\+ack\+\_\+t}} {\bfseries m\+\_\+ack}
\item 
\Hypertarget{class_tcpcl_v4_a745df0c622d9c9dc19006c681da4b6b8}\label{class_tcpcl_v4_a745df0c622d9c9dc19006c681da4b6b8} 
uint8\+\_\+t {\bfseries m\+\_\+message\+Rejection\+Reason\+Code}
\item 
\Hypertarget{class_tcpcl_v4_abbad7763ab04e1e61f5fd4c30060c586}\label{class_tcpcl_v4_abbad7763ab04e1e61f5fd4c30060c586} 
uint8\+\_\+t {\bfseries m\+\_\+rejected\+Message\+Header}
\item 
\Hypertarget{class_tcpcl_v4_a2044c1ae0e46743a339b5a885eb2530e}\label{class_tcpcl_v4_a2044c1ae0e46743a339b5a885eb2530e} 
uint8\+\_\+t {\bfseries m\+\_\+bundle\+Tranfer\+Refusal\+Reason\+Code}
\item 
\Hypertarget{class_tcpcl_v4_ab8c98a6bcce809660e1bcf5c2393ee70}\label{class_tcpcl_v4_ab8c98a6bcce809660e1bcf5c2393ee70} 
uint64\+\_\+t {\bfseries m\+\_\+bundle\+Tranfer\+Refusal\+Transfer\+Id}
\item 
\Hypertarget{class_tcpcl_v4_a338668c1daba0ad2ec02363bfc03b0ee}\label{class_tcpcl_v4_a338668c1daba0ad2ec02363bfc03b0ee} 
uint64\+\_\+t {\bfseries m\+\_\+next\+Bundle\+Length}
\item 
\Hypertarget{class_tcpcl_v4_a0b8662991a928c0010d1c03e35fd4ad2}\label{class_tcpcl_v4_a0b8662991a928c0010d1c03e35fd4ad2} 
uint8\+\_\+t {\bfseries m\+\_\+session\+Termination\+Flags}
\item 
\Hypertarget{class_tcpcl_v4_ab2f0bfab8674bbaacba4d025c1653181}\label{class_tcpcl_v4_ab2f0bfab8674bbaacba4d025c1653181} 
bool {\bfseries m\+\_\+is\+Session\+Termination\+Ack}
\item 
\Hypertarget{class_tcpcl_v4_aa70b19ad81f496cc1b2b804e43f602d6}\label{class_tcpcl_v4_aa70b19ad81f496cc1b2b804e43f602d6} 
TCPCLV4\+\_\+\+SESSION\+\_\+\+TERMINATION\+\_\+\+REASON\+\_\+\+CODES {\bfseries m\+\_\+session\+Termination\+Reason\+Code}
\item 
\Hypertarget{class_tcpcl_v4_a594b451330403659f9df490aced1fdf1}\label{class_tcpcl_v4_a594b451330403659f9df490aced1fdf1} 
Contact\+Header\+Read\+Callback\+\_\+t {\bfseries m\+\_\+contact\+Header\+Read\+Callback}
\item 
\Hypertarget{class_tcpcl_v4_aab851175fb1c05170d1be1855d23b26e}\label{class_tcpcl_v4_aab851175fb1c05170d1be1855d23b26e} 
Session\+Init\+Callback\+\_\+t {\bfseries m\+\_\+session\+Init\+Callback}
\item 
\Hypertarget{class_tcpcl_v4_a445c36349f98e4f4e07412e77f33278f}\label{class_tcpcl_v4_a445c36349f98e4f4e07412e77f33278f} 
Data\+Segment\+Contents\+Read\+Callback\+\_\+t {\bfseries m\+\_\+data\+Segment\+Contents\+Read\+Callback}
\item 
\Hypertarget{class_tcpcl_v4_ac526af537eb46282a9dbb52a40fc5919}\label{class_tcpcl_v4_ac526af537eb46282a9dbb52a40fc5919} 
Ack\+Segment\+Read\+Callback\+\_\+t {\bfseries m\+\_\+ack\+Segment\+Read\+Callback}
\item 
\Hypertarget{class_tcpcl_v4_a833cb5622bfdcf406e654f2e9a7a7813}\label{class_tcpcl_v4_a833cb5622bfdcf406e654f2e9a7a7813} 
Message\+Reject\+Callback\+\_\+t {\bfseries m\+\_\+message\+Reject\+Callback}
\item 
\Hypertarget{class_tcpcl_v4_a3298f69dec70e549fb830edc03d8fc0e}\label{class_tcpcl_v4_a3298f69dec70e549fb830edc03d8fc0e} 
Bundle\+Refusal\+Callback\+\_\+t {\bfseries m\+\_\+bundle\+Refusal\+Callback}
\item 
\Hypertarget{class_tcpcl_v4_a110bf65f695b1a0d07e7c82b91850c0d}\label{class_tcpcl_v4_a110bf65f695b1a0d07e7c82b91850c0d} 
Keep\+Alive\+Callback\+\_\+t {\bfseries m\+\_\+keep\+Alive\+Callback}
\item 
\Hypertarget{class_tcpcl_v4_ae110744180ef6c4bc4c9fc148cd2ff1c}\label{class_tcpcl_v4_ae110744180ef6c4bc4c9fc148cd2ff1c} 
Session\+Termination\+Message\+Callback\+\_\+t {\bfseries m\+\_\+session\+Termination\+Message\+Callback}
\end{DoxyCompactItemize}


The documentation for this class was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
common/tcpcl/include/\mbox{\hyperlink{_tcpcl_v4_8h}{Tcpcl\+V4.\+h}}\item 
common/tcpcl/src/\mbox{\hyperlink{_tcpcl_v4_8cpp}{Tcpcl\+V4.\+cpp}}\end{DoxyCompactItemize}
