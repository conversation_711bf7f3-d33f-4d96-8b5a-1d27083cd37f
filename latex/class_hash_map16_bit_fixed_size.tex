\doxysection{Hash\+Map16\+Bit\+Fixed\+Size\texorpdfstring{$<$}{<} key\+Type, value\+Type \texorpdfstring{$>$}{>} Class Template Reference}
\hypertarget{class_hash_map16_bit_fixed_size}{}\label{class_hash_map16_bit_fixed_size}\index{HashMap16BitFixedSize$<$ keyType, valueType $>$@{HashMap16BitFixedSize$<$ keyType, valueType $>$}}
\doxysubsubsection*{Public Types}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_ab0e99dd8dd30d8741e37b7facdd92eee}\label{class_hash_map16_bit_fixed_size_ab0e99dd8dd30d8741e37b7facdd92eee} 
typedef std\+::pair$<$ key\+Type, value\+Type $>$ {\bfseries key\+\_\+value\+\_\+pair\+\_\+t}
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_a8be9b9caa4afdafc7ee423df28a08ca1}\label{class_hash_map16_bit_fixed_size_a8be9b9caa4afdafc7ee423df28a08ca1} 
typedef std\+::forward\+\_\+list$<$ key\+\_\+value\+\_\+pair\+\_\+t $>$ {\bfseries bucket\+\_\+t}
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_af4bf7f5b27701350a5ab2d8ebc61271e}\label{class_hash_map16_bit_fixed_size_af4bf7f5b27701350a5ab2d8ebc61271e} 
typedef std\+::array$<$ bucket\+\_\+t, 65536 $>$ {\bfseries bucket\+\_\+array\+\_\+t}
\end{DoxyCompactItemize}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_adc19e37f0b1a7a8f58fb3dc409507bea}\label{class_hash_map16_bit_fixed_size_adc19e37f0b1a7a8f58fb3dc409507bea} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT const key\+\_\+value\+\_\+pair\+\_\+t \texorpdfstring{$\ast$}{*} {\bfseries Insert} (const key\+Type \&key, const value\+Type \&value)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_a5df4e0de8d7de69a8a1e6c01238f6f7b}\label{class_hash_map16_bit_fixed_size_a5df4e0de8d7de69a8a1e6c01238f6f7b} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT const key\+\_\+value\+\_\+pair\+\_\+t \texorpdfstring{$\ast$}{*} {\bfseries Insert} (const key\+Type \&key, value\+Type \&\&value)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_aa00a6a4711444588187d98e7b134fb43}\label{class_hash_map16_bit_fixed_size_aa00a6a4711444588187d98e7b134fb43} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT const key\+\_\+value\+\_\+pair\+\_\+t \texorpdfstring{$\ast$}{*} {\bfseries Insert} (const uint16\+\_\+t hash, const key\+Type \&key, const value\+Type \&value)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_a341f1e415b87972ca9dc4ca2205179aa}\label{class_hash_map16_bit_fixed_size_a341f1e415b87972ca9dc4ca2205179aa} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT const key\+\_\+value\+\_\+pair\+\_\+t \texorpdfstring{$\ast$}{*} {\bfseries Insert} (const uint16\+\_\+t hash, const key\+Type \&key, value\+Type \&\&value)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_a2d8160df7a898cef1d28ceddcfaa1380}\label{class_hash_map16_bit_fixed_size_a2d8160df7a898cef1d28ceddcfaa1380} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Get\+Value\+And\+Remove} (const key\+Type \&key, value\+Type \&value)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_a662a23f03e216b8197fdc84964f8b65e}\label{class_hash_map16_bit_fixed_size_a662a23f03e216b8197fdc84964f8b65e} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries Get\+Value\+And\+Remove} (const uint16\+\_\+t hash, const key\+Type \&key, value\+Type \&value)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_a23fec6dc9d65d538eeb9401a7a21225b}\label{class_hash_map16_bit_fixed_size_a23fec6dc9d65d538eeb9401a7a21225b} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT value\+Type \texorpdfstring{$\ast$}{*} {\bfseries Get\+Value\+Ptr} (const key\+Type \&key)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_ad83495b2fb1f6d45120244e9d16dc93b}\label{class_hash_map16_bit_fixed_size_ad83495b2fb1f6d45120244e9d16dc93b} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT value\+Type \texorpdfstring{$\ast$}{*} {\bfseries Get\+Value\+Ptr} (const uint16\+\_\+t hash, const key\+Type \&key)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_a35ca9b83aaf1f5ccd2e8d04fc95f1027}\label{class_hash_map16_bit_fixed_size_a35ca9b83aaf1f5ccd2e8d04fc95f1027} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Bucket\+To\+Vector} (const uint16\+\_\+t hash, std\+::vector$<$ key\+\_\+value\+\_\+pair\+\_\+t $>$ \&bucket\+As\+Vector)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_a308fae016b52285353fc43191711ebab}\label{class_hash_map16_bit_fixed_size_a308fae016b52285353fc43191711ebab} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT std\+::size\+\_\+t {\bfseries Get\+Bucket\+Size} (const uint16\+\_\+t hash)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_a6992929687eb494693351ee5699cd699}\label{class_hash_map16_bit_fixed_size_a6992929687eb494693351ee5699cd699} 
STORAGE\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Clear} ()
\end{DoxyCompactItemize}
\doxysubsubsection*{Static Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_ae855e8bf6cbfdf74d0c240719baca523}\label{class_hash_map16_bit_fixed_size_ae855e8bf6cbfdf74d0c240719baca523} 
static STORAGE\+\_\+\+LIB\+\_\+\+EXPORT uint16\+\_\+t {\bfseries Get\+Hash} (const \mbox{\hyperlink{structcbhe__bundle__uuid__t}{cbhe\+\_\+bundle\+\_\+uuid\+\_\+t}} \&bundle\+Uuid)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_a8896eb1352836d9a4121a37fa0b24a04}\label{class_hash_map16_bit_fixed_size_a8896eb1352836d9a4121a37fa0b24a04} 
static STORAGE\+\_\+\+LIB\+\_\+\+EXPORT uint16\+\_\+t {\bfseries Get\+Hash} (const \mbox{\hyperlink{structcbhe__bundle__uuid__nofragment__t}{cbhe\+\_\+bundle\+\_\+uuid\+\_\+nofragment\+\_\+t}} \&bundle\+Uuid)
\item 
\Hypertarget{class_hash_map16_bit_fixed_size_ae053ee0d4025392c1870c2b84eb717de}\label{class_hash_map16_bit_fixed_size_ae053ee0d4025392c1870c2b84eb717de} 
static STORAGE\+\_\+\+LIB\+\_\+\+EXPORT uint16\+\_\+t {\bfseries Get\+Hash} (const uint64\+\_\+t key)
\end{DoxyCompactItemize}


The documentation for this class was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
module/storage/include/\mbox{\hyperlink{_hash_map16_bit_fixed_size_8h}{Hash\+Map16\+Bit\+Fixed\+Size.\+h}}\item 
module/storage/src/\mbox{\hyperlink{_hash_map16_bit_fixed_size_8cpp}{Hash\+Map16\+Bit\+Fixed\+Size.\+cpp}}\end{DoxyCompactItemize}
