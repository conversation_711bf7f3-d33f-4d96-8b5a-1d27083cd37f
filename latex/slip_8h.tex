\doxysection{common/slip\+\_\+over\+\_\+uart/include/slip.h File Reference}
\hypertarget{slip_8h}{}\label{slip_8h}\index{common/slip\_over\_uart/include/slip.h@{common/slip\_over\_uart/include/slip.h}}
{\ttfamily \#include $<$stdlib.\+h$>$}\newline
{\ttfamily \#include $<$stdint.\+h$>$}\newline
{\ttfamily \#include $<$boost/config/detail/suffix.\+hpp$>$}\newline
{\ttfamily \#include "{}slip\+\_\+over\+\_\+uart\+\_\+lib\+\_\+export.\+h"{}}\newline
\doxysubsubsection*{Classes}
\begin{DoxyCompactItemize}
\item 
struct \mbox{\hyperlink{struct_slip_decode_state__t}{Slip\+Decode\+State\+\_\+t}}
\end{DoxyCompactItemize}
\doxysubsubsection*{Macros}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{slip_8h_ac08c4b8e75c6456f5654145f8b001005}\label{slip_8h_ac08c4b8e75c6456f5654145f8b001005} 
\#define {\bfseries SLIP\+\_\+\+END}~(0x\+C0)
\item 
\Hypertarget{slip_8h_a379cfa838122508f0e88fbbe8ce80c47}\label{slip_8h_a379cfa838122508f0e88fbbe8ce80c47} 
\#define {\bfseries SLIP\+\_\+\+ESC}~(0x\+DB)
\item 
\Hypertarget{slip_8h_a660be27a37d4105d47000a333dd26c1a}\label{slip_8h_a660be27a37d4105d47000a333dd26c1a} 
\#define {\bfseries SLIP\+\_\+\+ESC\+\_\+\+END}~(0x\+DC)
\item 
\Hypertarget{slip_8h_a75cd5d5e3cdfa54f2a7c6d137406bb72}\label{slip_8h_a75cd5d5e3cdfa54f2a7c6d137406bb72} 
\#define {\bfseries SLIP\+\_\+\+ESC\+\_\+\+ESC}~(0x\+DD)
\end{DoxyCompactItemize}
\doxysubsubsection*{Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{slip_8h_a4f1ecb0d34b51538dc5ee69c585b2d66}\label{slip_8h_a4f1ecb0d34b51538dc5ee69c585b2d66} 
SLIP\+\_\+\+OVER\+\_\+\+UART\+\_\+\+LIB\+\_\+\+EXPORT unsigned int {\bfseries Slip\+Encode} (const uint8\+\_\+t \texorpdfstring{$\ast$}{*}const input\+Ip\+Packet\+Raw\+Data, uint8\+\_\+t \texorpdfstring{$\ast$}{*}const output\+Slip\+Raw\+Data, const unsigned int size\+Input)
\item 
\Hypertarget{slip_8h_ae825694f8d2c839eff3c40f0214a6da4}\label{slip_8h_ae825694f8d2c839eff3c40f0214a6da4} 
SLIP\+\_\+\+OVER\+\_\+\+UART\+\_\+\+LIB\+\_\+\+EXPORT unsigned int {\bfseries Slip\+Encode\+Char} (const uint8\+\_\+t in\+Char, uint8\+\_\+t \texorpdfstring{$\ast$}{*}const output\+Slip\+Raw\+Data\+\_\+size2)
\item 
\Hypertarget{slip_8h_a55ce68e4d9e891c9d043311a34091c73}\label{slip_8h_a55ce68e4d9e891c9d043311a34091c73} 
SLIP\+\_\+\+OVER\+\_\+\+UART\+\_\+\+LIB\+\_\+\+EXPORT void {\bfseries Slip\+Decode\+Init} (\mbox{\hyperlink{struct_slip_decode_state__t}{Slip\+Decode\+State\+\_\+t}} \texorpdfstring{$\ast$}{*}slip\+Decode\+State)
\item 
\Hypertarget{slip_8h_a5314d05ee9f835991dec25acd77e595e}\label{slip_8h_a5314d05ee9f835991dec25acd77e595e} 
SLIP\+\_\+\+OVER\+\_\+\+UART\+\_\+\+LIB\+\_\+\+EXPORT unsigned int {\bfseries Slip\+Decode\+Char} (\mbox{\hyperlink{struct_slip_decode_state__t}{Slip\+Decode\+State\+\_\+t}} \texorpdfstring{$\ast$}{*}slip\+Decode\+State, const uint8\+\_\+t in\+Char, uint8\+\_\+t \texorpdfstring{$\ast$}{*}out\+Char)
\item 
\Hypertarget{slip_8h_ae49b0173d10a25c64794f268930c4332}\label{slip_8h_ae49b0173d10a25c64794f268930c4332} 
BOOST\+\_\+\+FORCEINLINE unsigned int {\bfseries Slip\+Decode\+Char\+\_\+\+Inline} (\mbox{\hyperlink{struct_slip_decode_state__t}{Slip\+Decode\+State\+\_\+t}} \texorpdfstring{$\ast$}{*}slip\+Decode\+State, const uint8\+\_\+t in\+Char, uint8\+\_\+t \texorpdfstring{$\ast$}{*}out\+Char)
\end{DoxyCompactItemize}


\doxysubsection{Detailed Description}
\begin{DoxyAuthor}{Author}
Brian Tomko \href{mailto:<EMAIL>}{\texttt{ brian.\+j.\+tomko@nasa.\+gov}}
\end{DoxyAuthor}
\begin{DoxyCopyright}{Copyright}
Copyright (c) 2021 United States Government as represented by the National Aeronautics and Space Administration. No copyright is claimed in the United States under Title 17, U.\+S.\+Code. All Other Rights Reserved.
\end{DoxyCopyright}
\hypertarget{import__installation_2src_2test__main_8cpp_LICENSE}{}\doxysubsection{\texorpdfstring{LICENSE}{LICENSE}}\label{import__installation_2src_2test__main_8cpp_LICENSE}
Released under the NASA Open Source Agreement (NOSA) See LICENSE.\+md in the source root directory for more information.\hypertarget{import__installation_2src_2test__main_8cpp_DESCRIPTION}{}\doxysubsection{\texorpdfstring{DESCRIPTION}{DESCRIPTION}}\label{import__installation_2src_2test__main_8cpp_DESCRIPTION}
This file contains functionality for SLIP encode and decode operations. 