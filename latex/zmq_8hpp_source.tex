\doxysection{zmq.\+hpp}
\hypertarget{zmq_8hpp_source}{}\label{zmq_8hpp_source}\index{common/util/include/zmq.hpp@{common/util/include/zmq.hpp}}

\begin{DoxyCode}{0}
\DoxyCodeLine{00001\ \textcolor{comment}{/*}}
\DoxyCodeLine{00002\ \textcolor{comment}{\ \ \ \ Copyright\ (c)\ 2016-\/2017\ ZeroMQ\ community}}
\DoxyCodeLine{00003\ \textcolor{comment}{\ \ \ \ Copyright\ (c)\ 2009-\/2011\ 250bpm\ s.r.o.}}
\DoxyCodeLine{00004\ \textcolor{comment}{\ \ \ \ Copyright\ (c)\ 2011\ Botond\ Ballo}}
\DoxyCodeLine{00005\ \textcolor{comment}{\ \ \ \ Copyright\ (c)\ 2007-\/2009\ iMatix\ Corporation}}
\DoxyCodeLine{00006\ \textcolor{comment}{}}
\DoxyCodeLine{00007\ \textcolor{comment}{\ \ \ \ Permission\ is\ hereby\ granted,\ free\ of\ charge,\ to\ any\ person\ obtaining\ a\ copy}}
\DoxyCodeLine{00008\ \textcolor{comment}{\ \ \ \ of\ this\ software\ and\ associated\ documentation\ files\ (the\ "{}Software"{}),\ to}}
\DoxyCodeLine{00009\ \textcolor{comment}{\ \ \ \ deal\ in\ the\ Software\ without\ restriction,\ including\ without\ limitation\ the}}
\DoxyCodeLine{00010\ \textcolor{comment}{\ \ \ \ rights\ to\ use,\ copy,\ modify,\ merge,\ publish,\ distribute,\ sublicense,\ and/or}}
\DoxyCodeLine{00011\ \textcolor{comment}{\ \ \ \ sell\ copies\ of\ the\ Software,\ and\ to\ permit\ persons\ to\ whom\ the\ Software\ is}}
\DoxyCodeLine{00012\ \textcolor{comment}{\ \ \ \ furnished\ to\ do\ so,\ subject\ to\ the\ following\ conditions:}}
\DoxyCodeLine{00013\ \textcolor{comment}{}}
\DoxyCodeLine{00014\ \textcolor{comment}{\ \ \ \ The\ above\ copyright\ notice\ and\ this\ permission\ notice\ shall\ be\ included\ in}}
\DoxyCodeLine{00015\ \textcolor{comment}{\ \ \ \ all\ copies\ or\ substantial\ portions\ of\ the\ Software.}}
\DoxyCodeLine{00016\ \textcolor{comment}{}}
\DoxyCodeLine{00017\ \textcolor{comment}{\ \ \ \ THE\ SOFTWARE\ IS\ PROVIDED\ "{}AS\ IS"{},\ WITHOUT\ WARRANTY\ OF\ ANY\ KIND,\ EXPRESS\ OR}}
\DoxyCodeLine{00018\ \textcolor{comment}{\ \ \ \ IMPLIED,\ INCLUDING\ BUT\ NOT\ LIMITED\ TO\ THE\ WARRANTIES\ OF\ MERCHANTABILITY,}}
\DoxyCodeLine{00019\ \textcolor{comment}{\ \ \ \ FITNESS\ FOR\ A\ PARTICULAR\ PURPOSE\ AND\ NONINFRINGEMENT.\ IN\ NO\ EVENT\ SHALL\ THE}}
\DoxyCodeLine{00020\ \textcolor{comment}{\ \ \ \ AUTHORS\ OR\ COPYRIGHT\ HOLDERS\ BE\ LIABLE\ FOR\ ANY\ CLAIM,\ DAMAGES\ OR\ OTHER}}
\DoxyCodeLine{00021\ \textcolor{comment}{\ \ \ \ LIABILITY,\ WHETHER\ IN\ AN\ ACTION\ OF\ CONTRACT,\ TORT\ OR\ OTHERWISE,\ ARISING}}
\DoxyCodeLine{00022\ \textcolor{comment}{\ \ \ \ FROM,\ OUT\ OF\ OR\ IN\ CONNECTION\ WITH\ THE\ SOFTWARE\ OR\ THE\ USE\ OR\ OTHER\ DEALINGS}}
\DoxyCodeLine{00023\ \textcolor{comment}{\ \ \ \ IN\ THE\ SOFTWARE.}}
\DoxyCodeLine{00024\ \textcolor{comment}{*/}}
\DoxyCodeLine{00025\ }
\DoxyCodeLine{00026\ \textcolor{preprocessor}{\#ifndef\ \_\_ZMQ\_HPP\_INCLUDED\_\_}}
\DoxyCodeLine{00027\ \textcolor{preprocessor}{\#define\ \_\_ZMQ\_HPP\_INCLUDED\_\_}}
\DoxyCodeLine{00028\ }
\DoxyCodeLine{00029\ \textcolor{preprocessor}{\#ifdef\ \_WIN32}}
\DoxyCodeLine{00030\ \textcolor{preprocessor}{\#ifndef\ NOMINMAX}}
\DoxyCodeLine{00031\ \textcolor{preprocessor}{\#define\ NOMINMAX}}
\DoxyCodeLine{00032\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00033\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00034\ }
\DoxyCodeLine{00035\ \textcolor{comment}{//\ included\ here\ for\ \_HAS\_CXX*\ macros}}
\DoxyCodeLine{00036\ \textcolor{preprocessor}{\#include\ <zmq.h>}}
\DoxyCodeLine{00037\ }
\DoxyCodeLine{00038\ \textcolor{preprocessor}{\#if\ defined(\_MSVC\_LANG)}}
\DoxyCodeLine{00039\ \textcolor{preprocessor}{\#define\ CPPZMQ\_LANG\ \_MSVC\_LANG}}
\DoxyCodeLine{00040\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{00041\ \textcolor{preprocessor}{\#define\ CPPZMQ\_LANG\ \_\_cplusplus}}
\DoxyCodeLine{00042\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00043\ \textcolor{comment}{//\ overwrite\ if\ specific\ language\ macros\ indicate\ higher\ version}}
\DoxyCodeLine{00044\ \textcolor{preprocessor}{\#if\ defined(\_HAS\_CXX14)\ \&\&\ \_HAS\_CXX14\ \&\&\ CPPZMQ\_LANG\ <\ 201402L}}
\DoxyCodeLine{00045\ \textcolor{preprocessor}{\#undef\ CPPZMQ\_LANG}}
\DoxyCodeLine{00046\ \textcolor{preprocessor}{\#define\ CPPZMQ\_LANG\ 201402L}}
\DoxyCodeLine{00047\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00048\ \textcolor{preprocessor}{\#if\ defined(\_HAS\_CXX17)\ \&\&\ \_HAS\_CXX17\ \&\&\ CPPZMQ\_LANG\ <\ 201703L}}
\DoxyCodeLine{00049\ \textcolor{preprocessor}{\#undef\ CPPZMQ\_LANG}}
\DoxyCodeLine{00050\ \textcolor{preprocessor}{\#define\ CPPZMQ\_LANG\ 201703L}}
\DoxyCodeLine{00051\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00052\ }
\DoxyCodeLine{00053\ \textcolor{comment}{//\ macros\ defined\ if\ has\ a\ specific\ standard\ or\ greater}}
\DoxyCodeLine{00054\ \textcolor{preprocessor}{\#if\ CPPZMQ\_LANG\ >=\ 201103L\ ||\ (defined(\_MSC\_VER)\ \&\&\ \_MSC\_VER\ >=\ 1900)}}
\DoxyCodeLine{00055\ \textcolor{preprocessor}{\#define\ ZMQ\_CPP11}}
\DoxyCodeLine{00056\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00057\ \textcolor{preprocessor}{\#if\ CPPZMQ\_LANG\ >=\ 201402L}}
\DoxyCodeLine{00058\ \textcolor{preprocessor}{\#define\ ZMQ\_CPP14}}
\DoxyCodeLine{00059\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00060\ \textcolor{preprocessor}{\#if\ CPPZMQ\_LANG\ >=\ 201703L}}
\DoxyCodeLine{00061\ \textcolor{preprocessor}{\#define\ ZMQ\_CPP17}}
\DoxyCodeLine{00062\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00063\ }
\DoxyCodeLine{00064\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_CPP14)\ \&\&\ !defined(\_MSC\_VER)}}
\DoxyCodeLine{00065\ \textcolor{preprocessor}{\#define\ ZMQ\_DEPRECATED(msg)\ [[deprecated(msg)]]}}
\DoxyCodeLine{00066\ \textcolor{preprocessor}{\#elif\ defined(\_MSC\_VER)}}
\DoxyCodeLine{00067\ \textcolor{preprocessor}{\#define\ ZMQ\_DEPRECATED(msg)\ \_\_declspec(deprecated(msg))}}
\DoxyCodeLine{00068\ \textcolor{preprocessor}{\#elif\ defined(\_\_GNUC\_\_)}}
\DoxyCodeLine{00069\ \textcolor{preprocessor}{\#define\ ZMQ\_DEPRECATED(msg)\ \_\_attribute\_\_((deprecated(msg)))}}
\DoxyCodeLine{00070\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00071\ }
\DoxyCodeLine{00072\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_CPP17)}}
\DoxyCodeLine{00073\ \textcolor{preprocessor}{\#define\ ZMQ\_NODISCARD\ [[nodiscard]]}}
\DoxyCodeLine{00074\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{00075\ \textcolor{preprocessor}{\#define\ ZMQ\_NODISCARD}}
\DoxyCodeLine{00076\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00077\ }
\DoxyCodeLine{00078\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_CPP11)}}
\DoxyCodeLine{00079\ \textcolor{preprocessor}{\#define\ ZMQ\_NOTHROW\ noexcept}}
\DoxyCodeLine{00080\ \textcolor{preprocessor}{\#define\ ZMQ\_EXPLICIT\ explicit}}
\DoxyCodeLine{00081\ \textcolor{preprocessor}{\#define\ ZMQ\_OVERRIDE\ override}}
\DoxyCodeLine{00082\ \textcolor{preprocessor}{\#define\ ZMQ\_NULLPTR\ nullptr}}
\DoxyCodeLine{00083\ \textcolor{preprocessor}{\#define\ ZMQ\_CONSTEXPR\_FN\ constexpr}}
\DoxyCodeLine{00084\ \textcolor{preprocessor}{\#define\ ZMQ\_CONSTEXPR\_VAR\ constexpr}}
\DoxyCodeLine{00085\ \textcolor{preprocessor}{\#define\ ZMQ\_CPP11\_DEPRECATED(msg)\ ZMQ\_DEPRECATED(msg)}}
\DoxyCodeLine{00086\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{00087\ \textcolor{preprocessor}{\#define\ ZMQ\_NOTHROW\ throw()}}
\DoxyCodeLine{00088\ \textcolor{preprocessor}{\#define\ ZMQ\_EXPLICIT}}
\DoxyCodeLine{00089\ \textcolor{preprocessor}{\#define\ ZMQ\_OVERRIDE}}
\DoxyCodeLine{00090\ \textcolor{preprocessor}{\#define\ ZMQ\_NULLPTR\ 0}}
\DoxyCodeLine{00091\ \textcolor{preprocessor}{\#define\ ZMQ\_CONSTEXPR\_FN}}
\DoxyCodeLine{00092\ \textcolor{preprocessor}{\#define\ ZMQ\_CONSTEXPR\_VAR\ const}}
\DoxyCodeLine{00093\ \textcolor{preprocessor}{\#define\ ZMQ\_CPP11\_DEPRECATED(msg)}}
\DoxyCodeLine{00094\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00095\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_CPP14)\ \&\&\ (!defined(\_MSC\_VER)\ ||\ \_MSC\_VER\ >\ 1900)}}
\DoxyCodeLine{00096\ \textcolor{preprocessor}{\#define\ ZMQ\_EXTENDED\_CONSTEXPR}}
\DoxyCodeLine{00097\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00098\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_CPP17)}}
\DoxyCodeLine{00099\ \textcolor{preprocessor}{\#define\ ZMQ\_INLINE\_VAR\ inline}}
\DoxyCodeLine{00100\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{00101\ \textcolor{preprocessor}{\#define\ ZMQ\_INLINE\_VAR}}
\DoxyCodeLine{00102\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00103\ }
\DoxyCodeLine{00104\ \textcolor{preprocessor}{\#include\ <cassert>}}
\DoxyCodeLine{00105\ \textcolor{preprocessor}{\#include\ <cstring>}}
\DoxyCodeLine{00106\ }
\DoxyCodeLine{00107\ \textcolor{preprocessor}{\#include\ <algorithm>}}
\DoxyCodeLine{00108\ \textcolor{preprocessor}{\#include\ <exception>}}
\DoxyCodeLine{00109\ \textcolor{preprocessor}{\#include\ <iomanip>}}
\DoxyCodeLine{00110\ \textcolor{preprocessor}{\#include\ <sstream>}}
\DoxyCodeLine{00111\ \textcolor{preprocessor}{\#include\ <string>}}
\DoxyCodeLine{00112\ \textcolor{preprocessor}{\#include\ <vector>}}
\DoxyCodeLine{00113\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{00114\ \textcolor{preprocessor}{\#include\ <array>}}
\DoxyCodeLine{00115\ \textcolor{preprocessor}{\#include\ <chrono>}}
\DoxyCodeLine{00116\ \textcolor{preprocessor}{\#include\ <tuple>}}
\DoxyCodeLine{00117\ \textcolor{preprocessor}{\#include\ <memory>}}
\DoxyCodeLine{00118\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00119\ }
\DoxyCodeLine{00120\ \textcolor{preprocessor}{\#if\ defined(\_\_has\_include)\ \&\&\ defined(ZMQ\_CPP17)}}
\DoxyCodeLine{00121\ \textcolor{preprocessor}{\#define\ CPPZMQ\_HAS\_INCLUDE\_CPP17(X)\ \_\_has\_include(X)}}
\DoxyCodeLine{00122\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{00123\ \textcolor{preprocessor}{\#define\ CPPZMQ\_HAS\_INCLUDE\_CPP17(X)\ 0}}
\DoxyCodeLine{00124\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00125\ }
\DoxyCodeLine{00126\ \textcolor{preprocessor}{\#if\ CPPZMQ\_HAS\_INCLUDE\_CPP17(<optional>)\ \&\&\ !defined(CPPZMQ\_HAS\_OPTIONAL)}}
\DoxyCodeLine{00127\ \textcolor{preprocessor}{\#define\ CPPZMQ\_HAS\_OPTIONAL\ 1}}
\DoxyCodeLine{00128\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00129\ \textcolor{preprocessor}{\#ifndef\ CPPZMQ\_HAS\_OPTIONAL}}
\DoxyCodeLine{00130\ \textcolor{preprocessor}{\#define\ CPPZMQ\_HAS\_OPTIONAL\ 0}}
\DoxyCodeLine{00131\ \textcolor{preprocessor}{\#elif\ CPPZMQ\_HAS\_OPTIONAL}}
\DoxyCodeLine{00132\ \textcolor{preprocessor}{\#include\ <optional>}}
\DoxyCodeLine{00133\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00134\ }
\DoxyCodeLine{00135\ \textcolor{preprocessor}{\#if\ CPPZMQ\_HAS\_INCLUDE\_CPP17(<string\_view>)\ \&\&\ !defined(CPPZMQ\_HAS\_STRING\_VIEW)}}
\DoxyCodeLine{00136\ \textcolor{preprocessor}{\#define\ CPPZMQ\_HAS\_STRING\_VIEW\ 1}}
\DoxyCodeLine{00137\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00138\ \textcolor{preprocessor}{\#ifndef\ CPPZMQ\_HAS\_STRING\_VIEW}}
\DoxyCodeLine{00139\ \textcolor{preprocessor}{\#define\ CPPZMQ\_HAS\_STRING\_VIEW\ 0}}
\DoxyCodeLine{00140\ \textcolor{preprocessor}{\#elif\ CPPZMQ\_HAS\_STRING\_VIEW}}
\DoxyCodeLine{00141\ \textcolor{preprocessor}{\#include\ <string\_view>}}
\DoxyCodeLine{00142\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00143\ }
\DoxyCodeLine{00144\ \textcolor{comment}{/*\ \ Version\ macros\ for\ compile-\/time\ API\ version\ detection\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ */}}
\DoxyCodeLine{00145\ \textcolor{preprocessor}{\#define\ CPPZMQ\_VERSION\_MAJOR\ 4}}
\DoxyCodeLine{00146\ \textcolor{preprocessor}{\#define\ CPPZMQ\_VERSION\_MINOR\ 7}}
\DoxyCodeLine{00147\ \textcolor{preprocessor}{\#define\ CPPZMQ\_VERSION\_PATCH\ 1}}
\DoxyCodeLine{00148\ }
\DoxyCodeLine{00149\ \textcolor{preprocessor}{\#define\ CPPZMQ\_VERSION\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{00150\ \textcolor{preprocessor}{\ \ \ \ ZMQ\_MAKE\_VERSION(CPPZMQ\_VERSION\_MAJOR,\ CPPZMQ\_VERSION\_MINOR,\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{00151\ \textcolor{preprocessor}{\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ CPPZMQ\_VERSION\_PATCH)}}
\DoxyCodeLine{00152\ }
\DoxyCodeLine{00153\ \textcolor{comment}{//\ \ Detect\ whether\ the\ compiler\ supports\ C++11\ rvalue\ references.}}
\DoxyCodeLine{00154\ \textcolor{preprocessor}{\#if\ (defined(\_\_GNUC\_\_)\ \&\&\ (\_\_GNUC\_\_\ >\ 4\ ||\ (\_\_GNUC\_\_\ ==\ 4\ \&\&\ \_\_GNUC\_MINOR\_\_\ >\ 2))\ \ \ \(\backslash\)}}
\DoxyCodeLine{00155\ \textcolor{preprocessor}{\ \ \ \ \ \&\&\ defined(\_\_GXX\_EXPERIMENTAL\_CXX0X\_\_))}}
\DoxyCodeLine{00156\ \textcolor{preprocessor}{\#define\ ZMQ\_HAS\_RVALUE\_REFS}}
\DoxyCodeLine{00157\ \textcolor{preprocessor}{\#define\ ZMQ\_DELETED\_FUNCTION\ =\ delete}}
\DoxyCodeLine{00158\ \textcolor{preprocessor}{\#elif\ defined(\_\_clang\_\_)}}
\DoxyCodeLine{00159\ \textcolor{preprocessor}{\#if\ \_\_has\_feature(cxx\_rvalue\_references)}}
\DoxyCodeLine{00160\ \textcolor{preprocessor}{\#define\ ZMQ\_HAS\_RVALUE\_REFS}}
\DoxyCodeLine{00161\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00162\ }
\DoxyCodeLine{00163\ \textcolor{preprocessor}{\#if\ \_\_has\_feature(cxx\_deleted\_functions)}}
\DoxyCodeLine{00164\ \textcolor{preprocessor}{\#define\ ZMQ\_DELETED\_FUNCTION\ =\ delete}}
\DoxyCodeLine{00165\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{00166\ \textcolor{preprocessor}{\#define\ ZMQ\_DELETED\_FUNCTION}}
\DoxyCodeLine{00167\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00168\ \textcolor{preprocessor}{\#elif\ defined(\_MSC\_VER)\ \&\&\ (\_MSC\_VER\ >=\ 1900)}}
\DoxyCodeLine{00169\ \textcolor{preprocessor}{\#define\ ZMQ\_HAS\_RVALUE\_REFS}}
\DoxyCodeLine{00170\ \textcolor{preprocessor}{\#define\ ZMQ\_DELETED\_FUNCTION\ =\ delete}}
\DoxyCodeLine{00171\ \textcolor{preprocessor}{\#elif\ defined(\_MSC\_VER)\ \&\&\ (\_MSC\_VER\ >=\ 1600)}}
\DoxyCodeLine{00172\ \textcolor{preprocessor}{\#define\ ZMQ\_HAS\_RVALUE\_REFS}}
\DoxyCodeLine{00173\ \textcolor{preprocessor}{\#define\ ZMQ\_DELETED\_FUNCTION}}
\DoxyCodeLine{00174\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{00175\ \textcolor{preprocessor}{\#define\ ZMQ\_DELETED\_FUNCTION}}
\DoxyCodeLine{00176\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00177\ }
\DoxyCodeLine{00178\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_CPP11)\ \&\&\ !defined(\_\_llvm\_\_)\ \&\&\ !defined(\_\_INTEL\_COMPILER)\ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{00179\ \textcolor{preprocessor}{\ \ \&\&\ defined(\_\_GNUC\_\_)\ \&\&\ \_\_GNUC\_\_\ <\ 5}}
\DoxyCodeLine{00180\ \textcolor{preprocessor}{\#define\ ZMQ\_CPP11\_PARTIAL}}
\DoxyCodeLine{00181\ \textcolor{preprocessor}{\#elif\ defined(\_\_GLIBCXX\_\_)\ \&\&\ \_\_GLIBCXX\_\_\ <\ 20160805}}
\DoxyCodeLine{00182\ \textcolor{comment}{//the\ date\ here\ is\ the\ last\ date\ of\ gcc\ 4.9.4,\ which}}
\DoxyCodeLine{00183\ \textcolor{comment}{//\ effectively\ means\ libstdc++\ from\ gcc\ 5.5\ and\ higher\ won't\ trigger\ this\ branch}}
\DoxyCodeLine{00184\ \textcolor{preprocessor}{\#define\ ZMQ\_CPP11\_PARTIAL}}
\DoxyCodeLine{00185\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00186\ }
\DoxyCodeLine{00187\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{00188\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11\_PARTIAL}}
\DoxyCodeLine{00189\ \textcolor{preprocessor}{\#define\ ZMQ\_IS\_TRIVIALLY\_COPYABLE(T)\ \_\_has\_trivial\_copy(T)}}
\DoxyCodeLine{00190\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{00191\ \textcolor{preprocessor}{\#include\ <type\_traits>}}
\DoxyCodeLine{00192\ \textcolor{preprocessor}{\#define\ ZMQ\_IS\_TRIVIALLY\_COPYABLE(T)\ std::is\_trivially\_copyable<T>::value}}
\DoxyCodeLine{00193\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00194\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00195\ }
\DoxyCodeLine{00196\ \textcolor{preprocessor}{\#if\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(3,\ 3,\ 0)}}
\DoxyCodeLine{00197\ \textcolor{preprocessor}{\#define\ ZMQ\_NEW\_MONITOR\_EVENT\_LAYOUT}}
\DoxyCodeLine{00198\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00199\ }
\DoxyCodeLine{00200\ \textcolor{preprocessor}{\#if\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(4,\ 1,\ 0)}}
\DoxyCodeLine{00201\ \textcolor{preprocessor}{\#define\ ZMQ\_HAS\_PROXY\_STEERABLE}}
\DoxyCodeLine{00202\ \textcolor{comment}{/*\ \ Socket\ event\ data\ \ */}}
\DoxyCodeLine{00203\ \textcolor{keyword}{typedef}\ \textcolor{keyword}{struct}}
\DoxyCodeLine{00204\ \{}
\DoxyCodeLine{00205\ \ \ \ \ uint16\_t\ event;\ \textcolor{comment}{//\ id\ of\ the\ event\ as\ bitfield}}
\DoxyCodeLine{00206\ \ \ \ \ int32\_t\ value;\ \ \textcolor{comment}{//\ value\ is\ either\ error\ code,\ fd\ or\ reconnect\ interval}}
\DoxyCodeLine{00207\ \}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}};}
\DoxyCodeLine{00208\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00209\ }
\DoxyCodeLine{00210\ \textcolor{comment}{//\ Avoid\ using\ deprecated\ message\ receive\ function\ when\ possible}}
\DoxyCodeLine{00211\ \textcolor{preprocessor}{\#if\ ZMQ\_VERSION\ <\ ZMQ\_MAKE\_VERSION(3,\ 2,\ 0)}}
\DoxyCodeLine{00212\ \textcolor{preprocessor}{\#define\ zmq\_msg\_recv(msg,\ socket,\ flags)\ zmq\_recvmsg(socket,\ msg,\ flags)}}
\DoxyCodeLine{00213\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00214\ }
\DoxyCodeLine{00215\ }
\DoxyCodeLine{00216\ \textcolor{comment}{//\ In\ order\ to\ prevent\ unused\ variable\ warnings\ when\ building\ in\ non-\/debug}}
\DoxyCodeLine{00217\ \textcolor{comment}{//\ mode\ use\ this\ macro\ to\ make\ assertions.}}
\DoxyCodeLine{00218\ \textcolor{preprocessor}{\#ifndef\ NDEBUG}}
\DoxyCodeLine{00219\ \textcolor{preprocessor}{\#define\ ZMQ\_ASSERT(expression)\ assert(expression)}}
\DoxyCodeLine{00220\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{00221\ \textcolor{preprocessor}{\#define\ ZMQ\_ASSERT(expression)\ (void)\ (expression)}}
\DoxyCodeLine{00222\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00223\ }
\DoxyCodeLine{00224\ \textcolor{keyword}{namespace\ }zmq}
\DoxyCodeLine{00225\ \{}
\DoxyCodeLine{00226\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{00227\ \textcolor{keyword}{namespace\ }detail}
\DoxyCodeLine{00228\ \{}
\DoxyCodeLine{00229\ \textcolor{keyword}{namespace\ }ranges}
\DoxyCodeLine{00230\ \{}
\DoxyCodeLine{00231\ \textcolor{keyword}{using\ }std::begin;}
\DoxyCodeLine{00232\ \textcolor{keyword}{using\ }std::end;}
\DoxyCodeLine{00233\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{auto}\ begin(T\ \&\&r)\ -\/>\ \textcolor{keyword}{decltype}(begin(std::forward<T>(r)))}
\DoxyCodeLine{00234\ \{}
\DoxyCodeLine{00235\ \ \ \ \ \textcolor{keywordflow}{return}\ begin(std::forward<T>(r));}
\DoxyCodeLine{00236\ \}}
\DoxyCodeLine{00237\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{auto}\ end(T\ \&\&r)\ -\/>\ \textcolor{keyword}{decltype}(end(std::forward<T>(r)))}
\DoxyCodeLine{00238\ \{}
\DoxyCodeLine{00239\ \ \ \ \ \textcolor{keywordflow}{return}\ end(std::forward<T>(r));}
\DoxyCodeLine{00240\ \}}
\DoxyCodeLine{00241\ \}\ \textcolor{comment}{//\ namespace\ ranges}}
\DoxyCodeLine{00242\ }
\DoxyCodeLine{00243\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{using\ }void\_t\ =\ void;}
\DoxyCodeLine{00244\ }
\DoxyCodeLine{00245\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ Iter>}
\DoxyCodeLine{00246\ \textcolor{keyword}{using\ }iter\_value\_t\ =\ \textcolor{keyword}{typename}\ std::iterator\_traits<Iter>::value\_type;}
\DoxyCodeLine{00247\ }
\DoxyCodeLine{00248\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ Range>}
\DoxyCodeLine{00249\ \textcolor{keyword}{using\ }range\_iter\_t\ =\ \textcolor{keyword}{decltype}(}
\DoxyCodeLine{00250\ \ \ ranges::begin(std::declval<\textcolor{keyword}{typename}\ std::remove\_reference<Range>::type\ \&>()));}
\DoxyCodeLine{00251\ }
\DoxyCodeLine{00252\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ Range>\ \textcolor{keyword}{using\ }range\_value\_t\ =\ iter\_value\_t<range\_iter\_t<Range>>;}
\DoxyCodeLine{00253\ }
\DoxyCodeLine{00254\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ =\ \textcolor{keywordtype}{void}>\ \textcolor{keyword}{struct\ }is\_range\ :\ std::false\_type}
\DoxyCodeLine{00255\ \{}
\DoxyCodeLine{00256\ \};}
\DoxyCodeLine{00257\ }
\DoxyCodeLine{00258\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>}
\DoxyCodeLine{00259\ \textcolor{keyword}{struct\ }is\_range<}
\DoxyCodeLine{00260\ \ \ T,}
\DoxyCodeLine{00261\ \ \ void\_t<decltype(}
\DoxyCodeLine{00262\ \ \ \ \ ranges::begin(std::declval<typename\ std::remove\_reference<T>::type\ \&>())}
\DoxyCodeLine{00263\ \ \ \ \ ==\ ranges::end(std::declval<typename\ std::remove\_reference<T>::type\ \&>()))>>}
\DoxyCodeLine{00264\ \ \ \ \ :\ std::true\_type}
\DoxyCodeLine{00265\ \{}
\DoxyCodeLine{00266\ \};}
\DoxyCodeLine{00267\ }
\DoxyCodeLine{00268\ \}\ \textcolor{comment}{//\ namespace\ detail}}
\DoxyCodeLine{00269\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00270\ }
\DoxyCodeLine{00271\ \textcolor{keyword}{typedef}\ zmq\_free\_fn\ free\_fn;}
\DoxyCodeLine{00272\ \textcolor{keyword}{typedef}\ zmq\_pollitem\_t\ pollitem\_t;}
\DoxyCodeLine{00273\ }
\DoxyCodeLine{00274\ \textcolor{keyword}{class\ }error\_t\ :\ \textcolor{keyword}{public}\ std::exception}
\DoxyCodeLine{00275\ \{}
\DoxyCodeLine{00276\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{00277\ \ \ \ \ error\_t()\ ZMQ\_NOTHROW\ :\ errnum(zmq\_errno())\ \{\}}
\DoxyCodeLine{00278\ \ \ \ \ \textcolor{keyword}{explicit}\ error\_t(\textcolor{keywordtype}{int}\ err)\ ZMQ\_NOTHROW\ :\ errnum(err)\ \{\}}
\DoxyCodeLine{00279\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *what()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW\ ZMQ\_OVERRIDE}
\DoxyCodeLine{00280\ \ \ \ \ \{}
\DoxyCodeLine{00281\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ zmq\_strerror(errnum);}
\DoxyCodeLine{00282\ \ \ \ \ \}}
\DoxyCodeLine{00283\ \ \ \ \ \textcolor{keywordtype}{int}\ num()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ errnum;\ \}}
\DoxyCodeLine{00284\ }
\DoxyCodeLine{00285\ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{00286\ \ \ \ \ \textcolor{keywordtype}{int}\ errnum;}
\DoxyCodeLine{00287\ \};}
\DoxyCodeLine{00288\ }
\DoxyCodeLine{00289\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{int}\ poll(zmq\_pollitem\_t\ *items\_,\ \textcolor{keywordtype}{size\_t}\ nitems\_,\ \textcolor{keywordtype}{long}\ timeout\_\ =\ -\/1)}
\DoxyCodeLine{00290\ \{}
\DoxyCodeLine{00291\ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_poll(items\_,\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{int}\textcolor{keyword}{>}(nitems\_),\ timeout\_);}
\DoxyCodeLine{00292\ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ <\ 0)}
\DoxyCodeLine{00293\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00294\ \ \ \ \ \textcolor{keywordflow}{return}\ rc;}
\DoxyCodeLine{00295\ \}}
\DoxyCodeLine{00296\ }
\DoxyCodeLine{00297\ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ poll\ taking\ non-\/const\ items"{}})}
\DoxyCodeLine{00298\ inline\ \textcolor{keywordtype}{int}\ poll(zmq\_pollitem\_t\ const\ *items\_,\ \textcolor{keywordtype}{size\_t}\ nitems\_,\ \textcolor{keywordtype}{long}\ timeout\_\ =\ -\/1)}
\DoxyCodeLine{00299\ \{}
\DoxyCodeLine{00300\ \ \ \ \ \textcolor{keywordflow}{return}\ poll(\textcolor{keyword}{const\_cast<}zmq\_pollitem\_t\ *\textcolor{keyword}{>}(items\_),\ nitems\_,\ timeout\_);}
\DoxyCodeLine{00301\ \}}
\DoxyCodeLine{00302\ }
\DoxyCodeLine{00303\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{00304\ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ poll\ taking\ non-\/const\ items"{}})}
\DoxyCodeLine{00305\ inline\ \textcolor{keywordtype}{int}}
\DoxyCodeLine{00306\ poll(zmq\_pollitem\_t\ const\ *items,\ \textcolor{keywordtype}{size\_t}\ nitems,\ std::chrono::milliseconds\ timeout)}
\DoxyCodeLine{00307\ \{}
\DoxyCodeLine{00308\ \ \ \ \ \textcolor{keywordflow}{return}\ poll(\textcolor{keyword}{const\_cast<}zmq\_pollitem\_t\ *\textcolor{keyword}{>}(items),\ nitems,}
\DoxyCodeLine{00309\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{long}\textcolor{keyword}{>}(timeout.count()));}
\DoxyCodeLine{00310\ \}}
\DoxyCodeLine{00311\ }
\DoxyCodeLine{00312\ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ poll\ taking\ non-\/const\ items"{}})}
\DoxyCodeLine{00313\ inline\ \textcolor{keywordtype}{int}\ poll(std::vector<zmq\_pollitem\_t>\ const\ \&items,}
\DoxyCodeLine{00314\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ std::chrono::milliseconds\ timeout)}
\DoxyCodeLine{00315\ \{}
\DoxyCodeLine{00316\ \ \ \ \ \textcolor{keywordflow}{return}\ poll(\textcolor{keyword}{const\_cast<}zmq\_pollitem\_t\ *\textcolor{keyword}{>}(items.data()),\ items.size(),}
\DoxyCodeLine{00317\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{long}\textcolor{keyword}{>}(timeout.count()));}
\DoxyCodeLine{00318\ \}}
\DoxyCodeLine{00319\ }
\DoxyCodeLine{00320\ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ poll\ taking\ non-\/const\ items"{}})}
\DoxyCodeLine{00321\ inline\ \textcolor{keywordtype}{int}\ poll(std::vector<zmq\_pollitem\_t>\ const\ \&items,\ \textcolor{keywordtype}{long}\ timeout\_\ =\ -\/1)}
\DoxyCodeLine{00322\ \{}
\DoxyCodeLine{00323\ \ \ \ \ \textcolor{keywordflow}{return}\ poll(\textcolor{keyword}{const\_cast<}zmq\_pollitem\_t\ *\textcolor{keyword}{>}(items.data()),\ items.size(),\ timeout\_);}
\DoxyCodeLine{00324\ \}}
\DoxyCodeLine{00325\ }
\DoxyCodeLine{00326\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{int}}
\DoxyCodeLine{00327\ poll(zmq\_pollitem\_t\ *items,\ \textcolor{keywordtype}{size\_t}\ nitems,\ std::chrono::milliseconds\ timeout)}
\DoxyCodeLine{00328\ \{}
\DoxyCodeLine{00329\ \ \ \ \ \textcolor{keywordflow}{return}\ poll(items,\ nitems,\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{long}\textcolor{keyword}{>}(timeout.count()));}
\DoxyCodeLine{00330\ \}}
\DoxyCodeLine{00331\ }
\DoxyCodeLine{00332\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{int}\ poll(std::vector<zmq\_pollitem\_t>\ \&items,}
\DoxyCodeLine{00333\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ std::chrono::milliseconds\ timeout)}
\DoxyCodeLine{00334\ \{}
\DoxyCodeLine{00335\ \ \ \ \ \textcolor{keywordflow}{return}\ poll(items.data(),\ items.size(),\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{long}\textcolor{keyword}{>}(timeout.count()));}
\DoxyCodeLine{00336\ \}}
\DoxyCodeLine{00337\ }
\DoxyCodeLine{00338\ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ poll\ taking\ std::chrono\ instead\ of\ long"{}})}
\DoxyCodeLine{00339\ inline\ \textcolor{keywordtype}{int}\ poll(std::vector<zmq\_pollitem\_t>\ \&items,\ \textcolor{keywordtype}{long}\ timeout\_\ =\ -\/1)}
\DoxyCodeLine{00340\ \{}
\DoxyCodeLine{00341\ \ \ \ \ \textcolor{keywordflow}{return}\ poll(items.data(),\ items.size(),\ timeout\_);}
\DoxyCodeLine{00342\ \}}
\DoxyCodeLine{00343\ }
\DoxyCodeLine{00344\ \textcolor{keyword}{template}<std::\textcolor{keywordtype}{size\_t}\ SIZE>}
\DoxyCodeLine{00345\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{int}\ poll(std::array<zmq\_pollitem\_t,\ SIZE>\ \&items,}
\DoxyCodeLine{00346\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ std::chrono::milliseconds\ timeout)}
\DoxyCodeLine{00347\ \{}
\DoxyCodeLine{00348\ \ \ \ \ \textcolor{keywordflow}{return}\ poll(items.data(),\ items.size(),\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{long}\textcolor{keyword}{>}(timeout.count()));}
\DoxyCodeLine{00349\ \}}
\DoxyCodeLine{00350\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00351\ }
\DoxyCodeLine{00352\ }
\DoxyCodeLine{00353\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{void}\ version(\textcolor{keywordtype}{int}\ *major\_,\ \textcolor{keywordtype}{int}\ *minor\_,\ \textcolor{keywordtype}{int}\ *patch\_)}
\DoxyCodeLine{00354\ \{}
\DoxyCodeLine{00355\ \ \ \ \ zmq\_version(major\_,\ minor\_,\ patch\_);}
\DoxyCodeLine{00356\ \}}
\DoxyCodeLine{00357\ }
\DoxyCodeLine{00358\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{00359\ \textcolor{keyword}{inline}\ std::tuple<int,\ int,\ int>\ version()}
\DoxyCodeLine{00360\ \{}
\DoxyCodeLine{00361\ \ \ \ \ std::tuple<int,\ int,\ int>\ v;}
\DoxyCodeLine{00362\ \ \ \ \ zmq\_version(\&std::get<0>(v),\ \&std::get<1>(v),\ \&std::get<2>(v));}
\DoxyCodeLine{00363\ \ \ \ \ \textcolor{keywordflow}{return}\ v;}
\DoxyCodeLine{00364\ \}}
\DoxyCodeLine{00365\ }
\DoxyCodeLine{00366\ \textcolor{preprocessor}{\#if\ !defined(ZMQ\_CPP11\_PARTIAL)}}
\DoxyCodeLine{00367\ \textcolor{keyword}{namespace\ }detail}
\DoxyCodeLine{00368\ \{}
\DoxyCodeLine{00369\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{struct\ }is\_char\_type}
\DoxyCodeLine{00370\ \{}
\DoxyCodeLine{00371\ \ \ \ \ \textcolor{comment}{//\ true\ if\ character\ type\ for\ string\ literals\ in\ C++11}}
\DoxyCodeLine{00372\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keyword}{constexpr}\ \textcolor{keywordtype}{bool}\ value\ =}
\DoxyCodeLine{00373\ \ \ \ \ \ \ std::is\_same<T,\ char>::value\ ||\ std::is\_same<T,\ wchar\_t>::value}
\DoxyCodeLine{00374\ \ \ \ \ \ \ ||\ std::is\_same<T,\ char16\_t>::value\ ||\ std::is\_same<T,\ char32\_t>::value;}
\DoxyCodeLine{00375\ \};}
\DoxyCodeLine{00376\ \}}
\DoxyCodeLine{00377\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00378\ }
\DoxyCodeLine{00379\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00380\ }
\DoxyCodeLine{00381\ \textcolor{keyword}{class\ }message\_t}
\DoxyCodeLine{00382\ \{}
\DoxyCodeLine{00383\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{00384\ \ \ \ \ message\_t()\ ZMQ\_NOTHROW}
\DoxyCodeLine{00385\ \ \ \ \ \{}
\DoxyCodeLine{00386\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_init(\&msg);}
\DoxyCodeLine{00387\ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{00388\ \ \ \ \ \}}
\DoxyCodeLine{00389\ }
\DoxyCodeLine{00390\ \ \ \ \ \textcolor{keyword}{explicit}\ message\_t(\textcolor{keywordtype}{size\_t}\ size\_)}
\DoxyCodeLine{00391\ \ \ \ \ \{}
\DoxyCodeLine{00392\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_init\_size(\&msg,\ size\_);}
\DoxyCodeLine{00393\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00394\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00395\ \ \ \ \ \}}
\DoxyCodeLine{00396\ }
\DoxyCodeLine{00397\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ ForwardIter>\ message\_t(ForwardIter\ first,\ ForwardIter\ last)}
\DoxyCodeLine{00398\ \ \ \ \ \{}
\DoxyCodeLine{00399\ \ \ \ \ \ \ \ \ \textcolor{keyword}{typedef}\ \textcolor{keyword}{typename}\ std::iterator\_traits<ForwardIter>::value\_type\ value\_t;}
\DoxyCodeLine{00400\ }
\DoxyCodeLine{00401\ \ \ \ \ \ \ \ \ assert(std::distance(first,\ last)\ >=\ 0);}
\DoxyCodeLine{00402\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{size\_t}\ \textcolor{keyword}{const}\ size\_\ =}
\DoxyCodeLine{00403\ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{size\_t}\textcolor{keyword}{>}(std::distance(first,\ last))\ *\ \textcolor{keyword}{sizeof}(value\_t);}
\DoxyCodeLine{00404\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ \textcolor{keyword}{const}\ rc\ =\ zmq\_msg\_init\_size(\&msg,\ size\_);}
\DoxyCodeLine{00405\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00406\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00407\ \ \ \ \ \ \ \ \ std::copy(first,\ last,\ data<value\_t>());}
\DoxyCodeLine{00408\ \ \ \ \ \}}
\DoxyCodeLine{00409\ }
\DoxyCodeLine{00410\ \ \ \ \ message\_t(\textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *data\_,\ \textcolor{keywordtype}{size\_t}\ size\_)}
\DoxyCodeLine{00411\ \ \ \ \ \{}
\DoxyCodeLine{00412\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_init\_size(\&msg,\ size\_);}
\DoxyCodeLine{00413\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00414\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00415\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (size\_)\ \{}
\DoxyCodeLine{00416\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ this\ constructor\ allows\ (nullptr,\ 0),}}
\DoxyCodeLine{00417\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ memcpy\ with\ a\ null\ pointer\ is\ UB}}
\DoxyCodeLine{00418\ \ \ \ \ \ \ \ \ \ \ \ \ memcpy(data(),\ data\_,\ size\_);}
\DoxyCodeLine{00419\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{00420\ \ \ \ \ \}}
\DoxyCodeLine{00421\ }
\DoxyCodeLine{00422\ \ \ \ \ message\_t(\textcolor{keywordtype}{void}\ *data\_,\ \textcolor{keywordtype}{size\_t}\ size\_,\ free\_fn\ *ffn\_,\ \textcolor{keywordtype}{void}\ *hint\_\ =\ ZMQ\_NULLPTR)}
\DoxyCodeLine{00423\ \ \ \ \ \{}
\DoxyCodeLine{00424\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_init\_data(\&msg,\ data\_,\ size\_,\ ffn\_,\ hint\_);}
\DoxyCodeLine{00425\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00426\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00427\ \ \ \ \ \}}
\DoxyCodeLine{00428\ }
\DoxyCodeLine{00429\ \ \ \ \ \textcolor{comment}{//\ overload\ set\ of\ string-\/like\ types\ and\ generic\ containers}}
\DoxyCodeLine{00430\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_CPP11)\ \&\&\ !defined(ZMQ\_CPP11\_PARTIAL)}}
\DoxyCodeLine{00431\ \ \ \ \ \textcolor{comment}{//\ NOTE\ this\ constructor\ will\ include\ the\ null\ terminator}}
\DoxyCodeLine{00432\ \ \ \ \ \textcolor{comment}{//\ when\ called\ with\ a\ string\ literal.}}
\DoxyCodeLine{00433\ \ \ \ \ \textcolor{comment}{//\ An\ overload\ taking\ const\ char*\ can\ not\ be\ added\ because}}
\DoxyCodeLine{00434\ \ \ \ \ \textcolor{comment}{//\ it\ would\ be\ preferred\ over\ this\ function\ and\ break\ compatiblity.}}
\DoxyCodeLine{00435\ \ \ \ \ \textcolor{keyword}{template}<}
\DoxyCodeLine{00436\ \ \ \ \ \ \ \textcolor{keyword}{class\ }Char,}
\DoxyCodeLine{00437\ \ \ \ \ \ \ \textcolor{keywordtype}{size\_t}\ N,}
\DoxyCodeLine{00438\ \ \ \ \ \ \ \textcolor{keyword}{typename}\ =\ \textcolor{keyword}{typename}\ std::enable\_if<detail::is\_char\_type<Char>::value>::type>}
\DoxyCodeLine{00439\ \ \ \ \ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.7.0,\ use\ constructors\ taking\ iterators,\ (pointer,\ size)\ "{}}}
\DoxyCodeLine{00440\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{stringliteral}{"{}or\ strings\ instead"{}})}
\DoxyCodeLine{00441\ \ \ \ \ \textcolor{keyword}{explicit}\ message\_t(\textcolor{keyword}{const}\ Char\ (\&data)[N])\ :}
\DoxyCodeLine{00442\ \ \ \ \ \ \ \ \ message\_t(detail::ranges::begin(data),\ detail::ranges::end(data))}
\DoxyCodeLine{00443\ \ \ \ \ \{}
\DoxyCodeLine{00444\ \ \ \ \ \}}
\DoxyCodeLine{00445\ }
\DoxyCodeLine{00446\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keyword}{class\ }Range,}
\DoxyCodeLine{00447\ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{typename}\ =\ \textcolor{keyword}{typename}\ std::enable\_if<}
\DoxyCodeLine{00448\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ detail::is\_range<Range>::value}
\DoxyCodeLine{00449\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \&\&\ ZMQ\_IS\_TRIVIALLY\_COPYABLE(detail::range\_value\_t<Range>)}
\DoxyCodeLine{00450\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \&\&\ !detail::is\_char\_type<detail::range\_value\_t<Range>>::value}
\DoxyCodeLine{00451\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \&\&\ !std::is\_same<Range,\ message\_t>::value>::type>}
\DoxyCodeLine{00452\ \ \ \ \ \textcolor{keyword}{explicit}\ message\_t(\textcolor{keyword}{const}\ Range\ \&rng)\ :}
\DoxyCodeLine{00453\ \ \ \ \ \ \ \ \ message\_t(detail::ranges::begin(rng),\ detail::ranges::end(rng))}
\DoxyCodeLine{00454\ \ \ \ \ \{}
\DoxyCodeLine{00455\ \ \ \ \ \}}
\DoxyCodeLine{00456\ }
\DoxyCodeLine{00457\ \ \ \ \ \textcolor{keyword}{explicit}\ message\_t(\textcolor{keyword}{const}\ std::string\ \&\mbox{\hyperlink{classzmq_1_1message__t_aed6502a922a73cda7f47d47689617d03}{str}})\ :\ message\_t(\mbox{\hyperlink{classzmq_1_1message__t_aed6502a922a73cda7f47d47689617d03}{str}}.data(),\ \mbox{\hyperlink{classzmq_1_1message__t_aed6502a922a73cda7f47d47689617d03}{str}}.size())\ \{\}}
\DoxyCodeLine{00458\ }
\DoxyCodeLine{00459\ \textcolor{preprocessor}{\#if\ CPPZMQ\_HAS\_STRING\_VIEW}}
\DoxyCodeLine{00460\ \ \ \ \ \textcolor{keyword}{explicit}\ message\_t(std::string\_view\ \mbox{\hyperlink{classzmq_1_1message__t_aed6502a922a73cda7f47d47689617d03}{str}})\ :\ message\_t(\mbox{\hyperlink{classzmq_1_1message__t_aed6502a922a73cda7f47d47689617d03}{str}}.data(),\ \mbox{\hyperlink{classzmq_1_1message__t_aed6502a922a73cda7f47d47689617d03}{str}}.size())\ \{\}}
\DoxyCodeLine{00461\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00462\ }
\DoxyCodeLine{00463\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00464\ }
\DoxyCodeLine{00465\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_HAS\_RVALUE\_REFS}}
\DoxyCodeLine{00466\ \ \ \ \ message\_t(message\_t\ \&\&rhs)\ ZMQ\_NOTHROW\ :\ msg(rhs.msg)}
\DoxyCodeLine{00467\ \ \ \ \ \{}
\DoxyCodeLine{00468\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_init(\&rhs.msg);}
\DoxyCodeLine{00469\ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{00470\ \ \ \ \ \}}
\DoxyCodeLine{00471\ }
\DoxyCodeLine{00472\ \ \ \ \ message\_t\ \&operator=(message\_t\ \&\&rhs)\ ZMQ\_NOTHROW}
\DoxyCodeLine{00473\ \ \ \ \ \{}
\DoxyCodeLine{00474\ \ \ \ \ \ \ \ \ std::swap(msg,\ rhs.msg);}
\DoxyCodeLine{00475\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ *\textcolor{keyword}{this};}
\DoxyCodeLine{00476\ \ \ \ \ \}}
\DoxyCodeLine{00477\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00478\ }
\DoxyCodeLine{00479\ \ \ \ \ \string~message\_t()\ ZMQ\_NOTHROW}
\DoxyCodeLine{00480\ \ \ \ \ \{}
\DoxyCodeLine{00481\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_close(\&msg);}
\DoxyCodeLine{00482\ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{00483\ \ \ \ \ \}}
\DoxyCodeLine{00484\ }
\DoxyCodeLine{00485\ \ \ \ \ \textcolor{keywordtype}{void}\ rebuild()}
\DoxyCodeLine{00486\ \ \ \ \ \{}
\DoxyCodeLine{00487\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_close(\&msg);}
\DoxyCodeLine{00488\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00489\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00490\ \ \ \ \ \ \ \ \ rc\ =\ zmq\_msg\_init(\&msg);}
\DoxyCodeLine{00491\ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{00492\ \ \ \ \ \}}
\DoxyCodeLine{00493\ }
\DoxyCodeLine{00494\ \ \ \ \ \textcolor{keywordtype}{void}\ rebuild(\textcolor{keywordtype}{size\_t}\ size\_)}
\DoxyCodeLine{00495\ \ \ \ \ \{}
\DoxyCodeLine{00496\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_close(\&msg);}
\DoxyCodeLine{00497\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00498\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00499\ \ \ \ \ \ \ \ \ rc\ =\ zmq\_msg\_init\_size(\&msg,\ size\_);}
\DoxyCodeLine{00500\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00501\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00502\ \ \ \ \ \}}
\DoxyCodeLine{00503\ }
\DoxyCodeLine{00504\ \ \ \ \ \textcolor{keywordtype}{void}\ rebuild(\textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *data\_,\ \textcolor{keywordtype}{size\_t}\ size\_)}
\DoxyCodeLine{00505\ \ \ \ \ \{}
\DoxyCodeLine{00506\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_close(\&msg);}
\DoxyCodeLine{00507\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00508\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00509\ \ \ \ \ \ \ \ \ rc\ =\ zmq\_msg\_init\_size(\&msg,\ size\_);}
\DoxyCodeLine{00510\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00511\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00512\ \ \ \ \ \ \ \ \ memcpy(data(),\ data\_,\ size\_);}
\DoxyCodeLine{00513\ \ \ \ \ \}}
\DoxyCodeLine{00514\ }
\DoxyCodeLine{00515\ \ \ \ \ \textcolor{keywordtype}{void}\ rebuild(\textcolor{keywordtype}{void}\ *data\_,\ \textcolor{keywordtype}{size\_t}\ size\_,\ free\_fn\ *ffn\_,\ \textcolor{keywordtype}{void}\ *hint\_\ =\ ZMQ\_NULLPTR)}
\DoxyCodeLine{00516\ \ \ \ \ \{}
\DoxyCodeLine{00517\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_close(\&msg);}
\DoxyCodeLine{00518\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00519\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00520\ \ \ \ \ \ \ \ \ rc\ =\ zmq\_msg\_init\_data(\&msg,\ data\_,\ size\_,\ ffn\_,\ hint\_);}
\DoxyCodeLine{00521\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00522\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00523\ \ \ \ \ \}}
\DoxyCodeLine{00524\ }
\DoxyCodeLine{00525\ \ \ \ \ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ move\ taking\ non-\/const\ reference\ instead"{}})}
\DoxyCodeLine{00526\ \ \ \ \ \textcolor{keywordtype}{void}\ move(message\_t\ \textcolor{keyword}{const}\ *msg\_)}
\DoxyCodeLine{00527\ \ \ \ \ \{}
\DoxyCodeLine{00528\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_move(\&msg,\ \textcolor{keyword}{const\_cast<}zmq\_msg\_t\ *\textcolor{keyword}{>}(msg\_-\/>handle()));}
\DoxyCodeLine{00529\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00530\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00531\ \ \ \ \ \}}
\DoxyCodeLine{00532\ }
\DoxyCodeLine{00533\ \ \ \ \ \textcolor{keywordtype}{void}\ move(message\_t\ \&msg\_)}
\DoxyCodeLine{00534\ \ \ \ \ \{}
\DoxyCodeLine{00535\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_move(\&msg,\ msg\_.handle());}
\DoxyCodeLine{00536\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00537\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00538\ \ \ \ \ \}}
\DoxyCodeLine{00539\ }
\DoxyCodeLine{00540\ \ \ \ \ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ copy\ taking\ non-\/const\ reference\ instead"{}})}
\DoxyCodeLine{00541\ \ \ \ \ \textcolor{keywordtype}{void}\ copy(message\_t\ \textcolor{keyword}{const}\ *msg\_)}
\DoxyCodeLine{00542\ \ \ \ \ \{}
\DoxyCodeLine{00543\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_copy(\&msg,\ \textcolor{keyword}{const\_cast<}zmq\_msg\_t\ *\textcolor{keyword}{>}(msg\_-\/>handle()));}
\DoxyCodeLine{00544\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00545\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00546\ \ \ \ \ \}}
\DoxyCodeLine{00547\ }
\DoxyCodeLine{00548\ \ \ \ \ \textcolor{keywordtype}{void}\ copy(message\_t\ \&msg\_)}
\DoxyCodeLine{00549\ \ \ \ \ \{}
\DoxyCodeLine{00550\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_copy(\&msg,\ msg\_.handle());}
\DoxyCodeLine{00551\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00552\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00553\ \ \ \ \ \}}
\DoxyCodeLine{00554\ }
\DoxyCodeLine{00555\ \ \ \ \ \textcolor{keywordtype}{bool}\ more()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW}
\DoxyCodeLine{00556\ \ \ \ \ \{}
\DoxyCodeLine{00557\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_more(\textcolor{keyword}{const\_cast<}zmq\_msg\_t\ *\textcolor{keyword}{>}(\&msg));}
\DoxyCodeLine{00558\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ rc\ !=\ 0;}
\DoxyCodeLine{00559\ \ \ \ \ \}}
\DoxyCodeLine{00560\ }
\DoxyCodeLine{00561\ \ \ \ \ \textcolor{keywordtype}{void}\ *data()\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ zmq\_msg\_data(\&msg);\ \}}
\DoxyCodeLine{00562\ }
\DoxyCodeLine{00563\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *data()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW}
\DoxyCodeLine{00564\ \ \ \ \ \{}
\DoxyCodeLine{00565\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ zmq\_msg\_data(\textcolor{keyword}{const\_cast<}zmq\_msg\_t\ *\textcolor{keyword}{>}(\&msg));}
\DoxyCodeLine{00566\ \ \ \ \ \}}
\DoxyCodeLine{00567\ }
\DoxyCodeLine{00568\ \ \ \ \ \textcolor{keywordtype}{size\_t}\ size()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW}
\DoxyCodeLine{00569\ \ \ \ \ \{}
\DoxyCodeLine{00570\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ zmq\_msg\_size(\textcolor{keyword}{const\_cast<}zmq\_msg\_t\ *\textcolor{keyword}{>}(\&msg));}
\DoxyCodeLine{00571\ \ \ \ \ \}}
\DoxyCodeLine{00572\ }
\DoxyCodeLine{00573\ \ \ \ \ ZMQ\_NODISCARD\ \textcolor{keywordtype}{bool}\ empty()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ size()\ ==\ 0u;\ \}}
\DoxyCodeLine{00574\ }
\DoxyCodeLine{00575\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keyword}{typename}\ T>\ T\ *data()\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}T\ *\textcolor{keyword}{>}(data());\ \}}
\DoxyCodeLine{00576\ }
\DoxyCodeLine{00577\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keyword}{typename}\ T>\ T\ \textcolor{keyword}{const}\ *data()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW}
\DoxyCodeLine{00578\ \ \ \ \ \{}
\DoxyCodeLine{00579\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}T\ \textcolor{keyword}{const\ }*\textcolor{keyword}{>}(data());}
\DoxyCodeLine{00580\ \ \ \ \ \}}
\DoxyCodeLine{00581\ }
\DoxyCodeLine{00582\ \ \ \ \ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.0,\ use\ operator==\ instead"{}})}
\DoxyCodeLine{00583\ \ \ \ \ \textcolor{keywordtype}{bool}\ equal(\textcolor{keyword}{const}\ message\_t\ *other)\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ *\textcolor{keyword}{this}\ ==\ *other;\ \}}
\DoxyCodeLine{00584\ }
\DoxyCodeLine{00585\ \ \ \ \ \textcolor{keywordtype}{bool}\ operator==(\textcolor{keyword}{const}\ message\_t\ \&other)\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW}
\DoxyCodeLine{00586\ \ \ \ \ \{}
\DoxyCodeLine{00587\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{size\_t}\ my\_size\ =\ size();}
\DoxyCodeLine{00588\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ my\_size\ ==\ other.size()\ \&\&\ 0\ ==\ memcmp(data(),\ other.data(),\ my\_size);}
\DoxyCodeLine{00589\ \ \ \ \ \}}
\DoxyCodeLine{00590\ }
\DoxyCodeLine{00591\ \ \ \ \ \textcolor{keywordtype}{bool}\ operator!=(\textcolor{keyword}{const}\ message\_t\ \&other)\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW}
\DoxyCodeLine{00592\ \ \ \ \ \{}
\DoxyCodeLine{00593\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ !(*\textcolor{keyword}{this}\ ==\ other);}
\DoxyCodeLine{00594\ \ \ \ \ \}}
\DoxyCodeLine{00595\ }
\DoxyCodeLine{00596\ \textcolor{preprocessor}{\#if\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(3,\ 2,\ 0)}}
\DoxyCodeLine{00597\ \ \ \ \ \textcolor{keywordtype}{int}\ get(\textcolor{keywordtype}{int}\ property\_)}
\DoxyCodeLine{00598\ \ \ \ \ \{}
\DoxyCodeLine{00599\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ value\ =\ zmq\_msg\_get(\&msg,\ property\_);}
\DoxyCodeLine{00600\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (value\ ==\ -\/1)}
\DoxyCodeLine{00601\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00602\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ value;}
\DoxyCodeLine{00603\ \ \ \ \ \}}
\DoxyCodeLine{00604\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00605\ }
\DoxyCodeLine{00606\ \textcolor{preprocessor}{\#if\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(4,\ 1,\ 0)}}
\DoxyCodeLine{00607\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *gets(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *property\_)}
\DoxyCodeLine{00608\ \ \ \ \ \{}
\DoxyCodeLine{00609\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *value\ =\ zmq\_msg\_gets(\&msg,\ property\_);}
\DoxyCodeLine{00610\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (value\ ==\ ZMQ\_NULLPTR)}
\DoxyCodeLine{00611\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00612\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ value;}
\DoxyCodeLine{00613\ \ \ \ \ \}}
\DoxyCodeLine{00614\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00615\ }
\DoxyCodeLine{00616\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_BUILD\_DRAFT\_API)\ \&\&\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(4,\ 2,\ 0)}}
\DoxyCodeLine{00617\ \ \ \ \ uint32\_t\ routing\_id()\textcolor{keyword}{\ const}}
\DoxyCodeLine{00618\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{00619\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ zmq\_msg\_routing\_id(\textcolor{keyword}{const\_cast<}zmq\_msg\_t\ *\textcolor{keyword}{>}(\&msg));}
\DoxyCodeLine{00620\ \ \ \ \ \}}
\DoxyCodeLine{00621\ }
\DoxyCodeLine{00622\ \ \ \ \ \textcolor{keywordtype}{void}\ set\_routing\_id(uint32\_t\ routing\_id)}
\DoxyCodeLine{00623\ \ \ \ \ \{}
\DoxyCodeLine{00624\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_set\_routing\_id(\&msg,\ routing\_id);}
\DoxyCodeLine{00625\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00626\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00627\ \ \ \ \ \}}
\DoxyCodeLine{00628\ }
\DoxyCodeLine{00629\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *group()\textcolor{keyword}{\ const}}
\DoxyCodeLine{00630\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{00631\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ zmq\_msg\_group(\textcolor{keyword}{const\_cast<}zmq\_msg\_t\ *\textcolor{keyword}{>}(\&msg));}
\DoxyCodeLine{00632\ \ \ \ \ \}}
\DoxyCodeLine{00633\ }
\DoxyCodeLine{00634\ \ \ \ \ \textcolor{keywordtype}{void}\ set\_group(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *group)}
\DoxyCodeLine{00635\ \ \ \ \ \{}
\DoxyCodeLine{00636\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_set\_group(\&msg,\ group);}
\DoxyCodeLine{00637\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{00638\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00639\ \ \ \ \ \}}
\DoxyCodeLine{00640\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00641\ }
\DoxyCodeLine{00642\ \ \ \ \ \textcolor{comment}{//\ interpret\ message\ content\ as\ a\ string}}
\DoxyCodeLine{00643\ \ \ \ \ std::string\ to\_string()\textcolor{keyword}{\ const}}
\DoxyCodeLine{00644\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{00645\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ std::string(\textcolor{keyword}{static\_cast<}\textcolor{keyword}{const\ }\textcolor{keywordtype}{char}\ *\textcolor{keyword}{>}(data()),\ size());}
\DoxyCodeLine{00646\ \ \ \ \ \}}
\DoxyCodeLine{00647\ \textcolor{preprocessor}{\#if\ CPPZMQ\_HAS\_STRING\_VIEW}}
\DoxyCodeLine{00648\ \ \ \ \ \textcolor{comment}{//\ interpret\ message\ content\ as\ a\ string}}
\DoxyCodeLine{00649\ \ \ \ \ std::string\_view\ to\_string\_view()\ \textcolor{keyword}{const}\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{00650\ \ \ \ \ \{}
\DoxyCodeLine{00651\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ std::string\_view(\textcolor{keyword}{static\_cast<}\textcolor{keyword}{const\ }\textcolor{keywordtype}{char}\ *\textcolor{keyword}{>}(data()),\ size());}
\DoxyCodeLine{00652\ \ \ \ \ \}}
\DoxyCodeLine{00653\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00654\ }
\DoxyCodeLine{00661\ \ \ \ \ std::string\ \mbox{\hyperlink{classzmq_1_1message__t_aed6502a922a73cda7f47d47689617d03}{str}}()\textcolor{keyword}{\ const}}
\DoxyCodeLine{00662\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{00663\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ Partly\ mutuated\ from\ the\ same\ method\ in\ zmq::multipart\_t}}
\DoxyCodeLine{00664\ \ \ \ \ \ \ \ \ std::stringstream\ os;}
\DoxyCodeLine{00665\ }
\DoxyCodeLine{00666\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{char}\ *msg\_data\ =\ this-\/>data<unsigned\ char>();}
\DoxyCodeLine{00667\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{char}\ byte;}
\DoxyCodeLine{00668\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{size\_t}\ size\ =\ this-\/>size();}
\DoxyCodeLine{00669\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ is\_ascii[2]\ =\ \{0,\ 0\};}
\DoxyCodeLine{00670\ }
\DoxyCodeLine{00671\ \ \ \ \ \ \ \ \ os\ <<\ \textcolor{stringliteral}{"{}zmq::message\_t\ [size\ "{}}\ <<\ std::dec\ <<\ std::setw(3)}
\DoxyCodeLine{00672\ \ \ \ \ \ \ \ \ \ \ \ <<\ std::setfill(\textcolor{charliteral}{'0'})\ <<\ size\ <<\ \textcolor{stringliteral}{"{}]\ ("{}};}
\DoxyCodeLine{00673\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ Totally\ arbitrary}}
\DoxyCodeLine{00674\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (size\ >=\ 1000)\ \{}
\DoxyCodeLine{00675\ \ \ \ \ \ \ \ \ \ \ \ \ os\ <<\ \textcolor{stringliteral}{"{}...\ too\ big\ to\ print)"{}};}
\DoxyCodeLine{00676\ \ \ \ \ \ \ \ \ \}\ \textcolor{keywordflow}{else}\ \{}
\DoxyCodeLine{00677\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{while}\ (size-\/-\/)\ \{}
\DoxyCodeLine{00678\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{byte}\ =\ *msg\_data++;}
\DoxyCodeLine{00679\ }
\DoxyCodeLine{00680\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ is\_ascii[1]\ =\ (\textcolor{keywordtype}{byte}\ >=\ 32\ \&\&\ \textcolor{keywordtype}{byte}\ <\ 127);}
\DoxyCodeLine{00681\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (is\_ascii[1]\ !=\ is\_ascii[0])}
\DoxyCodeLine{00682\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ os\ <<\ \textcolor{stringliteral}{"{}\ "{}};\ \textcolor{comment}{//\ Separate\ text/non\ text}}
\DoxyCodeLine{00683\ }
\DoxyCodeLine{00684\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (is\_ascii[1])\ \{}
\DoxyCodeLine{00685\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ os\ <<\ byte;}
\DoxyCodeLine{00686\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \}\ \textcolor{keywordflow}{else}\ \{}
\DoxyCodeLine{00687\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ os\ <<\ std::hex\ <<\ std::uppercase\ <<\ std::setw(2)}
\DoxyCodeLine{00688\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ <<\ std::setfill(\textcolor{charliteral}{'0'})\ <<\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{short}\textcolor{keyword}{>}(byte);}
\DoxyCodeLine{00689\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{00690\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ is\_ascii[0]\ =\ is\_ascii[1];}
\DoxyCodeLine{00691\ \ \ \ \ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{00692\ \ \ \ \ \ \ \ \ \ \ \ \ os\ <<\ \textcolor{stringliteral}{"{})"{}};}
\DoxyCodeLine{00693\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{00694\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ os.str();}
\DoxyCodeLine{00695\ \ \ \ \ \}}
\DoxyCodeLine{00696\ }
\DoxyCodeLine{00697\ \ \ \ \ \textcolor{keywordtype}{void}\ swap(\mbox{\hyperlink{classzmq_1_1message__t}{message\_t}}\ \&other)\ ZMQ\_NOTHROW}
\DoxyCodeLine{00698\ \ \ \ \ \{}
\DoxyCodeLine{00699\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ this\ assumes\ zmq::msg\_t\ from\ libzmq\ is\ trivially\ relocatable}}
\DoxyCodeLine{00700\ \ \ \ \ \ \ \ \ std::swap(msg,\ other.msg);}
\DoxyCodeLine{00701\ \ \ \ \ \}}
\DoxyCodeLine{00702\ }
\DoxyCodeLine{00703\ \ \ \ \ ZMQ\_NODISCARD\ zmq\_msg\_t\ *handle()\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ \&msg;\ \}}
\DoxyCodeLine{00704\ \ \ \ \ ZMQ\_NODISCARD\ \textcolor{keyword}{const}\ zmq\_msg\_t\ *handle()\ const\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ \&msg;\ \}}
\DoxyCodeLine{00705\ }
\DoxyCodeLine{00706\ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{00707\ \ \ \ \ \textcolor{comment}{//\ \ The\ underlying\ message}}
\DoxyCodeLine{00708\ \ \ \ \ zmq\_msg\_t\ msg;}
\DoxyCodeLine{00709\ }
\DoxyCodeLine{00710\ \ \ \ \ \textcolor{comment}{//\ \ Disable\ implicit\ message\ copying,\ so\ that\ users\ won't\ use\ shared}}
\DoxyCodeLine{00711\ \ \ \ \ \textcolor{comment}{//\ \ messages\ (less\ efficient)\ without\ being\ aware\ of\ the\ fact.}}
\DoxyCodeLine{00712\ \ \ \ \ message\_t(\textcolor{keyword}{const}\ message\_t\ \&)\ ZMQ\_DELETED\_FUNCTION;}
\DoxyCodeLine{00713\ \ \ \ \ \textcolor{keywordtype}{void}\ operator=(\textcolor{keyword}{const}\ message\_t\ \&)\ ZMQ\_DELETED\_FUNCTION;}
\DoxyCodeLine{00714\ \};}
\DoxyCodeLine{00715\ }
\DoxyCodeLine{00716\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{void}\ swap(\mbox{\hyperlink{classzmq_1_1message__t}{message\_t}}\ \&a,\ \mbox{\hyperlink{classzmq_1_1message__t}{message\_t}}\ \&b)\ ZMQ\_NOTHROW}
\DoxyCodeLine{00717\ \{}
\DoxyCodeLine{00718\ \ \ \ \ a.swap(b);}
\DoxyCodeLine{00719\ \}}
\DoxyCodeLine{00720\ }
\DoxyCodeLine{00721\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{00722\ \textcolor{keyword}{enum\ class}\ ctxopt}
\DoxyCodeLine{00723\ \{}
\DoxyCodeLine{00724\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_BLOCKY}}
\DoxyCodeLine{00725\ \ \ \ \ blocky\ =\ ZMQ\_BLOCKY,}
\DoxyCodeLine{00726\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00727\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_IO\_THREADS}}
\DoxyCodeLine{00728\ \ \ \ \ io\_threads\ =\ ZMQ\_IO\_THREADS,}
\DoxyCodeLine{00729\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00730\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_THREAD\_SCHED\_POLICY}}
\DoxyCodeLine{00731\ \ \ \ \ thread\_sched\_policy\ =\ ZMQ\_THREAD\_SCHED\_POLICY,}
\DoxyCodeLine{00732\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00733\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_THREAD\_PRIORITY}}
\DoxyCodeLine{00734\ \ \ \ \ thread\_priority\ =\ ZMQ\_THREAD\_PRIORITY,}
\DoxyCodeLine{00735\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00736\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_THREAD\_AFFINITY\_CPU\_ADD}}
\DoxyCodeLine{00737\ \ \ \ \ thread\_affinity\_cpu\_add\ =\ ZMQ\_THREAD\_AFFINITY\_CPU\_ADD,}
\DoxyCodeLine{00738\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00739\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_THREAD\_AFFINITY\_CPU\_REMOVE}}
\DoxyCodeLine{00740\ \ \ \ \ thread\_affinity\_cpu\_remove\ =\ ZMQ\_THREAD\_AFFINITY\_CPU\_REMOVE,}
\DoxyCodeLine{00741\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00742\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_THREAD\_NAME\_PREFIX}}
\DoxyCodeLine{00743\ \ \ \ \ thread\_name\_prefix\ =\ ZMQ\_THREAD\_NAME\_PREFIX,}
\DoxyCodeLine{00744\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00745\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_MAX\_MSGSZ}}
\DoxyCodeLine{00746\ \ \ \ \ max\_msgsz\ =\ ZMQ\_MAX\_MSGSZ,}
\DoxyCodeLine{00747\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00748\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_ZERO\_COPY\_RECV}}
\DoxyCodeLine{00749\ \ \ \ \ zero\_copy\_recv\ =\ ZMQ\_ZERO\_COPY\_RECV,}
\DoxyCodeLine{00750\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00751\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_MAX\_SOCKETS}}
\DoxyCodeLine{00752\ \ \ \ \ max\_sockets\ =\ ZMQ\_MAX\_SOCKETS,}
\DoxyCodeLine{00753\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00754\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_SOCKET\_LIMIT}}
\DoxyCodeLine{00755\ \ \ \ \ socket\_limit\ =\ ZMQ\_SOCKET\_LIMIT,}
\DoxyCodeLine{00756\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00757\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_IPV6}}
\DoxyCodeLine{00758\ \ \ \ \ ipv6\ =\ ZMQ\_IPV6,}
\DoxyCodeLine{00759\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00760\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_MSG\_T\_SIZE}}
\DoxyCodeLine{00761\ \ \ \ \ msg\_t\_size\ =\ ZMQ\_MSG\_T\_SIZE}
\DoxyCodeLine{00762\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00763\ \};}
\DoxyCodeLine{00764\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00765\ }
\DoxyCodeLine{00766\ \textcolor{keyword}{class\ }context\_t}
\DoxyCodeLine{00767\ \{}
\DoxyCodeLine{00768\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{00769\ \ \ \ \ context\_t()}
\DoxyCodeLine{00770\ \ \ \ \ \{}
\DoxyCodeLine{00771\ \ \ \ \ \ \ \ \ ptr\ =\ zmq\_ctx\_new();}
\DoxyCodeLine{00772\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (ptr\ ==\ ZMQ\_NULLPTR)}
\DoxyCodeLine{00773\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00774\ \ \ \ \ \}}
\DoxyCodeLine{00775\ }
\DoxyCodeLine{00776\ }
\DoxyCodeLine{00777\ \ \ \ \ \textcolor{keyword}{explicit}\ context\_t(\textcolor{keywordtype}{int}\ io\_threads\_,\ \textcolor{keywordtype}{int}\ max\_sockets\_\ =\ ZMQ\_MAX\_SOCKETS\_DFLT)}
\DoxyCodeLine{00778\ \ \ \ \ \{}
\DoxyCodeLine{00779\ \ \ \ \ \ \ \ \ ptr\ =\ zmq\_ctx\_new();}
\DoxyCodeLine{00780\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (ptr\ ==\ ZMQ\_NULLPTR)}
\DoxyCodeLine{00781\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00782\ }
\DoxyCodeLine{00783\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_ctx\_set(ptr,\ ZMQ\_IO\_THREADS,\ io\_threads\_);}
\DoxyCodeLine{00784\ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{00785\ }
\DoxyCodeLine{00786\ \ \ \ \ \ \ \ \ rc\ =\ zmq\_ctx\_set(ptr,\ ZMQ\_MAX\_SOCKETS,\ max\_sockets\_);}
\DoxyCodeLine{00787\ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{00788\ \ \ \ \ \}}
\DoxyCodeLine{00789\ }
\DoxyCodeLine{00790\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_HAS\_RVALUE\_REFS}}
\DoxyCodeLine{00791\ \ \ \ \ context\_t(context\_t\ \&\&rhs)\ ZMQ\_NOTHROW\ :\ ptr(rhs.ptr)\ \{\ rhs.ptr\ =\ ZMQ\_NULLPTR;\ \}}
\DoxyCodeLine{00792\ \ \ \ \ context\_t\ \&operator=(context\_t\ \&\&rhs)\ ZMQ\_NOTHROW}
\DoxyCodeLine{00793\ \ \ \ \ \{}
\DoxyCodeLine{00794\ \ \ \ \ \ \ \ \ close();}
\DoxyCodeLine{00795\ \ \ \ \ \ \ \ \ std::swap(ptr,\ rhs.ptr);}
\DoxyCodeLine{00796\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ *\textcolor{keyword}{this};}
\DoxyCodeLine{00797\ \ \ \ \ \}}
\DoxyCodeLine{00798\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00799\ }
\DoxyCodeLine{00800\ \ \ \ \ \string~context\_t()\ ZMQ\_NOTHROW\ \{\ close();\ \}}
\DoxyCodeLine{00801\ }
\DoxyCodeLine{00802\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.7.0,\ use\ set\ taking\ zmq::ctxopt\ instead"{}})}
\DoxyCodeLine{00803\ \ \ \ \ \textcolor{keywordtype}{int}\ setctxopt(\textcolor{keywordtype}{int}\ option\_,\ \textcolor{keywordtype}{int}\ optval\_)}
\DoxyCodeLine{00804\ \ \ \ \ \{}
\DoxyCodeLine{00805\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_ctx\_set(ptr,\ option\_,\ optval\_);}
\DoxyCodeLine{00806\ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{00807\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ rc;}
\DoxyCodeLine{00808\ \ \ \ \ \}}
\DoxyCodeLine{00809\ }
\DoxyCodeLine{00810\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.7.0,\ use\ get\ taking\ zmq::ctxopt\ instead"{}})}
\DoxyCodeLine{00811\ \ \ \ \ \textcolor{keywordtype}{int}\ getctxopt(\textcolor{keywordtype}{int}\ option\_)\ \{\ \textcolor{keywordflow}{return}\ zmq\_ctx\_get(ptr,\ option\_);\ \}}
\DoxyCodeLine{00812\ }
\DoxyCodeLine{00813\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{00814\ \ \ \ \ \textcolor{keywordtype}{void}\ set(ctxopt\ option,\ \textcolor{keywordtype}{int}\ optval)}
\DoxyCodeLine{00815\ \ \ \ \ \{}
\DoxyCodeLine{00816\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_ctx\_set(ptr,\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{int}\textcolor{keyword}{>}(option),\ optval);}
\DoxyCodeLine{00817\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ ==\ -\/1)}
\DoxyCodeLine{00818\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00819\ \ \ \ \ \}}
\DoxyCodeLine{00820\ }
\DoxyCodeLine{00821\ \ \ \ \ ZMQ\_NODISCARD\ \textcolor{keywordtype}{int}\ get(ctxopt\ option)}
\DoxyCodeLine{00822\ \ \ \ \ \{}
\DoxyCodeLine{00823\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_ctx\_get(ptr,\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{int}\textcolor{keyword}{>}(option));}
\DoxyCodeLine{00824\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ some\ options\ have\ a\ default\ value\ of\ -\/1}}
\DoxyCodeLine{00825\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ which\ is\ unfortunate,\ and\ may\ result\ in\ errors}}
\DoxyCodeLine{00826\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ that\ don't\ make\ sense}}
\DoxyCodeLine{00827\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ ==\ -\/1)}
\DoxyCodeLine{00828\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{00829\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ rc;}
\DoxyCodeLine{00830\ \ \ \ \ \}}
\DoxyCodeLine{00831\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00832\ }
\DoxyCodeLine{00833\ \ \ \ \ \textcolor{comment}{//\ Terminates\ context\ (see\ also\ shutdown()).}}
\DoxyCodeLine{00834\ \ \ \ \ \textcolor{keywordtype}{void}\ close()\ ZMQ\_NOTHROW}
\DoxyCodeLine{00835\ \ \ \ \ \{}
\DoxyCodeLine{00836\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (ptr\ ==\ ZMQ\_NULLPTR)}
\DoxyCodeLine{00837\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return};}
\DoxyCodeLine{00838\ }
\DoxyCodeLine{00839\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc;}
\DoxyCodeLine{00840\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{do}\ \{}
\DoxyCodeLine{00841\ \ \ \ \ \ \ \ \ \ \ \ \ rc\ =\ zmq\_ctx\_destroy(ptr);}
\DoxyCodeLine{00842\ \ \ \ \ \ \ \ \ \}\ \textcolor{keywordflow}{while}\ (rc\ ==\ -\/1\ \&\&\ errno\ ==\ EINTR);}
\DoxyCodeLine{00843\ }
\DoxyCodeLine{00844\ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{00845\ \ \ \ \ \ \ \ \ ptr\ =\ ZMQ\_NULLPTR;}
\DoxyCodeLine{00846\ \ \ \ \ \}}
\DoxyCodeLine{00847\ }
\DoxyCodeLine{00848\ \ \ \ \ \textcolor{comment}{//\ Shutdown\ context\ in\ preparation\ for\ termination\ (close()).}}
\DoxyCodeLine{00849\ \ \ \ \ \textcolor{comment}{//\ Causes\ all\ blocking\ socket\ operations\ and\ any\ further}}
\DoxyCodeLine{00850\ \ \ \ \ \textcolor{comment}{//\ socket\ operations\ to\ return\ with\ ETERM.}}
\DoxyCodeLine{00851\ \ \ \ \ \textcolor{keywordtype}{void}\ shutdown()\ ZMQ\_NOTHROW}
\DoxyCodeLine{00852\ \ \ \ \ \{}
\DoxyCodeLine{00853\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (ptr\ ==\ ZMQ\_NULLPTR)}
\DoxyCodeLine{00854\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return};}
\DoxyCodeLine{00855\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_ctx\_shutdown(ptr);}
\DoxyCodeLine{00856\ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{00857\ \ \ \ \ \}}
\DoxyCodeLine{00858\ }
\DoxyCodeLine{00859\ \ \ \ \ \textcolor{comment}{//\ \ Be\ careful\ with\ this,\ it's\ probably\ only\ useful\ for}}
\DoxyCodeLine{00860\ \ \ \ \ \textcolor{comment}{//\ \ using\ the\ C\ api\ together\ with\ an\ existing\ C++\ api.}}
\DoxyCodeLine{00861\ \ \ \ \ \textcolor{comment}{//\ \ Normally\ you\ should\ never\ need\ to\ use\ this.}}
\DoxyCodeLine{00862\ \ \ \ \ ZMQ\_EXPLICIT\ \textcolor{keyword}{operator}\ \textcolor{keywordtype}{void}\ *()\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ ptr;\ \}}
\DoxyCodeLine{00863\ }
\DoxyCodeLine{00864\ \ \ \ \ ZMQ\_EXPLICIT\ \textcolor{keyword}{operator}\ \textcolor{keywordtype}{void}\ \textcolor{keyword}{const}\ *()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ ptr;\ \}}
\DoxyCodeLine{00865\ }
\DoxyCodeLine{00866\ \ \ \ \ ZMQ\_NODISCARD\ \textcolor{keywordtype}{void}\ *handle()\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ ptr;\ \}}
\DoxyCodeLine{00867\ }
\DoxyCodeLine{00868\ \ \ \ \ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.7.0,\ use\ handle()\ !=\ nullptr\ instead"{}})}
\DoxyCodeLine{00869\ \ \ \ \ \textcolor{keyword}{operator}\ bool()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ ptr\ !=\ ZMQ\_NULLPTR;\ \}}
\DoxyCodeLine{00870\ }
\DoxyCodeLine{00871\ \ \ \ \ \textcolor{keywordtype}{void}\ swap(context\_t\ \&other)\ ZMQ\_NOTHROW\ \{\ std::swap(ptr,\ other.ptr);\ \}}
\DoxyCodeLine{00872\ }
\DoxyCodeLine{00873\ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{00874\ \ \ \ \ \textcolor{keywordtype}{void}\ *ptr;}
\DoxyCodeLine{00875\ }
\DoxyCodeLine{00876\ \ \ \ \ context\_t(\textcolor{keyword}{const}\ context\_t\ \&)\ ZMQ\_DELETED\_FUNCTION;}
\DoxyCodeLine{00877\ \ \ \ \ \textcolor{keywordtype}{void}\ operator=(\textcolor{keyword}{const}\ context\_t\ \&)\ ZMQ\_DELETED\_FUNCTION;}
\DoxyCodeLine{00878\ \};}
\DoxyCodeLine{00879\ }
\DoxyCodeLine{00880\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{void}\ swap(\mbox{\hyperlink{classzmq_1_1context__t}{context\_t}}\ \&a,\ \mbox{\hyperlink{classzmq_1_1context__t}{context\_t}}\ \&b)\ ZMQ\_NOTHROW}
\DoxyCodeLine{00881\ \{}
\DoxyCodeLine{00882\ \ \ \ \ a.swap(b);}
\DoxyCodeLine{00883\ \}}
\DoxyCodeLine{00884\ }
\DoxyCodeLine{00885\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{00886\ }
\DoxyCodeLine{00887\ \textcolor{keyword}{struct\ }recv\_buffer\_size}
\DoxyCodeLine{00888\ \{}
\DoxyCodeLine{00889\ \ \ \ \ \textcolor{keywordtype}{size\_t}\ size;\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ number\ of\ bytes\ written\ to\ buffer}}
\DoxyCodeLine{00890\ \ \ \ \ \textcolor{keywordtype}{size\_t}\ untruncated\_size;\ \textcolor{comment}{//\ untruncated\ message\ size\ in\ bytes}}
\DoxyCodeLine{00891\ }
\DoxyCodeLine{00892\ \ \ \ \ ZMQ\_NODISCARD\ \textcolor{keywordtype}{bool}\ truncated()\ const\ noexcept}
\DoxyCodeLine{00893\ \ \ \ \ \{}
\DoxyCodeLine{00894\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ size\ !=\ untruncated\_size;}
\DoxyCodeLine{00895\ \ \ \ \ \}}
\DoxyCodeLine{00896\ \};}
\DoxyCodeLine{00897\ }
\DoxyCodeLine{00898\ \textcolor{preprocessor}{\#if\ CPPZMQ\_HAS\_OPTIONAL}}
\DoxyCodeLine{00899\ }
\DoxyCodeLine{00900\ \textcolor{keyword}{using\ }send\_result\_t\ =\ std::optional<size\_t>;}
\DoxyCodeLine{00901\ \textcolor{keyword}{using\ }recv\_result\_t\ =\ std::optional<size\_t>;}
\DoxyCodeLine{00902\ \textcolor{keyword}{using\ }recv\_buffer\_result\_t\ =\ std::optional<recv\_buffer\_size>;}
\DoxyCodeLine{00903\ }
\DoxyCodeLine{00904\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{00905\ }
\DoxyCodeLine{00906\ \textcolor{keyword}{namespace\ }detail}
\DoxyCodeLine{00907\ \{}
\DoxyCodeLine{00908\ \textcolor{comment}{//\ A\ C++11\ type\ emulating\ the\ most\ basic}}
\DoxyCodeLine{00909\ \textcolor{comment}{//\ operations\ of\ std::optional\ for\ trivial\ types}}
\DoxyCodeLine{00910\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{class\ }trivial\_optional}
\DoxyCodeLine{00911\ \{}
\DoxyCodeLine{00912\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{00913\ \ \ \ \ \textcolor{keyword}{static\_assert}(std::is\_trivial<T>::value,\ \textcolor{stringliteral}{"{}T\ must\ be\ trivial"{}});}
\DoxyCodeLine{00914\ \ \ \ \ \textcolor{keyword}{using\ }value\_type\ =\ T;}
\DoxyCodeLine{00915\ }
\DoxyCodeLine{00916\ \ \ \ \ trivial\_optional()\ =\ \textcolor{keywordflow}{default};}
\DoxyCodeLine{00917\ \ \ \ \ trivial\_optional(T\ value)\ noexcept\ :\ \_value(value),\ \_has\_value(\textcolor{keyword}{true})\ \{\}}
\DoxyCodeLine{00918\ }
\DoxyCodeLine{00919\ \ \ \ \ \textcolor{keyword}{const}\ T\ *operator-\/>()\ const\ noexcept}
\DoxyCodeLine{00920\ \ \ \ \ \{}
\DoxyCodeLine{00921\ \ \ \ \ \ \ \ \ assert(\_has\_value);}
\DoxyCodeLine{00922\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \&\_value;}
\DoxyCodeLine{00923\ \ \ \ \ \}}
\DoxyCodeLine{00924\ \ \ \ \ T\ *operator-\/>()\ noexcept}
\DoxyCodeLine{00925\ \ \ \ \ \{}
\DoxyCodeLine{00926\ \ \ \ \ \ \ \ \ assert(\_has\_value);}
\DoxyCodeLine{00927\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \&\_value;}
\DoxyCodeLine{00928\ \ \ \ \ \}}
\DoxyCodeLine{00929\ }
\DoxyCodeLine{00930\ \ \ \ \ \textcolor{keyword}{const}\ T\ \&operator*()\ const\ noexcept}
\DoxyCodeLine{00931\ \ \ \ \ \{}
\DoxyCodeLine{00932\ \ \ \ \ \ \ \ \ assert(\_has\_value);}
\DoxyCodeLine{00933\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \_value;}
\DoxyCodeLine{00934\ \ \ \ \ \}}
\DoxyCodeLine{00935\ \ \ \ \ T\ \&operator*()\ noexcept}
\DoxyCodeLine{00936\ \ \ \ \ \{}
\DoxyCodeLine{00937\ \ \ \ \ \ \ \ \ assert(\_has\_value);}
\DoxyCodeLine{00938\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \_value;}
\DoxyCodeLine{00939\ \ \ \ \ \}}
\DoxyCodeLine{00940\ }
\DoxyCodeLine{00941\ \ \ \ \ T\ \&value()}
\DoxyCodeLine{00942\ \ \ \ \ \{}
\DoxyCodeLine{00943\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (!\_has\_value)}
\DoxyCodeLine{00944\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ std::exception();}
\DoxyCodeLine{00945\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \_value;}
\DoxyCodeLine{00946\ \ \ \ \ \}}
\DoxyCodeLine{00947\ \ \ \ \ \textcolor{keyword}{const}\ T\ \&value()\textcolor{keyword}{\ const}}
\DoxyCodeLine{00948\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{00949\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (!\_has\_value)}
\DoxyCodeLine{00950\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ std::exception();}
\DoxyCodeLine{00951\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \_value;}
\DoxyCodeLine{00952\ \ \ \ \ \}}
\DoxyCodeLine{00953\ }
\DoxyCodeLine{00954\ \ \ \ \ \textcolor{keyword}{explicit}\ \textcolor{keyword}{operator}\ bool()\ const\ noexcept\ \{\ \textcolor{keywordflow}{return}\ \_has\_value;\ \}}
\DoxyCodeLine{00955\ \ \ \ \ \textcolor{keywordtype}{bool}\ has\_value()\ const\ noexcept\ \{\ \textcolor{keywordflow}{return}\ \_has\_value;\ \}}
\DoxyCodeLine{00956\ }
\DoxyCodeLine{00957\ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{00958\ \ \ \ \ T\ \_value\{\};}
\DoxyCodeLine{00959\ \ \ \ \ \textcolor{keywordtype}{bool}\ \_has\_value\{\textcolor{keyword}{false}\};}
\DoxyCodeLine{00960\ \};}
\DoxyCodeLine{00961\ \}\ \textcolor{comment}{//\ namespace\ detail}}
\DoxyCodeLine{00962\ }
\DoxyCodeLine{00963\ \textcolor{keyword}{using\ }send\_result\_t\ =\ detail::trivial\_optional<size\_t>;}
\DoxyCodeLine{00964\ \textcolor{keyword}{using\ }recv\_result\_t\ =\ detail::trivial\_optional<size\_t>;}
\DoxyCodeLine{00965\ \textcolor{keyword}{using\ }recv\_buffer\_result\_t\ =\ detail::trivial\_optional<recv\_buffer\_size>;}
\DoxyCodeLine{00966\ }
\DoxyCodeLine{00967\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{00968\ }
\DoxyCodeLine{00969\ \textcolor{keyword}{namespace\ }detail}
\DoxyCodeLine{00970\ \{}
\DoxyCodeLine{00971\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{constexpr}\ T\ enum\_bit\_or(T\ a,\ T\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{00972\ \{}
\DoxyCodeLine{00973\ \ \ \ \ \textcolor{keyword}{static\_assert}(std::is\_enum<T>::value,\ \textcolor{stringliteral}{"{}must\ be\ enum"{}});}
\DoxyCodeLine{00974\ \ \ \ \ \textcolor{keyword}{using\ }U\ =\ \textcolor{keyword}{typename}\ std::underlying\_type<T>::type;}
\DoxyCodeLine{00975\ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}T\textcolor{keyword}{>}(\textcolor{keyword}{static\_cast<}U\textcolor{keyword}{>}(a)\ |\ \textcolor{keyword}{static\_cast<}U\textcolor{keyword}{>}(b));}
\DoxyCodeLine{00976\ \}}
\DoxyCodeLine{00977\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{constexpr}\ T\ enum\_bit\_and(T\ a,\ T\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{00978\ \{}
\DoxyCodeLine{00979\ \ \ \ \ \textcolor{keyword}{static\_assert}(std::is\_enum<T>::value,\ \textcolor{stringliteral}{"{}must\ be\ enum"{}});}
\DoxyCodeLine{00980\ \ \ \ \ \textcolor{keyword}{using\ }U\ =\ \textcolor{keyword}{typename}\ std::underlying\_type<T>::type;}
\DoxyCodeLine{00981\ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}T\textcolor{keyword}{>}(\textcolor{keyword}{static\_cast<}U\textcolor{keyword}{>}(a)\ \&\ \textcolor{keyword}{static\_cast<}U\textcolor{keyword}{>}(b));}
\DoxyCodeLine{00982\ \}}
\DoxyCodeLine{00983\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{constexpr}\ T\ enum\_bit\_xor(T\ a,\ T\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{00984\ \{}
\DoxyCodeLine{00985\ \ \ \ \ \textcolor{keyword}{static\_assert}(std::is\_enum<T>::value,\ \textcolor{stringliteral}{"{}must\ be\ enum"{}});}
\DoxyCodeLine{00986\ \ \ \ \ \textcolor{keyword}{using\ }U\ =\ \textcolor{keyword}{typename}\ std::underlying\_type<T>::type;}
\DoxyCodeLine{00987\ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}T\textcolor{keyword}{>}(\textcolor{keyword}{static\_cast<}U\textcolor{keyword}{>}(a)\ \string^\ \textcolor{keyword}{static\_cast<}U\textcolor{keyword}{>}(b));}
\DoxyCodeLine{00988\ \}}
\DoxyCodeLine{00989\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{constexpr}\ T\ enum\_bit\_not(T\ a)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{00990\ \{}
\DoxyCodeLine{00991\ \ \ \ \ \textcolor{keyword}{static\_assert}(std::is\_enum<T>::value,\ \textcolor{stringliteral}{"{}must\ be\ enum"{}});}
\DoxyCodeLine{00992\ \ \ \ \ \textcolor{keyword}{using\ }U\ =\ \textcolor{keyword}{typename}\ std::underlying\_type<T>::type;}
\DoxyCodeLine{00993\ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}T\textcolor{keyword}{>}(\string~static\_cast<U>(a));}
\DoxyCodeLine{00994\ \}}
\DoxyCodeLine{00995\ \}\ \textcolor{comment}{//\ namespace\ detail}}
\DoxyCodeLine{00996\ }
\DoxyCodeLine{00997\ \textcolor{comment}{//\ partially\ satisfies\ named\ requirement\ BitmaskType}}
\DoxyCodeLine{00998\ \textcolor{keyword}{enum\ class}\ send\_flags\ :\ \textcolor{keywordtype}{int}}
\DoxyCodeLine{00999\ \{}
\DoxyCodeLine{01000\ \ \ \ \ none\ =\ 0,}
\DoxyCodeLine{01001\ \ \ \ \ dontwait\ =\ ZMQ\_DONTWAIT,}
\DoxyCodeLine{01002\ \ \ \ \ sndmore\ =\ ZMQ\_SNDMORE}
\DoxyCodeLine{01003\ \};}
\DoxyCodeLine{01004\ }
\DoxyCodeLine{01005\ \textcolor{keyword}{constexpr}\ send\_flags\ operator|(send\_flags\ a,\ send\_flags\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01006\ \{}
\DoxyCodeLine{01007\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_or(a,\ b);}
\DoxyCodeLine{01008\ \}}
\DoxyCodeLine{01009\ \textcolor{keyword}{constexpr}\ send\_flags\ operator\&(send\_flags\ a,\ send\_flags\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01010\ \{}
\DoxyCodeLine{01011\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_and(a,\ b);}
\DoxyCodeLine{01012\ \}}
\DoxyCodeLine{01013\ \textcolor{keyword}{constexpr}\ send\_flags\ operator\string^(send\_flags\ a,\ send\_flags\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01014\ \{}
\DoxyCodeLine{01015\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_xor(a,\ b);}
\DoxyCodeLine{01016\ \}}
\DoxyCodeLine{01017\ \textcolor{keyword}{constexpr}\ send\_flags\ operator\string~(send\_flags\ a)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01018\ \{}
\DoxyCodeLine{01019\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_not(a);}
\DoxyCodeLine{01020\ \}}
\DoxyCodeLine{01021\ }
\DoxyCodeLine{01022\ \textcolor{comment}{//\ partially\ satisfies\ named\ requirement\ BitmaskType}}
\DoxyCodeLine{01023\ \textcolor{keyword}{enum\ class}\ recv\_flags\ :\ \textcolor{keywordtype}{int}}
\DoxyCodeLine{01024\ \{}
\DoxyCodeLine{01025\ \ \ \ \ none\ =\ 0,}
\DoxyCodeLine{01026\ \ \ \ \ dontwait\ =\ ZMQ\_DONTWAIT}
\DoxyCodeLine{01027\ \};}
\DoxyCodeLine{01028\ }
\DoxyCodeLine{01029\ \textcolor{keyword}{constexpr}\ recv\_flags\ operator|(recv\_flags\ a,\ recv\_flags\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01030\ \{}
\DoxyCodeLine{01031\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_or(a,\ b);}
\DoxyCodeLine{01032\ \}}
\DoxyCodeLine{01033\ \textcolor{keyword}{constexpr}\ recv\_flags\ operator\&(recv\_flags\ a,\ recv\_flags\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01034\ \{}
\DoxyCodeLine{01035\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_and(a,\ b);}
\DoxyCodeLine{01036\ \}}
\DoxyCodeLine{01037\ \textcolor{keyword}{constexpr}\ recv\_flags\ operator\string^(recv\_flags\ a,\ recv\_flags\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01038\ \{}
\DoxyCodeLine{01039\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_xor(a,\ b);}
\DoxyCodeLine{01040\ \}}
\DoxyCodeLine{01041\ \textcolor{keyword}{constexpr}\ recv\_flags\ operator\string~(recv\_flags\ a)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01042\ \{}
\DoxyCodeLine{01043\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_not(a);}
\DoxyCodeLine{01044\ \}}
\DoxyCodeLine{01045\ }
\DoxyCodeLine{01046\ }
\DoxyCodeLine{01047\ \textcolor{comment}{//\ mutable\_buffer,\ const\_buffer\ and\ buffer\ are\ based\ on}}
\DoxyCodeLine{01048\ \textcolor{comment}{//\ the\ Networking\ TS\ specification,\ draft:}}
\DoxyCodeLine{01049\ \textcolor{comment}{//\ http://www.open-\/std.org/jtc1/sc22/wg21/docs/papers/2018/n4771.pdf}}
\DoxyCodeLine{01050\ }
\DoxyCodeLine{01051\ \textcolor{keyword}{class\ }mutable\_buffer}
\DoxyCodeLine{01052\ \{}
\DoxyCodeLine{01053\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{01054\ \ \ \ \ \textcolor{keyword}{constexpr}\ mutable\_buffer()\ noexcept\ :\ \_data(\textcolor{keywordtype}{nullptr}),\ \_size(0)\ \{\}}
\DoxyCodeLine{01055\ \ \ \ \ \textcolor{keyword}{constexpr}\ mutable\_buffer(\textcolor{keywordtype}{void}\ *p,\ \textcolor{keywordtype}{size\_t}\ n)\ noexcept\ :\ \_data(p),\ \_size(n)}
\DoxyCodeLine{01056\ \ \ \ \ \{}
\DoxyCodeLine{01057\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_EXTENDED\_CONSTEXPR}}
\DoxyCodeLine{01058\ \ \ \ \ \ \ \ \ assert(p\ !=\ \textcolor{keyword}{nullptr}\ ||\ n\ ==\ 0);}
\DoxyCodeLine{01059\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01060\ \ \ \ \ \}}
\DoxyCodeLine{01061\ }
\DoxyCodeLine{01062\ \ \ \ \ \textcolor{keyword}{constexpr}\ \textcolor{keywordtype}{void}\ *data()\ const\ noexcept\ \{\ \textcolor{keywordflow}{return}\ \_data;\ \}}
\DoxyCodeLine{01063\ \ \ \ \ \textcolor{keyword}{constexpr}\ \textcolor{keywordtype}{size\_t}\ size()\ const\ noexcept\ \{\ \textcolor{keywordflow}{return}\ \_size;\ \}}
\DoxyCodeLine{01064\ \ \ \ \ mutable\_buffer\ \&operator+=(\textcolor{keywordtype}{size\_t}\ n)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01065\ \ \ \ \ \{}
\DoxyCodeLine{01066\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ (std::min)\ is\ a\ workaround\ for\ when\ a\ min\ macro\ is\ defined}}
\DoxyCodeLine{01067\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keyword}{auto}\ shift\ =\ (std::min)(n,\ \_size);}
\DoxyCodeLine{01068\ \ \ \ \ \ \ \ \ \_data\ =\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{char}\ *\textcolor{keyword}{>}(\_data)\ +\ shift;}
\DoxyCodeLine{01069\ \ \ \ \ \ \ \ \ \_size\ -\/=\ shift;}
\DoxyCodeLine{01070\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ *\textcolor{keyword}{this};}
\DoxyCodeLine{01071\ \ \ \ \ \}}
\DoxyCodeLine{01072\ }
\DoxyCodeLine{01073\ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{01074\ \ \ \ \ \textcolor{keywordtype}{void}\ *\_data;}
\DoxyCodeLine{01075\ \ \ \ \ \textcolor{keywordtype}{size\_t}\ \_size;}
\DoxyCodeLine{01076\ \};}
\DoxyCodeLine{01077\ }
\DoxyCodeLine{01078\ \textcolor{keyword}{inline}\ mutable\_buffer\ operator+(\textcolor{keyword}{const}\ mutable\_buffer\ \&mb,\ \textcolor{keywordtype}{size\_t}\ n)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01079\ \{}
\DoxyCodeLine{01080\ \ \ \ \ \textcolor{keywordflow}{return}\ mutable\_buffer(\textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{char}\ *\textcolor{keyword}{>}(mb.data())\ +\ (std::min)(n,\ mb.size()),}
\DoxyCodeLine{01081\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ mb.size()\ -\/\ (std::min)(n,\ mb.size()));}
\DoxyCodeLine{01082\ \}}
\DoxyCodeLine{01083\ \textcolor{keyword}{inline}\ mutable\_buffer\ operator+(\textcolor{keywordtype}{size\_t}\ n,\ \textcolor{keyword}{const}\ mutable\_buffer\ \&mb)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01084\ \{}
\DoxyCodeLine{01085\ \ \ \ \ \textcolor{keywordflow}{return}\ mb\ +\ n;}
\DoxyCodeLine{01086\ \}}
\DoxyCodeLine{01087\ }
\DoxyCodeLine{01088\ \textcolor{keyword}{class\ }const\_buffer}
\DoxyCodeLine{01089\ \{}
\DoxyCodeLine{01090\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{01091\ \ \ \ \ \textcolor{keyword}{constexpr}\ const\_buffer()\ noexcept\ :\ \_data(\textcolor{keywordtype}{nullptr}),\ \_size(0)\ \{\}}
\DoxyCodeLine{01092\ \ \ \ \ \textcolor{keyword}{constexpr}\ const\_buffer(\textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *p,\ \textcolor{keywordtype}{size\_t}\ n)\ noexcept\ :\ \_data(p),\ \_size(n)}
\DoxyCodeLine{01093\ \ \ \ \ \{}
\DoxyCodeLine{01094\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_EXTENDED\_CONSTEXPR}}
\DoxyCodeLine{01095\ \ \ \ \ \ \ \ \ assert(p\ !=\ \textcolor{keyword}{nullptr}\ ||\ n\ ==\ 0);}
\DoxyCodeLine{01096\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01097\ \ \ \ \ \}}
\DoxyCodeLine{01098\ \ \ \ \ \textcolor{keyword}{constexpr}\ const\_buffer(\textcolor{keyword}{const}\ mutable\_buffer\ \&mb)\ noexcept\ :}
\DoxyCodeLine{01099\ \ \ \ \ \ \ \ \ \_data(mb.data()),}
\DoxyCodeLine{01100\ \ \ \ \ \ \ \ \ \_size(mb.size())}
\DoxyCodeLine{01101\ \ \ \ \ \{}
\DoxyCodeLine{01102\ \ \ \ \ \}}
\DoxyCodeLine{01103\ }
\DoxyCodeLine{01104\ \ \ \ \ \textcolor{keyword}{constexpr}\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *data()\ const\ noexcept\ \{\ \textcolor{keywordflow}{return}\ \_data;\ \}}
\DoxyCodeLine{01105\ \ \ \ \ \textcolor{keyword}{constexpr}\ \textcolor{keywordtype}{size\_t}\ size()\ const\ noexcept\ \{\ \textcolor{keywordflow}{return}\ \_size;\ \}}
\DoxyCodeLine{01106\ \ \ \ \ const\_buffer\ \&operator+=(\textcolor{keywordtype}{size\_t}\ n)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01107\ \ \ \ \ \{}
\DoxyCodeLine{01108\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keyword}{auto}\ shift\ =\ (std::min)(n,\ \_size);}
\DoxyCodeLine{01109\ \ \ \ \ \ \ \ \ \_data\ =\ \textcolor{keyword}{static\_cast<}\textcolor{keyword}{const\ }\textcolor{keywordtype}{char}\ *\textcolor{keyword}{>}(\_data)\ +\ shift;}
\DoxyCodeLine{01110\ \ \ \ \ \ \ \ \ \_size\ -\/=\ shift;}
\DoxyCodeLine{01111\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ *\textcolor{keyword}{this};}
\DoxyCodeLine{01112\ \ \ \ \ \}}
\DoxyCodeLine{01113\ }
\DoxyCodeLine{01114\ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{01115\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *\_data;}
\DoxyCodeLine{01116\ \ \ \ \ \textcolor{keywordtype}{size\_t}\ \_size;}
\DoxyCodeLine{01117\ \};}
\DoxyCodeLine{01118\ }
\DoxyCodeLine{01119\ \textcolor{keyword}{inline}\ const\_buffer\ operator+(\textcolor{keyword}{const}\ const\_buffer\ \&cb,\ \textcolor{keywordtype}{size\_t}\ n)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01120\ \{}
\DoxyCodeLine{01121\ \ \ \ \ \textcolor{keywordflow}{return}\ const\_buffer(\textcolor{keyword}{static\_cast<}\textcolor{keyword}{const\ }\textcolor{keywordtype}{char}\ *\textcolor{keyword}{>}(cb.data())}
\DoxyCodeLine{01122\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ +\ (std::min)(n,\ cb.size()),}
\DoxyCodeLine{01123\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ cb.size()\ -\/\ (std::min)(n,\ cb.size()));}
\DoxyCodeLine{01124\ \}}
\DoxyCodeLine{01125\ \textcolor{keyword}{inline}\ const\_buffer\ operator+(\textcolor{keywordtype}{size\_t}\ n,\ \textcolor{keyword}{const}\ const\_buffer\ \&cb)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01126\ \{}
\DoxyCodeLine{01127\ \ \ \ \ \textcolor{keywordflow}{return}\ cb\ +\ n;}
\DoxyCodeLine{01128\ \}}
\DoxyCodeLine{01129\ }
\DoxyCodeLine{01130\ \textcolor{comment}{//\ buffer\ creation}}
\DoxyCodeLine{01131\ }
\DoxyCodeLine{01132\ \textcolor{keyword}{constexpr}\ mutable\_buffer\ buffer(\textcolor{keywordtype}{void}\ *p,\ \textcolor{keywordtype}{size\_t}\ n)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01133\ \{}
\DoxyCodeLine{01134\ \ \ \ \ \textcolor{keywordflow}{return}\ mutable\_buffer(p,\ n);}
\DoxyCodeLine{01135\ \}}
\DoxyCodeLine{01136\ \textcolor{keyword}{constexpr}\ const\_buffer\ buffer(\textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *p,\ \textcolor{keywordtype}{size\_t}\ n)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01137\ \{}
\DoxyCodeLine{01138\ \ \ \ \ \textcolor{keywordflow}{return}\ const\_buffer(p,\ n);}
\DoxyCodeLine{01139\ \}}
\DoxyCodeLine{01140\ \textcolor{keyword}{constexpr}\ mutable\_buffer\ buffer(\textcolor{keyword}{const}\ mutable\_buffer\ \&mb)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01141\ \{}
\DoxyCodeLine{01142\ \ \ \ \ \textcolor{keywordflow}{return}\ mb;}
\DoxyCodeLine{01143\ \}}
\DoxyCodeLine{01144\ \textcolor{keyword}{inline}\ mutable\_buffer\ buffer(\textcolor{keyword}{const}\ mutable\_buffer\ \&mb,\ \textcolor{keywordtype}{size\_t}\ n)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01145\ \{}
\DoxyCodeLine{01146\ \ \ \ \ \textcolor{keywordflow}{return}\ mutable\_buffer(mb.data(),\ (std::min)(mb.size(),\ n));}
\DoxyCodeLine{01147\ \}}
\DoxyCodeLine{01148\ \textcolor{keyword}{constexpr}\ const\_buffer\ buffer(\textcolor{keyword}{const}\ const\_buffer\ \&cb)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01149\ \{}
\DoxyCodeLine{01150\ \ \ \ \ \textcolor{keywordflow}{return}\ cb;}
\DoxyCodeLine{01151\ \}}
\DoxyCodeLine{01152\ \textcolor{keyword}{inline}\ const\_buffer\ buffer(\textcolor{keyword}{const}\ const\_buffer\ \&cb,\ \textcolor{keywordtype}{size\_t}\ n)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01153\ \{}
\DoxyCodeLine{01154\ \ \ \ \ \textcolor{keywordflow}{return}\ const\_buffer(cb.data(),\ (std::min)(cb.size(),\ n));}
\DoxyCodeLine{01155\ \}}
\DoxyCodeLine{01156\ }
\DoxyCodeLine{01157\ \textcolor{keyword}{namespace\ }detail}
\DoxyCodeLine{01158\ \{}
\DoxyCodeLine{01159\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{struct\ }is\_buffer}
\DoxyCodeLine{01160\ \{}
\DoxyCodeLine{01161\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keyword}{constexpr}\ \textcolor{keywordtype}{bool}\ value\ =}
\DoxyCodeLine{01162\ \ \ \ \ \ \ std::is\_same<T,\ const\_buffer>::value\ ||\ std::is\_same<T,\ mutable\_buffer>::value;}
\DoxyCodeLine{01163\ \};}
\DoxyCodeLine{01164\ }
\DoxyCodeLine{01165\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T>\ \textcolor{keyword}{struct\ }is\_pod\_like}
\DoxyCodeLine{01166\ \{}
\DoxyCodeLine{01167\ \ \ \ \ \textcolor{comment}{//\ NOTE:\ The\ networking\ draft\ N4771\ section\ 16.11\ requires}}
\DoxyCodeLine{01168\ \ \ \ \ \textcolor{comment}{//\ T\ in\ the\ buffer\ functions\ below\ to\ be}}
\DoxyCodeLine{01169\ \ \ \ \ \textcolor{comment}{//\ trivially\ copyable\ OR\ standard\ layout.}}
\DoxyCodeLine{01170\ \ \ \ \ \textcolor{comment}{//\ Here\ we\ decide\ to\ be\ conservative\ and\ require\ both.}}
\DoxyCodeLine{01171\ \ \ \ \ \textcolor{keyword}{static}\ \textcolor{keyword}{constexpr}\ \textcolor{keywordtype}{bool}\ value\ =}
\DoxyCodeLine{01172\ \ \ \ \ \ \ ZMQ\_IS\_TRIVIALLY\_COPYABLE(T)\ \&\&\ std::is\_standard\_layout<T>::value;}
\DoxyCodeLine{01173\ \};}
\DoxyCodeLine{01174\ }
\DoxyCodeLine{01175\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ C>\ \textcolor{keyword}{constexpr}\ \textcolor{keyword}{auto}\ seq\_size(\textcolor{keyword}{const}\ C\ \&c)\ \textcolor{keyword}{noexcept}\ -\/>\ \textcolor{keyword}{decltype}(c.size())}
\DoxyCodeLine{01176\ \{}
\DoxyCodeLine{01177\ \ \ \ \ \textcolor{keywordflow}{return}\ c.size();}
\DoxyCodeLine{01178\ \}}
\DoxyCodeLine{01179\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>}
\DoxyCodeLine{01180\ \textcolor{keyword}{constexpr}\ \textcolor{keywordtype}{size\_t}\ seq\_size(\textcolor{keyword}{const}\ T\ (\&\textcolor{comment}{/*array*/})[N])\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01181\ \{}
\DoxyCodeLine{01182\ \ \ \ \ \textcolor{keywordflow}{return}\ N;}
\DoxyCodeLine{01183\ \}}
\DoxyCodeLine{01184\ }
\DoxyCodeLine{01185\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ Seq>}
\DoxyCodeLine{01186\ \textcolor{keyword}{auto}\ buffer\_contiguous\_sequence(Seq\ \&\&seq)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01187\ \ \ -\/>\ \textcolor{keyword}{decltype}(buffer(std::addressof(*std::begin(seq)),\ \textcolor{keywordtype}{size\_t}\{\}))}
\DoxyCodeLine{01188\ \{}
\DoxyCodeLine{01189\ \ \ \ \ \textcolor{keyword}{using\ }T\ =\ \textcolor{keyword}{typename}\ std::remove\_cv<}
\DoxyCodeLine{01190\ \ \ \ \ \ \ \textcolor{keyword}{typename}\ std::remove\_reference<\textcolor{keyword}{decltype}(*std::begin(seq))>::type>::type;}
\DoxyCodeLine{01191\ \ \ \ \ \textcolor{keyword}{static\_assert}(detail::is\_pod\_like<T>::value,\ \textcolor{stringliteral}{"{}T\ must\ be\ POD"{}});}
\DoxyCodeLine{01192\ }
\DoxyCodeLine{01193\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keyword}{auto}\ size\ =\ seq\_size(seq);}
\DoxyCodeLine{01194\ \ \ \ \ \textcolor{keywordflow}{return}\ buffer(size\ !=\ 0u\ ?\ std::addressof(*std::begin(seq))\ :\ \textcolor{keyword}{nullptr},}
\DoxyCodeLine{01195\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ size\ *\ \textcolor{keyword}{sizeof}(T));}
\DoxyCodeLine{01196\ \}}
\DoxyCodeLine{01197\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ Seq>}
\DoxyCodeLine{01198\ \textcolor{keyword}{auto}\ buffer\_contiguous\_sequence(Seq\ \&\&seq,\ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01199\ \ \ -\/>\ \textcolor{keyword}{decltype}(buffer\_contiguous\_sequence(seq))}
\DoxyCodeLine{01200\ \{}
\DoxyCodeLine{01201\ \ \ \ \ \textcolor{keyword}{using\ }T\ =\ \textcolor{keyword}{typename}\ std::remove\_cv<}
\DoxyCodeLine{01202\ \ \ \ \ \ \ \textcolor{keyword}{typename}\ std::remove\_reference<\textcolor{keyword}{decltype}(*std::begin(seq))>::type>::type;}
\DoxyCodeLine{01203\ \ \ \ \ \textcolor{keyword}{static\_assert}(detail::is\_pod\_like<T>::value,\ \textcolor{stringliteral}{"{}T\ must\ be\ POD"{}});}
\DoxyCodeLine{01204\ }
\DoxyCodeLine{01205\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keyword}{auto}\ size\ =\ seq\_size(seq);}
\DoxyCodeLine{01206\ \ \ \ \ \textcolor{keywordflow}{return}\ buffer(size\ !=\ 0u\ ?\ std::addressof(*std::begin(seq))\ :\ \textcolor{keyword}{nullptr},}
\DoxyCodeLine{01207\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ (std::min)(size\ *\ \textcolor{keyword}{sizeof}(T),\ n\_bytes));}
\DoxyCodeLine{01208\ \}}
\DoxyCodeLine{01209\ }
\DoxyCodeLine{01210\ \}\ \textcolor{comment}{//\ namespace\ detail}}
\DoxyCodeLine{01211\ }
\DoxyCodeLine{01212\ \textcolor{comment}{//\ C\ array}}
\DoxyCodeLine{01213\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>\ mutable\_buffer\ buffer(T\ (\&data)[N])\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01214\ \{}
\DoxyCodeLine{01215\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data);}
\DoxyCodeLine{01216\ \}}
\DoxyCodeLine{01217\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>}
\DoxyCodeLine{01218\ mutable\_buffer\ buffer(T\ (\&data)[N],\ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01219\ \{}
\DoxyCodeLine{01220\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data,\ n\_bytes);}
\DoxyCodeLine{01221\ \}}
\DoxyCodeLine{01222\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>\ const\_buffer\ buffer(\textcolor{keyword}{const}\ T\ (\&data)[N])\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01223\ \{}
\DoxyCodeLine{01224\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data);}
\DoxyCodeLine{01225\ \}}
\DoxyCodeLine{01226\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>}
\DoxyCodeLine{01227\ const\_buffer\ buffer(\textcolor{keyword}{const}\ T\ (\&data)[N],\ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01228\ \{}
\DoxyCodeLine{01229\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data,\ n\_bytes);}
\DoxyCodeLine{01230\ \}}
\DoxyCodeLine{01231\ \textcolor{comment}{//\ std::array}}
\DoxyCodeLine{01232\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>\ mutable\_buffer\ buffer(std::array<T,\ N>\ \&data)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01233\ \{}
\DoxyCodeLine{01234\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data);}
\DoxyCodeLine{01235\ \}}
\DoxyCodeLine{01236\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>}
\DoxyCodeLine{01237\ mutable\_buffer\ buffer(std::array<T,\ N>\ \&data,\ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01238\ \{}
\DoxyCodeLine{01239\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data,\ n\_bytes);}
\DoxyCodeLine{01240\ \}}
\DoxyCodeLine{01241\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>}
\DoxyCodeLine{01242\ const\_buffer\ buffer(std::array<const\ T,\ N>\ \&data)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01243\ \{}
\DoxyCodeLine{01244\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data);}
\DoxyCodeLine{01245\ \}}
\DoxyCodeLine{01246\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>}
\DoxyCodeLine{01247\ const\_buffer\ buffer(std::array<const\ T,\ N>\ \&data,\ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01248\ \{}
\DoxyCodeLine{01249\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data,\ n\_bytes);}
\DoxyCodeLine{01250\ \}}
\DoxyCodeLine{01251\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>}
\DoxyCodeLine{01252\ const\_buffer\ buffer(\textcolor{keyword}{const}\ std::array<T,\ N>\ \&data)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01253\ \{}
\DoxyCodeLine{01254\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data);}
\DoxyCodeLine{01255\ \}}
\DoxyCodeLine{01256\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{size\_t}\ N>}
\DoxyCodeLine{01257\ const\_buffer\ buffer(\textcolor{keyword}{const}\ std::array<T,\ N>\ \&data,\ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01258\ \{}
\DoxyCodeLine{01259\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data,\ n\_bytes);}
\DoxyCodeLine{01260\ \}}
\DoxyCodeLine{01261\ \textcolor{comment}{//\ std::vector}}
\DoxyCodeLine{01262\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ Allocator>}
\DoxyCodeLine{01263\ mutable\_buffer\ buffer(std::vector<T,\ Allocator>\ \&data)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01264\ \{}
\DoxyCodeLine{01265\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data);}
\DoxyCodeLine{01266\ \}}
\DoxyCodeLine{01267\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ Allocator>}
\DoxyCodeLine{01268\ mutable\_buffer\ buffer(std::vector<T,\ Allocator>\ \&data,\ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01269\ \{}
\DoxyCodeLine{01270\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data,\ n\_bytes);}
\DoxyCodeLine{01271\ \}}
\DoxyCodeLine{01272\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ Allocator>}
\DoxyCodeLine{01273\ const\_buffer\ buffer(\textcolor{keyword}{const}\ std::vector<T,\ Allocator>\ \&data)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01274\ \{}
\DoxyCodeLine{01275\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data);}
\DoxyCodeLine{01276\ \}}
\DoxyCodeLine{01277\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ Allocator>}
\DoxyCodeLine{01278\ const\_buffer\ buffer(\textcolor{keyword}{const}\ std::vector<T,\ Allocator>\ \&data,\ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01279\ \{}
\DoxyCodeLine{01280\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data,\ n\_bytes);}
\DoxyCodeLine{01281\ \}}
\DoxyCodeLine{01282\ \textcolor{comment}{//\ std::basic\_string}}
\DoxyCodeLine{01283\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ Traits,\ \textcolor{keyword}{class}\ Allocator>}
\DoxyCodeLine{01284\ mutable\_buffer\ buffer(std::basic\_string<T,\ Traits,\ Allocator>\ \&data)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01285\ \{}
\DoxyCodeLine{01286\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data);}
\DoxyCodeLine{01287\ \}}
\DoxyCodeLine{01288\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ Traits,\ \textcolor{keyword}{class}\ Allocator>}
\DoxyCodeLine{01289\ mutable\_buffer\ buffer(std::basic\_string<T,\ Traits,\ Allocator>\ \&data,}
\DoxyCodeLine{01290\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01291\ \{}
\DoxyCodeLine{01292\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data,\ n\_bytes);}
\DoxyCodeLine{01293\ \}}
\DoxyCodeLine{01294\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ Traits,\ \textcolor{keyword}{class}\ Allocator>}
\DoxyCodeLine{01295\ const\_buffer\ buffer(\textcolor{keyword}{const}\ std::basic\_string<T,\ Traits,\ Allocator>\ \&data)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01296\ \{}
\DoxyCodeLine{01297\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data);}
\DoxyCodeLine{01298\ \}}
\DoxyCodeLine{01299\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ Traits,\ \textcolor{keyword}{class}\ Allocator>}
\DoxyCodeLine{01300\ const\_buffer\ buffer(\textcolor{keyword}{const}\ std::basic\_string<T,\ Traits,\ Allocator>\ \&data,}
\DoxyCodeLine{01301\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01302\ \{}
\DoxyCodeLine{01303\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data,\ n\_bytes);}
\DoxyCodeLine{01304\ \}}
\DoxyCodeLine{01305\ }
\DoxyCodeLine{01306\ \textcolor{preprocessor}{\#if\ CPPZMQ\_HAS\_STRING\_VIEW}}
\DoxyCodeLine{01307\ \textcolor{comment}{//\ std::basic\_string\_view}}
\DoxyCodeLine{01308\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ Traits>}
\DoxyCodeLine{01309\ const\_buffer\ buffer(std::basic\_string\_view<T,\ Traits>\ data)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01310\ \{}
\DoxyCodeLine{01311\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data);}
\DoxyCodeLine{01312\ \}}
\DoxyCodeLine{01313\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T,\ \textcolor{keyword}{class}\ Traits>}
\DoxyCodeLine{01314\ const\_buffer\ buffer(std::basic\_string\_view<T,\ Traits>\ data,\ \textcolor{keywordtype}{size\_t}\ n\_bytes)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01315\ \{}
\DoxyCodeLine{01316\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::buffer\_contiguous\_sequence(data,\ n\_bytes);}
\DoxyCodeLine{01317\ \}}
\DoxyCodeLine{01318\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01319\ }
\DoxyCodeLine{01320\ \textcolor{comment}{//\ Buffer\ for\ a\ string\ literal\ (null\ terminated)}}
\DoxyCodeLine{01321\ \textcolor{comment}{//\ where\ the\ buffer\ size\ excludes\ the\ terminating\ character.}}
\DoxyCodeLine{01322\ \textcolor{comment}{//\ Equivalent\ to\ zmq::buffer(std::string\_view("{}..."{})).}}
\DoxyCodeLine{01323\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ Char,\ \textcolor{keywordtype}{size\_t}\ N>}
\DoxyCodeLine{01324\ \textcolor{keyword}{constexpr}\ const\_buffer\ str\_buffer(\textcolor{keyword}{const}\ Char\ (\&data)[N])\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01325\ \{}
\DoxyCodeLine{01326\ \ \ \ \ \textcolor{keyword}{static\_assert}(detail::is\_pod\_like<Char>::value,\ \textcolor{stringliteral}{"{}Char\ must\ be\ POD"{}});}
\DoxyCodeLine{01327\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_EXTENDED\_CONSTEXPR}}
\DoxyCodeLine{01328\ \ \ \ \ assert(data[N\ -\/\ 1]\ ==\ Char\{0\});}
\DoxyCodeLine{01329\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01330\ \ \ \ \ \textcolor{keywordflow}{return}\ const\_buffer(\textcolor{keyword}{static\_cast<}\textcolor{keyword}{const\ }Char\ *\textcolor{keyword}{>}(data),\ (N\ -\/\ 1)\ *\ \textcolor{keyword}{sizeof}(Char));}
\DoxyCodeLine{01331\ \}}
\DoxyCodeLine{01332\ }
\DoxyCodeLine{01333\ \textcolor{keyword}{namespace\ }literals}
\DoxyCodeLine{01334\ \{}
\DoxyCodeLine{01335\ \textcolor{keyword}{constexpr}\ const\_buffer\ \textcolor{keyword}{operator}\textcolor{stringliteral}{"{}"{}}\ \_zbuf(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *str,\ \textcolor{keywordtype}{size\_t}\ len)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01336\ \{}
\DoxyCodeLine{01337\ \ \ \ \ \textcolor{keywordflow}{return}\ const\_buffer(str,\ len\ *\ \textcolor{keyword}{sizeof}(\textcolor{keywordtype}{char}));}
\DoxyCodeLine{01338\ \}}
\DoxyCodeLine{01339\ \textcolor{keyword}{constexpr}\ const\_buffer\ \textcolor{keyword}{operator}\textcolor{stringliteral}{"{}"{}}\ \_zbuf(\textcolor{keyword}{const}\ \textcolor{keywordtype}{wchar\_t}\ *str,\ \textcolor{keywordtype}{size\_t}\ len)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01340\ \{}
\DoxyCodeLine{01341\ \ \ \ \ \textcolor{keywordflow}{return}\ const\_buffer(str,\ len\ *\ \textcolor{keyword}{sizeof}(\textcolor{keywordtype}{wchar\_t}));}
\DoxyCodeLine{01342\ \}}
\DoxyCodeLine{01343\ \textcolor{keyword}{constexpr}\ const\_buffer\ \textcolor{keyword}{operator}\textcolor{stringliteral}{"{}"{}}\ \_zbuf(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char16\_t}\ *str,\ \textcolor{keywordtype}{size\_t}\ len)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01344\ \{}
\DoxyCodeLine{01345\ \ \ \ \ \textcolor{keywordflow}{return}\ const\_buffer(str,\ len\ *\ \textcolor{keyword}{sizeof}(\textcolor{keywordtype}{char16\_t}));}
\DoxyCodeLine{01346\ \}}
\DoxyCodeLine{01347\ \textcolor{keyword}{constexpr}\ const\_buffer\ \textcolor{keyword}{operator}\textcolor{stringliteral}{"{}"{}}\ \_zbuf(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char32\_t}\ *str,\ \textcolor{keywordtype}{size\_t}\ len)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{01348\ \{}
\DoxyCodeLine{01349\ \ \ \ \ \textcolor{keywordflow}{return}\ const\_buffer(str,\ len\ *\ \textcolor{keyword}{sizeof}(\textcolor{keywordtype}{char32\_t}));}
\DoxyCodeLine{01350\ \}}
\DoxyCodeLine{01351\ \}}
\DoxyCodeLine{01352\ }
\DoxyCodeLine{01353\ \textcolor{preprocessor}{\#endif\ }\textcolor{comment}{//\ ZMQ\_CPP11}}
\DoxyCodeLine{01354\ }
\DoxyCodeLine{01355\ }
\DoxyCodeLine{01356\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{01357\ \textcolor{keyword}{namespace\ }sockopt}
\DoxyCodeLine{01358\ \{}
\DoxyCodeLine{01359\ \textcolor{comment}{//\ There\ are\ two\ types\ of\ options,}}
\DoxyCodeLine{01360\ \textcolor{comment}{//\ integral\ type\ with\ known\ compiler\ time\ size\ (int,\ bool,\ int64\_t,\ uint64\_t)}}
\DoxyCodeLine{01361\ \textcolor{comment}{//\ and\ arrays\ with\ dynamic\ size\ (strings,\ binary\ data).}}
\DoxyCodeLine{01362\ }
\DoxyCodeLine{01363\ \textcolor{comment}{//\ BoolUnit:\ if\ true\ accepts\ values\ of\ type\ bool\ (but\ passed\ as\ T\ into\ libzmq)}}
\DoxyCodeLine{01364\ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{bool}\ BoolUnit\ =\ false>\ \textcolor{keyword}{struct\ }integral\_option}
\DoxyCodeLine{01365\ \{}
\DoxyCodeLine{01366\ \};}
\DoxyCodeLine{01367\ }
\DoxyCodeLine{01368\ \textcolor{comment}{//\ NullTerm:}}
\DoxyCodeLine{01369\ \textcolor{comment}{//\ 0:\ binary\ data}}
\DoxyCodeLine{01370\ \textcolor{comment}{//\ 1:\ null-\/terminated\ string\ (`getsockopt`\ size\ includes\ null)}}
\DoxyCodeLine{01371\ \textcolor{comment}{//\ 2:\ binary\ (size\ 32)\ or\ Z85\ encoder\ string\ of\ size\ 41\ (null\ included)}}
\DoxyCodeLine{01372\ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keywordtype}{int}\ NullTerm\ =\ 1>\ \textcolor{keyword}{struct\ }array\_option}
\DoxyCodeLine{01373\ \{}
\DoxyCodeLine{01374\ \};}
\DoxyCodeLine{01375\ }
\DoxyCodeLine{01376\ \textcolor{preprocessor}{\#define\ ZMQ\_DEFINE\_INTEGRAL\_OPT(OPT,\ NAME,\ TYPE)\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{01377\ \textcolor{preprocessor}{\ \ \ \ using\ NAME\#\#\_t\ =\ integral\_option<OPT,\ TYPE,\ false>;\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{01378\ \textcolor{preprocessor}{\ \ \ \ ZMQ\_INLINE\_VAR\ ZMQ\_CONSTEXPR\_VAR\ NAME\#\#\_t\ NAME\ \{\}}}
\DoxyCodeLine{01379\ \textcolor{preprocessor}{\#define\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(OPT,\ NAME,\ TYPE)\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{01380\ \textcolor{preprocessor}{\ \ \ \ using\ NAME\#\#\_t\ =\ integral\_option<OPT,\ TYPE,\ true>;\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{01381\ \textcolor{preprocessor}{\ \ \ \ ZMQ\_INLINE\_VAR\ ZMQ\_CONSTEXPR\_VAR\ NAME\#\#\_t\ NAME\ \{\}}}
\DoxyCodeLine{01382\ \textcolor{preprocessor}{\#define\ ZMQ\_DEFINE\_ARRAY\_OPT(OPT,\ NAME)\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{01383\ \textcolor{preprocessor}{\ \ \ \ using\ NAME\#\#\_t\ =\ array\_option<OPT>;\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{01384\ \textcolor{preprocessor}{\ \ \ \ ZMQ\_INLINE\_VAR\ ZMQ\_CONSTEXPR\_VAR\ NAME\#\#\_t\ NAME\ \{\}}}
\DoxyCodeLine{01385\ \textcolor{preprocessor}{\#define\ ZMQ\_DEFINE\_ARRAY\_OPT\_BINARY(OPT,\ NAME)\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{01386\ \textcolor{preprocessor}{\ \ \ \ using\ NAME\#\#\_t\ =\ array\_option<OPT,\ 0>;\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{01387\ \textcolor{preprocessor}{\ \ \ \ ZMQ\_INLINE\_VAR\ ZMQ\_CONSTEXPR\_VAR\ NAME\#\#\_t\ NAME\ \{\}}}
\DoxyCodeLine{01388\ \textcolor{preprocessor}{\#define\ ZMQ\_DEFINE\_ARRAY\_OPT\_BIN\_OR\_Z85(OPT,\ NAME)\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{01389\ \textcolor{preprocessor}{\ \ \ \ using\ NAME\#\#\_t\ =\ array\_option<OPT,\ 2>;\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \(\backslash\)}}
\DoxyCodeLine{01390\ \textcolor{preprocessor}{\ \ \ \ ZMQ\_INLINE\_VAR\ ZMQ\_CONSTEXPR\_VAR\ NAME\#\#\_t\ NAME\ \{\}}}
\DoxyCodeLine{01391\ }
\DoxyCodeLine{01392\ \textcolor{comment}{//\ duplicate\ definition\ from\ libzmq\ 4.3.3}}
\DoxyCodeLine{01393\ \textcolor{preprocessor}{\#if\ defined\ \_WIN32}}
\DoxyCodeLine{01394\ \textcolor{preprocessor}{\#if\ defined\ \_WIN64}}
\DoxyCodeLine{01395\ \textcolor{keyword}{typedef}\ \textcolor{keywordtype}{unsigned}\ \_\_int64\ cppzmq\_fd\_t;}
\DoxyCodeLine{01396\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{01397\ \textcolor{keyword}{typedef}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ cppzmq\_fd\_t;}
\DoxyCodeLine{01398\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01399\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{01400\ \textcolor{keyword}{typedef}\ \textcolor{keywordtype}{int}\ cppzmq\_fd\_t;}
\DoxyCodeLine{01401\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01402\ }
\DoxyCodeLine{01403\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_AFFINITY}}
\DoxyCodeLine{01404\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_AFFINITY,\ affinity,\ uint64\_t);}
\DoxyCodeLine{01405\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01406\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_BACKLOG}}
\DoxyCodeLine{01407\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_BACKLOG,\ backlog,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01408\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01409\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_BINDTODEVICE}}
\DoxyCodeLine{01410\ ZMQ\_DEFINE\_ARRAY\_OPT\_BINARY(ZMQ\_BINDTODEVICE,\ bindtodevice);}
\DoxyCodeLine{01411\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01412\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CONFLATE}}
\DoxyCodeLine{01413\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_CONFLATE,\ conflate,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01414\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01415\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CONNECT\_ROUTING\_ID}}
\DoxyCodeLine{01416\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_CONNECT\_ROUTING\_ID,\ connect\_routing\_id);}
\DoxyCodeLine{01417\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01418\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CONNECT\_TIMEOUT}}
\DoxyCodeLine{01419\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_CONNECT\_TIMEOUT,\ connect\_timeout,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01420\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01421\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CURVE\_PUBLICKEY}}
\DoxyCodeLine{01422\ ZMQ\_DEFINE\_ARRAY\_OPT\_BIN\_OR\_Z85(ZMQ\_CURVE\_PUBLICKEY,\ curve\_publickey);}
\DoxyCodeLine{01423\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01424\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CURVE\_SECRETKEY}}
\DoxyCodeLine{01425\ ZMQ\_DEFINE\_ARRAY\_OPT\_BIN\_OR\_Z85(ZMQ\_CURVE\_SECRETKEY,\ curve\_secretkey);}
\DoxyCodeLine{01426\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01427\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CURVE\_SERVER}}
\DoxyCodeLine{01428\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_CURVE\_SERVER,\ curve\_server,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01429\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01430\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CURVE\_SERVERKEY}}
\DoxyCodeLine{01431\ ZMQ\_DEFINE\_ARRAY\_OPT\_BIN\_OR\_Z85(ZMQ\_CURVE\_SERVERKEY,\ curve\_serverkey);}
\DoxyCodeLine{01432\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01433\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_EVENTS}}
\DoxyCodeLine{01434\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_EVENTS,\ events,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01435\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01436\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_FD}}
\DoxyCodeLine{01437\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_FD,\ fd,\ cppzmq\_fd\_t);}
\DoxyCodeLine{01438\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01439\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_GSSAPI\_PLAINTEXT}}
\DoxyCodeLine{01440\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_GSSAPI\_PLAINTEXT,\ gssapi\_plaintext,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01441\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01442\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_GSSAPI\_SERVER}}
\DoxyCodeLine{01443\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_GSSAPI\_SERVER,\ gssapi\_server,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01444\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01445\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_GSSAPI\_SERVICE\_PRINCIPAL}}
\DoxyCodeLine{01446\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_GSSAPI\_SERVICE\_PRINCIPAL,\ gssapi\_service\_principal);}
\DoxyCodeLine{01447\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01448\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_GSSAPI\_SERVICE\_PRINCIPAL\_NAMETYPE}}
\DoxyCodeLine{01449\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_GSSAPI\_SERVICE\_PRINCIPAL\_NAMETYPE,}
\DoxyCodeLine{01450\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ gssapi\_service\_principal\_nametype,}
\DoxyCodeLine{01451\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01452\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01453\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_GSSAPI\_PRINCIPAL}}
\DoxyCodeLine{01454\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_GSSAPI\_PRINCIPAL,\ gssapi\_principal);}
\DoxyCodeLine{01455\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01456\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_GSSAPI\_PRINCIPAL\_NAMETYPE}}
\DoxyCodeLine{01457\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_GSSAPI\_PRINCIPAL\_NAMETYPE,}
\DoxyCodeLine{01458\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ gssapi\_principal\_nametype,}
\DoxyCodeLine{01459\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01460\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01461\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_HANDSHAKE\_IVL}}
\DoxyCodeLine{01462\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_HANDSHAKE\_IVL,\ handshake\_ivl,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01463\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01464\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_HEARTBEAT\_IVL}}
\DoxyCodeLine{01465\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_HEARTBEAT\_IVL,\ heartbeat\_ivl,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01466\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01467\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_HEARTBEAT\_TIMEOUT}}
\DoxyCodeLine{01468\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_HEARTBEAT\_TIMEOUT,\ heartbeat\_timeout,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01469\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01470\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_HEARTBEAT\_TTL}}
\DoxyCodeLine{01471\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_HEARTBEAT\_TTL,\ heartbeat\_ttl,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01472\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01473\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_IMMEDIATE}}
\DoxyCodeLine{01474\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_IMMEDIATE,\ immediate,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01475\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01476\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_INVERT\_MATCHING}}
\DoxyCodeLine{01477\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_INVERT\_MATCHING,\ invert\_matching,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01478\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01479\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_IPV6}}
\DoxyCodeLine{01480\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_IPV6,\ ipv6,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01481\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01482\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_LAST\_ENDPOINT}}
\DoxyCodeLine{01483\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_LAST\_ENDPOINT,\ last\_endpoint);}
\DoxyCodeLine{01484\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01485\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_LINGER}}
\DoxyCodeLine{01486\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_LINGER,\ linger,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01487\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01488\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_MAXMSGSIZE}}
\DoxyCodeLine{01489\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_MAXMSGSIZE,\ maxmsgsize,\ int64\_t);}
\DoxyCodeLine{01490\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01491\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_MECHANISM}}
\DoxyCodeLine{01492\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_MECHANISM,\ mechanism,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01493\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01494\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_METADATA}}
\DoxyCodeLine{01495\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_METADATA,\ metadata);}
\DoxyCodeLine{01496\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01497\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_MULTICAST\_HOPS}}
\DoxyCodeLine{01498\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_MULTICAST\_HOPS,\ multicast\_hops,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01499\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01500\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_MULTICAST\_LOOP}}
\DoxyCodeLine{01501\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_MULTICAST\_LOOP,\ multicast\_loop,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01502\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01503\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_MULTICAST\_MAXTPDU}}
\DoxyCodeLine{01504\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_MULTICAST\_MAXTPDU,\ multicast\_maxtpdu,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01505\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01506\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_PLAIN\_SERVER}}
\DoxyCodeLine{01507\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_PLAIN\_SERVER,\ plain\_server,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01508\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01509\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_PLAIN\_PASSWORD}}
\DoxyCodeLine{01510\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_PLAIN\_PASSWORD,\ plain\_password);}
\DoxyCodeLine{01511\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01512\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_PLAIN\_USERNAME}}
\DoxyCodeLine{01513\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_PLAIN\_USERNAME,\ plain\_username);}
\DoxyCodeLine{01514\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01515\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_USE\_FD}}
\DoxyCodeLine{01516\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_USE\_FD,\ use\_fd,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01517\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01518\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_PROBE\_ROUTER}}
\DoxyCodeLine{01519\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_PROBE\_ROUTER,\ probe\_router,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01520\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01521\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_RATE}}
\DoxyCodeLine{01522\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_RATE,\ rate,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01523\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01524\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_RCVBUF}}
\DoxyCodeLine{01525\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_RCVBUF,\ rcvbuf,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01526\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01527\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_RCVHWM}}
\DoxyCodeLine{01528\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_RCVHWM,\ rcvhwm,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01529\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01530\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_RCVMORE}}
\DoxyCodeLine{01531\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_RCVMORE,\ rcvmore,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01532\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01533\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_RCVTIMEO}}
\DoxyCodeLine{01534\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_RCVTIMEO,\ rcvtimeo,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01535\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01536\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_RECONNECT\_IVL}}
\DoxyCodeLine{01537\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_RECONNECT\_IVL,\ reconnect\_ivl,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01538\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01539\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_RECONNECT\_IVL\_MAX}}
\DoxyCodeLine{01540\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_RECONNECT\_IVL\_MAX,\ reconnect\_ivl\_max,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01541\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01542\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_RECOVERY\_IVL}}
\DoxyCodeLine{01543\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_RECOVERY\_IVL,\ recovery\_ivl,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01544\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01545\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_REQ\_CORRELATE}}
\DoxyCodeLine{01546\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_REQ\_CORRELATE,\ req\_correlate,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01547\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01548\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_REQ\_RELAXED}}
\DoxyCodeLine{01549\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_REQ\_RELAXED,\ req\_relaxed,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01550\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01551\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_ROUTER\_HANDOVER}}
\DoxyCodeLine{01552\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_ROUTER\_HANDOVER,\ router\_handover,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01553\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01554\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_ROUTER\_MANDATORY}}
\DoxyCodeLine{01555\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_ROUTER\_MANDATORY,\ router\_mandatory,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01556\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01557\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_ROUTER\_NOTIFY}}
\DoxyCodeLine{01558\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_ROUTER\_NOTIFY,\ router\_notify,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01559\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01560\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_ROUTING\_ID}}
\DoxyCodeLine{01561\ ZMQ\_DEFINE\_ARRAY\_OPT\_BINARY(ZMQ\_ROUTING\_ID,\ routing\_id);}
\DoxyCodeLine{01562\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01563\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_SNDBUF}}
\DoxyCodeLine{01564\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_SNDBUF,\ sndbuf,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01565\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01566\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_SNDHWM}}
\DoxyCodeLine{01567\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_SNDHWM,\ sndhwm,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01568\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01569\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_SNDTIMEO}}
\DoxyCodeLine{01570\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_SNDTIMEO,\ sndtimeo,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01571\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01572\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_SOCKS\_PROXY}}
\DoxyCodeLine{01573\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_SOCKS\_PROXY,\ socks\_proxy);}
\DoxyCodeLine{01574\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01575\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_STREAM\_NOTIFY}}
\DoxyCodeLine{01576\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_STREAM\_NOTIFY,\ stream\_notify,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01577\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01578\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_SUBSCRIBE}}
\DoxyCodeLine{01579\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_SUBSCRIBE,\ subscribe);}
\DoxyCodeLine{01580\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01581\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_TCP\_KEEPALIVE}}
\DoxyCodeLine{01582\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_TCP\_KEEPALIVE,\ tcp\_keepalive,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01583\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01584\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_TCP\_KEEPALIVE\_CNT}}
\DoxyCodeLine{01585\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_TCP\_KEEPALIVE\_CNT,\ tcp\_keepalive\_cnt,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01586\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01587\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_TCP\_KEEPALIVE\_IDLE}}
\DoxyCodeLine{01588\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_TCP\_KEEPALIVE\_IDLE,\ tcp\_keepalive\_idle,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01589\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01590\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_TCP\_KEEPALIVE\_INTVL}}
\DoxyCodeLine{01591\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_TCP\_KEEPALIVE\_INTVL,\ tcp\_keepalive\_intvl,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01592\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01593\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_TCP\_MAXRT}}
\DoxyCodeLine{01594\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_TCP\_MAXRT,\ tcp\_maxrt,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01595\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01596\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_THREAD\_SAFE}}
\DoxyCodeLine{01597\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_THREAD\_SAFE,\ thread\_safe,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01598\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01599\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_TOS}}
\DoxyCodeLine{01600\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_TOS,\ tos,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01601\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01602\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_TYPE}}
\DoxyCodeLine{01603\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_TYPE,\ type,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01604\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01605\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_UNSUBSCRIBE}}
\DoxyCodeLine{01606\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_UNSUBSCRIBE,\ unsubscribe);}
\DoxyCodeLine{01607\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01608\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_VMCI\_BUFFER\_SIZE}}
\DoxyCodeLine{01609\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_VMCI\_BUFFER\_SIZE,\ vmci\_buffer\_size,\ uint64\_t);}
\DoxyCodeLine{01610\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01611\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_VMCI\_BUFFER\_MIN\_SIZE}}
\DoxyCodeLine{01612\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_VMCI\_BUFFER\_MIN\_SIZE,\ vmci\_buffer\_min\_size,\ uint64\_t);}
\DoxyCodeLine{01613\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01614\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_VMCI\_BUFFER\_MAX\_SIZE}}
\DoxyCodeLine{01615\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_VMCI\_BUFFER\_MAX\_SIZE,\ vmci\_buffer\_max\_size,\ uint64\_t);}
\DoxyCodeLine{01616\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01617\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_VMCI\_CONNECT\_TIMEOUT}}
\DoxyCodeLine{01618\ ZMQ\_DEFINE\_INTEGRAL\_OPT(ZMQ\_VMCI\_CONNECT\_TIMEOUT,\ vmci\_connect\_timeout,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01619\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01620\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_XPUB\_VERBOSE}}
\DoxyCodeLine{01621\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_XPUB\_VERBOSE,\ xpub\_verbose,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01622\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01623\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_XPUB\_VERBOSER}}
\DoxyCodeLine{01624\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_XPUB\_VERBOSER,\ xpub\_verboser,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01625\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01626\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_XPUB\_MANUAL}}
\DoxyCodeLine{01627\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_XPUB\_MANUAL,\ xpub\_manual,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01628\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01629\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_XPUB\_NODROP}}
\DoxyCodeLine{01630\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_XPUB\_NODROP,\ xpub\_nodrop,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01631\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01632\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_XPUB\_WELCOME\_MSG}}
\DoxyCodeLine{01633\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_XPUB\_WELCOME\_MSG,\ xpub\_welcome\_msg);}
\DoxyCodeLine{01634\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01635\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_ZAP\_ENFORCE\_DOMAIN}}
\DoxyCodeLine{01636\ ZMQ\_DEFINE\_INTEGRAL\_BOOL\_UNIT\_OPT(ZMQ\_ZAP\_ENFORCE\_DOMAIN,\ zap\_enforce\_domain,\ \textcolor{keywordtype}{int});}
\DoxyCodeLine{01637\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01638\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_ZAP\_DOMAIN}}
\DoxyCodeLine{01639\ ZMQ\_DEFINE\_ARRAY\_OPT(ZMQ\_ZAP\_DOMAIN,\ zap\_domain);}
\DoxyCodeLine{01640\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01641\ }
\DoxyCodeLine{01642\ \}\ \textcolor{comment}{//\ namespace\ sockopt}}
\DoxyCodeLine{01643\ \textcolor{preprocessor}{\#endif\ }\textcolor{comment}{//\ ZMQ\_CPP11}}
\DoxyCodeLine{01644\ }
\DoxyCodeLine{01645\ }
\DoxyCodeLine{01646\ \textcolor{keyword}{namespace\ }detail}
\DoxyCodeLine{01647\ \{}
\DoxyCodeLine{01648\ \textcolor{keyword}{class\ }socket\_base}
\DoxyCodeLine{01649\ \{}
\DoxyCodeLine{01650\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{01651\ \ \ \ \ socket\_base()\ ZMQ\_NOTHROW\ :\ \_handle(ZMQ\_NULLPTR)\ \{\}}
\DoxyCodeLine{01652\ \ \ \ \ ZMQ\_EXPLICIT\ socket\_base(\textcolor{keywordtype}{void}\ *handle)\ ZMQ\_NOTHROW\ :\ \_handle(handle)\ \{\}}
\DoxyCodeLine{01653\ }
\DoxyCodeLine{01654\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keyword}{typename}\ T>}
\DoxyCodeLine{01655\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.7.0,\ use\ \`{}set`\ taking\ option\ from\ zmq::sockopt"{}})}
\DoxyCodeLine{01656\ \ \ \ \ \textcolor{keywordtype}{void}\ setsockopt(\textcolor{keywordtype}{int}\ option\_,\ T\ \textcolor{keyword}{const}\ \&optval)}
\DoxyCodeLine{01657\ \ \ \ \ \{}
\DoxyCodeLine{01658\ \ \ \ \ \ \ \ \ setsockopt(option\_,\ \&optval,\ \textcolor{keyword}{sizeof}(T));}
\DoxyCodeLine{01659\ \ \ \ \ \}}
\DoxyCodeLine{01660\ }
\DoxyCodeLine{01661\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.7.0,\ use\ \`{}set`\ taking\ option\ from\ zmq::sockopt"{}})}
\DoxyCodeLine{01662\ \ \ \ \ \textcolor{keywordtype}{void}\ setsockopt(\textcolor{keywordtype}{int}\ option\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *optval\_,\ \textcolor{keywordtype}{size\_t}\ optvallen\_)}
\DoxyCodeLine{01663\ \ \ \ \ \{}
\DoxyCodeLine{01664\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_setsockopt(\_handle,\ option\_,\ optval\_,\ optvallen\_);}
\DoxyCodeLine{01665\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{01666\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01667\ \ \ \ \ \}}
\DoxyCodeLine{01668\ }
\DoxyCodeLine{01669\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.7.0,\ use\ \`{}get`\ taking\ option\ from\ zmq::sockopt"{}})}
\DoxyCodeLine{01670\ \ \ \ \ \textcolor{keywordtype}{void}\ getsockopt(\textcolor{keywordtype}{int}\ option\_,\ \textcolor{keywordtype}{void}\ *optval\_,\ \textcolor{keywordtype}{size\_t}\ *optvallen\_)\textcolor{keyword}{\ const}}
\DoxyCodeLine{01671\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{01672\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_getsockopt(\_handle,\ option\_,\ optval\_,\ optvallen\_);}
\DoxyCodeLine{01673\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{01674\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01675\ \ \ \ \ \}}
\DoxyCodeLine{01676\ }
\DoxyCodeLine{01677\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keyword}{typename}\ T>}
\DoxyCodeLine{01678\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.7.0,\ use\ \`{}get`\ taking\ option\ from\ zmq::sockopt"{}})}
\DoxyCodeLine{01679\ \ \ \ \ T\ getsockopt(\textcolor{keywordtype}{int}\ option\_)\textcolor{keyword}{\ const}}
\DoxyCodeLine{01680\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{01681\ \ \ \ \ \ \ \ \ T\ optval;}
\DoxyCodeLine{01682\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{size\_t}\ optlen\ =\ \textcolor{keyword}{sizeof}(T);}
\DoxyCodeLine{01683\ \ \ \ \ \ \ \ \ getsockopt(option\_,\ \&optval,\ \&optlen);}
\DoxyCodeLine{01684\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ optval;}
\DoxyCodeLine{01685\ \ \ \ \ \}}
\DoxyCodeLine{01686\ }
\DoxyCodeLine{01687\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{01688\ \ \ \ \ \textcolor{comment}{//\ Set\ integral\ socket\ option,\ e.g.}}
\DoxyCodeLine{01689\ \ \ \ \ \textcolor{comment}{//\ \`{}socket.set(zmq::sockopt::linger,\ 0)`}}
\DoxyCodeLine{01690\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{bool}\ BoolUnit>}
\DoxyCodeLine{01691\ \ \ \ \ \textcolor{keywordtype}{void}\ set(sockopt::integral\_option<Opt,\ T,\ BoolUnit>,\ \textcolor{keyword}{const}\ T\ \&val)}
\DoxyCodeLine{01692\ \ \ \ \ \{}
\DoxyCodeLine{01693\ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_assert}(std::is\_integral<T>::value,\ \textcolor{stringliteral}{"{}T\ must\ be\ integral"{}});}
\DoxyCodeLine{01694\ \ \ \ \ \ \ \ \ set\_option(Opt,\ \&val,\ \textcolor{keyword}{sizeof}\ val);}
\DoxyCodeLine{01695\ \ \ \ \ \}}
\DoxyCodeLine{01696\ }
\DoxyCodeLine{01697\ \ \ \ \ \textcolor{comment}{//\ Set\ integral\ socket\ option\ from\ boolean,\ e.g.}}
\DoxyCodeLine{01698\ \ \ \ \ \textcolor{comment}{//\ \`{}socket.set(zmq::sockopt::immediate,\ false)`}}
\DoxyCodeLine{01699\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keyword}{class}\ T>}
\DoxyCodeLine{01700\ \ \ \ \ \textcolor{keywordtype}{void}\ set(sockopt::integral\_option<Opt,\ T,\ true>,\ \textcolor{keywordtype}{bool}\ val)}
\DoxyCodeLine{01701\ \ \ \ \ \{}
\DoxyCodeLine{01702\ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_assert}(std::is\_integral<T>::value,\ \textcolor{stringliteral}{"{}T\ must\ be\ integral"{}});}
\DoxyCodeLine{01703\ \ \ \ \ \ \ \ \ T\ rep\_val\ =\ val;}
\DoxyCodeLine{01704\ \ \ \ \ \ \ \ \ set\_option(Opt,\ \&rep\_val,\ \textcolor{keyword}{sizeof}\ rep\_val);}
\DoxyCodeLine{01705\ \ \ \ \ \}}
\DoxyCodeLine{01706\ }
\DoxyCodeLine{01707\ \ \ \ \ \textcolor{comment}{//\ Set\ array\ socket\ option,\ e.g.}}
\DoxyCodeLine{01708\ \ \ \ \ \textcolor{comment}{//\ \`{}socket.set(zmq::sockopt::plain\_username,\ "{}foo123"{})`}}
\DoxyCodeLine{01709\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keywordtype}{int}\ NullTerm>}
\DoxyCodeLine{01710\ \ \ \ \ \textcolor{keywordtype}{void}\ set(sockopt::array\_option<Opt,\ NullTerm>,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *buf)}
\DoxyCodeLine{01711\ \ \ \ \ \{}
\DoxyCodeLine{01712\ \ \ \ \ \ \ \ \ set\_option(Opt,\ buf,\ std::strlen(buf));}
\DoxyCodeLine{01713\ \ \ \ \ \}}
\DoxyCodeLine{01714\ }
\DoxyCodeLine{01715\ \ \ \ \ \textcolor{comment}{//\ Set\ array\ socket\ option,\ e.g.}}
\DoxyCodeLine{01716\ \ \ \ \ \textcolor{comment}{//\ \`{}socket.set(zmq::sockopt::routing\_id,\ zmq::buffer(id))`}}
\DoxyCodeLine{01717\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keywordtype}{int}\ NullTerm>}
\DoxyCodeLine{01718\ \ \ \ \ \textcolor{keywordtype}{void}\ set(sockopt::array\_option<Opt,\ NullTerm>,\ const\_buffer\ buf)}
\DoxyCodeLine{01719\ \ \ \ \ \{}
\DoxyCodeLine{01720\ \ \ \ \ \ \ \ \ set\_option(Opt,\ buf.data(),\ buf.size());}
\DoxyCodeLine{01721\ \ \ \ \ \}}
\DoxyCodeLine{01722\ }
\DoxyCodeLine{01723\ \ \ \ \ \textcolor{comment}{//\ Set\ array\ socket\ option,\ e.g.}}
\DoxyCodeLine{01724\ \ \ \ \ \textcolor{comment}{//\ \`{}socket.set(zmq::sockopt::routing\_id,\ id\_str)`}}
\DoxyCodeLine{01725\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keywordtype}{int}\ NullTerm>}
\DoxyCodeLine{01726\ \ \ \ \ \textcolor{keywordtype}{void}\ set(sockopt::array\_option<Opt,\ NullTerm>,\ \textcolor{keyword}{const}\ std::string\ \&buf)}
\DoxyCodeLine{01727\ \ \ \ \ \{}
\DoxyCodeLine{01728\ \ \ \ \ \ \ \ \ set\_option(Opt,\ buf.data(),\ buf.size());}
\DoxyCodeLine{01729\ \ \ \ \ \}}
\DoxyCodeLine{01730\ }
\DoxyCodeLine{01731\ \textcolor{preprocessor}{\#if\ CPPZMQ\_HAS\_STRING\_VIEW}}
\DoxyCodeLine{01732\ \ \ \ \ \textcolor{comment}{//\ Set\ array\ socket\ option,\ e.g.}}
\DoxyCodeLine{01733\ \ \ \ \ \textcolor{comment}{//\ \`{}socket.set(zmq::sockopt::routing\_id,\ id\_str)`}}
\DoxyCodeLine{01734\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keywordtype}{int}\ NullTerm>}
\DoxyCodeLine{01735\ \ \ \ \ \textcolor{keywordtype}{void}\ set(sockopt::array\_option<Opt,\ NullTerm>,\ std::string\_view\ buf)}
\DoxyCodeLine{01736\ \ \ \ \ \{}
\DoxyCodeLine{01737\ \ \ \ \ \ \ \ \ set\_option(Opt,\ buf.data(),\ buf.size());}
\DoxyCodeLine{01738\ \ \ \ \ \}}
\DoxyCodeLine{01739\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01740\ }
\DoxyCodeLine{01741\ \ \ \ \ \textcolor{comment}{//\ Get\ scalar\ socket\ option,\ e.g.}}
\DoxyCodeLine{01742\ \ \ \ \ \textcolor{comment}{//\ \`{}auto\ opt\ =\ socket.get(zmq::sockopt::linger)`}}
\DoxyCodeLine{01743\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keyword}{class}\ T,\ \textcolor{keywordtype}{bool}\ BoolUnit>}
\DoxyCodeLine{01744\ \ \ \ \ ZMQ\_NODISCARD\ T\ get(sockopt::integral\_option<Opt,\ T,\ BoolUnit>)\textcolor{keyword}{\ const}}
\DoxyCodeLine{01745\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{01746\ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_assert}(std::is\_integral<T>::value,\ \textcolor{stringliteral}{"{}T\ must\ be\ integral"{}});}
\DoxyCodeLine{01747\ \ \ \ \ \ \ \ \ T\ val;}
\DoxyCodeLine{01748\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{size\_t}\ size\ =\ \textcolor{keyword}{sizeof}\ val;}
\DoxyCodeLine{01749\ \ \ \ \ \ \ \ \ get\_option(Opt,\ \&val,\ \&size);}
\DoxyCodeLine{01750\ \ \ \ \ \ \ \ \ assert(size\ ==\ \textcolor{keyword}{sizeof}\ val);}
\DoxyCodeLine{01751\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ val;}
\DoxyCodeLine{01752\ \ \ \ \ \}}
\DoxyCodeLine{01753\ }
\DoxyCodeLine{01754\ \ \ \ \ \textcolor{comment}{//\ Get\ array\ socket\ option,\ writes\ to\ buf,\ returns\ option\ size\ in\ bytes,\ e.g.}}
\DoxyCodeLine{01755\ \ \ \ \ \textcolor{comment}{//\ \`{}size\_t\ optsize\ =\ socket.get(zmq::sockopt::routing\_id,\ zmq::buffer(id))`}}
\DoxyCodeLine{01756\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keywordtype}{int}\ NullTerm>}
\DoxyCodeLine{01757\ \ \ \ \ ZMQ\_NODISCARD\ \textcolor{keywordtype}{size\_t}\ get(sockopt::array\_option<Opt,\ NullTerm>,}
\DoxyCodeLine{01758\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ mutable\_buffer\ buf)\textcolor{keyword}{\ const}}
\DoxyCodeLine{01759\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{01760\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{size\_t}\ size\ =\ buf.size();}
\DoxyCodeLine{01761\ \ \ \ \ \ \ \ \ get\_option(Opt,\ buf.data(),\ \&size);}
\DoxyCodeLine{01762\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ size;}
\DoxyCodeLine{01763\ \ \ \ \ \}}
\DoxyCodeLine{01764\ }
\DoxyCodeLine{01765\ \ \ \ \ \textcolor{comment}{//\ Get\ array\ socket\ option\ as\ string\ (initializes\ the\ string\ buffer\ size\ to\ init\_size)\ e.g.}}
\DoxyCodeLine{01766\ \ \ \ \ \textcolor{comment}{//\ \`{}auto\ s\ =\ socket.get(zmq::sockopt::routing\_id)`}}
\DoxyCodeLine{01767\ \ \ \ \ \textcolor{comment}{//\ Note:\ removes\ the\ null\ character\ from\ null-\/terminated\ string\ options,}}
\DoxyCodeLine{01768\ \ \ \ \ \textcolor{comment}{//\ i.e.\ the\ string\ size\ excludes\ the\ null\ character.}}
\DoxyCodeLine{01769\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keywordtype}{int}\ Opt,\ \textcolor{keywordtype}{int}\ NullTerm>}
\DoxyCodeLine{01770\ \ \ \ \ ZMQ\_NODISCARD\ std::string\ get(sockopt::array\_option<Opt,\ NullTerm>,}
\DoxyCodeLine{01771\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{size\_t}\ init\_size\ =\ 1024)\textcolor{keyword}{\ const}}
\DoxyCodeLine{01772\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{01773\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (NullTerm\ ==\ 2\ \&\&\ init\_size\ ==\ 1024)\ \{}
\DoxyCodeLine{01774\ \ \ \ \ \ \ \ \ \ \ \ \ init\_size\ =\ 41;\ \textcolor{comment}{//\ get\ as\ Z85\ string}}
\DoxyCodeLine{01775\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{01776\ \ \ \ \ \ \ \ \ std::string\ str(init\_size,\ \textcolor{charliteral}{'\(\backslash\)0'});}
\DoxyCodeLine{01777\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{size\_t}\ size\ =\ get(sockopt::array\_option<Opt>\{\},\ \mbox{\hyperlink{structbuffer}{buffer}}(str));}
\DoxyCodeLine{01778\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (NullTerm\ ==\ 1)\ \{}
\DoxyCodeLine{01779\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (size\ >\ 0)\ \{}
\DoxyCodeLine{01780\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ assert(str[size\ -\/\ 1]\ ==\ \textcolor{charliteral}{'\(\backslash\)0'});}
\DoxyCodeLine{01781\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ -\/-\/size;}
\DoxyCodeLine{01782\ \ \ \ \ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{01783\ \ \ \ \ \ \ \ \ \}\ \textcolor{keywordflow}{else}\ \textcolor{keywordflow}{if}\ (NullTerm\ ==\ 2)\ \{}
\DoxyCodeLine{01784\ \ \ \ \ \ \ \ \ \ \ \ \ assert(size\ ==\ 32\ ||\ size\ ==\ 41);}
\DoxyCodeLine{01785\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (size\ ==\ 41)\ \{}
\DoxyCodeLine{01786\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ assert(str[size\ -\/\ 1]\ ==\ \textcolor{charliteral}{'\(\backslash\)0'});}
\DoxyCodeLine{01787\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ -\/-\/size;}
\DoxyCodeLine{01788\ \ \ \ \ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{01789\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{01790\ \ \ \ \ \ \ \ \ str.resize(size);}
\DoxyCodeLine{01791\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ str;}
\DoxyCodeLine{01792\ \ \ \ \ \}}
\DoxyCodeLine{01793\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01794\ }
\DoxyCodeLine{01795\ \ \ \ \ \textcolor{keywordtype}{void}\ bind(std::string\ \textcolor{keyword}{const}\ \&addr)\ \{\ bind(addr.c\_str());\ \}}
\DoxyCodeLine{01796\ }
\DoxyCodeLine{01797\ \ \ \ \ \textcolor{keywordtype}{void}\ bind(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{01798\ \ \ \ \ \{}
\DoxyCodeLine{01799\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_bind(\_handle,\ addr\_);}
\DoxyCodeLine{01800\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{01801\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01802\ \ \ \ \ \}}
\DoxyCodeLine{01803\ }
\DoxyCodeLine{01804\ \ \ \ \ \textcolor{keywordtype}{void}\ unbind(std::string\ \textcolor{keyword}{const}\ \&addr)\ \{\ unbind(addr.c\_str());\ \}}
\DoxyCodeLine{01805\ }
\DoxyCodeLine{01806\ \ \ \ \ \textcolor{keywordtype}{void}\ unbind(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{01807\ \ \ \ \ \{}
\DoxyCodeLine{01808\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_unbind(\_handle,\ addr\_);}
\DoxyCodeLine{01809\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{01810\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01811\ \ \ \ \ \}}
\DoxyCodeLine{01812\ }
\DoxyCodeLine{01813\ \ \ \ \ \textcolor{keywordtype}{void}\ connect(std::string\ \textcolor{keyword}{const}\ \&addr)\ \{\ connect(addr.c\_str());\ \}}
\DoxyCodeLine{01814\ }
\DoxyCodeLine{01815\ \ \ \ \ \textcolor{keywordtype}{void}\ connect(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{01816\ \ \ \ \ \{}
\DoxyCodeLine{01817\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_connect(\_handle,\ addr\_);}
\DoxyCodeLine{01818\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{01819\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01820\ \ \ \ \ \}}
\DoxyCodeLine{01821\ }
\DoxyCodeLine{01822\ \ \ \ \ \textcolor{keywordtype}{void}\ disconnect(std::string\ \textcolor{keyword}{const}\ \&addr)\ \{\ disconnect(addr.c\_str());\ \}}
\DoxyCodeLine{01823\ }
\DoxyCodeLine{01824\ \ \ \ \ \textcolor{keywordtype}{void}\ disconnect(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{01825\ \ \ \ \ \{}
\DoxyCodeLine{01826\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_disconnect(\_handle,\ addr\_);}
\DoxyCodeLine{01827\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{01828\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01829\ \ \ \ \ \}}
\DoxyCodeLine{01830\ }
\DoxyCodeLine{01831\ \ \ \ \ \textcolor{keywordtype}{bool}\ connected()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ (\_handle\ !=\ ZMQ\_NULLPTR);\ \}}
\DoxyCodeLine{01832\ }
\DoxyCodeLine{01833\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ send\ taking\ a\ const\_buffer\ and\ send\_flags"{}})}
\DoxyCodeLine{01834\ \ \ \ \ \textcolor{keywordtype}{size\_t}\ send(\textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *buf\_,\ \textcolor{keywordtype}{size\_t}\ len\_,\ \textcolor{keywordtype}{int}\ flags\_\ =\ 0)}
\DoxyCodeLine{01835\ \ \ \ \ \{}
\DoxyCodeLine{01836\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ nbytes\ =\ zmq\_send(\_handle,\ buf\_,\ len\_,\ flags\_);}
\DoxyCodeLine{01837\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (nbytes\ >=\ 0)}
\DoxyCodeLine{01838\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{size\_t}\textcolor{keyword}{>}(nbytes);}
\DoxyCodeLine{01839\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ EAGAIN)}
\DoxyCodeLine{01840\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ 0;}
\DoxyCodeLine{01841\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01842\ \ \ \ \ \}}
\DoxyCodeLine{01843\ }
\DoxyCodeLine{01844\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ send\ taking\ message\_t\ and\ send\_flags"{}})}
\DoxyCodeLine{01845\ \ \ \ \ \textcolor{keywordtype}{bool}\ send(\mbox{\hyperlink{classzmq_1_1message__t}{message\_t}}\ \&msg\_,}
\DoxyCodeLine{01846\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ flags\_\ =\ 0)\ \textcolor{comment}{//\ default\ until\ removed}}
\DoxyCodeLine{01847\ \ \ \ \ \{}
\DoxyCodeLine{01848\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ nbytes\ =\ zmq\_msg\_send(msg\_.handle(),\ \_handle,\ flags\_);}
\DoxyCodeLine{01849\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (nbytes\ >=\ 0)}
\DoxyCodeLine{01850\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{true};}
\DoxyCodeLine{01851\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ EAGAIN)}
\DoxyCodeLine{01852\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{false};}
\DoxyCodeLine{01853\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01854\ \ \ \ \ \}}
\DoxyCodeLine{01855\ }
\DoxyCodeLine{01856\ \ \ \ \ \textcolor{keyword}{template}<\textcolor{keyword}{typename}\ T>}
\DoxyCodeLine{01857\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(}
\DoxyCodeLine{01858\ \ \ \ \ \ \ \textcolor{stringliteral}{"{}from\ 4.4.1,\ use\ send\ taking\ message\_t\ or\ buffer\ (for\ contiguous\ "{}}}
\DoxyCodeLine{01859\ \ \ \ \ \ \ \textcolor{stringliteral}{"{}ranges),\ and\ send\_flags"{}})}
\DoxyCodeLine{01860\ \ \ \ \ \textcolor{keywordtype}{bool}\ send(T\ first,\ T\ last,\ \textcolor{keywordtype}{int}\ flags\_\ =\ 0)}
\DoxyCodeLine{01861\ \ \ \ \ \{}
\DoxyCodeLine{01862\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{classzmq_1_1message__t}{zmq::message\_t}}\ msg(first,\ last);}
\DoxyCodeLine{01863\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ nbytes\ =\ zmq\_msg\_send(msg.handle(),\ \_handle,\ flags\_);}
\DoxyCodeLine{01864\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (nbytes\ >=\ 0)}
\DoxyCodeLine{01865\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{true};}
\DoxyCodeLine{01866\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ EAGAIN)}
\DoxyCodeLine{01867\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{false};}
\DoxyCodeLine{01868\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01869\ \ \ \ \ \}}
\DoxyCodeLine{01870\ }
\DoxyCodeLine{01871\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_HAS\_RVALUE\_REFS}}
\DoxyCodeLine{01872\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ send\ taking\ message\_t\ and\ send\_flags"{}})}
\DoxyCodeLine{01873\ \ \ \ \ \textcolor{keywordtype}{bool}\ send(\mbox{\hyperlink{classzmq_1_1message__t}{message\_t}}\ \&\&msg\_,}
\DoxyCodeLine{01874\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ flags\_\ =\ 0)\ \textcolor{comment}{//\ default\ until\ removed}}
\DoxyCodeLine{01875\ \ \ \ \ \{}
\DoxyCodeLine{01876\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{01877\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ send(msg\_,\ \textcolor{keyword}{static\_cast<}send\_flags\textcolor{keyword}{>}(flags\_)).has\_value();}
\DoxyCodeLine{01878\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{01879\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ send(msg\_,\ flags\_);}
\DoxyCodeLine{01880\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01881\ \ \ \ \ \}}
\DoxyCodeLine{01882\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01883\ }
\DoxyCodeLine{01884\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{01885\ \ \ \ \ send\_result\_t\ send(const\_buffer\ buf,\ send\_flags\ flags\ =\ send\_flags::none)}
\DoxyCodeLine{01886\ \ \ \ \ \{}
\DoxyCodeLine{01887\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{int}\ nbytes\ =}
\DoxyCodeLine{01888\ \ \ \ \ \ \ \ \ \ \ zmq\_send(\_handle,\ buf.data(),\ buf.size(),\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{int}\textcolor{keyword}{>}(flags));}
\DoxyCodeLine{01889\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (nbytes\ >=\ 0)}
\DoxyCodeLine{01890\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{size\_t}\textcolor{keyword}{>}(nbytes);}
\DoxyCodeLine{01891\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ EAGAIN)}
\DoxyCodeLine{01892\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \{\};}
\DoxyCodeLine{01893\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01894\ \ \ \ \ \}}
\DoxyCodeLine{01895\ }
\DoxyCodeLine{01896\ \ \ \ \ send\_result\_t\ send(\mbox{\hyperlink{classzmq_1_1message__t}{message\_t}}\ \&msg,\ send\_flags\ flags)}
\DoxyCodeLine{01897\ \ \ \ \ \{}
\DoxyCodeLine{01898\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ nbytes\ =\ zmq\_msg\_send(msg.handle(),\ \_handle,\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{int}\textcolor{keyword}{>}(flags));}
\DoxyCodeLine{01899\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (nbytes\ >=\ 0)}
\DoxyCodeLine{01900\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{size\_t}\textcolor{keyword}{>}(nbytes);}
\DoxyCodeLine{01901\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ EAGAIN)}
\DoxyCodeLine{01902\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \{\};}
\DoxyCodeLine{01903\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01904\ \ \ \ \ \}}
\DoxyCodeLine{01905\ }
\DoxyCodeLine{01906\ \ \ \ \ send\_result\_t\ send(\mbox{\hyperlink{classzmq_1_1message__t}{message\_t}}\ \&\&msg,\ send\_flags\ flags)}
\DoxyCodeLine{01907\ \ \ \ \ \{}
\DoxyCodeLine{01908\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ send(msg,\ flags);}
\DoxyCodeLine{01909\ \ \ \ \ \}}
\DoxyCodeLine{01910\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01911\ }
\DoxyCodeLine{01912\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(}
\DoxyCodeLine{01913\ \ \ \ \ \ \ \textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ recv\ taking\ a\ mutable\_buffer\ and\ recv\_flags"{}})}
\DoxyCodeLine{01914\ \ \ \ \ \textcolor{keywordtype}{size\_t}\ recv(\textcolor{keywordtype}{void}\ *buf\_,\ \textcolor{keywordtype}{size\_t}\ len\_,\ \textcolor{keywordtype}{int}\ flags\_\ =\ 0)}
\DoxyCodeLine{01915\ \ \ \ \ \{}
\DoxyCodeLine{01916\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ nbytes\ =\ zmq\_recv(\_handle,\ buf\_,\ len\_,\ flags\_);}
\DoxyCodeLine{01917\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (nbytes\ >=\ 0)}
\DoxyCodeLine{01918\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{size\_t}\textcolor{keyword}{>}(nbytes);}
\DoxyCodeLine{01919\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ EAGAIN)}
\DoxyCodeLine{01920\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ 0;}
\DoxyCodeLine{01921\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01922\ \ \ \ \ \}}
\DoxyCodeLine{01923\ }
\DoxyCodeLine{01924\ \ \ \ \ ZMQ\_CPP11\_DEPRECATED(}
\DoxyCodeLine{01925\ \ \ \ \ \ \ \textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ recv\ taking\ a\ reference\ to\ message\_t\ and\ recv\_flags"{}})}
\DoxyCodeLine{01926\ \ \ \ \ \textcolor{keywordtype}{bool}\ recv(\mbox{\hyperlink{classzmq_1_1message__t}{message\_t}}\ *msg\_,\ \textcolor{keywordtype}{int}\ flags\_\ =\ 0)}
\DoxyCodeLine{01927\ \ \ \ \ \{}
\DoxyCodeLine{01928\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ nbytes\ =\ zmq\_msg\_recv(msg\_-\/>handle(),\ \_handle,\ flags\_);}
\DoxyCodeLine{01929\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (nbytes\ >=\ 0)}
\DoxyCodeLine{01930\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{true};}
\DoxyCodeLine{01931\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ EAGAIN)}
\DoxyCodeLine{01932\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{false};}
\DoxyCodeLine{01933\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01934\ \ \ \ \ \}}
\DoxyCodeLine{01935\ }
\DoxyCodeLine{01936\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{01937\ \ \ \ \ ZMQ\_NODISCARD}
\DoxyCodeLine{01938\ \ \ \ \ recv\_buffer\_result\_t\ recv(mutable\_buffer\ buf,}
\DoxyCodeLine{01939\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ recv\_flags\ flags\ =\ recv\_flags::none)}
\DoxyCodeLine{01940\ \ \ \ \ \{}
\DoxyCodeLine{01941\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{int}\ nbytes\ =}
\DoxyCodeLine{01942\ \ \ \ \ \ \ \ \ \ \ zmq\_recv(\_handle,\ buf.data(),\ buf.size(),\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{int}\textcolor{keyword}{>}(flags));}
\DoxyCodeLine{01943\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (nbytes\ >=\ 0)\ \{}
\DoxyCodeLine{01944\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ recv\_buffer\_size\{}
\DoxyCodeLine{01945\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ (std::min)(\textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{size\_t}\textcolor{keyword}{>}(nbytes),\ buf.size()),}
\DoxyCodeLine{01946\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{size\_t}\textcolor{keyword}{>}(nbytes)\};}
\DoxyCodeLine{01947\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{01948\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ EAGAIN)}
\DoxyCodeLine{01949\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \{\};}
\DoxyCodeLine{01950\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01951\ \ \ \ \ \}}
\DoxyCodeLine{01952\ }
\DoxyCodeLine{01953\ \ \ \ \ ZMQ\_NODISCARD}
\DoxyCodeLine{01954\ \ \ \ \ recv\_result\_t\ recv(\mbox{\hyperlink{classzmq_1_1message__t}{message\_t}}\ \&msg,\ recv\_flags\ flags\ =\ recv\_flags::none)}
\DoxyCodeLine{01955\ \ \ \ \ \{}
\DoxyCodeLine{01956\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{int}\ nbytes\ =}
\DoxyCodeLine{01957\ \ \ \ \ \ \ \ \ \ \ zmq\_msg\_recv(msg.handle(),\ \_handle,\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{int}\textcolor{keyword}{>}(flags));}
\DoxyCodeLine{01958\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (nbytes\ >=\ 0)\ \{}
\DoxyCodeLine{01959\ \ \ \ \ \ \ \ \ \ \ \ \ assert(msg.size()\ ==\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{size\_t}\textcolor{keyword}{>}(nbytes));}
\DoxyCodeLine{01960\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{size\_t}\textcolor{keyword}{>}(nbytes);}
\DoxyCodeLine{01961\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{01962\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ EAGAIN)}
\DoxyCodeLine{01963\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \{\};}
\DoxyCodeLine{01964\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01965\ \ \ \ \ \}}
\DoxyCodeLine{01966\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01967\ }
\DoxyCodeLine{01968\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_BUILD\_DRAFT\_API)\ \&\&\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(4,\ 2,\ 0)}}
\DoxyCodeLine{01969\ \ \ \ \ \textcolor{keywordtype}{void}\ join(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *group)}
\DoxyCodeLine{01970\ \ \ \ \ \{}
\DoxyCodeLine{01971\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_join(\_handle,\ group);}
\DoxyCodeLine{01972\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{01973\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01974\ \ \ \ \ \}}
\DoxyCodeLine{01975\ }
\DoxyCodeLine{01976\ \ \ \ \ \textcolor{keywordtype}{void}\ leave(\textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *group)}
\DoxyCodeLine{01977\ \ \ \ \ \{}
\DoxyCodeLine{01978\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_leave(\_handle,\ group);}
\DoxyCodeLine{01979\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{01980\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{01981\ \ \ \ \ \}}
\DoxyCodeLine{01982\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{01983\ }
\DoxyCodeLine{01984\ \ \ \ \ ZMQ\_NODISCARD\ \textcolor{keywordtype}{void}\ *handle()\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ \_handle;\ \}}
\DoxyCodeLine{01985\ \ \ \ \ ZMQ\_NODISCARD\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *handle()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ \_handle;\ \}}
\DoxyCodeLine{01986\ }
\DoxyCodeLine{01987\ \ \ \ \ ZMQ\_EXPLICIT\ \textcolor{keyword}{operator}\ bool()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ \_handle\ !=\ ZMQ\_NULLPTR;\ \}}
\DoxyCodeLine{01988\ \ \ \ \ \textcolor{comment}{//\ note:\ non-\/const\ operator\ bool\ can\ be\ removed\ once}}
\DoxyCodeLine{01989\ \ \ \ \ \textcolor{comment}{//\ operator\ void*\ is\ removed\ from\ socket\_t}}
\DoxyCodeLine{01990\ \ \ \ \ ZMQ\_EXPLICIT\ \textcolor{keyword}{operator}\ bool()\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ \_handle\ !=\ ZMQ\_NULLPTR;\ \}}
\DoxyCodeLine{01991\ }
\DoxyCodeLine{01992\ \ \ \textcolor{keyword}{protected}:}
\DoxyCodeLine{01993\ \ \ \ \ \textcolor{keywordtype}{void}\ *\_handle;}
\DoxyCodeLine{01994\ }
\DoxyCodeLine{01995\ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{01996\ \ \ \ \ \textcolor{keywordtype}{void}\ set\_option(\textcolor{keywordtype}{int}\ option\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{void}\ *optval\_,\ \textcolor{keywordtype}{size\_t}\ optvallen\_)}
\DoxyCodeLine{01997\ \ \ \ \ \{}
\DoxyCodeLine{01998\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_setsockopt(\_handle,\ option\_,\ optval\_,\ optvallen\_);}
\DoxyCodeLine{01999\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{02000\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{02001\ \ \ \ \ \}}
\DoxyCodeLine{02002\ }
\DoxyCodeLine{02003\ \ \ \ \ \textcolor{keywordtype}{void}\ get\_option(\textcolor{keywordtype}{int}\ option\_,\ \textcolor{keywordtype}{void}\ *optval\_,\ \textcolor{keywordtype}{size\_t}\ *optvallen\_)\textcolor{keyword}{\ const}}
\DoxyCodeLine{02004\ \textcolor{keyword}{\ \ \ \ }\{}
\DoxyCodeLine{02005\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_getsockopt(\_handle,\ option\_,\ optval\_,\ optvallen\_);}
\DoxyCodeLine{02006\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{02007\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{02008\ \ \ \ \ \}}
\DoxyCodeLine{02009\ \};}
\DoxyCodeLine{02010\ \}\ \textcolor{comment}{//\ namespace\ detail}}
\DoxyCodeLine{02011\ }
\DoxyCodeLine{02012\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{02013\ \textcolor{keyword}{enum\ class}\ socket\_type\ :\ \textcolor{keywordtype}{int}}
\DoxyCodeLine{02014\ \{}
\DoxyCodeLine{02015\ \ \ \ \ req\ =\ ZMQ\_REQ,}
\DoxyCodeLine{02016\ \ \ \ \ rep\ =\ ZMQ\_REP,}
\DoxyCodeLine{02017\ \ \ \ \ dealer\ =\ ZMQ\_DEALER,}
\DoxyCodeLine{02018\ \ \ \ \ router\ =\ ZMQ\_ROUTER,}
\DoxyCodeLine{02019\ \ \ \ \ pub\ =\ ZMQ\_PUB,}
\DoxyCodeLine{02020\ \ \ \ \ sub\ =\ ZMQ\_SUB,}
\DoxyCodeLine{02021\ \ \ \ \ xpub\ =\ ZMQ\_XPUB,}
\DoxyCodeLine{02022\ \ \ \ \ xsub\ =\ ZMQ\_XSUB,}
\DoxyCodeLine{02023\ \ \ \ \ push\ =\ ZMQ\_PUSH,}
\DoxyCodeLine{02024\ \ \ \ \ pull\ =\ ZMQ\_PULL,}
\DoxyCodeLine{02025\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_BUILD\_DRAFT\_API)\ \&\&\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(4,\ 2,\ 0)}}
\DoxyCodeLine{02026\ \ \ \ \ server\ =\ ZMQ\_SERVER,}
\DoxyCodeLine{02027\ \ \ \ \ client\ =\ ZMQ\_CLIENT,}
\DoxyCodeLine{02028\ \ \ \ \ radio\ =\ ZMQ\_RADIO,}
\DoxyCodeLine{02029\ \ \ \ \ dish\ =\ ZMQ\_DISH,}
\DoxyCodeLine{02030\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02031\ \textcolor{preprocessor}{\#if\ ZMQ\_VERSION\_MAJOR\ >=\ 4}}
\DoxyCodeLine{02032\ \ \ \ \ stream\ =\ ZMQ\_STREAM,}
\DoxyCodeLine{02033\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02034\ \ \ \ \ pair\ =\ ZMQ\_PAIR}
\DoxyCodeLine{02035\ \};}
\DoxyCodeLine{02036\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02037\ }
\DoxyCodeLine{02038\ \textcolor{keyword}{struct\ }from\_handle\_t}
\DoxyCodeLine{02039\ \{}
\DoxyCodeLine{02040\ \ \ \ \ \textcolor{keyword}{struct\ }\mbox{\hyperlink{structzmq_1_1from__handle__t_1_1__private}{\_private}}}
\DoxyCodeLine{02041\ \ \ \ \ \{}
\DoxyCodeLine{02042\ \ \ \ \ \};\ \textcolor{comment}{//\ disabling\ use\ other\ than\ with\ from\_handle}}
\DoxyCodeLine{02043\ \ \ \ \ ZMQ\_CONSTEXPR\_FN\ ZMQ\_EXPLICIT\ from\_handle\_t(\mbox{\hyperlink{structzmq_1_1from__handle__t_1_1__private}{\_private}}\ \textcolor{comment}{/*p*/})\ ZMQ\_NOTHROW\ \{\}}
\DoxyCodeLine{02044\ \};}
\DoxyCodeLine{02045\ }
\DoxyCodeLine{02046\ ZMQ\_CONSTEXPR\_VAR\ from\_handle\_t\ from\_handle\ =}
\DoxyCodeLine{02047\ \ \ from\_handle\_t(from\_handle\_t::\_private());}
\DoxyCodeLine{02048\ }
\DoxyCodeLine{02049\ \textcolor{comment}{//\ A\ non-\/owning\ nullable\ reference\ to\ a\ socket.}}
\DoxyCodeLine{02050\ \textcolor{comment}{//\ The\ reference\ is\ invalidated\ on\ socket\ close\ or\ destruction.}}
\DoxyCodeLine{02051\ \textcolor{keyword}{class\ }socket\_ref\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{classzmq_1_1detail_1_1socket__base}{detail::socket\_base}}}
\DoxyCodeLine{02052\ \{}
\DoxyCodeLine{02053\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{02054\ \ \ \ \ socket\_ref()\ ZMQ\_NOTHROW\ :\ \mbox{\hyperlink{classzmq_1_1detail_1_1socket__base}{detail::socket\_base}}()\ \{\}}
\DoxyCodeLine{02055\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{02056\ \ \ \ \ socket\_ref(std::nullptr\_t)\ ZMQ\_NOTHROW\ :\ \mbox{\hyperlink{classzmq_1_1detail_1_1socket__base}{detail::socket\_base}}()\ \{\}}
\DoxyCodeLine{02057\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02058\ \ \ \ \ socket\_ref(\mbox{\hyperlink{structzmq_1_1from__handle__t}{from\_handle\_t}}\ \textcolor{comment}{/*fh*/},\ \textcolor{keywordtype}{void}\ *handle)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02059\ \ \ \ \ \ \ \ \ :\ \mbox{\hyperlink{classzmq_1_1detail_1_1socket__base}{detail::socket\_base}}(handle)}
\DoxyCodeLine{02060\ \ \ \ \ \{}
\DoxyCodeLine{02061\ \ \ \ \ \}}
\DoxyCodeLine{02062\ \};}
\DoxyCodeLine{02063\ }
\DoxyCodeLine{02064\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{02065\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{bool}\ operator==(\mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ sr,\ std::nullptr\_t\ \textcolor{comment}{/*p*/})\ ZMQ\_NOTHROW}
\DoxyCodeLine{02066\ \{}
\DoxyCodeLine{02067\ \ \ \ \ \textcolor{keywordflow}{return}\ sr.handle()\ ==\ \textcolor{keyword}{nullptr};}
\DoxyCodeLine{02068\ \}}
\DoxyCodeLine{02069\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{bool}\ operator==(std::nullptr\_t\ \textcolor{comment}{/*p*/},\ socket\_ref\ sr)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02070\ \{}
\DoxyCodeLine{02071\ \ \ \ \ \textcolor{keywordflow}{return}\ sr.handle()\ ==\ \textcolor{keyword}{nullptr};}
\DoxyCodeLine{02072\ \}}
\DoxyCodeLine{02073\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{bool}\ operator!=(\mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ sr,\ std::nullptr\_t\ \textcolor{comment}{/*p*/})\ ZMQ\_NOTHROW}
\DoxyCodeLine{02074\ \{}
\DoxyCodeLine{02075\ \ \ \ \ \textcolor{keywordflow}{return}\ !(sr\ ==\ \textcolor{keyword}{nullptr});}
\DoxyCodeLine{02076\ \}}
\DoxyCodeLine{02077\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{bool}\ operator!=(std::nullptr\_t\ \textcolor{comment}{/*p*/},\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ sr)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02078\ \{}
\DoxyCodeLine{02079\ \ \ \ \ \textcolor{keywordflow}{return}\ !(sr\ ==\ \textcolor{keyword}{nullptr});}
\DoxyCodeLine{02080\ \}}
\DoxyCodeLine{02081\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02082\ }
\DoxyCodeLine{02083\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{bool}\ operator==(\mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ a,\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ b)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02084\ \{}
\DoxyCodeLine{02085\ \ \ \ \ \textcolor{keywordflow}{return}\ std::equal\_to<void\ *>()(a.handle(),\ b.handle());}
\DoxyCodeLine{02086\ \}}
\DoxyCodeLine{02087\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{bool}\ operator!=(\mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ a,\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ b)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02088\ \{}
\DoxyCodeLine{02089\ \ \ \ \ \textcolor{keywordflow}{return}\ !(a\ ==\ b);}
\DoxyCodeLine{02090\ \}}
\DoxyCodeLine{02091\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{bool}\ operator<(\mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ a,\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ b)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02092\ \{}
\DoxyCodeLine{02093\ \ \ \ \ \textcolor{keywordflow}{return}\ std::less<void\ *>()(a.handle(),\ b.handle());}
\DoxyCodeLine{02094\ \}}
\DoxyCodeLine{02095\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{bool}\ operator>(\mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ a,\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ b)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02096\ \{}
\DoxyCodeLine{02097\ \ \ \ \ \textcolor{keywordflow}{return}\ b\ <\ a;}
\DoxyCodeLine{02098\ \}}
\DoxyCodeLine{02099\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{bool}\ operator<=(\mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ a,\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ b)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02100\ \{}
\DoxyCodeLine{02101\ \ \ \ \ \textcolor{keywordflow}{return}\ !(a\ >\ b);}
\DoxyCodeLine{02102\ \}}
\DoxyCodeLine{02103\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{bool}\ operator>=(\mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ a,\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ b)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02104\ \{}
\DoxyCodeLine{02105\ \ \ \ \ \textcolor{keywordflow}{return}\ !(a\ <\ b);}
\DoxyCodeLine{02106\ \}}
\DoxyCodeLine{02107\ }
\DoxyCodeLine{02108\ \}\ \textcolor{comment}{//\ namespace\ zmq}}
\DoxyCodeLine{02109\ }
\DoxyCodeLine{02110\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{02111\ \textcolor{keyword}{namespace\ }std}
\DoxyCodeLine{02112\ \{}
\DoxyCodeLine{02113\ \textcolor{keyword}{template}<>\ \textcolor{keyword}{struct\ }hash<zmq::socket\_ref>}
\DoxyCodeLine{02114\ \{}
\DoxyCodeLine{02115\ \ \ \ \ \textcolor{keywordtype}{size\_t}\ operator()(zmq::socket\_ref\ sr)\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW}
\DoxyCodeLine{02116\ \ \ \ \ \{}
\DoxyCodeLine{02117\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ hash<void\ *>()(sr.handle());}
\DoxyCodeLine{02118\ \ \ \ \ \}}
\DoxyCodeLine{02119\ \};}
\DoxyCodeLine{02120\ \}\ \textcolor{comment}{//\ namespace\ std}}
\DoxyCodeLine{02121\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02122\ }
\DoxyCodeLine{02123\ \textcolor{keyword}{namespace\ }zmq}
\DoxyCodeLine{02124\ \{}
\DoxyCodeLine{02125\ \textcolor{keyword}{class\ }socket\_t\ :\ \textcolor{keyword}{public}\ \mbox{\hyperlink{classzmq_1_1detail_1_1socket__base}{detail::socket\_base}}}
\DoxyCodeLine{02126\ \{}
\DoxyCodeLine{02127\ \ \ \ \ \textcolor{keyword}{friend}\ \textcolor{keyword}{class\ }monitor\_t;}
\DoxyCodeLine{02128\ }
\DoxyCodeLine{02129\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{02130\ \ \ \ \ socket\_t()\ ZMQ\_NOTHROW\ :\ \mbox{\hyperlink{classzmq_1_1detail_1_1socket__base}{detail::socket\_base}}(ZMQ\_NULLPTR),\ ctxptr(ZMQ\_NULLPTR)\ \{\}}
\DoxyCodeLine{02131\ }
\DoxyCodeLine{02132\ \ \ \ \ socket\_t(\mbox{\hyperlink{classzmq_1_1context__t}{context\_t}}\ \&context\_,\ \textcolor{keywordtype}{int}\ type\_)\ :}
\DoxyCodeLine{02133\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{classzmq_1_1detail_1_1socket__base}{detail::socket\_base}}(zmq\_socket(context\_.handle(),\ type\_)),}
\DoxyCodeLine{02134\ \ \ \ \ \ \ \ \ ctxptr(context\_.handle())}
\DoxyCodeLine{02135\ \ \ \ \ \{}
\DoxyCodeLine{02136\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (\_handle\ ==\ ZMQ\_NULLPTR)}
\DoxyCodeLine{02137\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{02138\ \ \ \ \ \}}
\DoxyCodeLine{02139\ }
\DoxyCodeLine{02140\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_CPP11}}
\DoxyCodeLine{02141\ \ \ \ \ socket\_t(\mbox{\hyperlink{classzmq_1_1context__t}{context\_t}}\ \&context\_,\ socket\_type\ type\_)\ :}
\DoxyCodeLine{02142\ \ \ \ \ \ \ \ \ socket\_t(context\_,\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{int}\textcolor{keyword}{>}(type\_))}
\DoxyCodeLine{02143\ \ \ \ \ \{}
\DoxyCodeLine{02144\ \ \ \ \ \}}
\DoxyCodeLine{02145\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02146\ }
\DoxyCodeLine{02147\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_HAS\_RVALUE\_REFS}}
\DoxyCodeLine{02148\ \ \ \ \ socket\_t(socket\_t\ \&\&rhs)\ ZMQ\_NOTHROW\ :\ \mbox{\hyperlink{classzmq_1_1detail_1_1socket__base}{detail::socket\_base}}(rhs.\_handle),}
\DoxyCodeLine{02149\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ ctxptr(rhs.ctxptr)}
\DoxyCodeLine{02150\ \ \ \ \ \{}
\DoxyCodeLine{02151\ \ \ \ \ \ \ \ \ rhs.\_handle\ =\ ZMQ\_NULLPTR;}
\DoxyCodeLine{02152\ \ \ \ \ \ \ \ \ rhs.ctxptr\ =\ ZMQ\_NULLPTR;}
\DoxyCodeLine{02153\ \ \ \ \ \}}
\DoxyCodeLine{02154\ \ \ \ \ socket\_t\ \&operator=(socket\_t\ \&\&rhs)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02155\ \ \ \ \ \{}
\DoxyCodeLine{02156\ \ \ \ \ \ \ \ \ close();}
\DoxyCodeLine{02157\ \ \ \ \ \ \ \ \ std::swap(\_handle,\ rhs.\_handle);}
\DoxyCodeLine{02158\ \ \ \ \ \ \ \ \ std::swap(ctxptr,\ rhs.ctxptr);}
\DoxyCodeLine{02159\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ *\textcolor{keyword}{this};}
\DoxyCodeLine{02160\ \ \ \ \ \}}
\DoxyCodeLine{02161\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02162\ }
\DoxyCodeLine{02163\ \ \ \ \ \string~socket\_t()\ ZMQ\_NOTHROW\ \{\ close();\ \}}
\DoxyCodeLine{02164\ }
\DoxyCodeLine{02165\ \ \ \ \ \textcolor{keyword}{operator}\ \textcolor{keywordtype}{void}\ *()\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ \_handle;\ \}}
\DoxyCodeLine{02166\ }
\DoxyCodeLine{02167\ \ \ \ \ \textcolor{keyword}{operator}\ \textcolor{keywordtype}{void}\ \textcolor{keyword}{const}\ *()\ \textcolor{keyword}{const}\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ \_handle;\ \}}
\DoxyCodeLine{02168\ }
\DoxyCodeLine{02169\ \ \ \ \ \textcolor{keywordtype}{void}\ close()\ ZMQ\_NOTHROW}
\DoxyCodeLine{02170\ \ \ \ \ \{}
\DoxyCodeLine{02171\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (\_handle\ ==\ ZMQ\_NULLPTR)}
\DoxyCodeLine{02172\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ already\ closed}}
\DoxyCodeLine{02173\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return};}
\DoxyCodeLine{02174\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_close(\_handle);}
\DoxyCodeLine{02175\ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{02176\ \ \ \ \ \ \ \ \ \_handle\ =\ ZMQ\_NULLPTR;}
\DoxyCodeLine{02177\ \ \ \ \ \ \ \ \ ctxptr\ =\ ZMQ\_NULLPTR;}
\DoxyCodeLine{02178\ \ \ \ \ \}}
\DoxyCodeLine{02179\ }
\DoxyCodeLine{02180\ \ \ \ \ \textcolor{keywordtype}{void}\ swap(socket\_t\ \&other)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02181\ \ \ \ \ \{}
\DoxyCodeLine{02182\ \ \ \ \ \ \ \ \ std::swap(\_handle,\ other.\_handle);}
\DoxyCodeLine{02183\ \ \ \ \ \ \ \ \ std::swap(ctxptr,\ other.ctxptr);}
\DoxyCodeLine{02184\ \ \ \ \ \}}
\DoxyCodeLine{02185\ }
\DoxyCodeLine{02186\ \ \ \ \ \textcolor{keyword}{operator}\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}()\ ZMQ\_NOTHROW\ \{\ \textcolor{keywordflow}{return}\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}(from\_handle,\ \_handle);\ \}}
\DoxyCodeLine{02187\ }
\DoxyCodeLine{02188\ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{02189\ \ \ \ \ \textcolor{keywordtype}{void}\ *ctxptr;}
\DoxyCodeLine{02190\ }
\DoxyCodeLine{02191\ \ \ \ \ socket\_t(\textcolor{keyword}{const}\ socket\_t\ \&)\ ZMQ\_DELETED\_FUNCTION;}
\DoxyCodeLine{02192\ \ \ \ \ \textcolor{keywordtype}{void}\ operator=(\textcolor{keyword}{const}\ socket\_t\ \&)\ ZMQ\_DELETED\_FUNCTION;}
\DoxyCodeLine{02193\ }
\DoxyCodeLine{02194\ \ \ \ \ \textcolor{comment}{//\ used\ by\ monitor\_t}}
\DoxyCodeLine{02195\ \ \ \ \ socket\_t(\textcolor{keywordtype}{void}\ *context\_,\ \textcolor{keywordtype}{int}\ type\_)\ :}
\DoxyCodeLine{02196\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{classzmq_1_1detail_1_1socket__base}{detail::socket\_base}}(zmq\_socket(context\_,\ type\_)),}
\DoxyCodeLine{02197\ \ \ \ \ \ \ \ \ ctxptr(context\_)}
\DoxyCodeLine{02198\ \ \ \ \ \{}
\DoxyCodeLine{02199\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (\_handle\ ==\ ZMQ\_NULLPTR)}
\DoxyCodeLine{02200\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{02201\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (ctxptr\ ==\ ZMQ\_NULLPTR)}
\DoxyCodeLine{02202\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{02203\ \ \ \ \ \}}
\DoxyCodeLine{02204\ \};}
\DoxyCodeLine{02205\ }
\DoxyCodeLine{02206\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{void}\ swap(\mbox{\hyperlink{classzmq_1_1socket__t}{socket\_t}}\ \&a,\ \mbox{\hyperlink{classzmq_1_1socket__t}{socket\_t}}\ \&b)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02207\ \{}
\DoxyCodeLine{02208\ \ \ \ \ a.swap(b);}
\DoxyCodeLine{02209\ \}}
\DoxyCodeLine{02210\ }
\DoxyCodeLine{02211\ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ proxy\ taking\ socket\_t\ objects"{}})}
\DoxyCodeLine{02212\ inline\ \textcolor{keywordtype}{void}\ proxy(\textcolor{keywordtype}{void}\ *frontend,\ \textcolor{keywordtype}{void}\ *backend,\ \textcolor{keywordtype}{void}\ *capture)}
\DoxyCodeLine{02213\ \{}
\DoxyCodeLine{02214\ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_proxy(frontend,\ backend,\ capture);}
\DoxyCodeLine{02215\ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{02216\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ error\_t();}
\DoxyCodeLine{02217\ \}}
\DoxyCodeLine{02218\ }
\DoxyCodeLine{02219\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{void}}
\DoxyCodeLine{02220\ proxy(\mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ frontend,\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ backend,\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ capture\ =\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}())}
\DoxyCodeLine{02221\ \{}
\DoxyCodeLine{02222\ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_proxy(frontend.handle(),\ backend.handle(),\ capture.handle());}
\DoxyCodeLine{02223\ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{02224\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{02225\ \}}
\DoxyCodeLine{02226\ }
\DoxyCodeLine{02227\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_HAS\_PROXY\_STEERABLE}}
\DoxyCodeLine{02228\ ZMQ\_DEPRECATED(\textcolor{stringliteral}{"{}from\ 4.3.1,\ use\ proxy\_steerable\ taking\ socket\_t\ objects"{}})}
\DoxyCodeLine{02229\ inline\ \textcolor{keywordtype}{void}}
\DoxyCodeLine{02230\ proxy\_steerable(\textcolor{keywordtype}{void}\ *frontend,\ \textcolor{keywordtype}{void}\ *backend,\ \textcolor{keywordtype}{void}\ *capture,\ \textcolor{keywordtype}{void}\ *control)}
\DoxyCodeLine{02231\ \{}
\DoxyCodeLine{02232\ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_proxy\_steerable(frontend,\ backend,\ capture,\ control);}
\DoxyCodeLine{02233\ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{02234\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{02235\ \}}
\DoxyCodeLine{02236\ }
\DoxyCodeLine{02237\ \textcolor{keyword}{inline}\ \textcolor{keywordtype}{void}\ proxy\_steerable(\mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ frontend,}
\DoxyCodeLine{02238\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ backend,}
\DoxyCodeLine{02239\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ capture,}
\DoxyCodeLine{02240\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ control)}
\DoxyCodeLine{02241\ \{}
\DoxyCodeLine{02242\ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_proxy\_steerable(frontend.handle(),\ backend.handle(),}
\DoxyCodeLine{02243\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ capture.handle(),\ control.handle());}
\DoxyCodeLine{02244\ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{02245\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{02246\ \}}
\DoxyCodeLine{02247\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02248\ }
\DoxyCodeLine{02249\ \textcolor{keyword}{class\ }monitor\_t}
\DoxyCodeLine{02250\ \{}
\DoxyCodeLine{02251\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{02252\ \ \ \ \ monitor\_t()\ :\ \_socket(),\ \_monitor\_socket()\ \{\}}
\DoxyCodeLine{02253\ }
\DoxyCodeLine{02254\ \ \ \ \ \textcolor{keyword}{virtual}\ \string~monitor\_t()\ \{\ close();\ \}}
\DoxyCodeLine{02255\ }
\DoxyCodeLine{02256\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_HAS\_RVALUE\_REFS}}
\DoxyCodeLine{02257\ \ \ \ \ monitor\_t(monitor\_t\ \&\&rhs)\ ZMQ\_NOTHROW\ :\ \_socket(),\ \_monitor\_socket()}
\DoxyCodeLine{02258\ \ \ \ \ \{}
\DoxyCodeLine{02259\ \ \ \ \ \ \ \ \ std::swap(\_socket,\ rhs.\_socket);}
\DoxyCodeLine{02260\ \ \ \ \ \ \ \ \ std::swap(\_monitor\_socket,\ rhs.\_monitor\_socket);}
\DoxyCodeLine{02261\ \ \ \ \ \}}
\DoxyCodeLine{02262\ }
\DoxyCodeLine{02263\ \ \ \ \ monitor\_t\ \&operator=(monitor\_t\ \&\&rhs)\ ZMQ\_NOTHROW}
\DoxyCodeLine{02264\ \ \ \ \ \{}
\DoxyCodeLine{02265\ \ \ \ \ \ \ \ \ close();}
\DoxyCodeLine{02266\ \ \ \ \ \ \ \ \ \_socket\ =\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}();}
\DoxyCodeLine{02267\ \ \ \ \ \ \ \ \ std::swap(\_socket,\ rhs.\_socket);}
\DoxyCodeLine{02268\ \ \ \ \ \ \ \ \ std::swap(\_monitor\_socket,\ rhs.\_monitor\_socket);}
\DoxyCodeLine{02269\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ *\textcolor{keyword}{this};}
\DoxyCodeLine{02270\ \ \ \ \ \}}
\DoxyCodeLine{02271\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02272\ }
\DoxyCodeLine{02273\ }
\DoxyCodeLine{02274\ \ \ \ \ \textcolor{keywordtype}{void}}
\DoxyCodeLine{02275\ \ \ \ \ monitor(\mbox{\hyperlink{classzmq_1_1socket__t}{socket\_t}}\ \&socket,\ std::string\ \textcolor{keyword}{const}\ \&addr,\ \textcolor{keywordtype}{int}\ events\ =\ ZMQ\_EVENT\_ALL)}
\DoxyCodeLine{02276\ \ \ \ \ \{}
\DoxyCodeLine{02277\ \ \ \ \ \ \ \ \ monitor(socket,\ addr.c\_str(),\ events);}
\DoxyCodeLine{02278\ \ \ \ \ \}}
\DoxyCodeLine{02279\ }
\DoxyCodeLine{02280\ \ \ \ \ \textcolor{keywordtype}{void}\ monitor(\mbox{\hyperlink{classzmq_1_1socket__t}{socket\_t}}\ \&socket,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_,\ \textcolor{keywordtype}{int}\ events\ =\ ZMQ\_EVENT\_ALL)}
\DoxyCodeLine{02281\ \ \ \ \ \{}
\DoxyCodeLine{02282\ \ \ \ \ \ \ \ \ init(socket,\ addr\_,\ events);}
\DoxyCodeLine{02283\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{while}\ (\textcolor{keyword}{true})\ \{}
\DoxyCodeLine{02284\ \ \ \ \ \ \ \ \ \ \ \ \ check\_event(-\/1);}
\DoxyCodeLine{02285\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{02286\ \ \ \ \ \}}
\DoxyCodeLine{02287\ }
\DoxyCodeLine{02288\ \ \ \ \ \textcolor{keywordtype}{void}\ init(\mbox{\hyperlink{classzmq_1_1socket__t}{socket\_t}}\ \&socket,\ std::string\ \textcolor{keyword}{const}\ \&addr,\ \textcolor{keywordtype}{int}\ events\ =\ ZMQ\_EVENT\_ALL)}
\DoxyCodeLine{02289\ \ \ \ \ \{}
\DoxyCodeLine{02290\ \ \ \ \ \ \ \ \ init(socket,\ addr.c\_str(),\ events);}
\DoxyCodeLine{02291\ \ \ \ \ \}}
\DoxyCodeLine{02292\ }
\DoxyCodeLine{02293\ \ \ \ \ \textcolor{keywordtype}{void}\ init(\mbox{\hyperlink{classzmq_1_1socket__t}{socket\_t}}\ \&socket,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_,\ \textcolor{keywordtype}{int}\ events\ =\ ZMQ\_EVENT\_ALL)}
\DoxyCodeLine{02294\ \ \ \ \ \{}
\DoxyCodeLine{02295\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_socket\_monitor(socket.handle(),\ addr\_,\ events);}
\DoxyCodeLine{02296\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ !=\ 0)}
\DoxyCodeLine{02297\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ \mbox{\hyperlink{classzmq_1_1error__t}{error\_t}}();}
\DoxyCodeLine{02298\ }
\DoxyCodeLine{02299\ \ \ \ \ \ \ \ \ \_socket\ =\ socket;}
\DoxyCodeLine{02300\ \ \ \ \ \ \ \ \ \_monitor\_socket\ =\ \mbox{\hyperlink{classzmq_1_1socket__t}{socket\_t}}(socket.ctxptr,\ ZMQ\_PAIR);}
\DoxyCodeLine{02301\ \ \ \ \ \ \ \ \ \_monitor\_socket.connect(addr\_);}
\DoxyCodeLine{02302\ }
\DoxyCodeLine{02303\ \ \ \ \ \ \ \ \ on\_monitor\_started();}
\DoxyCodeLine{02304\ \ \ \ \ \}}
\DoxyCodeLine{02305\ }
\DoxyCodeLine{02306\ \ \ \ \ \textcolor{keywordtype}{bool}\ check\_event(\textcolor{keywordtype}{int}\ timeout\ =\ 0)}
\DoxyCodeLine{02307\ \ \ \ \ \{}
\DoxyCodeLine{02308\ \ \ \ \ \ \ \ \ assert(\_monitor\_socket);}
\DoxyCodeLine{02309\ }
\DoxyCodeLine{02310\ \ \ \ \ \ \ \ \ zmq\_msg\_t\ eventMsg;}
\DoxyCodeLine{02311\ \ \ \ \ \ \ \ \ zmq\_msg\_init(\&eventMsg);}
\DoxyCodeLine{02312\ }
\DoxyCodeLine{02313\ \ \ \ \ \ \ \ \ zmq::pollitem\_t\ items[]\ =\ \{}
\DoxyCodeLine{02314\ \ \ \ \ \ \ \ \ \ \ \{\_monitor\_socket.handle(),\ 0,\ ZMQ\_POLLIN,\ 0\},}
\DoxyCodeLine{02315\ \ \ \ \ \ \ \ \ \};}
\DoxyCodeLine{02316\ }
\DoxyCodeLine{02317\ \ \ \ \ \ \ \ \ zmq::poll(\&items[0],\ 1,\ timeout);}
\DoxyCodeLine{02318\ }
\DoxyCodeLine{02319\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (items[0].revents\ \&\ ZMQ\_POLLIN)\ \{}
\DoxyCodeLine{02320\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_recv(\&eventMsg,\ \_monitor\_socket.handle(),\ 0);}
\DoxyCodeLine{02321\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ ==\ -\/1\ \&\&\ zmq\_errno()\ ==\ ETERM)}
\DoxyCodeLine{02322\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{false};}
\DoxyCodeLine{02323\ \ \ \ \ \ \ \ \ \ \ \ \ assert(rc\ !=\ -\/1);}
\DoxyCodeLine{02324\ }
\DoxyCodeLine{02325\ \ \ \ \ \ \ \ \ \}\ \textcolor{keywordflow}{else}\ \{}
\DoxyCodeLine{02326\ \ \ \ \ \ \ \ \ \ \ \ \ zmq\_msg\_close(\&eventMsg);}
\DoxyCodeLine{02327\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{false};}
\DoxyCodeLine{02328\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{02329\ }
\DoxyCodeLine{02330\ \textcolor{preprocessor}{\#if\ ZMQ\_VERSION\_MAJOR\ >=\ 4}}
\DoxyCodeLine{02331\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *data\ =\ \textcolor{keyword}{static\_cast<}\textcolor{keyword}{const\ }\textcolor{keywordtype}{char}\ *\textcolor{keyword}{>}(zmq\_msg\_data(\&eventMsg));}
\DoxyCodeLine{02332\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ msgEvent;}
\DoxyCodeLine{02333\ \ \ \ \ \ \ \ \ memcpy(\&msgEvent.event,\ data,\ \textcolor{keyword}{sizeof}(uint16\_t));}
\DoxyCodeLine{02334\ \ \ \ \ \ \ \ \ data\ +=\ \textcolor{keyword}{sizeof}(uint16\_t);}
\DoxyCodeLine{02335\ \ \ \ \ \ \ \ \ memcpy(\&msgEvent.value,\ data,\ \textcolor{keyword}{sizeof}(int32\_t));}
\DoxyCodeLine{02336\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ *\textcolor{keyword}{event}\ =\ \&msgEvent;}
\DoxyCodeLine{02337\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{02338\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ *\textcolor{keyword}{event}\ =\ \textcolor{keyword}{static\_cast<}\mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ *\textcolor{keyword}{>}(zmq\_msg\_data(\&eventMsg));}
\DoxyCodeLine{02339\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02340\ }
\DoxyCodeLine{02341\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_NEW\_MONITOR\_EVENT\_LAYOUT}}
\DoxyCodeLine{02342\ \ \ \ \ \ \ \ \ zmq\_msg\_t\ addrMsg;}
\DoxyCodeLine{02343\ \ \ \ \ \ \ \ \ zmq\_msg\_init(\&addrMsg);}
\DoxyCodeLine{02344\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_msg\_recv(\&addrMsg,\ \_monitor\_socket.handle(),\ 0);}
\DoxyCodeLine{02345\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ ==\ -\/1\ \&\&\ zmq\_errno()\ ==\ ETERM)\ \{}
\DoxyCodeLine{02346\ \ \ \ \ \ \ \ \ \ \ \ \ zmq\_msg\_close(\&eventMsg);}
\DoxyCodeLine{02347\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{false};}
\DoxyCodeLine{02348\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{02349\ }
\DoxyCodeLine{02350\ \ \ \ \ \ \ \ \ assert(rc\ !=\ -\/1);}
\DoxyCodeLine{02351\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *str\ =\ \textcolor{keyword}{static\_cast<}\textcolor{keyword}{const\ }\textcolor{keywordtype}{char}\ *\textcolor{keyword}{>}(zmq\_msg\_data(\&addrMsg));}
\DoxyCodeLine{02352\ \ \ \ \ \ \ \ \ std::string\ address(str,\ str\ +\ zmq\_msg\_size(\&addrMsg));}
\DoxyCodeLine{02353\ \ \ \ \ \ \ \ \ zmq\_msg\_close(\&addrMsg);}
\DoxyCodeLine{02354\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{02355\ \ \ \ \ \ \ \ \ \textcolor{comment}{//\ Bit\ of\ a\ hack,\ but\ all\ events\ in\ the\ zmq\_event\_t\ union\ have\ the\ same\ layout\ so\ this\ will\ work\ for\ all\ event\ types.}}
\DoxyCodeLine{02356\ \ \ \ \ \ \ \ \ std::string\ address\ =\ \textcolor{keyword}{event}-\/>data.connected.addr;}
\DoxyCodeLine{02357\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02358\ }
\DoxyCodeLine{02359\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_EVENT\_MONITOR\_STOPPED}}
\DoxyCodeLine{02360\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (event-\/>event\ ==\ ZMQ\_EVENT\_MONITOR\_STOPPED)\ \{}
\DoxyCodeLine{02361\ \ \ \ \ \ \ \ \ \ \ \ \ zmq\_msg\_close(\&eventMsg);}
\DoxyCodeLine{02362\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{false};}
\DoxyCodeLine{02363\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{02364\ }
\DoxyCodeLine{02365\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02366\ }
\DoxyCodeLine{02367\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{switch}\ (event-\/>event)\ \{}
\DoxyCodeLine{02368\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_CONNECTED:}
\DoxyCodeLine{02369\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_connected(*event,\ address.c\_str());}
\DoxyCodeLine{02370\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02371\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_CONNECT\_DELAYED:}
\DoxyCodeLine{02372\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_connect\_delayed(*event,\ address.c\_str());}
\DoxyCodeLine{02373\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02374\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_CONNECT\_RETRIED:}
\DoxyCodeLine{02375\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_connect\_retried(*event,\ address.c\_str());}
\DoxyCodeLine{02376\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02377\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_LISTENING:}
\DoxyCodeLine{02378\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_listening(*event,\ address.c\_str());}
\DoxyCodeLine{02379\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02380\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_BIND\_FAILED:}
\DoxyCodeLine{02381\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_bind\_failed(*event,\ address.c\_str());}
\DoxyCodeLine{02382\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02383\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_ACCEPTED:}
\DoxyCodeLine{02384\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_accepted(*event,\ address.c\_str());}
\DoxyCodeLine{02385\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02386\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_ACCEPT\_FAILED:}
\DoxyCodeLine{02387\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_accept\_failed(*event,\ address.c\_str());}
\DoxyCodeLine{02388\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02389\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_CLOSED:}
\DoxyCodeLine{02390\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_closed(*event,\ address.c\_str());}
\DoxyCodeLine{02391\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02392\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_CLOSE\_FAILED:}
\DoxyCodeLine{02393\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_close\_failed(*event,\ address.c\_str());}
\DoxyCodeLine{02394\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02395\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_DISCONNECTED:}
\DoxyCodeLine{02396\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_disconnected(*event,\ address.c\_str());}
\DoxyCodeLine{02397\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02398\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_BUILD\_DRAFT\_API}}
\DoxyCodeLine{02399\ \textcolor{preprocessor}{\#if\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(4,\ 2,\ 3)}}
\DoxyCodeLine{02400\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_HANDSHAKE\_FAILED\_NO\_DETAIL:}
\DoxyCodeLine{02401\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_handshake\_failed\_no\_detail(*event,\ address.c\_str());}
\DoxyCodeLine{02402\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02403\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_HANDSHAKE\_FAILED\_PROTOCOL:}
\DoxyCodeLine{02404\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_handshake\_failed\_protocol(*event,\ address.c\_str());}
\DoxyCodeLine{02405\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02406\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_HANDSHAKE\_FAILED\_AUTH:}
\DoxyCodeLine{02407\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_handshake\_failed\_auth(*event,\ address.c\_str());}
\DoxyCodeLine{02408\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02409\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_HANDSHAKE\_SUCCEEDED:}
\DoxyCodeLine{02410\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_handshake\_succeeded(*event,\ address.c\_str());}
\DoxyCodeLine{02411\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02412\ \textcolor{preprocessor}{\#elif\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(4,\ 2,\ 1)}}
\DoxyCodeLine{02413\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_HANDSHAKE\_FAILED:}
\DoxyCodeLine{02414\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_handshake\_failed(*event,\ address.c\_str());}
\DoxyCodeLine{02415\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02416\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{case}\ ZMQ\_EVENT\_HANDSHAKE\_SUCCEED:}
\DoxyCodeLine{02417\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_handshake\_succeed(*event,\ address.c\_str());}
\DoxyCodeLine{02418\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02419\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02420\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02421\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{default}:}
\DoxyCodeLine{02422\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ on\_event\_unknown(*event,\ address.c\_str());}
\DoxyCodeLine{02423\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{break};}
\DoxyCodeLine{02424\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{02425\ \ \ \ \ \ \ \ \ zmq\_msg\_close(\&eventMsg);}
\DoxyCodeLine{02426\ }
\DoxyCodeLine{02427\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{true};}
\DoxyCodeLine{02428\ \ \ \ \ \}}
\DoxyCodeLine{02429\ }
\DoxyCodeLine{02430\ \textcolor{preprocessor}{\#ifdef\ ZMQ\_EVENT\_MONITOR\_STOPPED}}
\DoxyCodeLine{02431\ \ \ \ \ \textcolor{keywordtype}{void}\ abort()}
\DoxyCodeLine{02432\ \ \ \ \ \{}
\DoxyCodeLine{02433\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (\_socket)}
\DoxyCodeLine{02434\ \ \ \ \ \ \ \ \ \ \ \ \ zmq\_socket\_monitor(\_socket.handle(),\ ZMQ\_NULLPTR,\ 0);}
\DoxyCodeLine{02435\ }
\DoxyCodeLine{02436\ \ \ \ \ \ \ \ \ \_socket\ =\ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}();}
\DoxyCodeLine{02437\ \ \ \ \ \}}
\DoxyCodeLine{02438\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02439\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_monitor\_started()\ \{\}}
\DoxyCodeLine{02440\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_connected(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02441\ \ \ \ \ \{}
\DoxyCodeLine{02442\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02443\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02444\ \ \ \ \ \}}
\DoxyCodeLine{02445\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_connect\_delayed(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,}
\DoxyCodeLine{02446\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02447\ \ \ \ \ \{}
\DoxyCodeLine{02448\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02449\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02450\ \ \ \ \ \}}
\DoxyCodeLine{02451\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_connect\_retried(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,}
\DoxyCodeLine{02452\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02453\ \ \ \ \ \{}
\DoxyCodeLine{02454\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02455\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02456\ \ \ \ \ \}}
\DoxyCodeLine{02457\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_listening(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02458\ \ \ \ \ \{}
\DoxyCodeLine{02459\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02460\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02461\ \ \ \ \ \}}
\DoxyCodeLine{02462\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_bind\_failed(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02463\ \ \ \ \ \{}
\DoxyCodeLine{02464\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02465\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02466\ \ \ \ \ \}}
\DoxyCodeLine{02467\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_accepted(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02468\ \ \ \ \ \{}
\DoxyCodeLine{02469\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02470\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02471\ \ \ \ \ \}}
\DoxyCodeLine{02472\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_accept\_failed(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02473\ \ \ \ \ \{}
\DoxyCodeLine{02474\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02475\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02476\ \ \ \ \ \}}
\DoxyCodeLine{02477\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_closed(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02478\ \ \ \ \ \{}
\DoxyCodeLine{02479\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02480\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02481\ \ \ \ \ \}}
\DoxyCodeLine{02482\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_close\_failed(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02483\ \ \ \ \ \{}
\DoxyCodeLine{02484\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02485\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02486\ \ \ \ \ \}}
\DoxyCodeLine{02487\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_disconnected(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02488\ \ \ \ \ \{}
\DoxyCodeLine{02489\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02490\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02491\ \ \ \ \ \}}
\DoxyCodeLine{02492\ \textcolor{preprocessor}{\#if\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(4,\ 2,\ 3)}}
\DoxyCodeLine{02493\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_handshake\_failed\_no\_detail(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,}
\DoxyCodeLine{02494\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02495\ \ \ \ \ \{}
\DoxyCodeLine{02496\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02497\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02498\ \ \ \ \ \}}
\DoxyCodeLine{02499\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_handshake\_failed\_protocol(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,}
\DoxyCodeLine{02500\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02501\ \ \ \ \ \{}
\DoxyCodeLine{02502\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02503\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02504\ \ \ \ \ \}}
\DoxyCodeLine{02505\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_handshake\_failed\_auth(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,}
\DoxyCodeLine{02506\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02507\ \ \ \ \ \{}
\DoxyCodeLine{02508\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02509\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02510\ \ \ \ \ \}}
\DoxyCodeLine{02511\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_handshake\_succeeded(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,}
\DoxyCodeLine{02512\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02513\ \ \ \ \ \{}
\DoxyCodeLine{02514\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02515\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02516\ \ \ \ \ \}}
\DoxyCodeLine{02517\ \textcolor{preprocessor}{\#elif\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(4,\ 2,\ 1)}}
\DoxyCodeLine{02518\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_handshake\_failed(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,}
\DoxyCodeLine{02519\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02520\ \ \ \ \ \{}
\DoxyCodeLine{02521\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02522\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02523\ \ \ \ \ \}}
\DoxyCodeLine{02524\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_handshake\_succeed(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,}
\DoxyCodeLine{02525\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02526\ \ \ \ \ \{}
\DoxyCodeLine{02527\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02528\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02529\ \ \ \ \ \}}
\DoxyCodeLine{02530\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02531\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ on\_event\_unknown(\textcolor{keyword}{const}\ \mbox{\hyperlink{structzmq__event__t}{zmq\_event\_t}}\ \&event\_,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{char}\ *addr\_)}
\DoxyCodeLine{02532\ \ \ \ \ \{}
\DoxyCodeLine{02533\ \ \ \ \ \ \ \ \ (void)\ event\_;}
\DoxyCodeLine{02534\ \ \ \ \ \ \ \ \ (void)\ addr\_;}
\DoxyCodeLine{02535\ \ \ \ \ \}}
\DoxyCodeLine{02536\ }
\DoxyCodeLine{02537\ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{02538\ \ \ \ \ monitor\_t(\textcolor{keyword}{const}\ monitor\_t\ \&)\ ZMQ\_DELETED\_FUNCTION;}
\DoxyCodeLine{02539\ \ \ \ \ \textcolor{keywordtype}{void}\ operator=(\textcolor{keyword}{const}\ monitor\_t\ \&)\ ZMQ\_DELETED\_FUNCTION;}
\DoxyCodeLine{02540\ }
\DoxyCodeLine{02541\ \ \ \ \ \mbox{\hyperlink{classzmq_1_1socket__ref}{socket\_ref}}\ \_socket;}
\DoxyCodeLine{02542\ \ \ \ \ \mbox{\hyperlink{classzmq_1_1socket__t}{socket\_t}}\ \_monitor\_socket;}
\DoxyCodeLine{02543\ }
\DoxyCodeLine{02544\ \ \ \ \ \textcolor{keywordtype}{void}\ close()\ ZMQ\_NOTHROW}
\DoxyCodeLine{02545\ \ \ \ \ \{}
\DoxyCodeLine{02546\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (\_socket)}
\DoxyCodeLine{02547\ \ \ \ \ \ \ \ \ \ \ \ \ zmq\_socket\_monitor(\_socket.handle(),\ ZMQ\_NULLPTR,\ 0);}
\DoxyCodeLine{02548\ \ \ \ \ \ \ \ \ \_monitor\_socket.close();}
\DoxyCodeLine{02549\ \ \ \ \ \}}
\DoxyCodeLine{02550\ \};}
\DoxyCodeLine{02551\ }
\DoxyCodeLine{02552\ \textcolor{preprocessor}{\#if\ defined(ZMQ\_BUILD\_DRAFT\_API)\ \&\&\ defined(ZMQ\_CPP11)\ \&\&\ defined(ZMQ\_HAVE\_POLLER)}}
\DoxyCodeLine{02553\ }
\DoxyCodeLine{02554\ \textcolor{comment}{//\ polling\ events}}
\DoxyCodeLine{02555\ \textcolor{keyword}{enum\ class}\ event\_flags\ :\ \textcolor{keywordtype}{short}}
\DoxyCodeLine{02556\ \{}
\DoxyCodeLine{02557\ \ \ \ \ none\ =\ 0,}
\DoxyCodeLine{02558\ \ \ \ \ pollin\ =\ ZMQ\_POLLIN,}
\DoxyCodeLine{02559\ \ \ \ \ pollout\ =\ ZMQ\_POLLOUT,}
\DoxyCodeLine{02560\ \ \ \ \ pollerr\ =\ ZMQ\_POLLERR,}
\DoxyCodeLine{02561\ \ \ \ \ pollpri\ =\ ZMQ\_POLLPRI}
\DoxyCodeLine{02562\ \};}
\DoxyCodeLine{02563\ }
\DoxyCodeLine{02564\ \textcolor{keyword}{constexpr}\ event\_flags\ operator|(event\_flags\ a,\ event\_flags\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{02565\ \{}
\DoxyCodeLine{02566\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_or(a,\ b);}
\DoxyCodeLine{02567\ \}}
\DoxyCodeLine{02568\ \textcolor{keyword}{constexpr}\ event\_flags\ operator\&(event\_flags\ a,\ event\_flags\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{02569\ \{}
\DoxyCodeLine{02570\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_and(a,\ b);}
\DoxyCodeLine{02571\ \}}
\DoxyCodeLine{02572\ \textcolor{keyword}{constexpr}\ event\_flags\ operator\string^(event\_flags\ a,\ event\_flags\ b)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{02573\ \{}
\DoxyCodeLine{02574\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_xor(a,\ b);}
\DoxyCodeLine{02575\ \}}
\DoxyCodeLine{02576\ \textcolor{keyword}{constexpr}\ event\_flags\ operator\string~(event\_flags\ a)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{02577\ \{}
\DoxyCodeLine{02578\ \ \ \ \ \textcolor{keywordflow}{return}\ detail::enum\_bit\_not(a);}
\DoxyCodeLine{02579\ \}}
\DoxyCodeLine{02580\ }
\DoxyCodeLine{02581\ \textcolor{keyword}{struct\ }no\_user\_data;}
\DoxyCodeLine{02582\ }
\DoxyCodeLine{02583\ \textcolor{comment}{//\ layout\ compatible\ with\ zmq\_poller\_event\_t}}
\DoxyCodeLine{02584\ \textcolor{keyword}{template}<\textcolor{keyword}{class}\ T\ =\ no\_user\_data>\ \textcolor{keyword}{struct\ }poller\_event}
\DoxyCodeLine{02585\ \{}
\DoxyCodeLine{02586\ \ \ \ \ socket\_ref\ socket;}
\DoxyCodeLine{02587\ \textcolor{preprocessor}{\#ifdef\ \_WIN32}}
\DoxyCodeLine{02588\ \ \ \ \ SOCKET\ fd;}
\DoxyCodeLine{02589\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{02590\ \ \ \ \ \textcolor{keywordtype}{int}\ fd;}
\DoxyCodeLine{02591\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02592\ \ \ \ \ T\ *user\_data;}
\DoxyCodeLine{02593\ \ \ \ \ event\_flags\ events;}
\DoxyCodeLine{02594\ \};}
\DoxyCodeLine{02595\ }
\DoxyCodeLine{02596\ \textcolor{keyword}{template}<\textcolor{keyword}{typename}\ T\ =\ no\_user\_data>\ \textcolor{keyword}{class\ }poller\_t}
\DoxyCodeLine{02597\ \{}
\DoxyCodeLine{02598\ \ \ \textcolor{keyword}{public}:}
\DoxyCodeLine{02599\ \ \ \ \ \textcolor{keyword}{using\ }event\_type\ =\ poller\_event<T>;}
\DoxyCodeLine{02600\ }
\DoxyCodeLine{02601\ \ \ \ \ poller\_t()\ :\ poller\_ptr(zmq\_poller\_new())}
\DoxyCodeLine{02602\ \ \ \ \ \{}
\DoxyCodeLine{02603\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (!poller\_ptr)}
\DoxyCodeLine{02604\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ error\_t();}
\DoxyCodeLine{02605\ \ \ \ \ \}}
\DoxyCodeLine{02606\ }
\DoxyCodeLine{02607\ \ \ \ \ \textcolor{keyword}{template}<}
\DoxyCodeLine{02608\ \ \ \ \ \ \ \textcolor{keyword}{typename}\ Dummy\ =\ void,}
\DoxyCodeLine{02609\ \ \ \ \ \ \ \textcolor{keyword}{typename}\ =}
\DoxyCodeLine{02610\ \ \ \ \ \ \ \ \ \textcolor{keyword}{typename}\ std::enable\_if<!std::is\_same<T,\ no\_user\_data>::value,\ Dummy>::type>}
\DoxyCodeLine{02611\ \ \ \ \ \textcolor{keywordtype}{void}\ add(zmq::socket\_ref\ socket,\ event\_flags\ events,\ T\ *user\_data)}
\DoxyCodeLine{02612\ \ \ \ \ \{}
\DoxyCodeLine{02613\ \ \ \ \ \ \ \ \ add\_impl(socket,\ events,\ user\_data);}
\DoxyCodeLine{02614\ \ \ \ \ \}}
\DoxyCodeLine{02615\ }
\DoxyCodeLine{02616\ \ \ \ \ \textcolor{keywordtype}{void}\ add(zmq::socket\_ref\ socket,\ event\_flags\ events)}
\DoxyCodeLine{02617\ \ \ \ \ \{}
\DoxyCodeLine{02618\ \ \ \ \ \ \ \ \ add\_impl(socket,\ events,\ \textcolor{keyword}{nullptr});}
\DoxyCodeLine{02619\ \ \ \ \ \}}
\DoxyCodeLine{02620\ }
\DoxyCodeLine{02621\ \ \ \ \ \textcolor{keywordtype}{void}\ remove(zmq::socket\_ref\ socket)}
\DoxyCodeLine{02622\ \ \ \ \ \{}
\DoxyCodeLine{02623\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (0\ !=\ zmq\_poller\_remove(poller\_ptr.get(),\ socket.handle()))\ \{}
\DoxyCodeLine{02624\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ error\_t();}
\DoxyCodeLine{02625\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{02626\ \ \ \ \ \}}
\DoxyCodeLine{02627\ }
\DoxyCodeLine{02628\ \ \ \ \ \textcolor{keywordtype}{void}\ modify(zmq::socket\_ref\ socket,\ event\_flags\ events)}
\DoxyCodeLine{02629\ \ \ \ \ \{}
\DoxyCodeLine{02630\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (0}
\DoxyCodeLine{02631\ \ \ \ \ \ \ \ \ \ \ \ \ !=\ zmq\_poller\_modify(poller\_ptr.get(),\ socket.handle(),}
\DoxyCodeLine{02632\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{short}\textcolor{keyword}{>}(events)))\ \{}
\DoxyCodeLine{02633\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ error\_t();}
\DoxyCodeLine{02634\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{02635\ \ \ \ \ \}}
\DoxyCodeLine{02636\ }
\DoxyCodeLine{02637\ \ \ \ \ \textcolor{keywordtype}{size\_t}\ wait\_all(std::vector<event\_type>\ \&poller\_events,}
\DoxyCodeLine{02638\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ std::chrono::milliseconds\ timeout)}
\DoxyCodeLine{02639\ \ \ \ \ \{}
\DoxyCodeLine{02640\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_poller\_wait\_all(}
\DoxyCodeLine{02641\ \ \ \ \ \ \ \ \ \ \ poller\_ptr.get(),}
\DoxyCodeLine{02642\ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{reinterpret\_cast<}zmq\_poller\_event\_t\ *\textcolor{keyword}{>}(poller\_events.data()),}
\DoxyCodeLine{02643\ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{int}\textcolor{keyword}{>}(poller\_events.size()),}
\DoxyCodeLine{02644\ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{long}\textcolor{keyword}{>}(timeout.count()));}
\DoxyCodeLine{02645\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (rc\ >\ 0)}
\DoxyCodeLine{02646\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{size\_t}\textcolor{keyword}{>}(rc);}
\DoxyCodeLine{02647\ }
\DoxyCodeLine{02648\ \textcolor{preprocessor}{\#if\ ZMQ\_VERSION\ >=\ ZMQ\_MAKE\_VERSION(4,\ 2,\ 3)}}
\DoxyCodeLine{02649\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ EAGAIN)}
\DoxyCodeLine{02650\ \textcolor{preprocessor}{\#else}}
\DoxyCodeLine{02651\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (zmq\_errno()\ ==\ ETIMEDOUT)}
\DoxyCodeLine{02652\ \textcolor{preprocessor}{\#endif}}
\DoxyCodeLine{02653\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{return}\ 0;}
\DoxyCodeLine{02654\ }
\DoxyCodeLine{02655\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ error\_t();}
\DoxyCodeLine{02656\ \ \ \ \ \}}
\DoxyCodeLine{02657\ }
\DoxyCodeLine{02658\ \ \ \textcolor{keyword}{private}:}
\DoxyCodeLine{02659\ \ \ \ \ \textcolor{keyword}{struct\ }destroy\_poller\_t}
\DoxyCodeLine{02660\ \ \ \ \ \{}
\DoxyCodeLine{02661\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{void}\ operator()(\textcolor{keywordtype}{void}\ *ptr)\ \textcolor{keyword}{noexcept}}
\DoxyCodeLine{02662\ \ \ \ \ \ \ \ \ \{}
\DoxyCodeLine{02663\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{int}\ rc\ =\ zmq\_poller\_destroy(\&ptr);}
\DoxyCodeLine{02664\ \ \ \ \ \ \ \ \ \ \ \ \ ZMQ\_ASSERT(rc\ ==\ 0);}
\DoxyCodeLine{02665\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{02666\ \ \ \ \ \};}
\DoxyCodeLine{02667\ }
\DoxyCodeLine{02668\ \ \ \ \ std::unique\_ptr<void,\ destroy\_poller\_t>\ poller\_ptr;}
\DoxyCodeLine{02669\ }
\DoxyCodeLine{02670\ \ \ \ \ \textcolor{keywordtype}{void}\ add\_impl(zmq::socket\_ref\ socket,\ event\_flags\ events,\ T\ *user\_data)}
\DoxyCodeLine{02671\ \ \ \ \ \{}
\DoxyCodeLine{02672\ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{if}\ (0}
\DoxyCodeLine{02673\ \ \ \ \ \ \ \ \ \ \ \ \ !=\ zmq\_poller\_add(poller\_ptr.get(),\ socket.handle(),\ user\_data,}
\DoxyCodeLine{02674\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keyword}{static\_cast<}\textcolor{keywordtype}{short}\textcolor{keyword}{>}(events)))\ \{}
\DoxyCodeLine{02675\ \ \ \ \ \ \ \ \ \ \ \ \ \textcolor{keywordflow}{throw}\ error\_t();}
\DoxyCodeLine{02676\ \ \ \ \ \ \ \ \ \}}
\DoxyCodeLine{02677\ \ \ \ \ \}}
\DoxyCodeLine{02678\ \};}
\DoxyCodeLine{02679\ \textcolor{preprocessor}{\#endif\ }\textcolor{comment}{//\ \ defined(ZMQ\_BUILD\_DRAFT\_API)\ \&\&\ defined(ZMQ\_CPP11)\ \&\&\ defined(ZMQ\_HAVE\_POLLER)}}
\DoxyCodeLine{02680\ }
\DoxyCodeLine{02681\ \textcolor{keyword}{inline}\ std::ostream\ \&operator<<(std::ostream\ \&os,\ \textcolor{keyword}{const}\ \mbox{\hyperlink{classzmq_1_1message__t}{message\_t}}\ \&msg)}
\DoxyCodeLine{02682\ \{}
\DoxyCodeLine{02683\ \ \ \ \ \textcolor{keywordflow}{return}\ os\ <<\ msg.str();}
\DoxyCodeLine{02684\ \}}
\DoxyCodeLine{02685\ }
\DoxyCodeLine{02686\ \}\ \textcolor{comment}{//\ namespace\ zmq}}
\DoxyCodeLine{02687\ }
\DoxyCodeLine{02688\ \textcolor{preprocessor}{\#endif\ }\textcolor{comment}{//\ \_\_ZMQ\_HPP\_INCLUDED\_\_}}

\end{DoxyCode}
