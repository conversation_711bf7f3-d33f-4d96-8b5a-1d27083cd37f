\doxysection{Bp\+Sec\+Bundle\+Processor.\+h}
\hypertarget{_bp_sec_bundle_processor_8h_source}{}\label{_bp_sec_bundle_processor_8h_source}\index{common/bpsec/include/BpSecBundleProcessor.h@{common/bpsec/include/BpSecBundleProcessor.h}}
\mbox{\hyperlink{_bp_sec_bundle_processor_8h}{Go to the documentation of this file.}}
\begin{DoxyCode}{0}
\DoxyCodeLine{00001\ }
\DoxyCodeLine{00023\ }
\DoxyCodeLine{00024\ }
\DoxyCodeLine{00025\ \textcolor{preprocessor}{\#ifndef\ BPSEC\_BUNDLE\_PROCESSOR\_H}}
\DoxyCodeLine{00026\ \textcolor{preprocessor}{\#define\ BPSEC\_BUNDLE\_PROCESSOR\_H\ 1}}
\DoxyCodeLine{00027\ }
\DoxyCodeLine{00028\ \textcolor{preprocessor}{\#include\ <string>}}
\DoxyCodeLine{00029\ \textcolor{preprocessor}{\#include\ <memory>}}
\DoxyCodeLine{00030\ \textcolor{preprocessor}{\#include\ <cstdint>}}
\DoxyCodeLine{00031\ \textcolor{preprocessor}{\#include\ <vector>}}
\DoxyCodeLine{00032\ \textcolor{preprocessor}{\#include\ <forward\_list>}}
\DoxyCodeLine{00033\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_bundle_view_v7_8h}{codec/BundleViewV7.h}}"{}}}
\DoxyCodeLine{00034\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{bpv7_8h}{codec/bpv7.h}}"{}}}
\DoxyCodeLine{00035\ \textcolor{preprocessor}{\#include\ "{}bpsec\_lib\_export.h"{}}}
\DoxyCodeLine{00036\ }
\DoxyCodeLine{00037\ \textcolor{keyword}{class\ }BpSecBundleProcessor\ \{}
\DoxyCodeLine{00038\ \ \ \ \ BpSecBundleProcessor()\ =\ \textcolor{keyword}{delete};}
\DoxyCodeLine{00039\ \textcolor{keyword}{public}:}
\DoxyCodeLine{00040\ \ \ \ \ \textcolor{keyword}{struct\ }EvpCipherCtxWrapper\ \{}
\DoxyCodeLine{00041\ \ \ \ \ \ \ \ \ BPSEC\_LIB\_EXPORT\ EvpCipherCtxWrapper();}
\DoxyCodeLine{00042\ \ \ \ \ \ \ \ \ BPSEC\_LIB\_EXPORT\ \string~EvpCipherCtxWrapper();}
\DoxyCodeLine{00044\ \ \ \ \ \ \ \ \ \textcolor{keyword}{struct\ }\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper_1_1_impl}{Impl}};}
\DoxyCodeLine{00046\ \ \ \ \ \ \ \ \ std::unique\_ptr<Impl>\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper_a490f7d0309a9528506a16387b6fcaa1c}{m\_pimpl}};}
\DoxyCodeLine{00047\ \ \ \ \ \};}
\DoxyCodeLine{00048\ \ \ \ \ \textcolor{keyword}{struct\ }HmacCtxWrapper\ \{}
\DoxyCodeLine{00049\ \ \ \ \ \ \ \ \ BPSEC\_LIB\_EXPORT\ HmacCtxWrapper();}
\DoxyCodeLine{00050\ \ \ \ \ \ \ \ \ BPSEC\_LIB\_EXPORT\ \string~HmacCtxWrapper();}
\DoxyCodeLine{00052\ \ \ \ \ \ \ \ \ \textcolor{keyword}{struct\ }\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_hmac_ctx_wrapper_1_1_impl}{Impl}};}
\DoxyCodeLine{00054\ \ \ \ \ \ \ \ \ std::unique\_ptr<Impl>\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_hmac_ctx_wrapper_a6d3969306bcccaf61ffe3af069de7c67}{m\_pimpl}};}
\DoxyCodeLine{00055\ \ \ \ \ \};}
\DoxyCodeLine{00056\ \ \ \ \ \textcolor{keyword}{struct\ }\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_reusable_elements_internal}{ReusableElementsInternal}}\ \{}
\DoxyCodeLine{00057\ \ \ \ \ \ \ \ \ std::vector<boost::asio::const\_buffer>\ constBufferVec;\ \textcolor{comment}{//aadParts\ and\ ipptParts}}
\DoxyCodeLine{00058\ \ \ \ \ \ \ \ \ std::vector<uint8\_t>\ verifyOnlyDecryptionTemporaryMemory;\ \textcolor{comment}{//will\ grow\ to\ max\ bundle\ size\ received\ if\ verify\ enabled}}
\DoxyCodeLine{00059\ \ \ \ \ \};}
\DoxyCodeLine{00060\ \ \ \ \ \textcolor{keyword}{enum\ class}\ BPSEC\_ERROR\_CODES\ :\ uint8\_t\ \{}
\DoxyCodeLine{00061\ \ \ \ \ \ \ \ \ CORRUPTED\ =\ 0,}
\DoxyCodeLine{00062\ \ \ \ \ \ \ \ \ MISCONFIGURED,}
\DoxyCodeLine{00063\ \ \ \ \ \ \ \ \ MISSING}
\DoxyCodeLine{00064\ \ \ \ \ \};}
\DoxyCodeLine{00065\ \ \ \ \ \textcolor{keyword}{struct\ }BpSecError\ \{}
\DoxyCodeLine{00066\ \ \ \ \ \ \ \ \ BpSecError()\ =\ \textcolor{keyword}{delete};}
\DoxyCodeLine{00067\ \ \ \ \ \ \ \ \ BpSecError(\textcolor{keyword}{const}\ BPSEC\_ERROR\_CODES\ ec,\ uint64\_t\ securityTargetIndex,\ std::unique\_ptr<std::string>\&\&\ es)\ :}
\DoxyCodeLine{00068\ \ \ \ \ \ \ \ \ \ \ \ \ m\_errorCode(ec),\ m\_securityTargetIndex(securityTargetIndex),\ m\_errorStringPtr(std::move(es))\ \{\}}
\DoxyCodeLine{00069\ \ \ \ \ \ \ \ \ BPSEC\_ERROR\_CODES\ m\_errorCode;}
\DoxyCodeLine{00070\ \ \ \ \ \ \ \ \ uint64\_t\ m\_securityTargetIndex;}
\DoxyCodeLine{00071\ \ \ \ \ \ \ \ \ std::unique\_ptr<std::string>\ m\_errorStringPtr;}
\DoxyCodeLine{00072\ \ \ \ \ \};}
\DoxyCodeLine{00073\ \ \ \ \ \textcolor{keyword}{typedef}\ std::forward\_list<BpSecError>\ BpSecErrorFlist;}
\DoxyCodeLine{00074\ \ \ \ \ BPSEC\_LIB\_EXPORT\ \textcolor{keyword}{static}\ std::string\ ErrorListToString(\textcolor{keyword}{const}\ BpSecErrorFlist\&\ errorList);}
\DoxyCodeLine{00075\ \ \ \ \ \textcolor{keyword}{struct\ }\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_integrity_received_parameters}{IntegrityReceivedParameters}}\ \{}
\DoxyCodeLine{00077\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_integrity_received_parameters_a5b39212d7820a7ed3db826878802b7c4}{keyEncryptionKey}};}
\DoxyCodeLine{00079\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_integrity_received_parameters_a89e349476ae5c2e0a6154969234c0da5}{keyEncryptionKeyLength}};\ }
\DoxyCodeLine{00080\ }
\DoxyCodeLine{00082\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_integrity_received_parameters_a7747d9d154bb8fa26294801c7c393ae5}{hmacKey}};}
\DoxyCodeLine{00084\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_integrity_received_parameters_a6cc9d2c109015e0ff0a0f9d41b6de551}{hmacKeyLength}};}
\DoxyCodeLine{00085\ }
\DoxyCodeLine{00087\ \ \ \ \ \ \ \ \ COSE\_ALGORITHMS\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_integrity_received_parameters_aeae84f3d1afb63ed1bffa41f6113b1ca}{expectedVariant}};}
\DoxyCodeLine{00089\ \ \ \ \ \ \ \ \ BPSEC\_BIB\_HMAC\_SHA2\_INTEGRITY\_SCOPE\_MASKS\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_integrity_received_parameters_a0ff1ee1c32a8ab8c10bca284b4aeeb2f}{expectedScopeMask}};}
\DoxyCodeLine{00093\ \ \ \ \ \ \ \ \ uint64\_t\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_integrity_received_parameters_a92c39b0dee143eb264dc51eeadec91cc}{expectedTargetBlockTypesMask}};}
\DoxyCodeLine{00094\ \ \ \ \ \};}
\DoxyCodeLine{00095\ \ \ \ \ \textcolor{keyword}{struct\ }\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_confidentiality_received_parameters}{ConfidentialityReceivedParameters}}\ \{}
\DoxyCodeLine{00100\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_confidentiality_received_parameters_a2414dae583270f6eea38f7ec369d42b5}{keyEncryptionKey}};}
\DoxyCodeLine{00102\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_confidentiality_received_parameters_af98fa6d43b7d54b08efc95b1b274fb5b}{keyEncryptionKeyLength}};}
\DoxyCodeLine{00103\ }
\DoxyCodeLine{00106\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_confidentiality_received_parameters_ace75a16911780c51e013f641a3529e3a}{dataEncryptionKey}};}
\DoxyCodeLine{00108\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_confidentiality_received_parameters_a03c0e4b01d85e85fed47f2a11238a293}{dataEncryptionKeyLength}};}
\DoxyCodeLine{00109\ }
\DoxyCodeLine{00112\ \ \ \ \ \ \ \ \ uint8\_t\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_confidentiality_received_parameters_ad70fef54f89cdf1f75960110e5ef9553}{expectedIvLength}};}
\DoxyCodeLine{00114\ \ \ \ \ \ \ \ \ COSE\_ALGORITHMS\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_confidentiality_received_parameters_ab3ec905cffe6f3a1e4646ba0f4c2b5e9}{expectedVariant}};}
\DoxyCodeLine{00117\ \ \ \ \ \ \ \ \ BPSEC\_BCB\_AES\_GCM\_AAD\_SCOPE\_MASKS\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_confidentiality_received_parameters_a7ae6c7c596806ffc786436cdace97d54}{expectedAadScopeMask}};}
\DoxyCodeLine{00121\ \ \ \ \ \ \ \ \ uint64\_t\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_confidentiality_received_parameters_a109427f643342183ac0c212fb452a930}{expectedTargetBlockTypesMask}};}
\DoxyCodeLine{00122\ \ \ \ \ \};}
\DoxyCodeLine{00123\ \ \ \ \ }
\DoxyCodeLine{00137\ \ \ \ \ BPSEC\_LIB\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_bp_sec_bundle_processor_a4b680a024e893a789e5f813b7d49155d}{HmacSha}}(\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_hmac_ctx_wrapper}{HmacCtxWrapper}}\&\ ctxWrapper,}
\DoxyCodeLine{00138\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ COSE\_ALGORITHMS\ variant,}
\DoxyCodeLine{00139\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ std::vector<boost::asio::const\_buffer>\&\ ipptParts,}
\DoxyCodeLine{00140\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ key,\ \textcolor{keyword}{const}\ uint64\_t\ keyLength,}
\DoxyCodeLine{00141\ \ \ \ \ \ \ \ \ uint8\_t*\ messageDigestOut,\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\&\ messageDigestOutSize);}
\DoxyCodeLine{00142\ }
\DoxyCodeLine{00143\ \ \ \ \ }
\DoxyCodeLine{00161\ \ \ \ \ BPSEC\_LIB\_EXPORT\ \textcolor{keyword}{static}\ BpSecErrorFlist\ \mbox{\hyperlink{class_bp_sec_bundle_processor_aa90331ee62608ba27e5981c6fac4bae0}{TryVerifyBundleIntegrityByIndividualBib}}(\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_hmac_ctx_wrapper}{HmacCtxWrapper}}\&\ ctxWrapper,}
\DoxyCodeLine{00162\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper}{EvpCipherCtxWrapper}}\&\ ctxWrapperForKeyUnwrap,}
\DoxyCodeLine{00163\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{class_bundle_view_v7}{BundleViewV7}}\&\ bv,}
\DoxyCodeLine{00164\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_bundle_view_v7_1_1_bpv7_canonical_block_view}{BundleViewV7::Bpv7CanonicalBlockView}}\&\ bibBlockView,}
\DoxyCodeLine{00165\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_integrity_received_parameters}{IntegrityReceivedParameters}}\&\ integrityReceivedParameters,}
\DoxyCodeLine{00166\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_reusable_elements_internal}{ReusableElementsInternal}}\&\ reusableElementsInternal,}
\DoxyCodeLine{00167\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ markBibForDeletion);}
\DoxyCodeLine{00168\ }
\DoxyCodeLine{00193\ \ \ \ \ BPSEC\_LIB\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_bp_sec_bundle_processor_a94601cc6c5b3887c949c60fc94fc1126}{TryAddBundleIntegrity}}(\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_hmac_ctx_wrapper}{HmacCtxWrapper}}\&\ ctxWrapper,}
\DoxyCodeLine{00194\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper}{EvpCipherCtxWrapper}}\&\ ctxWrapperForKeyWrap,}
\DoxyCodeLine{00195\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{class_bundle_view_v7}{BundleViewV7}}\&\ bv,}
\DoxyCodeLine{00196\ \ \ \ \ \ \ \ \ BPSEC\_BIB\_HMAC\_SHA2\_INTEGRITY\_SCOPE\_MASKS\ integrityScopeMask,}
\DoxyCodeLine{00197\ \ \ \ \ \ \ \ \ COSE\_ALGORITHMS\ variant,}
\DoxyCodeLine{00198\ \ \ \ \ \ \ \ \ BPV7\_CRC\_TYPE\ bibCrcType,}
\DoxyCodeLine{00199\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \mbox{\hyperlink{structcbhe__eid__t}{cbhe\_eid\_t}}\&\ securitySource,}
\DoxyCodeLine{00200\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t*\ targetBlockNumbers,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ numTargetBlockNumbers,}
\DoxyCodeLine{00201\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ keyEncryptionKey,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ keyEncryptionKeyLength,\ \textcolor{comment}{//NULL\ if\ not\ present\ (for\ unwrapping\ hmac\ key\ only)}}
\DoxyCodeLine{00202\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ hmacKey,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ hmacKeyLength,\ \textcolor{comment}{//NULL\ if\ not\ present\ (when\ no\ wrapped\ key\ is\ present)}}
\DoxyCodeLine{00203\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_reusable_elements_internal}{ReusableElementsInternal}}\&\ reusableElementsInternal,}
\DoxyCodeLine{00204\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t*\ insertBibBeforeThisBlockNumberIfNotNull,}
\DoxyCodeLine{00205\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ renderInPlaceWhenFinished);}
\DoxyCodeLine{00206\ }
\DoxyCodeLine{00224\ \ \ \ \ BPSEC\_LIB\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_bp_sec_bundle_processor_aecfb29c842d1135cbb4f9ecab888f3ff}{AesGcmEncrypt}}(\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper}{EvpCipherCtxWrapper}}\&\ ctxWrapper,}
\DoxyCodeLine{00225\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ unencryptedData,\ \textcolor{keyword}{const}\ uint64\_t\ unencryptedDataLength,}
\DoxyCodeLine{00226\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ key,\ \textcolor{keyword}{const}\ uint64\_t\ keyLength,}
\DoxyCodeLine{00227\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ iv,\ \textcolor{keyword}{const}\ uint64\_t\ ivLength,}
\DoxyCodeLine{00228\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ std::vector<boost::asio::const\_buffer>\&\ aadParts,}
\DoxyCodeLine{00229\ \ \ \ \ \ \ \ \ uint8\_t*\ cipherTextOut,\ uint64\_t\&\ cipherTextOutSize,\ uint8\_t*\ tagOut);}
\DoxyCodeLine{00230\ }
\DoxyCodeLine{00249\ \ \ \ \ BPSEC\_LIB\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_bp_sec_bundle_processor_af0db7c90728deaccb154ae40d3c9e70d}{AesGcmDecrypt}}(\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper}{EvpCipherCtxWrapper}}\&\ ctxWrapper,}
\DoxyCodeLine{00250\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ encryptedData,\ \textcolor{keyword}{const}\ uint64\_t\ encryptedDataLength,}
\DoxyCodeLine{00251\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ key,\ \textcolor{keyword}{const}\ uint64\_t\ keyLength,}
\DoxyCodeLine{00252\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ iv,\ \textcolor{keyword}{const}\ uint64\_t\ ivLength,}
\DoxyCodeLine{00253\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ std::vector<boost::asio::const\_buffer>\&\ aadParts,}
\DoxyCodeLine{00254\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ tag,}
\DoxyCodeLine{00255\ \ \ \ \ \ \ \ \ uint8\_t*\ decryptedDataOut,\ uint64\_t\&\ decryptedDataOutSize);}
\DoxyCodeLine{00256\ }
\DoxyCodeLine{00269\ \ \ \ \ BPSEC\_LIB\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_bp_sec_bundle_processor_a8bbe50080064bb652581c56a5a8da335}{AesWrapKey}}(\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper}{EvpCipherCtxWrapper}}\&\ ctxWrapper,}
\DoxyCodeLine{00270\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ keyEncryptionKey,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ keyEncryptionKeyLength,}
\DoxyCodeLine{00271\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ keyToWrap,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ keyToWrapLength,}
\DoxyCodeLine{00272\ \ \ \ \ \ \ \ \ uint8\_t*\ wrappedKeyOut,\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\&\ wrappedKeyOutSize);}
\DoxyCodeLine{00273\ }
\DoxyCodeLine{00286\ \ \ \ \ BPSEC\_LIB\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_bp_sec_bundle_processor_ad8918388b59855cfcc789cd5c74f83f1}{AesUnwrapKey}}(\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper}{EvpCipherCtxWrapper}}\&\ ctxWrapper,}
\DoxyCodeLine{00287\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ keyEncryptionKey,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ keyEncryptionKeyLength,}
\DoxyCodeLine{00288\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ keyToUnwrap,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ keyToUnwrapLength,}
\DoxyCodeLine{00289\ \ \ \ \ \ \ \ \ uint8\_t*\ unwrappedKeyOut,\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\&\ unwrappedKeyOutSize);}
\DoxyCodeLine{00290\ }
\DoxyCodeLine{00307\ \ \ \ \ BPSEC\_LIB\_EXPORT\ \textcolor{keyword}{static}\ BpSecErrorFlist\ \mbox{\hyperlink{class_bp_sec_bundle_processor_aeb036dd7c08006767755e7c3a7a4674e}{TryDecryptBundleByIndividualBcb}}(\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper}{EvpCipherCtxWrapper}}\&\ ctxWrapper,}
\DoxyCodeLine{00308\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper}{EvpCipherCtxWrapper}}\&\ ctxWrapperForKeyUnwrap,}
\DoxyCodeLine{00309\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{class_bundle_view_v7}{BundleViewV7}}\&\ bv,}
\DoxyCodeLine{00310\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_bundle_view_v7_1_1_bpv7_canonical_block_view}{BundleViewV7::Bpv7CanonicalBlockView}}\&\ bcbBlockView,}
\DoxyCodeLine{00311\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_confidentiality_received_parameters}{ConfidentialityReceivedParameters}}\&\ confidentialityReceivedParameters,}
\DoxyCodeLine{00312\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_reusable_elements_internal}{ReusableElementsInternal}}\&\ reusableElementsInternal,}
\DoxyCodeLine{00313\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ verifyOnly);}
\DoxyCodeLine{00314\ }
\DoxyCodeLine{00342\ \ \ \ \ BPSEC\_LIB\_EXPORT\ \textcolor{keyword}{static}\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_bp_sec_bundle_processor_a8b46badbd37602cfc03a1d89b47b5daa}{TryEncryptBundle}}(\mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper}{EvpCipherCtxWrapper}}\&\ ctxWrapper,}
\DoxyCodeLine{00343\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_evp_cipher_ctx_wrapper}{EvpCipherCtxWrapper}}\&\ ctxWrapperForKeyWrap,}
\DoxyCodeLine{00344\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{class_bundle_view_v7}{BundleViewV7}}\&\ bv,}
\DoxyCodeLine{00345\ \ \ \ \ \ \ \ \ BPSEC\_BCB\_AES\_GCM\_AAD\_SCOPE\_MASKS\ aadScopeMask,}
\DoxyCodeLine{00346\ \ \ \ \ \ \ \ \ COSE\_ALGORITHMS\ aesVariant,}
\DoxyCodeLine{00347\ \ \ \ \ \ \ \ \ BPV7\_CRC\_TYPE\ bcbCrcType,}
\DoxyCodeLine{00348\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \mbox{\hyperlink{structcbhe__eid__t}{cbhe\_eid\_t}}\&\ securitySource,}
\DoxyCodeLine{00349\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t*\ targetBlockNumbers,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ numTargetBlockNumbers,}
\DoxyCodeLine{00350\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ iv,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ ivLength,}
\DoxyCodeLine{00351\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ keyEncryptionKey,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ keyEncryptionKeyLength,}
\DoxyCodeLine{00352\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t*\ dataEncryptionKey,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ dataEncryptionKeyLength,}
\DoxyCodeLine{00353\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_bp_sec_bundle_processor_1_1_reusable_elements_internal}{ReusableElementsInternal}}\&\ reusableElementsInternal,}
\DoxyCodeLine{00354\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t*\ insertBcbBeforeThisBlockNumberIfNotNull,}
\DoxyCodeLine{00355\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ renderInPlaceWhenFinished);}
\DoxyCodeLine{00356\ }
\DoxyCodeLine{00357\ \};}
\DoxyCodeLine{00358\ }
\DoxyCodeLine{00359\ }
\DoxyCodeLine{00360\ \textcolor{preprocessor}{\#endif\ }\textcolor{comment}{//\ BPSEC\_BUNDLE\_PROCESSOR\_H}}
\DoxyCodeLine{00361\ }

\end{DoxyCode}
