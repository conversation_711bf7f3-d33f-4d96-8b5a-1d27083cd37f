\doxysection{Ltp\+Engine.\+h}
\hypertarget{_ltp_engine_8h_source}{}\label{_ltp_engine_8h_source}\index{common/ltp/include/LtpEngine.h@{common/ltp/include/LtpEngine.h}}
\mbox{\hyperlink{_ltp_engine_8h}{Go to the documentation of this file.}}
\begin{DoxyCode}{0}
\DoxyCodeLine{00001\ }
\DoxyCodeLine{00018\ }
\DoxyCodeLine{00019\ \textcolor{preprocessor}{\#ifndef\ LTP\_ENGINE\_H}}
\DoxyCodeLine{00020\ \textcolor{preprocessor}{\#define\ LTP\_ENGINE\_H\ 1}}
\DoxyCodeLine{00021\ }
\DoxyCodeLine{00022\ \textcolor{preprocessor}{\#include\ <boost/thread.hpp>}}
\DoxyCodeLine{00023\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_ltp_fragment_set_8h}{LtpFragmentSet.h}}"{}}}
\DoxyCodeLine{00024\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_ltp_8h}{Ltp.h}}"{}}}
\DoxyCodeLine{00025\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_ltp_random_number_generator_8h}{LtpRandomNumberGenerator.h}}"{}}}
\DoxyCodeLine{00026\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_ltp_session_receiver_8h}{LtpSessionReceiver.h}}"{}}}
\DoxyCodeLine{00027\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_ltp_session_sender_8h}{LtpSessionSender.h}}"{}}}
\DoxyCodeLine{00028\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_ltp_notices_to_client_service_8h}{LtpNoticesToClientService.h}}"{}}}
\DoxyCodeLine{00029\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_ltp_client_service_data_to_send_8h}{LtpClientServiceDataToSend.h}}"{}}}
\DoxyCodeLine{00030\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_ltp_session_recreation_preventer_8h}{LtpSessionRecreationPreventer.h}}"{}}}
\DoxyCodeLine{00031\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_ltp_engine_config_8h}{LtpEngineConfig.h}}"{}}}
\DoxyCodeLine{00032\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_token_rate_limiter_8h}{TokenRateLimiter.h}}"{}}}
\DoxyCodeLine{00033\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_bundle_callback_function_defines_8h}{BundleCallbackFunctionDefines.h}}"{}}}
\DoxyCodeLine{00034\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_memory_in_files_8h}{MemoryInFiles.h}}"{}}}
\DoxyCodeLine{00035\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_free_list_allocator_8h}{FreeListAllocator.h}}"{}}}
\DoxyCodeLine{00036\ \textcolor{preprocessor}{\#include\ <unordered\_map>}}
\DoxyCodeLine{00037\ \textcolor{preprocessor}{\#include\ <queue>}}
\DoxyCodeLine{00038\ \textcolor{preprocessor}{\#include\ <memory>}}
\DoxyCodeLine{00039\ \textcolor{preprocessor}{\#include\ <atomic>}}
\DoxyCodeLine{00040\ \textcolor{preprocessor}{\#include\ <boost/core/noncopyable.hpp>}}
\DoxyCodeLine{00041\ }
\DoxyCodeLine{00042\ \textcolor{keyword}{class\ }CLASS\_VISIBILITY\_LTP\_LIB\ LtpEngine\ :\ \textcolor{keyword}{private}\ boost::noncopyable\ \{}
\DoxyCodeLine{00043\ \textcolor{keyword}{private}:}
\DoxyCodeLine{00044\ \ \ \ \ LtpEngine()\ =\ \textcolor{keyword}{delete};}
\DoxyCodeLine{00045\ \textcolor{keyword}{public}:}
\DoxyCodeLine{00047\ \ \ \ \ \textcolor{keyword}{struct\ }\mbox{\hyperlink{struct_ltp_engine_1_1transmission__request__t}{transmission\_request\_t}}\ \{}
\DoxyCodeLine{00049\ \ \ \ \ \ \ \ \ uint64\_t\ \mbox{\hyperlink{struct_ltp_engine_1_1transmission__request__t_a8f70c9efc70c9b15e4639841dbd0c78f}{destinationClientServiceId}};}
\DoxyCodeLine{00051\ \ \ \ \ \ \ \ \ uint64\_t\ \mbox{\hyperlink{struct_ltp_engine_1_1transmission__request__t_ad9fa7f388dcc58dfcc0e2e54a56b9248}{destinationLtpEngineId}};}
\DoxyCodeLine{00053\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{class_ltp_client_service_data_to_send}{LtpClientServiceDataToSend}}\ \mbox{\hyperlink{struct_ltp_engine_1_1transmission__request__t_a061f387a65eeb49da56fe077b69368c3}{clientServiceDataToSend}};}
\DoxyCodeLine{00055\ \ \ \ \ \ \ \ \ uint64\_t\ \mbox{\hyperlink{struct_ltp_engine_1_1transmission__request__t_aa7a6667b813dfb840d4d9cba616b7a7c}{lengthOfRedPart}};}
\DoxyCodeLine{00057\ \ \ \ \ \ \ \ \ std::shared\_ptr<LtpTransmissionRequestUserData>\ \mbox{\hyperlink{struct_ltp_engine_1_1transmission__request__t_a0ec8d28e7749004596a54a67759e876c}{userDataPtr}};}
\DoxyCodeLine{00058\ \ \ \ \ \};}
\DoxyCodeLine{00059\ \ \ \ \ }
\DoxyCodeLine{00061\ \ \ \ \ \textcolor{keyword}{struct\ }\mbox{\hyperlink{struct_ltp_engine_1_1cancel__segment__timer__info__t}{cancel\_segment\_timer\_info\_t}}\ \{}
\DoxyCodeLine{00063\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \mbox{\hyperlink{struct_ltp_engine_1_1cancel__segment__timer__info__t_ad758bcf58e4189b9d7ed2abfc14d777d}{sessionId}};}
\DoxyCodeLine{00065\ \ \ \ \ \ \ \ \ CANCEL\_SEGMENT\_REASON\_CODES\ \mbox{\hyperlink{struct_ltp_engine_1_1cancel__segment__timer__info__t_ab354a045a6862fc07ebfb97ad40b3ed6}{reasonCode}};}
\DoxyCodeLine{00067\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{struct_ltp_engine_1_1cancel__segment__timer__info__t_acba210741ef75141c5d3414411ceccbd}{isFromSender}};}
\DoxyCodeLine{00069\ \ \ \ \ \ \ \ \ uint8\_t\ \mbox{\hyperlink{struct_ltp_engine_1_1cancel__segment__timer__info__t_ab51b814ca0c8c56c1f2dc7811b46b776}{retryCount}};}
\DoxyCodeLine{00070\ \ \ \ \ \};}
\DoxyCodeLine{00071\ \ \ \ \ }
\DoxyCodeLine{00084\ \ \ \ \ LTP\_LIB\_EXPORT\ LtpEngine(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_engine_config}{LtpEngineConfig}}\&\ ltpRxOrTxCfg,\ \textcolor{keyword}{const}\ uint8\_t\ engineIndexForEncodingIntoRandomSessionNumber,\ \textcolor{keywordtype}{bool}\ startIoServiceThread);}
\DoxyCodeLine{00085\ }
\DoxyCodeLine{00090\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ \string~LtpEngine();}
\DoxyCodeLine{00091\ }
\DoxyCodeLine{00100\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a97feb8f9afbd72c0059d3962a528582b}{Reset}}();}
\DoxyCodeLine{00101\ \ \ \ \ }
\DoxyCodeLine{00107\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a971d1bd8b790709bd4bf23b0dc796046}{SetCheckpointEveryNthDataPacketForSenders}}(uint64\_t\ checkpointEveryNthDataPacketSender);}
\DoxyCodeLine{00108\ \ \ \ \ }
\DoxyCodeLine{00113\ \ \ \ \ LTP\_LIB\_EXPORT\ uint8\_t\ \mbox{\hyperlink{class_ltp_engine_a91161ddb66cf94e6803426cedf8947fa}{GetEngineIndex}}();}
\DoxyCodeLine{00114\ }
\DoxyCodeLine{00122\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a246dc8848d370f2e0a84c566df062ef4}{TransmissionRequest}}(std::shared\_ptr<transmission\_request\_t>\ \&\ transmissionRequest);}
\DoxyCodeLine{00123\ \ \ \ \ }
\DoxyCodeLine{00130\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a95e0dd55d54d86bea09c180b5f8d4888}{TransmissionRequest\_ThreadSafe}}(std::shared\_ptr<transmission\_request\_t>\ \&\&\ transmissionRequest);}
\DoxyCodeLine{00131\ \ \ \ \ }
\DoxyCodeLine{00147\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a246dc8848d370f2e0a84c566df062ef4}{TransmissionRequest}}(uint64\_t\ destinationClientServiceId,\ uint64\_t\ destinationLtpEngineId,}
\DoxyCodeLine{00148\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{class_ltp_client_service_data_to_send}{LtpClientServiceDataToSend}}\ \&\&\ clientServiceDataToSend,\ std::shared\_ptr<LtpTransmissionRequestUserData>\ \&\&\ userDataPtrToTake,\ uint64\_t\ lengthOfRedPart);}
\DoxyCodeLine{00149\ \ \ \ \ }
\DoxyCodeLine{00164\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a246dc8848d370f2e0a84c566df062ef4}{TransmissionRequest}}(uint64\_t\ destinationClientServiceId,\ uint64\_t\ destinationLtpEngineId,}
\DoxyCodeLine{00165\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t\ *\ clientServiceDataToCopyAndSend,\ uint64\_t\ length,\ std::shared\_ptr<LtpTransmissionRequestUserData>\ \&\&\ userDataPtrToTake,\ uint64\_t\ lengthOfRedPart);}
\DoxyCodeLine{00166\ \ \ \ \ }
\DoxyCodeLine{00179\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a246dc8848d370f2e0a84c566df062ef4}{TransmissionRequest}}(uint64\_t\ destinationClientServiceId,\ uint64\_t\ destinationLtpEngineId,}
\DoxyCodeLine{00180\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint8\_t\ *\ clientServiceDataToCopyAndSend,\ uint64\_t\ length,\ uint64\_t\ lengthOfRedPart);}
\DoxyCodeLine{00181\ \textcolor{keyword}{private}:}
\DoxyCodeLine{00196\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ DoTransmissionRequest(uint64\_t\ destinationClientServiceId,\ uint64\_t\ destinationLtpEngineId,}
\DoxyCodeLine{00197\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{class_ltp_client_service_data_to_send}{LtpClientServiceDataToSend}}\&\&\ clientServiceDataToSend,\ std::shared\_ptr<LtpTransmissionRequestUserData>\&\&\ userDataPtrToTake,\ uint64\_t\ lengthOfRedPart,\ uint64\_t\ memoryBlockId);}
\DoxyCodeLine{00198\ \ \ \ \ }
\DoxyCodeLine{00217\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ OnTransmissionRequestDataWrittenToDisk(uint64\_t\ destinationClientServiceId,\ uint64\_t\ destinationLtpEngineId,}
\DoxyCodeLine{00218\ \ \ \ \ \ \ \ \ std::shared\_ptr<LtpClientServiceDataToSend>\&\ clientServiceDataToSendPtrToTake,\ std::shared\_ptr<LtpTransmissionRequestUserData>\&\ userDataPtrToTake,}
\DoxyCodeLine{00219\ \ \ \ \ \ \ \ \ uint64\_t\ lengthOfRedPart,\ uint64\_t\ memoryBlockId);}
\DoxyCodeLine{00220\ \textcolor{keyword}{public}:}
\DoxyCodeLine{00236\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_ltp_engine_ad7394c450f2e6bdd6a97742e81f4143d}{CancellationRequest}}(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId);}
\DoxyCodeLine{00237\ \ \ \ \ }
\DoxyCodeLine{00243\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a28480f01282f829dfbeb364524dca2d1}{CancellationRequest\_ThreadSafe}}(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId);}
\DoxyCodeLine{00244\ \ \ \ \ }
\DoxyCodeLine{00249\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a5603105c2cfda6253b35c1506cb3bd8d}{SetSessionStartCallback}}(\textcolor{keyword}{const}\ SessionStartCallback\_t\ \&\ callback);}
\DoxyCodeLine{00250\ \ \ \ \ }
\DoxyCodeLine{00255\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a31312b9da9693a7a76438219626f38b9}{SetRedPartReceptionCallback}}(\textcolor{keyword}{const}\ RedPartReceptionCallback\_t\ \&\ callback);}
\DoxyCodeLine{00256\ \ \ \ \ }
\DoxyCodeLine{00261\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_ab61ea92dfeba438b2e4f3c4f455c98d3}{SetGreenPartSegmentArrivalCallback}}(\textcolor{keyword}{const}\ GreenPartSegmentArrivalCallback\_t\ \&\ callback);}
\DoxyCodeLine{00262\ \ \ \ \ }
\DoxyCodeLine{00267\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a773bd49c8ec82f41967225938184eac4}{SetReceptionSessionCancelledCallback}}(\textcolor{keyword}{const}\ ReceptionSessionCancelledCallback\_t\ \&\ callback);}
\DoxyCodeLine{00268\ \ \ \ \ }
\DoxyCodeLine{00273\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_ae88cb57be73e00ba89646165f5f520ab}{SetTransmissionSessionCompletedCallback}}(\textcolor{keyword}{const}\ TransmissionSessionCompletedCallback\_t\ \&\ callback);}
\DoxyCodeLine{00274\ \ \ \ \ }
\DoxyCodeLine{00279\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_ad8b1d868d0906fb5994a40a9081a5473}{SetInitialTransmissionCompletedCallback}}(\textcolor{keyword}{const}\ InitialTransmissionCompletedCallback\_t\ \&\ callback);}
\DoxyCodeLine{00280\ \ \ \ \ }
\DoxyCodeLine{00285\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_ada18dcd3df9b464a01d8194b1ebc49fe}{SetTransmissionSessionCancelledCallback}}(\textcolor{keyword}{const}\ TransmissionSessionCancelledCallback\_t\ \&\ callback);}
\DoxyCodeLine{00286\ }
\DoxyCodeLine{00291\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a821b8b38c306d58fbb95d0502805ab7a}{SetOnFailedBundleVecSendCallback}}(\textcolor{keyword}{const}\ OnFailedBundleVecSendCallback\_t\&\ callback);}
\DoxyCodeLine{00292\ \ \ \ \ }
\DoxyCodeLine{00297\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a59076af529936255e27971570f105400}{SetOnFailedBundleZmqSendCallback}}(\textcolor{keyword}{const}\ OnFailedBundleZmqSendCallback\_t\&\ callback);}
\DoxyCodeLine{00298\ \ \ \ \ }
\DoxyCodeLine{00303\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a6e5d18cc85f6d3922e7e6886c9e1286a}{SetOnSuccessfulBundleSendCallback}}(\textcolor{keyword}{const}\ OnSuccessfulBundleSendCallback\_t\&\ callback);}
\DoxyCodeLine{00304\ \ \ \ \ }
\DoxyCodeLine{00309\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_affa6969c0c3a53ea679816f3ae78ead6}{SetOnOutductLinkStatusChangedCallback}}(\textcolor{keyword}{const}\ OnOutductLinkStatusChangedCallback\_t\&\ callback);}
\DoxyCodeLine{00310\ \ \ \ \ }
\DoxyCodeLine{00315\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a8ad4bbecb6c499f4f5c17a39956a2060}{SetUserAssignedUuid}}(uint64\_t\ userAssignedUuid);}
\DoxyCodeLine{00316\ \ \ \ \ }
\DoxyCodeLine{00333\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_ltp_engine_a64bb0494968816f34b5306f2a45c74e5}{PacketIn}}(\textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ isLastChunkOfPacket,\ \textcolor{keyword}{const}\ uint8\_t\ *\ data,\ \textcolor{keyword}{const}\ std::size\_t\ size,}
\DoxyCodeLine{00334\ \ \ \ \ \ \ \ \ Ltp::SessionOriginatorEngineIdDecodedCallback\_t\ *\ sessionOriginatorEngineIdDecodedCallbackPtr\ =\ NULL);}
\DoxyCodeLine{00335\ \ \ \ }
\DoxyCodeLine{00343\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_ltp_engine_a64bb0494968816f34b5306f2a45c74e5}{PacketIn}}(\textcolor{keyword}{const}\ std::vector<boost::asio::const\_buffer>\ \&\ constBufferVec);\ \textcolor{comment}{//for\ testing}}
\DoxyCodeLine{00344\ }
\DoxyCodeLine{00352\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_ab32abf93dbdfbf50911440699941d721}{PacketIn\_ThreadSafe}}(\textcolor{keyword}{const}\ uint8\_t\ *\ data,\ \textcolor{keyword}{const}\ std::size\_t\ size,\ Ltp::SessionOriginatorEngineIdDecodedCallback\_t\ *\ sessionOriginatorEngineIdDecodedCallbackPtr\ =\ NULL);}
\DoxyCodeLine{00353\ \ \ \ \ }
\DoxyCodeLine{00359\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_ab32abf93dbdfbf50911440699941d721}{PacketIn\_ThreadSafe}}(\textcolor{keyword}{const}\ std::vector<boost::asio::const\_buffer>\ \&\ constBufferVec);\ \textcolor{comment}{//for\ testing}}
\DoxyCodeLine{00360\ }
\DoxyCodeLine{00365\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_aa8b227e7e66f397cc811d23e1d323ce1}{PostExternalLinkDownEvent\_ThreadSafe}}();}
\DoxyCodeLine{00366\ \ \ \ \ }
\DoxyCodeLine{00436\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{bool}\ \mbox{\hyperlink{class_ltp_engine_ac85ce5836f8fc5186fc5eb9c8eab7b60}{GetNextPacketToSend}}(\mbox{\hyperlink{struct_udp_send_packet_info}{UdpSendPacketInfo}}\&\ udpSendPacketInfo);}
\DoxyCodeLine{00437\ }
\DoxyCodeLine{00442\ \ \ \ \ LTP\_LIB\_EXPORT\ std::size\_t\ \mbox{\hyperlink{class_ltp_engine_a527be4fce2ec201f40f2c3199ff803e5}{NumActiveReceivers}}()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00443\ \ \ \ \ }
\DoxyCodeLine{00448\ \ \ \ \ LTP\_LIB\_EXPORT\ std::size\_t\ \mbox{\hyperlink{class_ltp_engine_a0d9a84917c557db610ef934fb05c5cb9}{NumActiveSenders}}()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00449\ \ \ \ \ }
\DoxyCodeLine{00454\ \ \ \ \ LTP\_LIB\_EXPORT\ uint64\_t\ \mbox{\hyperlink{class_ltp_engine_a4d92e87ddbec73364195aa96ff26943d}{GetMaxNumberOfSessionsInPipeline}}()\ \textcolor{keyword}{const};}
\DoxyCodeLine{00455\ \ \ \ \ }
\DoxyCodeLine{00461\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_aeef2662c62f7d21d6fec951aa6984d53}{SetRate\_ThreadSafe}}(\textcolor{keyword}{const}\ uint64\_t\ maxSendRateBitsPerSecOrZeroToDisable);}
\DoxyCodeLine{00462\ }
\DoxyCodeLine{00468\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a1359f7d61a41b77bed11301de8f3abcc}{SetPing\_ThreadSafe}}(\textcolor{keyword}{const}\ uint64\_t\ senderPingSecondsOrZeroToDisable);}
\DoxyCodeLine{00469\ }
\DoxyCodeLine{00472\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_ad6d546634c7f6b4161d384c623c0aec4}{SetPingToDefaultConfig\_ThreadSafe}}();}
\DoxyCodeLine{00473\ \ \ \ \ }
\DoxyCodeLine{00481\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a3071c64c76d53530278ca8e4ed2d2020}{SetDelays\_ThreadSafe}}(\textcolor{keyword}{const}\ boost::posix\_time::time\_duration\&\ oneWayLightTime,\ \textcolor{keyword}{const}\ boost::posix\_time::time\_duration\&\ oneWayMarginTime,\ \textcolor{keywordtype}{bool}\ updateRunningTimers);}
\DoxyCodeLine{00482\ \ \ \ \ }
\DoxyCodeLine{00489\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_af8583ec3de26586c59f69bf1bf539528}{SetDeferDelays\_ThreadSafe}}(\textcolor{keyword}{const}\ uint64\_t\ delaySendingOfReportSegmentsTimeMsOrZeroToDisable,\ \textcolor{keyword}{const}\ uint64\_t\ delaySendingOfDataSegmentsTimeMsOrZeroToDisable);}
\DoxyCodeLine{00490\ \ \ \ \ }
\DoxyCodeLine{00496\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a6df3abcfdb9483420a8c741a722d9fd4}{SetMtuReportSegment\_ThreadSafe}}(uint64\_t\ mtuReportSegment);}
\DoxyCodeLine{00497\ \ \ \ \ }
\DoxyCodeLine{00503\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a0ac0e4173a8c54014cb3a0a0ae159f15}{SetMtuDataSegment\_ThreadSafe}}(uint64\_t\ mtuDataSegment);}
\DoxyCodeLine{00504\ \textcolor{keyword}{protected}:}
\DoxyCodeLine{00511\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_ac4ba9ce09bccb9141c1df926a8d50d9c}{SetRate}}(\textcolor{keyword}{const}\ uint64\_t\ maxSendRateBitsPerSecOrZeroToDisable);}
\DoxyCodeLine{00512\ }
\DoxyCodeLine{00517\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a60c38194c8ba48ffaf553bcaa78a33a6}{SetPing}}(\textcolor{keyword}{const}\ uint64\_t\ senderPingSecondsOrZeroToDisable);}
\DoxyCodeLine{00518\ }
\DoxyCodeLine{00521\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a275c7fe16d2bdf692d1447f371278224}{SetPingToDefaultConfig}}();}
\DoxyCodeLine{00522\ }
\DoxyCodeLine{00531\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a78364e8c88d84aafa75a6b6ff8f51d5e}{SetDelays}}(\textcolor{keyword}{const}\ boost::posix\_time::time\_duration\&\ oneWayLightTime,\ \textcolor{keyword}{const}\ boost::posix\_time::time\_duration\&\ oneWayMarginTime,\ \textcolor{keywordtype}{bool}\ updateRunningTimers);}
\DoxyCodeLine{00532\ \ \ \ \ }
\DoxyCodeLine{00541\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a19a1b2b299a2f596b2a7f93f6595283c}{SetDeferDelays}}(\textcolor{keyword}{const}\ uint64\_t\ delaySendingOfReportSegmentsTimeMsOrZeroToDisable,\ \textcolor{keyword}{const}\ uint64\_t\ delaySendingOfDataSegmentsTimeMsOrZeroToDisable);}
\DoxyCodeLine{00542\ \ \ \ \ }
\DoxyCodeLine{00548\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a009c7ca31d567ec910e5effe4eadf98a}{SetMtuReportSegment}}(uint64\_t\ mtuReportSegment);}
\DoxyCodeLine{00549\ \ \ \ \ }
\DoxyCodeLine{00554\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_afae9b922d32fe829c6ada4c983457799}{SetMtuDataSegment}}(uint64\_t\ mtuDataSegment);}
\DoxyCodeLine{00555\ }
\DoxyCodeLine{00561\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a0edf399b2bd002c21ebb65b40032f10b}{PacketInFullyProcessedCallback}}(\textcolor{keywordtype}{bool}\ success);}
\DoxyCodeLine{00562\ \ \ \ \ }
\DoxyCodeLine{00570\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_ad518dc5aa6135beac01e9c26c1acd6bc}{SendPacket}}(\textcolor{keyword}{const}\ std::vector<boost::asio::const\_buffer>\&\ constBufferVec,}
\DoxyCodeLine{00571\ \ \ \ \ \ \ \ \ std::shared\_ptr<std::vector<std::vector<uint8\_t>\ >\ >\ \&\&\ underlyingDataToDeleteOnSentCallback,}
\DoxyCodeLine{00572\ \ \ \ \ \ \ \ \ std::shared\_ptr<LtpClientServiceDataToSend>\&\&\ underlyingCsDataToDeleteOnSentCallback);}
\DoxyCodeLine{00573\ \ \ \ \ }
\DoxyCodeLine{00581\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_a9baa2210c16306fd40c21b7e70512fb6}{SendPackets}}(std::shared\_ptr<std::vector<UdpSendPacketInfo>\ >\&\&\ udpSendPacketInfoVecSharedPtr,\ \textcolor{keyword}{const}\ std::size\_t\ numPacketsToSend);}
\DoxyCodeLine{00582\ \ \ \ \ }
\DoxyCodeLine{00587\ \ \ \ \ LTP\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ \mbox{\hyperlink{class_ltp_engine_af468073c4352e673fc27eb14c8736323}{OnSendPacketsSystemCallCompleted\_ThreadSafe}}();}
\DoxyCodeLine{00588\ \textcolor{keyword}{private}:}
\DoxyCodeLine{00593\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ SignalReadyForSend\_ThreadSafe();}
\DoxyCodeLine{00594\ \ \ \ \ }
\DoxyCodeLine{00599\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ TrySaturateSendPacketPipeline();}
\DoxyCodeLine{00600\ \ \ \ \ }
\DoxyCodeLine{00628\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{bool}\ TrySendPacketIfAvailable();}
\DoxyCodeLine{00629\ \ \ \ \ }
\DoxyCodeLine{00637\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ OnDeferredReadCompleted(\textcolor{keywordtype}{bool}\ success,\ \textcolor{keyword}{const}\ std::vector<boost::asio::const\_buffer>\&\ constBufferVec,}
\DoxyCodeLine{00638\ \ \ \ \ \ \ \ \ std::shared\_ptr<std::vector<std::vector<uint8\_t>\ >\ >\&\ underlyingDataToDeleteOnSentCallback);}
\DoxyCodeLine{00639\ \ \ \ \ }
\DoxyCodeLine{00648\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ OnDeferredMultiReadCompleted(\textcolor{keywordtype}{bool}\ success,\ std::shared\_ptr<std::vector<UdpSendPacketInfo>\ >\&\ udpSendPacketInfoVecSharedPtr,\ \textcolor{keyword}{const}\ std::size\_t\ numPacketsToSend);}
\DoxyCodeLine{00649\ }
\DoxyCodeLine{00671\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ CancelSegmentReceivedCallback(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId,\ CANCEL\_SEGMENT\_REASON\_CODES\ reasonCode,\ \textcolor{keywordtype}{bool}\ isFromSender,}
\DoxyCodeLine{00672\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_ltp_1_1ltp__extensions__t}{Ltp::ltp\_extensions\_t}}\ \&\ headerExtensions,\ \mbox{\hyperlink{struct_ltp_1_1ltp__extensions__t}{Ltp::ltp\_extensions\_t}}\ \&\ trailerExtensions);}
\DoxyCodeLine{00673\ \ \ \ \ }
\DoxyCodeLine{00689\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ CancelAcknowledgementSegmentReceivedCallback(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId,\ \textcolor{keywordtype}{bool}\ isToSender,}
\DoxyCodeLine{00690\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_ltp_1_1ltp__extensions__t}{Ltp::ltp\_extensions\_t}}\ \&\ headerExtensions,\ \mbox{\hyperlink{struct_ltp_1_1ltp__extensions__t}{Ltp::ltp\_extensions\_t}}\ \&\ trailerExtensions);}
\DoxyCodeLine{00691\ \ \ \ \ }
\DoxyCodeLine{00702\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ ReportAcknowledgementSegmentReceivedCallback(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId,\ uint64\_t\ reportSerialNumberBeingAcknowledged,}
\DoxyCodeLine{00703\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_ltp_1_1ltp__extensions__t}{Ltp::ltp\_extensions\_t}}\ \&\ headerExtensions,\ \mbox{\hyperlink{struct_ltp_1_1ltp__extensions__t}{Ltp::ltp\_extensions\_t}}\ \&\ trailerExtensions);}
\DoxyCodeLine{00704\ \ \ \ \ }
\DoxyCodeLine{00716\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ ReportSegmentReceivedCallback(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId,\ \textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1report__segment__t}{Ltp::report\_segment\_t}}\ \&\ reportSegment,}
\DoxyCodeLine{00717\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_ltp_1_1ltp__extensions__t}{Ltp::ltp\_extensions\_t}}\ \&\ headerExtensions,\ \mbox{\hyperlink{struct_ltp_1_1ltp__extensions__t}{Ltp::ltp\_extensions\_t}}\ \&\ trailerExtensions);}
\DoxyCodeLine{00718\ }
\DoxyCodeLine{00740\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{bool}\ DataSegmentReceivedCallback(uint8\_t\ segmentTypeFlags,\ \textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId,}
\DoxyCodeLine{00741\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_ltp_1_1client__service__raw__data__t}{Ltp::client\_service\_raw\_data\_t}}\&\ clientServiceRawData,\ \textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1data__segment__metadata__t}{Ltp::data\_segment\_metadata\_t}}\ \&\ dataSegmentMetadata,}
\DoxyCodeLine{00742\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_ltp_1_1ltp__extensions__t}{Ltp::ltp\_extensions\_t}}\ \&\ headerExtensions,\ \mbox{\hyperlink{struct_ltp_1_1ltp__extensions__t}{Ltp::ltp\_extensions\_t}}\ \&\ trailerExtensions);}
\DoxyCodeLine{00743\ }
\DoxyCodeLine{00754\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ CancelSegmentTimerExpiredCallback(\mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ cancelSegmentTimerSerialNumber,\ std::vector<uint8\_t>\ \&\ userData);}
\DoxyCodeLine{00755\ \ \ \ \ }
\DoxyCodeLine{00770\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ NotifyEngineThatThisSenderNeedsDeletedCallback(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId,\ \textcolor{keywordtype}{bool}\ wasCancelled,\ CANCEL\_SEGMENT\_REASON\_CODES\ reasonCode,\ std::shared\_ptr<LtpTransmissionRequestUserData>\ \&\ userDataPtr);}
\DoxyCodeLine{00771\ \ \ \ \ }
\DoxyCodeLine{00777\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ NotifyEngineThatThisSenderHasProducibleData(\textcolor{keyword}{const}\ uint64\_t\ sessionNumber);}
\DoxyCodeLine{00778\ \ \ \ \ }
\DoxyCodeLine{00791\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ NotifyEngineThatThisReceiverNeedsDeletedCallback(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId,\ \textcolor{keywordtype}{bool}\ wasCancelled,\ CANCEL\_SEGMENT\_REASON\_CODES\ reasonCode);}
\DoxyCodeLine{00792\ \ \ \ \ }
\DoxyCodeLine{00798\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ NotifyEngineThatThisReceiversTimersHasProducibleData(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId);}
\DoxyCodeLine{00799\ \ \ \ \ }
\DoxyCodeLine{00804\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ NotifyEngineThatThisReceiverCompletedDeferredOperation();}
\DoxyCodeLine{00805\ \ \ \ \ }
\DoxyCodeLine{00813\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ InitialTransmissionCompletedCallback(\textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\ \&\ sessionId,\ std::shared\_ptr<LtpTransmissionRequestUserData>\ \&\ userDataPtr);}
\DoxyCodeLine{00814\ }
\DoxyCodeLine{00820\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ TryRestartTokenRefreshTimer();}
\DoxyCodeLine{00821\ \ \ \ \ }
\DoxyCodeLine{00828\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ TryRestartTokenRefreshTimer(\textcolor{keyword}{const}\ boost::posix\_time::ptime\ \&\ nowPtime);}
\DoxyCodeLine{00829\ \ \ \ \ }
\DoxyCodeLine{00839\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ OnTokenRefresh\_TimerExpired(\textcolor{keyword}{const}\ boost::system::error\_code\&\ e);}
\DoxyCodeLine{00840\ }
\DoxyCodeLine{00861\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ OnHousekeeping\_TimerExpired(\textcolor{keyword}{const}\ boost::system::error\_code\&\ e);}
\DoxyCodeLine{00862\ }
\DoxyCodeLine{00868\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ DoExternalLinkDownEvent();}
\DoxyCodeLine{00869\ \ \ \ \ }
\DoxyCodeLine{00870\ \ \ \ \ \textcolor{comment}{//these\ four\ functions\ eliminate\ need\ for\ each\ session\ to\ have\ its\ own\ expensive\ boost::function/boost::bind\ using\ a\ reinterpret\_cast\ of\ classPtr}}
\DoxyCodeLine{00878\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ LtpSessionReceiverReportSegmentTimerExpiredCallback(\textcolor{keywordtype}{void}*\ classPtr,\ \textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\&\ reportSerialNumberPlusSessionNumber,\ std::vector<uint8\_t>\&\ userData);}
\DoxyCodeLine{00879\ \ \ \ \ }
\DoxyCodeLine{00880\ \ \ \ \ \textcolor{comment}{//these\ four\ functions\ eliminate\ need\ for\ each\ session\ to\ have\ its\ own\ expensive\ boost::function/boost::bind\ using\ a\ reinterpret\_cast\ of\ classPtr}}
\DoxyCodeLine{00888\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ LtpSessionReceiverDelaySendReportSegmentTimerExpiredCallback(\textcolor{keywordtype}{void}*\ classPtr,\ \textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\&\ checkpointSerialNumberPlusSessionNumber,\ std::vector<uint8\_t>\&\ userData);}
\DoxyCodeLine{00889\ \ \ \ \ }
\DoxyCodeLine{00890\ \ \ \ \ \textcolor{comment}{//these\ four\ functions\ eliminate\ need\ for\ each\ session\ to\ have\ its\ own\ expensive\ boost::function/boost::bind\ using\ a\ reinterpret\_cast\ of\ classPtr}}
\DoxyCodeLine{00898\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ LtpSessionSenderCheckpointTimerExpiredCallback(\textcolor{keywordtype}{void}*\ classPtr,\ \textcolor{keyword}{const}\ \mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}}\&\ checkpointSerialNumberPlusSessionNumber,\ std::vector<uint8\_t>\&\ userData);}
\DoxyCodeLine{00899\ \ \ \ \ }
\DoxyCodeLine{00900\ \ \ \ \ \textcolor{comment}{//these\ four\ functions\ eliminate\ need\ for\ each\ session\ to\ have\ its\ own\ expensive\ boost::function/boost::bind\ using\ a\ reinterpret\_cast\ of\ classPtr}}
\DoxyCodeLine{00908\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ LtpSessionSenderDelaySendDataSegmentsTimerExpiredCallback(\textcolor{keywordtype}{void}*\ classPtr,\ \textcolor{keyword}{const}\ uint64\_t\&\ sessionNumber,\ std::vector<uint8\_t>\&\ userData);}
\DoxyCodeLine{00909\ \textcolor{keyword}{private}:}
\DoxyCodeLine{00911\ \ \ \ \ \mbox{\hyperlink{class_ltp}{Ltp}}\ m\_ltpRxStateMachine;}
\DoxyCodeLine{00913\ \ \ \ \ \mbox{\hyperlink{class_ltp_random_number_generator}{LtpRandomNumberGenerator}}\ m\_rng;}
\DoxyCodeLine{00915\ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ M\_THIS\_ENGINE\_ID;}
\DoxyCodeLine{00917\ \ \ \ \ std::atomic<unsigned\ int>\ m\_numQueuedSendSystemCallsAtomic;}
\DoxyCodeLine{00918\ \textcolor{keyword}{protected}:}
\DoxyCodeLine{00920\ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ \mbox{\hyperlink{class_ltp_engine_ab7af72361a64ed96f399f09da03aa32f}{M\_MAX\_UDP\_PACKETS\_TO\_SEND\_PER\_SYSTEM\_CALL}};}
\DoxyCodeLine{00921\ \textcolor{keyword}{private}:}
\DoxyCodeLine{00923\ \ \ \ \ boost::posix\_time::time\_duration\ m\_transmissionToAckReceivedTime;}
\DoxyCodeLine{00927\ \ \ \ \ boost::posix\_time::time\_duration\ m\_delaySendingOfReportSegmentsTime;}
\DoxyCodeLine{00931\ \ \ \ \ boost::posix\_time::time\_duration\ m\_delaySendingOfDataSegmentsTime;}
\DoxyCodeLine{00933\ \ \ \ \ \textcolor{keyword}{const}\ boost::posix\_time::time\_duration\ M\_HOUSEKEEPING\_INTERVAL;}
\DoxyCodeLine{00935\ \ \ \ \ boost::posix\_time::ptime\ m\_nowTimeRef;}
\DoxyCodeLine{00938\ \ \ \ \ boost::posix\_time::time\_duration\ m\_stagnantRxSessionTime;}
\DoxyCodeLine{00940\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ M\_FORCE\_32\_BIT\_RANDOM\_NUMBERS;}
\DoxyCodeLine{00943\ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ M\_DEFAULT\_SENDER\_PING\_SECONDS\_OR\_ZERO\_TO\_DISABLE;}
\DoxyCodeLine{00945\ \ \ \ \ uint64\_t\ m\_senderPingSecondsOrZeroToDisable;}
\DoxyCodeLine{00947\ \ \ \ \ boost::posix\_time::time\_duration\ m\_senderPingTimeDuration;}
\DoxyCodeLine{00949\ \ \ \ \ boost::posix\_time::ptime\ m\_nextPingStartExpiry;}
\DoxyCodeLine{00951\ \ \ \ \ \textcolor{keywordtype}{bool}\ m\_transmissionRequestServedAsPing;}
\DoxyCodeLine{00953\ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ M\_MAX\_SIMULTANEOUS\_SESSIONS;}
\DoxyCodeLine{00955\ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ M\_MAX\_SESSIONS\_IN\_PIPELINE;}
\DoxyCodeLine{00957\ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ M\_DISK\_BUNDLE\_ACK\_CALLBACK\_LIMIT;}
\DoxyCodeLine{00960\ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ M\_MAX\_RX\_DATA\_SEGMENT\_HISTORY\_OR\_ZERO\_DISABLE;}
\DoxyCodeLine{00961\ }
\DoxyCodeLine{00962\ \ \ \ \ \textcolor{comment}{//session\ receiver\ functions\ to\ be\ passed\ in\ AS\ REFERENCES\ (note\ declared\ before\ m\_mapSessionIdToSessionReceiver\ so\ destroyed\ after\ map)}}
\DoxyCodeLine{00964\ \ \ \ \ NotifyEngineThatThisReceiverNeedsDeletedCallback\_t\ m\_notifyEngineThatThisReceiverNeedsDeletedCallback;}
\DoxyCodeLine{00966\ \ \ \ \ NotifyEngineThatThisReceiversTimersHasProducibleDataFunction\_t\ m\_notifyEngineThatThisReceiversTimersHasProducibleDataFunction;}
\DoxyCodeLine{00968\ \ \ \ \ NotifyEngineThatThisReceiverCompletedDeferredOperationFunction\_t\ m\_notifyEngineThatThisReceiverCompletedDeferredOperationFunction;}
\DoxyCodeLine{00969\ }
\DoxyCodeLine{00970\ \ \ \ \ \textcolor{comment}{//session\ sender\ functions\ to\ be\ passed\ in\ AS\ REFERENCES\ (note\ declared\ before\ m\_mapSessionNumberToSessionSender\ so\ destroyed\ after\ map)}}
\DoxyCodeLine{00972\ \ \ \ \ NotifyEngineThatThisSenderNeedsDeletedCallback\_t\ m\_notifyEngineThatThisSenderNeedsDeletedCallback;}
\DoxyCodeLine{00974\ \ \ \ \ NotifyEngineThatThisSenderHasProducibleDataFunction\_t\ m\_notifyEngineThatThisSenderHasProducibleDataFunction;}
\DoxyCodeLine{00976\ \ \ \ \ InitialTransmissionCompletedCallback\_t\ m\_initialTransmissionCompletedCallbackCalledBySender;}
\DoxyCodeLine{00977\ }
\DoxyCodeLine{00979\ \ \ \ \ \textcolor{keyword}{typedef}\ std::unordered\_map<uint64\_t,\ \mbox{\hyperlink{class_ltp_session_sender}{LtpSessionSender}},}
\DoxyCodeLine{00980\ \ \ \ \ \ \ \ \ std::hash<uint64\_t>,}
\DoxyCodeLine{00981\ \ \ \ \ \ \ \ \ std::equal\_to<uint64\_t>,}
\DoxyCodeLine{00982\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{class_free_list_allocator_dynamic}{FreeListAllocatorDynamic<std::pair<const\ uint64\_t,\ LtpSessionSender>}}\ >\ >\ map\_session\_number\_to\_session\_sender\_t;}
\DoxyCodeLine{00984\ \ \ \ \ \textcolor{keyword}{typedef}\ std::unordered\_map<\mbox{\hyperlink{struct_ltp_1_1session__id__t}{Ltp::session\_id\_t}},\ \mbox{\hyperlink{class_ltp_session_receiver}{LtpSessionReceiver}},}
\DoxyCodeLine{00985\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{struct_ltp_1_1hash__session__id__t}{Ltp::hash\_session\_id\_t}},}
\DoxyCodeLine{00986\ \ \ \ \ \ \ \ \ std::equal\_to<Ltp::session\_id\_t>,}
\DoxyCodeLine{00987\ \ \ \ \ \ \ \ \ \mbox{\hyperlink{class_free_list_allocator_dynamic}{FreeListAllocatorDynamic<std::pair<const\ Ltp::session\_id\_t,\ LtpSessionReceiver>}}\ >\ >\ map\_session\_id\_to\_session\_receiver\_t;}
\DoxyCodeLine{00989\ \ \ \ \ map\_session\_number\_to\_session\_sender\_t\ m\_mapSessionNumberToSessionSender;}
\DoxyCodeLine{00991\ \ \ \ \ map\_session\_id\_to\_session\_receiver\_t\ m\_mapSessionIdToSessionReceiver;}
\DoxyCodeLine{00996\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ TryReturnTxSessionDataToUser(map\_session\_number\_to\_session\_sender\_t::iterator\&\ txSessionIt);}
\DoxyCodeLine{01003\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ EraseTxSession(map\_session\_number\_to\_session\_sender\_t::iterator\&\ txSessionIt);}
\DoxyCodeLine{01009\ \ \ \ \ LTP\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ EraseRxSession(map\_session\_id\_to\_session\_receiver\_t::iterator\&\ rxSessionIt);}
\DoxyCodeLine{01010\ }
\DoxyCodeLine{01011\ \ \ \ \ \textcolor{comment}{//reserve\ data\ so\ that\ operator\ new\ doesn't\ need\ called\ for\ resize\ of\ std::vector<boost::asio::const\_buffer>}}
\DoxyCodeLine{01012\ \ \ \ \ \textcolor{comment}{//\ non-\/batch\ sender\ reserved}}
\DoxyCodeLine{01013\ \ \ \ \ std::vector<UdpSendPacketInfo>\ m\_reservedUdpSendPacketInfo;}
\DoxyCodeLine{01014\ \ \ \ \ std::vector<UdpSendPacketInfo>::iterator\ m\_reservedUdpSendPacketInfoIterator;}
\DoxyCodeLine{01015\ \ \ \ \ \textcolor{comment}{//\ batch\ sender\ reserved}}
\DoxyCodeLine{01016\ \ \ \ \ std::vector<std::shared\_ptr<std::vector<UdpSendPacketInfo>\ >\ >\ m\_reservedUdpSendPacketInfoVecsForBatchSender;}
\DoxyCodeLine{01017\ \ \ \ \ std::vector<std::shared\_ptr<std::vector<UdpSendPacketInfo>\ >\ >::iterator\ m\_reservedUdpSendPacketInfoVecsForBatchSenderIterator;}
\DoxyCodeLine{01018\ \ \ \ \ std::vector<MemoryInFiles::deferred\_read\_t>\ m\_reservedDeferredReadsVec;\ \textcolor{comment}{//only\ used\ immediately\ and\ passed\ as\ const\ ref}}
\DoxyCodeLine{01019\ \ \ \ \ }
\DoxyCodeLine{01021\ \ \ \ \ std::set<Ltp::session\_id\_t>\ m\_ltpSessionsWithWrongClientServiceId;}
\DoxyCodeLine{01023\ \ \ \ \ std::queue<std::pair<uint64\_t,\ std::vector<uint8\_t>\ >\ >\ m\_queueClosedSessionDataToSend;\ \textcolor{comment}{//sessionOriginatorEngineId,\ data}}
\DoxyCodeLine{01025\ \ \ \ \ std::queue<cancel\_segment\_timer\_info\_t>\ m\_queueCancelSegmentTimerInfo;}
\DoxyCodeLine{01027\ \ \ \ \ std::queue<uint64\_t>\ m\_queueSendersNeedingDeleted;}
\DoxyCodeLine{01029\ \ \ \ \ std::queue<uint64\_t>\ m\_queueSendersNeedingTimeCriticalDataSent;}
\DoxyCodeLine{01031\ \ \ \ \ std::queue<uint64\_t>\ m\_queueSendersNeedingFirstPassDataSent;}
\DoxyCodeLine{01033\ \ \ \ \ std::queue<Ltp::session\_id\_t>\ m\_queueReceiversNeedingDeleted;}
\DoxyCodeLine{01035\ \ \ \ \ std::queue<Ltp::session\_id\_t>\ m\_queueReceiversNeedingDeletedButUnsafeToDelete;}
\DoxyCodeLine{01037\ \ \ \ \ std::queue<Ltp::session\_id\_t>\ m\_queueReceiversNeedingDataSent;}
\DoxyCodeLine{01038\ }
\DoxyCodeLine{01040\ \ \ \ \ SessionStartCallback\_t\ m\_sessionStartCallback;}
\DoxyCodeLine{01042\ \ \ \ \ RedPartReceptionCallback\_t\ m\_redPartReceptionCallback;}
\DoxyCodeLine{01044\ \ \ \ \ GreenPartSegmentArrivalCallback\_t\ m\_greenPartSegmentArrivalCallback;}
\DoxyCodeLine{01046\ \ \ \ \ ReceptionSessionCancelledCallback\_t\ m\_receptionSessionCancelledCallback;}
\DoxyCodeLine{01048\ \ \ \ \ TransmissionSessionCompletedCallback\_t\ m\_transmissionSessionCompletedCallback;}
\DoxyCodeLine{01050\ \ \ \ \ InitialTransmissionCompletedCallback\_t\ m\_initialTransmissionCompletedCallbackForUser;}
\DoxyCodeLine{01052\ \ \ \ \ TransmissionSessionCancelledCallback\_t\ m\_transmissionSessionCancelledCallback;}
\DoxyCodeLine{01053\ }
\DoxyCodeLine{01056\ \ \ \ \ OnFailedBundleVecSendCallback\_t\ m\_onFailedBundleVecSendCallback;}
\DoxyCodeLine{01059\ \ \ \ \ OnFailedBundleZmqSendCallback\_t\ m\_onFailedBundleZmqSendCallback;}
\DoxyCodeLine{01062\ \ \ \ \ OnSuccessfulBundleSendCallback\_t\ m\_onSuccessfulBundleSendCallback;}
\DoxyCodeLine{01064\ \ \ \ \ OnOutductLinkStatusChangedCallback\_t\ m\_onOutductLinkStatusChangedCallback;}
\DoxyCodeLine{01066\ \ \ \ \ uint64\_t\ m\_userAssignedUuid;}
\DoxyCodeLine{01067\ }
\DoxyCodeLine{01069\ \ \ \ \ uint32\_t\ m\_maxRetriesPerSerialNumber;}
\DoxyCodeLine{01070\ \textcolor{keyword}{protected}:}
\DoxyCodeLine{01072\ \ \ \ \ boost::asio::io\_service\ \mbox{\hyperlink{class_ltp_engine_a40ecc6a317c1939ac4a610185f2325a3}{m\_ioServiceLtpEngine}};\ \textcolor{comment}{//for\ timers\ and\ post\ calls\ only}}
\DoxyCodeLine{01073\ \textcolor{keyword}{private}:}
\DoxyCodeLine{01075\ \ \ \ \ std::unique\_ptr<boost::asio::io\_service::work>\ m\_workLtpEnginePtr;}
\DoxyCodeLine{01076\ }
\DoxyCodeLine{01078\ \ \ \ \ boost::asio::deadline\_timer\ m\_deadlineTimerForTimeManagerOfReportSerialNumbers;}
\DoxyCodeLine{01079\ \ \ \ \ \textcolor{comment}{//\ within\ a\ session\ would\ normally\ be\ LtpTimerManager<uint64\_t,\ std::hash<uint64\_t>\ >\ m\_timeManagerOfReportSerialNumbers;}}
\DoxyCodeLine{01080\ \ \ \ \ \textcolor{comment}{//\ but\ now\ sharing\ a\ single\ LtpTimerManager\ among\ all\ sessions,\ so\ use\ a}}
\DoxyCodeLine{01081\ \ \ \ \ \textcolor{comment}{//\ LtpTimerManager<Ltp::session\_id\_t,\ Ltp::hash\_session\_id\_t>\ (which\ has\ hash\ map\ hashing\ function\ support)}}
\DoxyCodeLine{01082\ \ \ \ \ \textcolor{comment}{//\ such\ that:\ }}
\DoxyCodeLine{01083\ \ \ \ \ \textcolor{comment}{//\ \ sessionOriginatorEngineId\ =\ REPORT\ serial\ number}}
\DoxyCodeLine{01084\ \ \ \ \ \textcolor{comment}{//\ \ sessionNumber\ =\ the\ session\ number}}
\DoxyCodeLine{01085\ \ \ \ \ \textcolor{comment}{//\ \ since\ this\ is\ a\ receiver,\ the\ real\ sessionOriginatorEngineId\ is\ constant\ among\ all\ receiving\ sessions\ and\ is\ not\ needed}}
\DoxyCodeLine{01087\ \ \ \ \ \mbox{\hyperlink{class_ltp_timer_manager}{LtpTimerManager<Ltp::session\_id\_t,\ Ltp::hash\_session\_id\_t>}}\ m\_timeManagerOfReportSerialNumbers;}
\DoxyCodeLine{01089\ \ \ \ \ \mbox{\hyperlink{class_ltp_timer_manager_abfb1b7e1ff33ba507d6ae0f5cb7acffc}{LtpTimerManager<Ltp::session\_id\_t,\ Ltp::hash\_session\_id\_t>::LtpTimerExpiredCallback\_t}}\ m\_rsnTimerExpiredCallback;}
\DoxyCodeLine{01090\ }
\DoxyCodeLine{01092\ \ \ \ \ boost::asio::deadline\_timer\ m\_deadlineTimerForTimeManagerOfSendingDelayedReceptionReports;}
\DoxyCodeLine{01093\ \ \ \ \ \textcolor{comment}{//\ \ sessionOriginatorEngineId\ =\ CHECKPOINT\ serial\ number\ to\ which\ RS\ pertains}}
\DoxyCodeLine{01094\ \ \ \ \ \textcolor{comment}{//\ \ sessionNumber\ =\ the\ session\ number}}
\DoxyCodeLine{01095\ \ \ \ \ \textcolor{comment}{//\ \ since\ this\ is\ a\ receiver,\ the\ real\ sessionOriginatorEngineId\ is\ constant\ among\ all\ receiving\ sessions\ and\ is\ not\ needed}}
\DoxyCodeLine{01097\ \ \ \ \ \mbox{\hyperlink{class_ltp_timer_manager}{LtpTimerManager<Ltp::session\_id\_t,\ Ltp::hash\_session\_id\_t>}}\ m\_timeManagerOfSendingDelayedReceptionReports;}
\DoxyCodeLine{01099\ \ \ \ \ \mbox{\hyperlink{class_ltp_timer_manager_abfb1b7e1ff33ba507d6ae0f5cb7acffc}{LtpTimerManager<Ltp::session\_id\_t,\ Ltp::hash\_session\_id\_t>::LtpTimerExpiredCallback\_t}}\ m\_delayedReceptionReportTimerExpiredCallback;}
\DoxyCodeLine{01100\ }
\DoxyCodeLine{01102\ \ \ \ \ boost::asio::deadline\_timer\ m\_deadlineTimerForTimeManagerOfCheckpointSerialNumbers;}
\DoxyCodeLine{01103\ \ \ \ \ \textcolor{comment}{//\ within\ a\ session\ would\ normally\ be\ LtpTimerManager<uint64\_t,\ std::hash<uint64\_t>\ >\ m\_timeManagerOfCheckpointSerialNumbers;}}
\DoxyCodeLine{01104\ \ \ \ \ \textcolor{comment}{//\ but\ now\ sharing\ a\ single\ LtpTimerManager\ among\ all\ sessions,\ so\ use\ a}}
\DoxyCodeLine{01105\ \ \ \ \ \textcolor{comment}{//\ LtpTimerManager<Ltp::session\_id\_t,\ Ltp::hash\_session\_id\_t>\ (which\ has\ hash\ map\ hashing\ function\ support)}}
\DoxyCodeLine{01106\ \ \ \ \ \textcolor{comment}{//\ such\ that:\ }}
\DoxyCodeLine{01107\ \ \ \ \ \textcolor{comment}{//\ \ sessionOriginatorEngineId\ =\ CHECKPOINT\ serial\ number}}
\DoxyCodeLine{01108\ \ \ \ \ \textcolor{comment}{//\ \ sessionNumber\ =\ the\ session\ number}}
\DoxyCodeLine{01109\ \ \ \ \ \textcolor{comment}{//\ \ since\ this\ is\ a\ sender,\ the\ real\ sessionOriginatorEngineId\ is\ constant\ among\ all\ sending\ sessions\ and\ is\ not\ needed}}
\DoxyCodeLine{01111\ \ \ \ \ \mbox{\hyperlink{class_ltp_timer_manager}{LtpTimerManager<Ltp::session\_id\_t,\ Ltp::hash\_session\_id\_t>}}\ m\_timeManagerOfCheckpointSerialNumbers;}
\DoxyCodeLine{01113\ \ \ \ \ \mbox{\hyperlink{class_ltp_timer_manager_abfb1b7e1ff33ba507d6ae0f5cb7acffc}{LtpTimerManager<Ltp::session\_id\_t,\ Ltp::hash\_session\_id\_t>::LtpTimerExpiredCallback\_t}}\ m\_csnTimerExpiredCallback;}
\DoxyCodeLine{01114\ }
\DoxyCodeLine{01116\ \ \ \ \ boost::asio::deadline\_timer\ m\_deadlineTimerForTimeManagerOfSendingDelayedDataSegments;}
\DoxyCodeLine{01117\ \ \ \ \ \textcolor{comment}{//\ within\ a\ session\ would\ normally\ be\ a\ single\ deadline\ timer;}}
\DoxyCodeLine{01118\ \ \ \ \ \textcolor{comment}{//\ but\ now\ sharing\ a\ single\ LtpTimerManager\ among\ all\ sessions,\ so\ use\ a}}
\DoxyCodeLine{01119\ \ \ \ \ \textcolor{comment}{//\ LtpTimerManager<uint64\_t,\ std::hash<uint64\_t>\ >}}
\DoxyCodeLine{01120\ \ \ \ \ \textcolor{comment}{//\ such\ that:\ }}
\DoxyCodeLine{01121\ \ \ \ \ \textcolor{comment}{//\ \ uint64\_t\ =\ the\ session\ number}}
\DoxyCodeLine{01122\ \ \ \ \ \textcolor{comment}{//\ \ since\ this\ is\ a\ sender,\ the\ real\ sessionOriginatorEngineId\ is\ constant\ among\ all\ sending\ sessions\ and\ is\ not\ needed}}
\DoxyCodeLine{01124\ \ \ \ \ \mbox{\hyperlink{class_ltp_timer_manager}{LtpTimerManager<uint64\_t,\ std::hash<uint64\_t>}}\ >\ m\_timeManagerOfSendingDelayedDataSegments;}
\DoxyCodeLine{01126\ \ \ \ \ \mbox{\hyperlink{class_ltp_timer_manager}{LtpTimerManager<uint64\_t,\ std::hash<uint64\_t>}}\ >::LtpTimerExpiredCallback\_t\ m\_delayedDataSegmentsTimerExpiredCallback;}
\DoxyCodeLine{01127\ }
\DoxyCodeLine{01129\ \ \ \ \ \mbox{\hyperlink{class_ltp_timer_manager_abfb1b7e1ff33ba507d6ae0f5cb7acffc}{LtpTimerManager<Ltp::session\_id\_t,\ Ltp::hash\_session\_id\_t>::LtpTimerExpiredCallback\_t}}\ m\_cancelSegmentTimerExpiredCallback;}
\DoxyCodeLine{01131\ \ \ \ \ boost::asio::deadline\_timer\ m\_deadlineTimerForTimeManagerOfCancelSegments;}
\DoxyCodeLine{01133\ \ \ \ \ \mbox{\hyperlink{class_ltp_timer_manager}{LtpTimerManager<Ltp::session\_id\_t,\ Ltp::hash\_session\_id\_t>}}\ m\_timeManagerOfCancelSegments;}
\DoxyCodeLine{01134\ \ \ \ \ }
\DoxyCodeLine{01136\ \ \ \ \ boost::asio::deadline\_timer\ m\_housekeepingTimer;}
\DoxyCodeLine{01138\ \ \ \ \ \mbox{\hyperlink{class_token_rate_limiter}{TokenRateLimiter}}\ m\_tokenRateLimiter;}
\DoxyCodeLine{01140\ \ \ \ \ boost::asio::deadline\_timer\ m\_tokenRefreshTimer;}
\DoxyCodeLine{01142\ \ \ \ \ uint64\_t\ m\_maxSendRateBitsPerSecOrZeroToDisable;}
\DoxyCodeLine{01144\ \ \ \ \ \textcolor{keywordtype}{bool}\ m\_tokenRefreshTimerIsRunning;}
\DoxyCodeLine{01146\ \ \ \ \ boost::posix\_time::ptime\ m\_lastTimeTokensWereRefreshed;}
\DoxyCodeLine{01148\ \ \ \ \ boost::posix\_time::time\_duration\ m\_rateLimitPrecisionInterval;}
\DoxyCodeLine{01150\ \ \ \ \ boost::posix\_time::time\_duration\ m\_tokenRefreshInterval;}
\DoxyCodeLine{01152\ \ \ \ \ std::unique\_ptr<boost::thread>\ m\_ioServiceLtpEngineThreadPtr;}
\DoxyCodeLine{01153\ }
\DoxyCodeLine{01154\ \ \ \ \ \textcolor{comment}{//session\ re-\/creation\ prevention}}
\DoxyCodeLine{01156\ \ \ \ \ std::map<uint64\_t,\ LtpSessionRecreationPreventer>\ m\_mapSessionOriginatorEngineIdToLtpSessionRecreationPreventer;}
\DoxyCodeLine{01157\ }
\DoxyCodeLine{01158\ \ \ \ \ \textcolor{comment}{//memory\ in\ files}}
\DoxyCodeLine{01160\ \ \ \ \ std::unique\_ptr<MemoryInFiles>\ m\_memoryInFilesPtr;}
\DoxyCodeLine{01162\ \ \ \ \ std::queue<uint64\_t>\ m\_memoryBlockIdsPendingDeletionQueue;}
\DoxyCodeLine{01164\ \ \ \ \ std::queue<std::vector<uint8\_t>\ >\ m\_userDataPendingSuccessfulBundleSendCallbackQueue;}
\DoxyCodeLine{01165\ }
\DoxyCodeLine{01166\ }
\DoxyCodeLine{01167\ \ \ \ \ \textcolor{comment}{//reference\ structs\ common\ to\ all\ sessions}}
\DoxyCodeLine{01169\ \ \ \ \ LtpSessionSender::LtpSessionSenderRecycler\ m\_ltpSessionSenderRecycler;}
\DoxyCodeLine{01170\ \ \ \ \ \mbox{\hyperlink{struct_ltp_session_sender_1_1_ltp_session_sender_common_data}{LtpSessionSender::LtpSessionSenderCommonData}}\ m\_ltpSessionSenderCommonData;}
\DoxyCodeLine{01172\ \ \ \ \ LtpSessionReceiver::LtpSessionReceiverRecycler\ m\_ltpSessionReceiverRecycler;}
\DoxyCodeLine{01173\ \ \ \ \ \mbox{\hyperlink{struct_ltp_session_receiver_1_1_ltp_session_receiver_common_data}{LtpSessionReceiver::LtpSessionReceiverCommonData}}\ m\_ltpSessionReceiverCommonData;}
\DoxyCodeLine{01174\ }
\DoxyCodeLine{01175\ \textcolor{keyword}{public}:}
\DoxyCodeLine{01176\ \ \ \ \ \textcolor{comment}{//stats}}
\DoxyCodeLine{01177\ }
\DoxyCodeLine{01178\ \ \ \ \ \textcolor{comment}{//LtpEngine}}
\DoxyCodeLine{01180\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_afaec1d6368c078f8ab48b70a866163be}{m\_countAsyncSendsLimitedByRate}};}
\DoxyCodeLine{01182\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_a1e1b3add6403a97ec1cc088f5bdade75}{m\_countPacketsWithOngoingOperations}};}
\DoxyCodeLine{01184\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_ae8b9956d00992ec381a3dde36052d9e0}{m\_countPacketsThatCompletedOngoingOperations}};}
\DoxyCodeLine{01186\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_afc4e2efef914afc93e64c82914be0021}{m\_numEventsTransmissionRequestDiskWritesTooSlow}};}
\DoxyCodeLine{01188\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_a636aab451f26fe2953648351dde9f8c6}{m\_totalRedDataBytesSuccessfullySent}};}
\DoxyCodeLine{01190\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_ad2623f77cb51cc0fc6ec4e4a6c0ca5d0}{m\_totalRedDataBytesFailedToSend}};}
\DoxyCodeLine{01192\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_acc439a7edd94073c12bfe384ad10d32e}{m\_totalCancelSegmentsStarted}};}
\DoxyCodeLine{01194\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_ae079576b5fbc92cf4305560456ecd7eb}{m\_totalCancelSegmentSendRetries}};}
\DoxyCodeLine{01196\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_acfd032abbb14553c1286a52db854e29e}{m\_totalCancelSegmentsFailedToSend}};}
\DoxyCodeLine{01198\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_acfaed5ddbfb81f4a8231ea549ee576f0}{m\_totalCancelSegmentsAcknowledged}};}
\DoxyCodeLine{01200\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_a9f796012072e7a5cf5eb6ed9c7827932}{m\_totalPingsStarted}};}
\DoxyCodeLine{01202\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_a69d7c7df39e9c3a70c05018380b64dcd}{m\_totalPingRetries}};}
\DoxyCodeLine{01204\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_aba7405ff7bcce1fb92b81723bef37a42}{m\_totalPingsFailedToSend}};}
\DoxyCodeLine{01206\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_aaccc32aa5044fcccdfebfce9138978d8}{m\_totalPingsAcknowledged}};}
\DoxyCodeLine{01208\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_aaacad4876ebaf2a0ca609bdb2486b8b9}{m\_numTxSessionsReturnedToStorage}};}
\DoxyCodeLine{01210\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_a5fdcaa43c182aa129db5ff43a7a0cf1b}{m\_numTxSessionsCancelledByReceiver}};}
\DoxyCodeLine{01212\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_af8e0b9091c58b682193f8d82f3a32306}{m\_numRxSessionsCancelledBySender}};}
\DoxyCodeLine{01214\ \ \ \ \ std::atomic<uint64\_t>\ \mbox{\hyperlink{class_ltp_engine_a89e22b17ea67c8745c91a0db340e85c3}{m\_numStagnantRxSessionsDeleted}};}
\DoxyCodeLine{01215\ }
\DoxyCodeLine{01216\ }
\DoxyCodeLine{01217\ \ \ \ \ \textcolor{comment}{//session\ sender\ stats\ (references\ to\ variables\ within\ m\_ltpSessionSenderCommonData)}}
\DoxyCodeLine{01219\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_a1763e65d50a7c8bf398f26f583802343}{m\_numCheckpointTimerExpiredCallbacksRef}};}
\DoxyCodeLine{01221\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_a76849a49ef076249f14230a85434ec76}{m\_numDiscretionaryCheckpointsNotResentRef}};}
\DoxyCodeLine{01223\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_aef60d5b1702f7056ec0792dd5b4b861f}{m\_numDeletedFullyClaimedPendingReportsRef}};}
\DoxyCodeLine{01224\ }
\DoxyCodeLine{01225\ \ \ \ \ \textcolor{comment}{//session\ receiver\ stats}}
\DoxyCodeLine{01227\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_a95ecb54427055af145d6aa11aecc5fb4}{m\_numReportSegmentTimerExpiredCallbacksRef}};}
\DoxyCodeLine{01229\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_a3198a5091e8c461058b7e810b5789e32}{m\_numReportSegmentsUnableToBeIssuedRef}};}
\DoxyCodeLine{01231\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_a9a5dd020869dae6b25ad0a71bc65c28e}{m\_numReportSegmentsTooLargeAndNeedingSplitRef}};}
\DoxyCodeLine{01233\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_a2c366ddcce89f58bddd5117b789abc40}{m\_numReportSegmentsCreatedViaSplitRef}};}
\DoxyCodeLine{01235\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_aa96c52cebe678593e90904420bad57d1}{m\_numGapsFilledByOutOfOrderDataSegmentsRef}};}
\DoxyCodeLine{01237\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_a118775a99920325d3610df81635fcfce}{m\_numDelayedFullyClaimedPrimaryReportSegmentsSentRef}};}
\DoxyCodeLine{01239\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_a07cc9142b412ee2276a6b3ce13c46339}{m\_numDelayedFullyClaimedSecondaryReportSegmentsSentRef}};}
\DoxyCodeLine{01241\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_a7865d752880e0c9ccca8698e46e42336}{m\_numDelayedPartiallyClaimedPrimaryReportSegmentsSentRef}};}
\DoxyCodeLine{01243\ \ \ \ \ std::atomic<uint64\_t>\&\ \mbox{\hyperlink{class_ltp_engine_ac44e7895529da02c5e7945efc12b7f45}{m\_numDelayedPartiallyClaimedSecondaryReportSegmentsSentRef}};}
\DoxyCodeLine{01244\ \};}
\DoxyCodeLine{01245\ }
\DoxyCodeLine{01246\ \textcolor{preprocessor}{\#endif\ }\textcolor{comment}{//\ LTP\_ENGINE\_H}}
\DoxyCodeLine{01247\ }

\end{DoxyCode}
