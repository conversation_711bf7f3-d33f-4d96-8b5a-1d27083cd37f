\doxysection{Hdtn\+Config Class Reference}
\hypertarget{class_hdtn_config}{}\label{class_hdtn_config}\index{HdtnConfig@{HdtnConfig}}
Inheritance diagram for Hdtn\+Config\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{class_hdtn_config}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_hdtn_config_ad03897fa47232970f333672292c39950}\label{class_hdtn_config_ad03897fa47232970f333672292c39950} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT {\bfseries Hdtn\+Config} (const \mbox{\hyperlink{class_hdtn_config}{Hdtn\+Config}} \&o)
\item 
\Hypertarget{class_hdtn_config_a113f174c56f5f446f9fefcfe58b9ddce}\label{class_hdtn_config_a113f174c56f5f446f9fefcfe58b9ddce} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT {\bfseries Hdtn\+Config} (\mbox{\hyperlink{class_hdtn_config}{Hdtn\+Config}} \&\&o) noexcept
\item 
\Hypertarget{class_hdtn_config_a04cbd66a05a17d79bd3db08071689193}\label{class_hdtn_config_a04cbd66a05a17d79bd3db08071689193} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT \mbox{\hyperlink{class_hdtn_config}{Hdtn\+Config}} \& {\bfseries operator=} (const \mbox{\hyperlink{class_hdtn_config}{Hdtn\+Config}} \&o)
\item 
\Hypertarget{class_hdtn_config_a7027b9deb43d4017ef932066fc882f40}\label{class_hdtn_config_a7027b9deb43d4017ef932066fc882f40} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT \mbox{\hyperlink{class_hdtn_config}{Hdtn\+Config}} \& {\bfseries operator=} (\mbox{\hyperlink{class_hdtn_config}{Hdtn\+Config}} \&\&o) noexcept
\item 
\Hypertarget{class_hdtn_config_a76b43b07f1e0bb13c642b91ffa96c1c2}\label{class_hdtn_config_a76b43b07f1e0bb13c642b91ffa96c1c2} 
CONFIG\+\_\+\+LIB\+\_\+\+EXPORT bool {\bfseries operator==} (const \mbox{\hyperlink{class_hdtn_config}{Hdtn\+Config}} \&other) const
\item 
virtual CONFIG\+\_\+\+LIB\+\_\+\+EXPORT boost\+::property\+\_\+tree\+::ptree \mbox{\hyperlink{class_hdtn_config_ae2660007b111d9ae481d3fcc0906dc36}{Get\+New\+Property\+Tree}} () const override
\item 
virtual CONFIG\+\_\+\+LIB\+\_\+\+EXPORT bool \mbox{\hyperlink{class_hdtn_config_a3c39b18edcbf4225f5788445b456f5f9}{Set\+Values\+From\+Property\+Tree}} (const boost\+::property\+\_\+tree\+::ptree \&pt) override
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}}
\begin{DoxyCompactItemize}
\item 
std\+::string {\bfseries To\+Json} (bool pretty=true) const
\item 
bool {\bfseries To\+Json\+File} (const boost\+::filesystem\+::path \&file\+Path, bool pretty=true) const
\item 
std\+::string {\bfseries To\+Xml} () const
\item 
bool {\bfseries To\+Xml\+File} (const std\+::string \&file\+Name, char indent\+Character=\textquotesingle{} \textquotesingle{}, int indent\+Count=2) const
\item 
bool {\bfseries Set\+Values\+From\+Json} (const std\+::string \&json\+String)
\item 
bool {\bfseries Set\+Values\+From\+Json\+Char\+Array} (const char \texorpdfstring{$\ast$}{*}data, const std\+::size\+\_\+t size)
\end{DoxyCompactItemize}
\doxysubsubsection*{Static Public Member Functions}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_hdtn_config_a7a733e5694fd20b0ea0fa9b9063f277a}\label{class_hdtn_config_a7a733e5694fd20b0ea0fa9b9063f277a} 
static CONFIG\+\_\+\+LIB\+\_\+\+EXPORT Hdtn\+Config\+\_\+ptr {\bfseries Create\+From\+Ptree} (const boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
\Hypertarget{class_hdtn_config_a581732873620f17751cfa67013ff531d}\label{class_hdtn_config_a581732873620f17751cfa67013ff531d} 
static CONFIG\+\_\+\+LIB\+\_\+\+EXPORT Hdtn\+Config\+\_\+ptr {\bfseries Create\+From\+Json} (const std\+::string \&json\+String, bool verify\+No\+Unused\+Json\+Keys=true)
\item 
\Hypertarget{class_hdtn_config_a118a7f2b962d037462f57113f92dedf6}\label{class_hdtn_config_a118a7f2b962d037462f57113f92dedf6} 
static CONFIG\+\_\+\+LIB\+\_\+\+EXPORT Hdtn\+Config\+\_\+ptr {\bfseries Create\+From\+Json\+File\+Path} (const boost\+::filesystem\+::path \&json\+File\+Path, bool verify\+No\+Unused\+Json\+Keys=true)
\end{DoxyCompactItemize}
\doxysubsection*{Static Public Member Functions inherited from \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}}
\begin{DoxyCompactItemize}
\item 
static bool {\bfseries Load\+Text\+File\+Into\+String} (const boost\+::filesystem\+::path \&file\+Path, std\+::string \&file\+Contents\+As\+String)
\item 
static void {\bfseries Get\+All\+Json\+Keys} (const std\+::string \&json\+Text, std\+::set$<$ std\+::string $>$ \&json\+Keys\+No\+Quotes\+Set\+To\+Append)
\item 
static void {\bfseries Get\+All\+Json\+Keys\+Line\+By\+Line} (std\+::istream \&stream, std\+::set$<$ std\+::string $>$ \&json\+Keys\+No\+Quotes\+Set\+To\+Append)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+File\+Path} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, const boost\+::filesystem\+::path \&original\+User\+Json\+File\+Path, std\+::string \&returned\+Error\+Message)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+String} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, const std\+::string \&original\+User\+Json\+String, std\+::string \&returned\+Error\+Message)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+Stream} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, std\+::istream \&original\+User\+Json\+Stream, std\+::string \&returned\+Error\+Message)
\item 
static std\+::string {\bfseries Pt\+To\+Json\+String} (const boost\+::property\+\_\+tree\+::ptree \&pt, bool pretty=true)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+Char\+Array} (char \texorpdfstring{$\ast$}{*}data, const std\+::size\+\_\+t size, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+Stream} (std\+::istream \&json\+Stream, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+String} (const std\+::string \&json\+Str, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+File\+Path} (const boost\+::filesystem\+::path \&json\+File\+Path, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static std\+::string {\bfseries Pt\+To\+Xml\+String} (const boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static boost\+::property\+\_\+tree\+::ptree {\bfseries Get\+Property\+Tree\+From\+Xml\+String} (const std\+::string \&json\+Str)
\item 
static boost\+::property\+\_\+tree\+::ptree {\bfseries Get\+Property\+Tree\+From\+Xml\+File} (const std\+::string \&xml\+File\+Name)
\end{DoxyCompactItemize}
\doxysubsubsection*{Public Attributes}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{class_hdtn_config_a50c048930ecf1a39f4f47f635f819dbb}\label{class_hdtn_config_a50c048930ecf1a39f4f47f635f819dbb} 
std\+::string {\bfseries m\+\_\+hdtn\+Config\+Name}
\item 
\Hypertarget{class_hdtn_config_afa17a00a8e7275c9a9ede7dee1003639}\label{class_hdtn_config_afa17a00a8e7275c9a9ede7dee1003639} 
bool {\bfseries m\+\_\+user\+Interface\+On}
\item 
\Hypertarget{class_hdtn_config_a995720100dc1a62ad8eed4500d6e6cf4}\label{class_hdtn_config_a995720100dc1a62ad8eed4500d6e6cf4} 
std\+::string {\bfseries m\+\_\+my\+Scheme\+Name}
\item 
\Hypertarget{class_hdtn_config_a2dbaf2b1c1a9984debee48ddf91d743c}\label{class_hdtn_config_a2dbaf2b1c1a9984debee48ddf91d743c} 
uint64\+\_\+t {\bfseries m\+\_\+my\+Node\+Id}
\item 
\Hypertarget{class_hdtn_config_a74dd054a8f9a0f81250adfd64a76c79c}\label{class_hdtn_config_a74dd054a8f9a0f81250adfd64a76c79c} 
uint64\+\_\+t {\bfseries m\+\_\+my\+Bp\+Echo\+Service\+Id}
\item 
\Hypertarget{class_hdtn_config_aff9ba970535a33f800bd88ad89643ab2}\label{class_hdtn_config_aff9ba970535a33f800bd88ad89643ab2} 
std\+::string {\bfseries m\+\_\+my\+Custodial\+Ssp}
\item 
\Hypertarget{class_hdtn_config_a5f1d3dbd77bc3a766fc9da8e8b3cbfcc}\label{class_hdtn_config_a5f1d3dbd77bc3a766fc9da8e8b3cbfcc} 
uint64\+\_\+t {\bfseries m\+\_\+my\+Custodial\+Service\+Id}
\item 
\Hypertarget{class_hdtn_config_a33e682b1f20ac34285ee9a3d53bf90ee}\label{class_hdtn_config_a33e682b1f20ac34285ee9a3d53bf90ee} 
uint64\+\_\+t {\bfseries m\+\_\+my\+Router\+Service\+Id}
\item 
\Hypertarget{class_hdtn_config_a2dd719cbcc24f4a5fc41a23697b7e6ff}\label{class_hdtn_config_a2dd719cbcc24f4a5fc41a23697b7e6ff} 
bool {\bfseries m\+\_\+is\+Acs\+Aware}
\item 
\Hypertarget{class_hdtn_config_a917424b1a5201d29f3c6a101a317f8e8}\label{class_hdtn_config_a917424b1a5201d29f3c6a101a317f8e8} 
uint64\+\_\+t {\bfseries m\+\_\+acs\+Max\+Fills\+Per\+Acs\+Packet}
\item 
\Hypertarget{class_hdtn_config_aace1225fb35c736205d75d5b1e932e28}\label{class_hdtn_config_aace1225fb35c736205d75d5b1e932e28} 
uint64\+\_\+t {\bfseries m\+\_\+acs\+Send\+Period\+Milliseconds}
\item 
\Hypertarget{class_hdtn_config_ade57fbebe9559984750b0bf080bb2472}\label{class_hdtn_config_ade57fbebe9559984750b0bf080bb2472} 
uint64\+\_\+t {\bfseries m\+\_\+retransmit\+Bundle\+After\+No\+Custody\+Signal\+Milliseconds}
\item 
\Hypertarget{class_hdtn_config_aef61c6cac664c61f9246f7b89a296822}\label{class_hdtn_config_aef61c6cac664c61f9246f7b89a296822} 
uint64\+\_\+t {\bfseries m\+\_\+max\+Bundle\+Size\+Bytes}
\item 
\Hypertarget{class_hdtn_config_ab24c1dad3311075f11f222d63628af4e}\label{class_hdtn_config_ab24c1dad3311075f11f222d63628af4e} 
uint64\+\_\+t {\bfseries m\+\_\+max\+Ingress\+Bundle\+Wait\+On\+Egress\+Milliseconds}
\item 
\Hypertarget{class_hdtn_config_a425af5d97ce6ab5ea9684edfbee4d8cb}\label{class_hdtn_config_a425af5d97ce6ab5ea9684edfbee4d8cb} 
bool {\bfseries m\+\_\+buffer\+Rx\+To\+Storage\+On\+Link\+Up\+Saturation}
\item 
\Hypertarget{class_hdtn_config_a7c1257977bce6abb25e81dff99f32919}\label{class_hdtn_config_a7c1257977bce6abb25e81dff99f32919} 
uint64\+\_\+t {\bfseries m\+\_\+max\+Ltp\+Receive\+Udp\+Packet\+Size\+Bytes}
\item 
\Hypertarget{class_hdtn_config_a1430f70dfb75b202f76012c0b190ed79}\label{class_hdtn_config_a1430f70dfb75b202f76012c0b190ed79} 
uint64\+\_\+t {\bfseries m\+\_\+neighbor\+Depleted\+Storage\+Delay\+Seconds}
\item 
\Hypertarget{class_hdtn_config_a2347e8ddc21e761a9f4c4ecbb1f40382}\label{class_hdtn_config_a2347e8ddc21e761a9f4c4ecbb1f40382} 
uint64\+\_\+t {\bfseries m\+\_\+fragment\+Bundles\+Larger\+Than\+Bytes}
\item 
\Hypertarget{class_hdtn_config_ab84acd5888713c592aa29599639f8016}\label{class_hdtn_config_ab84acd5888713c592aa29599639f8016} 
bool {\bfseries m\+\_\+enforce\+Bundle\+Priority}
\item 
\Hypertarget{class_hdtn_config_acfc9a81b532bc9e5e97a71cdc2271802}\label{class_hdtn_config_acfc9a81b532bc9e5e97a71cdc2271802} 
uint16\+\_\+t {\bfseries m\+\_\+zmq\+Bound\+Router\+Pub\+Sub\+Port\+Path}
\item 
\Hypertarget{class_hdtn_config_aac5a626a77e90b917a4791c93068d004}\label{class_hdtn_config_aac5a626a77e90b917a4791c93068d004} 
uint16\+\_\+t {\bfseries m\+\_\+zmq\+Bound\+Telem\+Api\+Port\+Path}
\item 
\Hypertarget{class_hdtn_config_a8599e73ce647c6eaf9a127d8209e5fd9}\label{class_hdtn_config_a8599e73ce647c6eaf9a127d8209e5fd9} 
\mbox{\hyperlink{class_inducts_config}{Inducts\+Config}} {\bfseries m\+\_\+inducts\+Config}
\item 
\Hypertarget{class_hdtn_config_a10c625aae11e62b87d8107cacd773dd0}\label{class_hdtn_config_a10c625aae11e62b87d8107cacd773dd0} 
\mbox{\hyperlink{class_outducts_config}{Outducts\+Config}} {\bfseries m\+\_\+outducts\+Config}
\item 
\Hypertarget{class_hdtn_config_a6d7d209f02cb2422dbd366f6b1f596ec}\label{class_hdtn_config_a6d7d209f02cb2422dbd366f6b1f596ec} 
\mbox{\hyperlink{class_storage_config}{Storage\+Config}} {\bfseries m\+\_\+storage\+Config}
\end{DoxyCompactItemize}


\doxysubsection{Member Function Documentation}
\Hypertarget{class_hdtn_config_ae2660007b111d9ae481d3fcc0906dc36}\index{HdtnConfig@{HdtnConfig}!GetNewPropertyTree@{GetNewPropertyTree}}
\index{GetNewPropertyTree@{GetNewPropertyTree}!HdtnConfig@{HdtnConfig}}
\doxysubsubsection{\texorpdfstring{GetNewPropertyTree()}{GetNewPropertyTree()}}
{\footnotesize\ttfamily \label{class_hdtn_config_ae2660007b111d9ae481d3fcc0906dc36} 
boost\+::property\+\_\+tree\+::ptree Hdtn\+Config\+::\+Get\+New\+Property\+Tree (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption}) const\hspace{0.3cm}{\ttfamily [override]}, {\ttfamily [virtual]}}



Implements \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}.

\Hypertarget{class_hdtn_config_a3c39b18edcbf4225f5788445b456f5f9}\index{HdtnConfig@{HdtnConfig}!SetValuesFromPropertyTree@{SetValuesFromPropertyTree}}
\index{SetValuesFromPropertyTree@{SetValuesFromPropertyTree}!HdtnConfig@{HdtnConfig}}
\doxysubsubsection{\texorpdfstring{SetValuesFromPropertyTree()}{SetValuesFromPropertyTree()}}
{\footnotesize\ttfamily \label{class_hdtn_config_a3c39b18edcbf4225f5788445b456f5f9} 
bool Hdtn\+Config\+::\+Set\+Values\+From\+Property\+Tree (\begin{DoxyParamCaption}\item[{const boost\+::property\+\_\+tree\+::ptree \&}]{pt}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [override]}, {\ttfamily [virtual]}}



Implements \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}.



The documentation for this class was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
common/config/include/\mbox{\hyperlink{_hdtn_config_8h}{Hdtn\+Config.\+h}}\item 
common/config/src/\mbox{\hyperlink{_hdtn_config_8cpp}{Hdtn\+Config.\+cpp}}\end{DoxyCompactItemize}
