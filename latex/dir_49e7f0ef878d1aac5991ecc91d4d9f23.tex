\doxysection{containers/docker Directory Reference}
\hypertarget{dir_49e7f0ef878d1aac5991ecc91d4d9f23}{}\label{dir_49e7f0ef878d1aac5991ecc91d4d9f23}\index{containers/docker Directory Reference@{containers/docker Directory Reference}}
\doxysubsubsection*{Directories}
\begin{DoxyCompactItemize}
\item 
directory \mbox{\hyperlink{dir_4fac0654cd5c37249b8d26cb1c4d157d}{debian-\/minimal}}
\item 
directory \mbox{\hyperlink{dir_eea8826d70282a48ad35db932bbd507e}{scripts}}
\end{DoxyCompactItemize}


\doxysubsection{Detailed Description}
This directory contains Docker configurations for HDTN development and deployment. The setup includes optimized environments for both development and production use cases.\hypertarget{README.md_autotoc_md47}{}\doxysubsection{\texorpdfstring{Table of Contents}{Table of Contents}}\label{README.md_autotoc_md47}

\begin{DoxyItemize}
\item Overview
\item Prerequisites
\item Quick Start
\item Development Environment
\item Production Environment
\item Environment Variables
\item Common Operations
\item Best Practices
\item Troubleshooting
\end{DoxyItemize}\hypertarget{README.md_autotoc_md48}{}\doxysubsection{\texorpdfstring{Overview}{Overview}}\label{README.md_autotoc_md48}
The HDTN Docker environment consists of\+:


\begin{DoxyEnumerate}
\item {\bfseries{Development \doxylink{class_environment}{Environment}}}\+: Optimized for local development with hot-\/reloading capabilities
\item {\bfseries{Production \doxylink{class_environment}{Environment}}}\+: Minimized image size with multi-\/stage builds for deployment
\item {\bfseries{Docker Compose Setup}}\+: Orchestrates multiple containers with proper networking and resource limits
\end{DoxyEnumerate}\hypertarget{README.md_autotoc_md49}{}\doxysubsection{\texorpdfstring{Prerequisites}{Prerequisites}}\label{README.md_autotoc_md49}

\begin{DoxyItemize}
\item Docker Engine 20.\+10.\+0 or later
\item Docker Compose 2.\+0.\+0 or later
\item Git
\item 8GB+ RAM recommended for development
\item 20GB+ free disk space
\end{DoxyItemize}\hypertarget{README.md_autotoc_md50}{}\doxysubsection{\texorpdfstring{Quick Start}{Quick Start}}\label{README.md_autotoc_md50}
From the root of the HDTN repository\+:


\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Start\ the\ development\ environment}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ up\ hdtn-\/dev}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ In\ another\ terminal,\ access\ the\ development\ container}
\DoxyCodeLine{docker\ exec\ -\/it\ hdtn-\/dev\ bash}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Inside\ the\ container,\ build\ HDTN}
\DoxyCodeLine{cd\ /hdtn/build}
\DoxyCodeLine{cmake\ ..}
\DoxyCodeLine{make\ -\/j\$(nproc)}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Run\ HDTN}
\DoxyCodeLine{hdtn-\/one-\/process\ -\/-\/hdtn-\/config-\/file=/hdtn/config\_files/hdtn/hdtn\_node1\_cfg.json}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md51}{}\doxysubsection{\texorpdfstring{Development Environment}{Development Environment}}\label{README.md_autotoc_md51}
The development environment is designed for iterative development with\+:


\begin{DoxyItemize}
\item Source code mounted from the host for hot-\/reloading
\item Persistent build artifacts to speed up incremental builds
\item Development tools pre-\/installed (gdb, valgrind, etc.)
\end{DoxyItemize}\hypertarget{README.md_autotoc_md52}{}\doxysubsubsection{\texorpdfstring{Starting the Development Environment}{Starting the Development Environment}}\label{README.md_autotoc_md52}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Start\ only\ the\ development\ container}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ up\ hdtn-\/dev}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Or\ start\ in\ detached\ mode}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ up\ -\/d\ hdtn-\/dev}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md53}{}\doxysubsubsection{\texorpdfstring{Building HDTN in Development}{Building HDTN in Development}}\label{README.md_autotoc_md53}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Access\ the\ container}
\DoxyCodeLine{docker\ exec\ -\/it\ hdtn-\/dev\ bash}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Run\ the\ build\ script\ (incremental\ build)}
\DoxyCodeLine{build.sh}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Or\ run\ a\ clean\ build}
\DoxyCodeLine{clean-\/build.sh}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md54}{}\doxysubsubsection{\texorpdfstring{Development Workflow}{Development Workflow}}\label{README.md_autotoc_md54}

\begin{DoxyEnumerate}
\item Edit code on your host machine
\item Build inside the container using {\ttfamily build.\+sh}
\item Run and test inside the container
\item Repeat
\end{DoxyEnumerate}\hypertarget{README.md_autotoc_md55}{}\doxysubsection{\texorpdfstring{Production Environment}{Production Environment}}\label{README.md_autotoc_md55}
The production environment is optimized for deployment with\+:


\begin{DoxyItemize}
\item Multi-\/stage builds to minimize image size
\item Only runtime dependencies included
\item Health checks for monitoring
\item Resource limits for stability
\end{DoxyItemize}\hypertarget{README.md_autotoc_md56}{}\doxysubsubsection{\texorpdfstring{Building Production Images}{Building Production Images}}\label{README.md_autotoc_md56}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Build\ the\ production\ image}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ build\ hdtn-\/sender\ hdtn-\/receiver}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Or\ build\ with\ custom\ arguments}
\DoxyCodeLine{docker\ build\ -\/f\ containers/docker/Dockerfile.prod\ \(\backslash\)}
\DoxyCodeLine{\ \ -\/-\/build-\/arg\ BUILD\_TYPE=Release\ \(\backslash\)}
\DoxyCodeLine{\ \ -\/-\/build-\/arg\ CORES=8\ \(\backslash\)}
\DoxyCodeLine{\ \ -\/t\ hdtn:prod\ .}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md57}{}\doxysubsubsection{\texorpdfstring{Running a Production Setup}{Running a Production Setup}}\label{README.md_autotoc_md57}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Start\ the\ sender\ and\ receiver\ nodes}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ up\ hdtn-\/sender\ hdtn-\/receiver}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Or\ start\ in\ detached\ mode}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ up\ -\/d\ hdtn-\/sender\ hdtn-\/receiver}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md58}{}\doxysubsection{\texorpdfstring{Environment Variables}{Environment Variables}}\label{README.md_autotoc_md58}
\hypertarget{README.md_autotoc_md59}{}\doxysubsubsection{\texorpdfstring{Development Environment}{Development Environment}}\label{README.md_autotoc_md59}
\tabulinesep=1mm
\begin{longtabu}spread 0pt [c]{*{3}{|X[-1]}|}
\hline
\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Variable   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Description   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Default    }\\\cline{1-3}
\endfirsthead
\hline
\endfoot
\hline
\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Variable   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Description   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Default    }\\\cline{1-3}
\endhead
{\ttfamily HDTN\+\_\+\+SOURCE\+\_\+\+ROOT}   &Root directory of HDTN source code   &{\ttfamily /hdtn}    \\\cline{1-3}
{\ttfamily HDTN\+\_\+\+BUILD\+\_\+\+ROOT}   &Directory for build artifacts   &{\ttfamily /hdtn/build}    \\\cline{1-3}
{\ttfamily CMAKE\+\_\+\+BUILD\+\_\+\+TYPE}   &Build type (Debug/\+Release)   &{\ttfamily Debug}   \\\cline{1-3}
\end{longtabu}
\hypertarget{README.md_autotoc_md60}{}\doxysubsubsection{\texorpdfstring{Production Environment}{Production Environment}}\label{README.md_autotoc_md60}
\tabulinesep=1mm
\begin{longtabu}spread 0pt [c]{*{3}{|X[-1]}|}
\hline
\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Variable   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Description   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Default    }\\\cline{1-3}
\endfirsthead
\hline
\endfoot
\hline
\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Variable   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Description   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Default    }\\\cline{1-3}
\endhead
{\ttfamily HDTN\+\_\+\+CONFIG\+\_\+\+FILE}   &Path to HDTN configuration file   &{\ttfamily /etc/hdtn/config\+\_\+files/hdtn/hdtn\+\_\+node1\+\_\+cfg.json}   \\\cline{1-3}
\end{longtabu}
\hypertarget{README.md_autotoc_md61}{}\doxysubsubsection{\texorpdfstring{Build Arguments}{Build Arguments}}\label{README.md_autotoc_md61}
\tabulinesep=1mm
\begin{longtabu}spread 0pt [c]{*{3}{|X[-1]}|}
\hline
\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Argument   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Description   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Default    }\\\cline{1-3}
\endfirsthead
\hline
\endfoot
\hline
\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Argument   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Description   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Default    }\\\cline{1-3}
\endhead
{\ttfamily CORES}   &Number of cores to use for building   &{\ttfamily 4}    \\\cline{1-3}
{\ttfamily BUILD\+\_\+\+TYPE}   &Build type (Debug/\+Release)   &{\ttfamily Release}    \\\cline{1-3}
{\ttfamily ENABLE\+\_\+\+WEB\+\_\+\+INTERFACE}   &Enable web interface   &{\ttfamily ON}    \\\cline{1-3}
{\ttfamily ENABLE\+\_\+\+OPENSSL\+\_\+\+SUPPORT}   &Enable Open\+SSL support   &{\ttfamily ON}    \\\cline{1-3}
{\ttfamily ENABLE\+\_\+\+BPSEC}   &Enable BP Security   &{\ttfamily ON}   \\\cline{1-3}
\end{longtabu}
\hypertarget{README.md_autotoc_md62}{}\doxysubsection{\texorpdfstring{Common Operations}{Common Operations}}\label{README.md_autotoc_md62}
\hypertarget{README.md_autotoc_md63}{}\doxysubsubsection{\texorpdfstring{Accessing Container Logs}{Accessing Container Logs}}\label{README.md_autotoc_md63}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ View\ logs\ from\ a\ specific\ container}
\DoxyCodeLine{docker\ logs\ hdtn-\/dev}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Follow\ logs\ in\ real-\/time}
\DoxyCodeLine{docker\ logs\ -\/f\ hdtn-\/sender}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md64}{}\doxysubsubsection{\texorpdfstring{Executing Commands in Containers}{Executing Commands in Containers}}\label{README.md_autotoc_md64}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Run\ a\ command\ in\ a\ container}
\DoxyCodeLine{docker\ exec\ hdtn-\/dev\ ls\ -\/la\ /hdtn}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Start\ an\ interactive\ shell}
\DoxyCodeLine{docker\ exec\ -\/it\ hdtn-\/receiver\ bash}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md65}{}\doxysubsubsection{\texorpdfstring{Stopping and Removing Containers}{Stopping and Removing Containers}}\label{README.md_autotoc_md65}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Stop\ all\ containers}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ down}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Stop\ a\ specific\ container}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ stop\ hdtn-\/dev}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md66}{}\doxysubsubsection{\texorpdfstring{Cleaning Up}{Cleaning Up}}\label{README.md_autotoc_md66}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Remove\ containers\ and\ networks,\ but\ keep\ volumes}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ down}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Remove\ everything\ including\ volumes}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ down\ -\/v}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Remove\ dangling\ images}
\DoxyCodeLine{docker\ image\ prune}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md67}{}\doxysubsection{\texorpdfstring{Best Practices}{Best Practices}}\label{README.md_autotoc_md67}

\begin{DoxyEnumerate}
\item {\bfseries{Development Workflow}}\+:
\begin{DoxyItemize}
\item Edit code on your host machine
\item Build and test inside the container
\item Use incremental builds for faster development
\end{DoxyItemize}
\item {\bfseries{Resource Management}}\+:
\begin{DoxyItemize}
\item Adjust resource limits in docker-\/compose.\+yml based on your system capabilities
\item Monitor container resource usage with {\ttfamily docker stats}
\end{DoxyItemize}
\item {\bfseries{Data Persistence}}\+:
\begin{DoxyItemize}
\item Use named volumes for build artifacts and logs
\item Back up important configuration files
\end{DoxyItemize}
\item {\bfseries{Security}}\+:
\begin{DoxyItemize}
\item Keep Docker and base images updated
\item Avoid exposing unnecessary ports
\end{DoxyItemize}
\end{DoxyEnumerate}\hypertarget{README.md_autotoc_md68}{}\doxysubsection{\texorpdfstring{Troubleshooting}{Troubleshooting}}\label{README.md_autotoc_md68}
\hypertarget{README.md_autotoc_md69}{}\doxysubsubsection{\texorpdfstring{Common Issues}{Common Issues}}\label{README.md_autotoc_md69}

\begin{DoxyEnumerate}
\item {\bfseries{Build Failures}}\+:
\begin{DoxyItemize}
\item Check if you have enough disk space
\item Ensure all dependencies are installed
\item Try a clean build with {\ttfamily clean-\/build.\+sh}
\end{DoxyItemize}
\item {\bfseries{Container Networking Issues}}\+:
\begin{DoxyItemize}
\item Check if the container can reach other containers using {\ttfamily ping}
\item Verify port mappings with {\ttfamily docker compose ps}
\end{DoxyItemize}
\item {\bfseries{Performance Issues}}\+:
\begin{DoxyItemize}
\item Adjust resource limits in docker-\/compose.\+yml
\item Monitor resource usage with {\ttfamily docker stats}
\end{DoxyItemize}
\end{DoxyEnumerate}\hypertarget{README.md_autotoc_md70}{}\doxysubsubsection{\texorpdfstring{Getting Help}{Getting Help}}\label{README.md_autotoc_md70}
If you encounter issues not covered here, please\+:


\begin{DoxyEnumerate}
\item Check the HDTN documentation
\item Search for similar issues in the HDTN Git\+Hub repository
\item Open a new issue with detailed information about your problem
\end{DoxyEnumerate}\hypertarget{README.md_autotoc_md71}{}\doxysubsubsection{\texorpdfstring{Production Environment}{Production Environment}}\label{README.md_autotoc_md71}
\tabulinesep=1mm
\begin{longtabu}spread 0pt [c]{*{3}{|X[-1]}|}
\hline
\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Variable   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Description   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Default    }\\\cline{1-3}
\endfirsthead
\hline
\endfoot
\hline
\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Variable   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Description   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Default    }\\\cline{1-3}
\endhead
{\ttfamily HDTN\+\_\+\+CONFIG\+\_\+\+FILE}   &Path to HDTN configuration file   &{\ttfamily /etc/hdtn/config\+\_\+files/hdtn/hdtn\+\_\+node1\+\_\+cfg.json}   \\\cline{1-3}
\end{longtabu}
\hypertarget{README.md_autotoc_md72}{}\doxysubsubsection{\texorpdfstring{Build Arguments}{Build Arguments}}\label{README.md_autotoc_md72}
\tabulinesep=1mm
\begin{longtabu}spread 0pt [c]{*{3}{|X[-1]}|}
\hline
\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Argument   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Description   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Default    }\\\cline{1-3}
\endfirsthead
\hline
\endfoot
\hline
\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Argument   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Description   }&\PBS\centering \cellcolor{\tableheadbgcolor}\textbf{ Default    }\\\cline{1-3}
\endhead
{\ttfamily CORES}   &Number of cores to use for building   &{\ttfamily 4}    \\\cline{1-3}
{\ttfamily BUILD\+\_\+\+TYPE}   &Build type (Debug/\+Release)   &{\ttfamily Release}    \\\cline{1-3}
{\ttfamily ENABLE\+\_\+\+WEB\+\_\+\+INTERFACE}   &Enable web interface   &{\ttfamily ON}    \\\cline{1-3}
{\ttfamily ENABLE\+\_\+\+OPENSSL\+\_\+\+SUPPORT}   &Enable Open\+SSL support   &{\ttfamily ON}    \\\cline{1-3}
{\ttfamily ENABLE\+\_\+\+BPSEC}   &Enable BP Security   &{\ttfamily ON}   \\\cline{1-3}
\end{longtabu}
\hypertarget{README.md_autotoc_md73}{}\doxysubsection{\texorpdfstring{Common Operations}{Common Operations}}\label{README.md_autotoc_md73}
\hypertarget{README.md_autotoc_md74}{}\doxysubsubsection{\texorpdfstring{Accessing Container Logs}{Accessing Container Logs}}\label{README.md_autotoc_md74}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ View\ logs\ from\ a\ specific\ container}
\DoxyCodeLine{docker\ logs\ hdtn-\/dev}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Follow\ logs\ in\ real-\/time}
\DoxyCodeLine{docker\ logs\ -\/f\ hdtn-\/sender}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md75}{}\doxysubsubsection{\texorpdfstring{Executing Commands in Containers}{Executing Commands in Containers}}\label{README.md_autotoc_md75}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Run\ a\ command\ in\ a\ container}
\DoxyCodeLine{docker\ exec\ hdtn-\/dev\ ls\ -\/la\ /hdtn}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Start\ an\ interactive\ shell}
\DoxyCodeLine{docker\ exec\ -\/it\ hdtn-\/receiver\ bash}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md76}{}\doxysubsubsection{\texorpdfstring{Stopping and Removing Containers}{Stopping and Removing Containers}}\label{README.md_autotoc_md76}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Stop\ all\ containers}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ down}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Stop\ a\ specific\ container}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ stop\ hdtn-\/dev}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md77}{}\doxysubsubsection{\texorpdfstring{Cleaning Up}{Cleaning Up}}\label{README.md_autotoc_md77}

\begin{DoxyCode}{0}
\DoxyCodeLine{\#\ Remove\ containers\ and\ networks,\ but\ keep\ volumes}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ down}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Remove\ everything\ including\ volumes}
\DoxyCodeLine{docker\ compose\ -\/f\ containers/docker/docker-\/compose.yml\ down\ -\/v}
\DoxyCodeLine{}
\DoxyCodeLine{\#\ Remove\ dangling\ images}
\DoxyCodeLine{docker\ image\ prune}

\end{DoxyCode}
\hypertarget{README.md_autotoc_md78}{}\doxysubsection{\texorpdfstring{Best Practices}{Best Practices}}\label{README.md_autotoc_md78}

\begin{DoxyEnumerate}
\item {\bfseries{Development Workflow}}\+:
\begin{DoxyItemize}
\item Edit code on your host machine
\item Build and test inside the container
\item Use incremental builds for faster development
\end{DoxyItemize}
\item {\bfseries{Resource Management}}\+:
\begin{DoxyItemize}
\item Adjust resource limits in docker-\/compose.\+yml based on your system capabilities
\item Monitor container resource usage with {\ttfamily docker stats}
\end{DoxyItemize}
\item {\bfseries{Data Persistence}}\+:
\begin{DoxyItemize}
\item Use named volumes for build artifacts and logs
\item Back up important configuration files
\end{DoxyItemize}
\item {\bfseries{Security}}\+:
\begin{DoxyItemize}
\item Keep Docker and base images updated
\item Avoid exposing unnecessary ports
\end{DoxyItemize}
\end{DoxyEnumerate}\hypertarget{README.md_autotoc_md79}{}\doxysubsection{\texorpdfstring{Troubleshooting}{Troubleshooting}}\label{README.md_autotoc_md79}
\hypertarget{README.md_autotoc_md80}{}\doxysubsubsection{\texorpdfstring{Common Issues}{Common Issues}}\label{README.md_autotoc_md80}

\begin{DoxyEnumerate}
\item {\bfseries{Build Failures}}\+:
\begin{DoxyItemize}
\item Check if you have enough disk space
\item Ensure all dependencies are installed
\item Try a clean build with {\ttfamily clean-\/build.\+sh}
\end{DoxyItemize}
\item {\bfseries{Container Networking Issues}}\+:
\begin{DoxyItemize}
\item Check if the container can reach other containers using {\ttfamily ping}
\item Verify port mappings with {\ttfamily docker compose ps}
\end{DoxyItemize}
\item {\bfseries{Performance Issues}}\+:
\begin{DoxyItemize}
\item Adjust resource limits in docker-\/compose.\+yml
\item Monitor resource usage with {\ttfamily docker stats}
\end{DoxyItemize}
\end{DoxyEnumerate}\hypertarget{README.md_autotoc_md81}{}\doxysubsubsection{\texorpdfstring{Getting Help}{Getting Help}}\label{README.md_autotoc_md81}
If you encounter issues not covered here, please\+:


\begin{DoxyEnumerate}
\item Check the HDTN documentation
\item Search for similar issues in the HDTN Git\+Hub repository
\item Open a new issue with detailed information about your problem 
\end{DoxyEnumerate}