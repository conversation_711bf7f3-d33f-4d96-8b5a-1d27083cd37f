\doxysection{Tcpcl\+V3\+Bidirectional\+Link.\+h}
\hypertarget{_tcpcl_v3_bidirectional_link_8h_source}{}\label{_tcpcl_v3_bidirectional_link_8h_source}\index{common/tcpcl/include/TcpclV3BidirectionalLink.h@{common/tcpcl/include/TcpclV3BidirectionalLink.h}}
\mbox{\hyperlink{_tcpcl_v3_bidirectional_link_8h}{Go to the documentation of this file.}}
\begin{DoxyCode}{0}
\DoxyCodeLine{00001\ }
\DoxyCodeLine{00022\ }
\DoxyCodeLine{00023\ \textcolor{preprocessor}{\#ifndef\ \_TCPCLV3\_BIDIRECTIONAL\_LINK\_H}}
\DoxyCodeLine{00024\ \textcolor{preprocessor}{\#define\ \_TCPCLV3\_BIDIRECTIONAL\_LINK\_H\ 1}}
\DoxyCodeLine{00025\ }
\DoxyCodeLine{00026\ \textcolor{preprocessor}{\#include\ <string>}}
\DoxyCodeLine{00027\ \textcolor{preprocessor}{\#include\ <boost/thread.hpp>}}
\DoxyCodeLine{00028\ \textcolor{preprocessor}{\#include\ <boost/asio.hpp>}}
\DoxyCodeLine{00029\ \textcolor{preprocessor}{\#include\ <map>}}
\DoxyCodeLine{00030\ \textcolor{preprocessor}{\#include\ <vector>}}
\DoxyCodeLine{00031\ \textcolor{preprocessor}{\#include\ <memory>}}
\DoxyCodeLine{00032\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_tcpcl_8h}{Tcpcl.h}}"{}}}
\DoxyCodeLine{00033\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_tcp_async_sender_8h}{TcpAsyncSender.h}}"{}}}
\DoxyCodeLine{00034\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_telemetry_definitions_8h}{TelemetryDefinitions.h}}"{}}}
\DoxyCodeLine{00035\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_circular_index_buffer_single_producer_single_consumer_configurable_8h}{CircularIndexBufferSingleProducerSingleConsumerConfigurable.h}}"{}}}
\DoxyCodeLine{00036\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_bidirectional_link_8h}{BidirectionalLink.h}}"{}}}
\DoxyCodeLine{00037\ \textcolor{preprocessor}{\#include\ "{}\mbox{\hyperlink{_bundle_callback_function_defines_8h}{BundleCallbackFunctionDefines.h}}"{}}}
\DoxyCodeLine{00038\ \textcolor{preprocessor}{\#include\ <atomic>}}
\DoxyCodeLine{00039\ }
\DoxyCodeLine{00040\ \textcolor{keyword}{class\ }CLASS\_VISIBILITY\_TCPCL\_LIB\ TcpclV3BidirectionalLink\ :\ \textcolor{keyword}{public}\ BidirectionalLink\ \{}
\DoxyCodeLine{00041\ \textcolor{keyword}{public}:}
\DoxyCodeLine{00042\ \ \ \ \ TCPCL\_LIB\_EXPORT\ TcpclV3BidirectionalLink(}
\DoxyCodeLine{00043\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ std::string\ \&\ implementationStringForCout,}
\DoxyCodeLine{00044\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ shutdownMessageReconnectionDelaySecondsToSend,}
\DoxyCodeLine{00045\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ deleteSocketAfterShutdown,}
\DoxyCodeLine{00046\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ contactHeaderMustReply,}
\DoxyCodeLine{00047\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint16\_t\ desiredKeepAliveIntervalSeconds,}
\DoxyCodeLine{00048\ \ \ \ \ \ \ \ \ boost::asio::io\_service\ *\ externalIoServicePtr,}
\DoxyCodeLine{00049\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ maxUnacked,}
\DoxyCodeLine{00050\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ maxBundleSizeBytes,}
\DoxyCodeLine{00051\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ maxFragmentSize,}
\DoxyCodeLine{00052\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ myNodeId,}
\DoxyCodeLine{00053\ \ \ \ \ \ \ \ \ \textcolor{keyword}{const}\ std::string\ \&\ expectedRemoteEidUriStringIfNotEmpty}
\DoxyCodeLine{00054\ \ \ \ \ );}
\DoxyCodeLine{00055\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ \string~TcpclV3BidirectionalLink();}
\DoxyCodeLine{00056\ }
\DoxyCodeLine{00057\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{bool}\ BaseClass\_Forward(\textcolor{keyword}{const}\ uint8\_t*\ bundleData,\ \textcolor{keyword}{const}\ std::size\_t\ size,\ std::vector<uint8\_t>\&\&\ userData);}
\DoxyCodeLine{00058\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{bool}\ BaseClass\_Forward(padded\_vector\_uint8\_t\&\ dataVec,\ std::vector<uint8\_t>\&\&\ userData);}
\DoxyCodeLine{00059\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{bool}\ BaseClass\_Forward(\mbox{\hyperlink{classzmq_1_1message__t}{zmq::message\_t}}\ \&\ dataZmq,\ std::vector<uint8\_t>\&\&\ userData);}
\DoxyCodeLine{00060\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{bool}\ BaseClass\_Forward(std::unique\_ptr<zmq::message\_t>\ \&\ zmqMessageUniquePtr,\ padded\_vector\_uint8\_t\&\ vecMessage,\ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ usingZmqData,\ std::vector<uint8\_t>\&\&\ userData);}
\DoxyCodeLine{00061\ }
\DoxyCodeLine{00062\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ Virtual\_GetMaxTxBundlesInPipeline()\ \textcolor{keyword}{override};}
\DoxyCodeLine{00063\ }
\DoxyCodeLine{00064\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_SetOnFailedBundleVecSendCallback(\textcolor{keyword}{const}\ OnFailedBundleVecSendCallback\_t\&\ callback);}
\DoxyCodeLine{00065\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_SetOnFailedBundleZmqSendCallback(\textcolor{keyword}{const}\ OnFailedBundleZmqSendCallback\_t\&\ callback);}
\DoxyCodeLine{00066\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_SetOnSuccessfulBundleSendCallback(\textcolor{keyword}{const}\ OnSuccessfulBundleSendCallback\_t\&\ callback);}
\DoxyCodeLine{00067\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_SetOnOutductLinkStatusChangedCallback(\textcolor{keyword}{const}\ OnOutductLinkStatusChangedCallback\_t\&\ callback);}
\DoxyCodeLine{00068\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_SetUserAssignedUuid(uint64\_t\ userAssignedUuid);}
\DoxyCodeLine{00069\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_GetTelemetry(\mbox{\hyperlink{struct_tcpcl_v3_induct_connection_telemetry__t}{TcpclV3InductConnectionTelemetry\_t}}\&\ telem)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00070\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_GetTelemetry(\mbox{\hyperlink{struct_tcpcl_v3_outduct_telemetry__t}{TcpclV3OutductTelemetry\_t}}\&\ telem)\ \textcolor{keyword}{const};}
\DoxyCodeLine{00071\ }
\DoxyCodeLine{00072\ \textcolor{keyword}{protected}:}
\DoxyCodeLine{00073\ \ \ \ \ \textcolor{keyword}{const}\ std::string\ M\_BASE\_IMPLEMENTATION\_STRING\_FOR\_COUT;}
\DoxyCodeLine{00074\ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ M\_BASE\_SHUTDOWN\_MESSAGE\_RECONNECTION\_DELAY\_SECONDS\_TO\_SEND;}
\DoxyCodeLine{00075\ \ \ \ \ \textcolor{keyword}{const}\ uint16\_t\ M\_BASE\_DESIRED\_KEEPALIVE\_INTERVAL\_SECONDS;}
\DoxyCodeLine{00076\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ M\_BASE\_DELETE\_SOCKET\_AFTER\_SHUTDOWN;}
\DoxyCodeLine{00077\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{bool}\ M\_BASE\_CONTACT\_HEADER\_MUST\_REPLY;}
\DoxyCodeLine{00078\ \ \ \ \ \textcolor{keyword}{const}\ std::string\ M\_BASE\_THIS\_TCPCL\_EID\_STRING;}
\DoxyCodeLine{00079\ \ \ \ \ std::string\ M\_BASE\_EXPECTED\_REMOTE\_CONTACT\_HEADER\_EID\_STRING\_IF\_NOT\_EMPTY;}
\DoxyCodeLine{00080\ \ \ \ \ uint16\_t\ m\_base\_keepAliveIntervalSeconds;}
\DoxyCodeLine{00081\ \ \ \ \ std::unique\_ptr<boost::asio::io\_service>\ m\_base\_localIoServiceUniquePtr;\ \textcolor{comment}{//if\ an\ external\ one\ is\ not\ provided,\ create\ it\ here\ and\ set\ the\ ioServiceRef\ below\ to\ it}}
\DoxyCodeLine{00082\ \ \ \ \ boost::asio::io\_service\ \&\ m\_base\_ioServiceRef;}
\DoxyCodeLine{00083\ \ \ \ \ boost::asio::deadline\_timer\ m\_base\_noKeepAlivePacketReceivedTimer;}
\DoxyCodeLine{00084\ \ \ \ \ boost::asio::deadline\_timer\ m\_base\_needToSendKeepAliveMessageTimer;}
\DoxyCodeLine{00085\ \ \ \ \ boost::asio::deadline\_timer\ m\_base\_sendShutdownMessageTimeoutTimer;}
\DoxyCodeLine{00086\ \ \ \ \ \textcolor{keywordtype}{bool}\ m\_base\_shutdownCalled;}
\DoxyCodeLine{00087\ \ \ \ \ std::atomic<bool>\ m\_base\_readyToForward;\ \textcolor{comment}{//bundleSource}}
\DoxyCodeLine{00088\ \ \ \ \ std::atomic<bool>\ m\_base\_sinkIsSafeToDelete;\ \textcolor{comment}{//bundleSink}}
\DoxyCodeLine{00089\ \ \ \ \ std::atomic<bool>\ m\_base\_tcpclShutdownComplete;\ \textcolor{comment}{//bundleSource}}
\DoxyCodeLine{00090\ \ \ \ \ std::atomic<bool>\ m\_base\_useLocalConditionVariableAckReceived;\ \textcolor{comment}{//bundleSource}}
\DoxyCodeLine{00091\ \ \ \ \ std::atomic<bool>\ m\_base\_dataReceivedServedAsKeepaliveReceived;}
\DoxyCodeLine{00092\ \ \ \ \ std::atomic<bool>\ m\_base\_dataSentServedAsKeepaliveSent;}
\DoxyCodeLine{00093\ \ \ \ \ boost::condition\_variable\ m\_base\_localConditionVariableAckReceived;}
\DoxyCodeLine{00094\ \ \ \ \ uint64\_t\ m\_base\_reconnectionDelaySecondsIfNotZero;}
\DoxyCodeLine{00095\ }
\DoxyCodeLine{00096\ \ \ \ \ \mbox{\hyperlink{class_tcpcl}{Tcpcl}}\ m\_base\_tcpclV3RxStateMachine;}
\DoxyCodeLine{00097\ \ \ \ \ CONTACT\_HEADER\_FLAGS\ m\_base\_contactHeaderFlags;}
\DoxyCodeLine{00098\ \ \ \ \ std::string\ m\_base\_tcpclRemoteEidString;}
\DoxyCodeLine{00099\ \ \ \ \ uint64\_t\ m\_base\_tcpclRemoteNodeId;}
\DoxyCodeLine{00100\ \ \ \ \ std::shared\_ptr<boost::asio::ip::tcp::socket>\ m\_base\_tcpSocketPtr;}
\DoxyCodeLine{00101\ \ \ \ \ std::unique\_ptr<TcpAsyncSender>\ m\_base\_tcpAsyncSenderPtr;}
\DoxyCodeLine{00102\ \ \ \ \ TcpAsyncSenderElement::OnSuccessfulSendCallbackByIoServiceThread\_t\ m\_base\_handleTcpSendCallback;}
\DoxyCodeLine{00103\ \ \ \ \ TcpAsyncSenderElement::OnSuccessfulSendCallbackByIoServiceThread\_t\ m\_base\_handleTcpSendShutdownCallback;}
\DoxyCodeLine{00104\ \ \ \ \ padded\_vector\_uint8\_t\ m\_base\_fragmentedBundleRxConcat;}
\DoxyCodeLine{00105\ }
\DoxyCodeLine{00106\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ M\_BASE\_MAX\_UNACKED\_BUNDLES\_IN\_PIPELINE;}
\DoxyCodeLine{00107\ \ \ \ \ \textcolor{keyword}{const}\ \textcolor{keywordtype}{unsigned}\ \textcolor{keywordtype}{int}\ M\_BASE\_UNACKED\_BUNDLE\_CB\_SIZE;}
\DoxyCodeLine{00108\ \ \ \ \ \mbox{\hyperlink{class_circular_index_buffer_single_producer_single_consumer_configurable}{CircularIndexBufferSingleProducerSingleConsumerConfigurable}}\ m\_base\_bytesToAckCb;}
\DoxyCodeLine{00109\ \ \ \ \ std::vector<uint64\_t>\ m\_base\_bytesToAckCbVec;}
\DoxyCodeLine{00110\ \ \ \ \ std::vector<std::vector<uint64\_t>\ >\ m\_base\_fragmentBytesToAckCbVec;}
\DoxyCodeLine{00111\ \ \ \ \ std::vector<uint64\_t>\ m\_base\_fragmentVectorIndexCbVec;}
\DoxyCodeLine{00112\ \ \ \ \ std::vector<std::vector<uint8\_t>\ >\ m\_base\_userDataCbVec;}
\DoxyCodeLine{00113\ \ \ \ \ \textcolor{keyword}{const}\ uint64\_t\ M\_BASE\_MAX\_FRAGMENT\_SIZE;}
\DoxyCodeLine{00114\ }
\DoxyCodeLine{00115\ \ \ \ \ OnFailedBundleVecSendCallback\_t\ m\_base\_onFailedBundleVecSendCallback;}
\DoxyCodeLine{00116\ \ \ \ \ OnFailedBundleZmqSendCallback\_t\ m\_base\_onFailedBundleZmqSendCallback;}
\DoxyCodeLine{00117\ \ \ \ \ OnSuccessfulBundleSendCallback\_t\ m\_base\_onSuccessfulBundleSendCallback;}
\DoxyCodeLine{00118\ \ \ \ \ OnOutductLinkStatusChangedCallback\_t\ m\_base\_onOutductLinkStatusChangedCallback;}
\DoxyCodeLine{00119\ \ \ \ \ uint64\_t\ m\_base\_userAssignedUuid;}
\DoxyCodeLine{00120\ }
\DoxyCodeLine{00121\ \ \ \ \ std::string\ m\_base\_inductConnectionName;}
\DoxyCodeLine{00122\ \ \ \ \ std::string\ m\_base\_inductInputName;}
\DoxyCodeLine{00123\ }
\DoxyCodeLine{00124\ \textcolor{keyword}{protected}:}
\DoxyCodeLine{00125\ \ \ \ \ }
\DoxyCodeLine{00126\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_TryToWaitForAllBundlesToFinishSending();}
\DoxyCodeLine{00127\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_DoTcpclShutdown(\textcolor{keywordtype}{bool}\ sendShutdownMessage,\ \textcolor{keywordtype}{bool}\ reasonWasTimeOut);}
\DoxyCodeLine{00128\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ Virtual\_OnTcpclShutdownComplete\_CalledFromIoServiceThread()\ =\ 0;}
\DoxyCodeLine{00129\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ Virtual\_OnSuccessfulWholeBundleAcknowledged()\ =\ 0;}
\DoxyCodeLine{00130\ \ \ \ \ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ Virtual\_WholeBundleReady(padded\_vector\_uint8\_t\ \&\ wholeBundleVec)\ =\ 0;}
\DoxyCodeLine{00131\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ Virtual\_OnTcpSendSuccessful\_CalledFromIoServiceThread();}
\DoxyCodeLine{00132\ \ \ \ \ TCPCL\_LIB\_EXPORT\ \textcolor{keyword}{virtual}\ \textcolor{keywordtype}{void}\ Virtual\_OnContactHeaderCompletedSuccessfully();}
\DoxyCodeLine{00133\ }
\DoxyCodeLine{00134\ \textcolor{keyword}{private}:}
\DoxyCodeLine{00135\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_ContactHeaderCallback(CONTACT\_HEADER\_FLAGS\ flags,\ uint16\_t\ keepAliveIntervalSeconds,\ \textcolor{keyword}{const}\ std::string\ \&\ localEid);}
\DoxyCodeLine{00136\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_DataSegmentCallback(padded\_vector\_uint8\_t\ \&\ dataSegmentDataVec,\ \textcolor{keywordtype}{bool}\ isStartFlag,\ \textcolor{keywordtype}{bool}\ isEndFlag);}
\DoxyCodeLine{00137\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_AckCallback(uint64\_t\ totalBytesAcknowledged);}
\DoxyCodeLine{00138\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_RestartNoKeepaliveReceivedTimer();}
\DoxyCodeLine{00139\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_RestartNeedToSendKeepAliveMessageTimer();}
\DoxyCodeLine{00140\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_KeepAliveCallback();}
\DoxyCodeLine{00141\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_ShutdownCallback(\textcolor{keywordtype}{bool}\ hasReasonCode,\ SHUTDOWN\_REASON\_CODES\ shutdownReasonCode,}
\DoxyCodeLine{00142\ \ \ \ \ \ \ \ \ \textcolor{keywordtype}{bool}\ hasReconnectionDelay,\ uint64\_t\ reconnectionDelaySeconds);}
\DoxyCodeLine{00143\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_BundleRefusalCallback(BUNDLE\_REFUSAL\_CODES\ refusalCode);}
\DoxyCodeLine{00144\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_NextBundleLengthCallback(uint64\_t\ nextBundleLength);}
\DoxyCodeLine{00145\ }
\DoxyCodeLine{00146\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_HandleTcpSend(\textcolor{keyword}{const}\ boost::system::error\_code\&\ error,\ std::size\_t\ bytes\_transferred,\ \mbox{\hyperlink{struct_tcp_async_sender_element}{TcpAsyncSenderElement}}*\ elPtr);}
\DoxyCodeLine{00147\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_HandleTcpSendShutdown(\textcolor{keyword}{const}\ boost::system::error\_code\&\ error,\ std::size\_t\ bytes\_transferred,\ \mbox{\hyperlink{struct_tcp_async_sender_element}{TcpAsyncSenderElement}}*\ elPtr);}
\DoxyCodeLine{00148\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_OnNoKeepAlivePacketReceived\_TimerExpired(\textcolor{keyword}{const}\ boost::system::error\_code\&\ e);}
\DoxyCodeLine{00149\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_OnNeedToSendKeepAliveMessage\_TimerExpired(\textcolor{keyword}{const}\ boost::system::error\_code\&\ e);}
\DoxyCodeLine{00150\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_DoHandleSocketShutdown(\textcolor{keywordtype}{bool}\ sendShutdownMessage,\ \textcolor{keywordtype}{bool}\ reasonWasTimeOut);}
\DoxyCodeLine{00151\ \ \ \ \ TCPCL\_LIB\_NO\_EXPORT\ \textcolor{keywordtype}{void}\ BaseClass\_OnSendShutdownMessageTimeout\_TimerExpired(\textcolor{keyword}{const}\ boost::system::error\_code\&\ e);}
\DoxyCodeLine{00152\ }
\DoxyCodeLine{00153\ \ \ \ \ }
\DoxyCodeLine{00154\ \};}
\DoxyCodeLine{00155\ }
\DoxyCodeLine{00156\ }
\DoxyCodeLine{00157\ }
\DoxyCodeLine{00158\ \textcolor{preprocessor}{\#endif\ \ }\textcolor{comment}{//\_TCPCLV3\_BIDIRECTIONAL\_LINK\_H}}

\end{DoxyCode}
