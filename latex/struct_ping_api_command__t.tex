\doxysection{Ping\+Api\+Command\+\_\+t Struct Reference}
\hypertarget{struct_ping_api_command__t}{}\label{struct_ping_api_command__t}\index{PingApiCommand\_t@{PingApiCommand\_t}}
Inheritance diagram for Ping\+Api\+Command\+\_\+t\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=3.000000cm]{struct_ping_api_command__t}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT \mbox{\hyperlink{struct_ping_api_command__t_a6b59dd907433d32437cc99ee041ed430}{Ping\+Api\+Command\+\_\+t}} ()
\item 
\Hypertarget{struct_ping_api_command__t_a70e8ed6c82d8cf8437114ac6ea9b8ba9}\label{struct_ping_api_command__t_a70e8ed6c82d8cf8437114ac6ea9b8ba9} 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT bool {\bfseries operator==} (const \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}} \&o) const
\item 
\Hypertarget{struct_ping_api_command__t_a109e0aa361df317071ad4b7655d7faaf}\label{struct_ping_api_command__t_a109e0aa361df317071ad4b7655d7faaf} 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT bool {\bfseries operator!=} (const \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}} \&o) const
\item 
virtual TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT boost\+::property\+\_\+tree\+::ptree \mbox{\hyperlink{struct_ping_api_command__t_ac280e8eb92ec7ea51b8d55537c8453fd}{Get\+New\+Property\+Tree}} () const override
\item 
virtual TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT bool \mbox{\hyperlink{struct_ping_api_command__t_a33a1a6c3cdf8c449fcf768a8bbe2aaca}{Set\+Values\+From\+Property\+Tree}} (const boost\+::property\+\_\+tree\+::ptree \&pt) override
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}}}
\begin{DoxyCompactItemize}
\item 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT \mbox{\hyperlink{struct_api_command__t_a1aebcd99dd9eed18498c4638bc46a04d}{Api\+Command\+\_\+t}} ()
\item 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT bool {\bfseries operator==} (const \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}} \&o) const
\item 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT bool {\bfseries operator!=} (const \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}} \&o) const
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}}
\begin{DoxyCompactItemize}
\item 
std\+::string {\bfseries To\+Json} (bool pretty=true) const
\item 
bool {\bfseries To\+Json\+File} (const boost\+::filesystem\+::path \&file\+Path, bool pretty=true) const
\item 
std\+::string {\bfseries To\+Xml} () const
\item 
bool {\bfseries To\+Xml\+File} (const std\+::string \&file\+Name, char indent\+Character=\textquotesingle{} \textquotesingle{}, int indent\+Count=2) const
\item 
bool {\bfseries Set\+Values\+From\+Json} (const std\+::string \&json\+String)
\item 
bool {\bfseries Set\+Values\+From\+Json\+Char\+Array} (const char \texorpdfstring{$\ast$}{*}data, const std\+::size\+\_\+t size)
\end{DoxyCompactItemize}
\doxysubsubsection*{Public Attributes}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{struct_ping_api_command__t_a1a4ba31140a1d0b164479296187a0171}\label{struct_ping_api_command__t_a1a4ba31140a1d0b164479296187a0171} 
uint64\+\_\+t {\bfseries m\+\_\+node\+Id}
\item 
\Hypertarget{struct_ping_api_command__t_ae1f8beca663ed8990dd706c5af123927}\label{struct_ping_api_command__t_ae1f8beca663ed8990dd706c5af123927} 
uint64\+\_\+t {\bfseries m\+\_\+ping\+Service\+Number}
\item 
\Hypertarget{struct_ping_api_command__t_aa4a1309249c223c196a10040fa19dac6}\label{struct_ping_api_command__t_aa4a1309249c223c196a10040fa19dac6} 
uint64\+\_\+t {\bfseries m\+\_\+bp\+Version}
\end{DoxyCompactItemize}
\doxysubsection*{Public Attributes inherited from \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}}}
\begin{DoxyCompactItemize}
\item 
std\+::string {\bfseries m\+\_\+api\+Call}
\end{DoxyCompactItemize}
\doxysubsubsection*{Static Public Attributes}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{struct_ping_api_command__t_a34088cdd24cb7ae42dcc5662bb02953a}\label{struct_ping_api_command__t_a34088cdd24cb7ae42dcc5662bb02953a} 
static TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT const std\+::string {\bfseries name} = "{}ping"{}
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Static Public Member Functions inherited from \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}}}
\begin{DoxyCompactItemize}
\item 
static TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT std\+::shared\+\_\+ptr$<$ \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}} $>$ {\bfseries Create\+From\+Json} (const std\+::string \&json\+Str)
\end{DoxyCompactItemize}
\doxysubsection*{Static Public Member Functions inherited from \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}}
\begin{DoxyCompactItemize}
\item 
static bool {\bfseries Load\+Text\+File\+Into\+String} (const boost\+::filesystem\+::path \&file\+Path, std\+::string \&file\+Contents\+As\+String)
\item 
static void {\bfseries Get\+All\+Json\+Keys} (const std\+::string \&json\+Text, std\+::set$<$ std\+::string $>$ \&json\+Keys\+No\+Quotes\+Set\+To\+Append)
\item 
static void {\bfseries Get\+All\+Json\+Keys\+Line\+By\+Line} (std\+::istream \&stream, std\+::set$<$ std\+::string $>$ \&json\+Keys\+No\+Quotes\+Set\+To\+Append)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+File\+Path} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, const boost\+::filesystem\+::path \&original\+User\+Json\+File\+Path, std\+::string \&returned\+Error\+Message)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+String} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, const std\+::string \&original\+User\+Json\+String, std\+::string \&returned\+Error\+Message)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+Stream} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, std\+::istream \&original\+User\+Json\+Stream, std\+::string \&returned\+Error\+Message)
\item 
static std\+::string {\bfseries Pt\+To\+Json\+String} (const boost\+::property\+\_\+tree\+::ptree \&pt, bool pretty=true)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+Char\+Array} (char \texorpdfstring{$\ast$}{*}data, const std\+::size\+\_\+t size, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+Stream} (std\+::istream \&json\+Stream, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+String} (const std\+::string \&json\+Str, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+File\+Path} (const boost\+::filesystem\+::path \&json\+File\+Path, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static std\+::string {\bfseries Pt\+To\+Xml\+String} (const boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static boost\+::property\+\_\+tree\+::ptree {\bfseries Get\+Property\+Tree\+From\+Xml\+String} (const std\+::string \&json\+Str)
\item 
static boost\+::property\+\_\+tree\+::ptree {\bfseries Get\+Property\+Tree\+From\+Xml\+File} (const std\+::string \&xml\+File\+Name)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{struct_ping_api_command__t_a6b59dd907433d32437cc99ee041ed430}\index{PingApiCommand\_t@{PingApiCommand\_t}!PingApiCommand\_t@{PingApiCommand\_t}}
\index{PingApiCommand\_t@{PingApiCommand\_t}!PingApiCommand\_t@{PingApiCommand\_t}}
\doxysubsubsection{\texorpdfstring{PingApiCommand\_t()}{PingApiCommand\_t()}}
{\footnotesize\ttfamily \label{struct_ping_api_command__t_a6b59dd907433d32437cc99ee041ed430} 
Ping\+Api\+Command\+\_\+t\+::\+Ping\+Api\+Command\+\_\+t (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\doxylink{struct_ping_api_command__t}{Ping\+Api\+Command\+\_\+t} 

\doxysubsection{Member Function Documentation}
\Hypertarget{struct_ping_api_command__t_ac280e8eb92ec7ea51b8d55537c8453fd}\index{PingApiCommand\_t@{PingApiCommand\_t}!GetNewPropertyTree@{GetNewPropertyTree}}
\index{GetNewPropertyTree@{GetNewPropertyTree}!PingApiCommand\_t@{PingApiCommand\_t}}
\doxysubsubsection{\texorpdfstring{GetNewPropertyTree()}{GetNewPropertyTree()}}
{\footnotesize\ttfamily \label{struct_ping_api_command__t_ac280e8eb92ec7ea51b8d55537c8453fd} 
boost\+::property\+\_\+tree\+::ptree Ping\+Api\+Command\+\_\+t\+::\+Get\+New\+Property\+Tree (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption}) const\hspace{0.3cm}{\ttfamily [override]}, {\ttfamily [virtual]}}



Reimplemented from \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}}.

\Hypertarget{struct_ping_api_command__t_a33a1a6c3cdf8c449fcf768a8bbe2aaca}\index{PingApiCommand\_t@{PingApiCommand\_t}!SetValuesFromPropertyTree@{SetValuesFromPropertyTree}}
\index{SetValuesFromPropertyTree@{SetValuesFromPropertyTree}!PingApiCommand\_t@{PingApiCommand\_t}}
\doxysubsubsection{\texorpdfstring{SetValuesFromPropertyTree()}{SetValuesFromPropertyTree()}}
{\footnotesize\ttfamily \label{struct_ping_api_command__t_a33a1a6c3cdf8c449fcf768a8bbe2aaca} 
bool Ping\+Api\+Command\+\_\+t\+::\+Set\+Values\+From\+Property\+Tree (\begin{DoxyParamCaption}\item[{const boost\+::property\+\_\+tree\+::ptree \&}]{pt}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [override]}, {\ttfamily [virtual]}}



Reimplemented from \mbox{\hyperlink{struct_api_command__t}{Api\+Command\+\_\+t}}.



The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
common/telemetry\+\_\+definitions/include/\mbox{\hyperlink{_telemetry_definitions_8h}{Telemetry\+Definitions.\+h}}\item 
common/telemetry\+\_\+definitions/src/Telemetry\+Definitions.\+cpp\end{DoxyCompactItemize}
