\doxysection{Api\+Resp\+\_\+t Struct Reference}
\hypertarget{struct_api_resp__t}{}\label{struct_api_resp__t}\index{ApiResp\_t@{ApiResp\_t}}
Inheritance diagram for Api\+Resp\+\_\+t\+:\begin{figure}[H]
\begin{center}
\leavevmode
\includegraphics[height=2.000000cm]{struct_api_resp__t}
\end{center}
\end{figure}
\doxysubsubsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT \mbox{\hyperlink{struct_api_resp__t_aa23cbcda5cace96e01f1f02b31889537}{Api\+Resp\+\_\+t}} ()
\item 
virtual TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT boost\+::property\+\_\+tree\+::ptree \mbox{\hyperlink{struct_api_resp__t_a9ca5e376ef51a4f1a81ffa8b384e0d97}{Get\+New\+Property\+Tree}} () const override
\item 
virtual TELEMETRY\+\_\+\+DEFINITIONS\+\_\+\+EXPORT bool \mbox{\hyperlink{struct_api_resp__t_ae5dd20e6a788da297bb31e78f8094b4e}{Set\+Values\+From\+Property\+Tree}} (const boost\+::property\+\_\+tree\+::ptree \&pt) override
\end{DoxyCompactItemize}
\doxysubsection*{Public Member Functions inherited from \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}}
\begin{DoxyCompactItemize}
\item 
std\+::string {\bfseries To\+Json} (bool pretty=true) const
\item 
bool {\bfseries To\+Json\+File} (const boost\+::filesystem\+::path \&file\+Path, bool pretty=true) const
\item 
std\+::string {\bfseries To\+Xml} () const
\item 
bool {\bfseries To\+Xml\+File} (const std\+::string \&file\+Name, char indent\+Character=\textquotesingle{} \textquotesingle{}, int indent\+Count=2) const
\item 
bool {\bfseries Set\+Values\+From\+Json} (const std\+::string \&json\+String)
\item 
bool {\bfseries Set\+Values\+From\+Json\+Char\+Array} (const char \texorpdfstring{$\ast$}{*}data, const std\+::size\+\_\+t size)
\end{DoxyCompactItemize}
\doxysubsubsection*{Public Attributes}
\begin{DoxyCompactItemize}
\item 
\Hypertarget{struct_api_resp__t_a3c0551ef27d9d6da5a059133fff3fdf7}\label{struct_api_resp__t_a3c0551ef27d9d6da5a059133fff3fdf7} 
bool {\bfseries m\+\_\+success}
\item 
\Hypertarget{struct_api_resp__t_a08948943b76b0cedb4ff0dd3bfd2777c}\label{struct_api_resp__t_a08948943b76b0cedb4ff0dd3bfd2777c} 
std\+::string {\bfseries m\+\_\+message}
\end{DoxyCompactItemize}
\doxysubsubsection*{Additional Inherited Members}
\doxysubsection*{Static Public Member Functions inherited from \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}}
\begin{DoxyCompactItemize}
\item 
static bool {\bfseries Load\+Text\+File\+Into\+String} (const boost\+::filesystem\+::path \&file\+Path, std\+::string \&file\+Contents\+As\+String)
\item 
static void {\bfseries Get\+All\+Json\+Keys} (const std\+::string \&json\+Text, std\+::set$<$ std\+::string $>$ \&json\+Keys\+No\+Quotes\+Set\+To\+Append)
\item 
static void {\bfseries Get\+All\+Json\+Keys\+Line\+By\+Line} (std\+::istream \&stream, std\+::set$<$ std\+::string $>$ \&json\+Keys\+No\+Quotes\+Set\+To\+Append)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+File\+Path} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, const boost\+::filesystem\+::path \&original\+User\+Json\+File\+Path, std\+::string \&returned\+Error\+Message)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+String} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, const std\+::string \&original\+User\+Json\+String, std\+::string \&returned\+Error\+Message)
\item 
static bool {\bfseries Has\+Unused\+Json\+Variables\+In\+Stream} (const \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}} \&config, std\+::istream \&original\+User\+Json\+Stream, std\+::string \&returned\+Error\+Message)
\item 
static std\+::string {\bfseries Pt\+To\+Json\+String} (const boost\+::property\+\_\+tree\+::ptree \&pt, bool pretty=true)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+Char\+Array} (char \texorpdfstring{$\ast$}{*}data, const std\+::size\+\_\+t size, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+Stream} (std\+::istream \&json\+Stream, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+String} (const std\+::string \&json\+Str, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static bool {\bfseries Get\+Property\+Tree\+From\+Json\+File\+Path} (const boost\+::filesystem\+::path \&json\+File\+Path, boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static std\+::string {\bfseries Pt\+To\+Xml\+String} (const boost\+::property\+\_\+tree\+::ptree \&pt)
\item 
static boost\+::property\+\_\+tree\+::ptree {\bfseries Get\+Property\+Tree\+From\+Xml\+String} (const std\+::string \&json\+Str)
\item 
static boost\+::property\+\_\+tree\+::ptree {\bfseries Get\+Property\+Tree\+From\+Xml\+File} (const std\+::string \&xml\+File\+Name)
\end{DoxyCompactItemize}


\doxysubsection{Constructor \& Destructor Documentation}
\Hypertarget{struct_api_resp__t_aa23cbcda5cace96e01f1f02b31889537}\index{ApiResp\_t@{ApiResp\_t}!ApiResp\_t@{ApiResp\_t}}
\index{ApiResp\_t@{ApiResp\_t}!ApiResp\_t@{ApiResp\_t}}
\doxysubsubsection{\texorpdfstring{ApiResp\_t()}{ApiResp\_t()}}
{\footnotesize\ttfamily \label{struct_api_resp__t_aa23cbcda5cace96e01f1f02b31889537} 
Api\+Resp\+\_\+t\+::\+Api\+Resp\+\_\+t (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption})}

\doxylink{struct_api_resp__t}{Api\+Resp\+\_\+t} 

\doxysubsection{Member Function Documentation}
\Hypertarget{struct_api_resp__t_a9ca5e376ef51a4f1a81ffa8b384e0d97}\index{ApiResp\_t@{ApiResp\_t}!GetNewPropertyTree@{GetNewPropertyTree}}
\index{GetNewPropertyTree@{GetNewPropertyTree}!ApiResp\_t@{ApiResp\_t}}
\doxysubsubsection{\texorpdfstring{GetNewPropertyTree()}{GetNewPropertyTree()}}
{\footnotesize\ttfamily \label{struct_api_resp__t_a9ca5e376ef51a4f1a81ffa8b384e0d97} 
boost\+::property\+\_\+tree\+::ptree Api\+Resp\+\_\+t\+::\+Get\+New\+Property\+Tree (\begin{DoxyParamCaption}{}{}\end{DoxyParamCaption}) const\hspace{0.3cm}{\ttfamily [override]}, {\ttfamily [virtual]}}



Implements \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}.

\Hypertarget{struct_api_resp__t_ae5dd20e6a788da297bb31e78f8094b4e}\index{ApiResp\_t@{ApiResp\_t}!SetValuesFromPropertyTree@{SetValuesFromPropertyTree}}
\index{SetValuesFromPropertyTree@{SetValuesFromPropertyTree}!ApiResp\_t@{ApiResp\_t}}
\doxysubsubsection{\texorpdfstring{SetValuesFromPropertyTree()}{SetValuesFromPropertyTree()}}
{\footnotesize\ttfamily \label{struct_api_resp__t_ae5dd20e6a788da297bb31e78f8094b4e} 
bool Api\+Resp\+\_\+t\+::\+Set\+Values\+From\+Property\+Tree (\begin{DoxyParamCaption}\item[{const boost\+::property\+\_\+tree\+::ptree \&}]{pt}{}\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [override]}, {\ttfamily [virtual]}}



Implements \mbox{\hyperlink{class_json_serializable}{Json\+Serializable}}.



The documentation for this struct was generated from the following files\+:\begin{DoxyCompactItemize}
\item 
common/telemetry\+\_\+definitions/include/\mbox{\hyperlink{_telemetry_definitions_8h}{Telemetry\+Definitions.\+h}}\item 
common/telemetry\+\_\+definitions/src/Telemetry\+Definitions.\+cpp\end{DoxyCompactItemize}
