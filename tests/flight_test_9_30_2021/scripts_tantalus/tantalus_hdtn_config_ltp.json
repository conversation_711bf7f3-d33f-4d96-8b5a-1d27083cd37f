{"hdtnConfigName": "my hdtn config", "userInterfaceOn": true, "mySchemeName": "unused_scheme_name", "myNodeId": 20, "myBpEchoServiceId": 2047, "myCustodialSsp": "unused_custodial_ssp", "myCustodialServiceId": 0, "myRouterServiceId": 100, "isAcsAware": true, "acsMaxFillsPerAcsPacket": 100, "acsSendPeriodMilliseconds": 1000, "retransmitBundleAfterNoCustodySignalMilliseconds": 10000, "maxBundleSizeBytes": 10000000, "maxIngressBundleWaitOnEgressMilliseconds": 2000, "bufferRxToStorageOnLinkUpSaturation": false, "maxLtpReceiveUdpPacketSizeBytes": 65536, "neighborDepletedStorageDelaySeconds": 0, "fragmentBundlesLargerThanBytes": 0, "enforceBundlePriority": false, "zmqBoundRouterPubSubPortPath": 10200, "zmqBoundTelemApiPortPath": 10305, "inductsConfig": {"inductConfigName": "myconfig", "inductVector": [{"name": "from nuc3", "convergenceLayer": "ltp_over_udp", "boundPort": 1113, "numRxCircularBufferElements": 5000, "thisLtpEngineId": 20, "remoteLtpEngineId": 10, "ltpReportSegmentMtu": 1000, "oneWayLightTimeMs": 1000, "oneWayMarginTimeMs": 200, "clientServiceId": 1, "preallocatedRedDataBytes": 1000000, "ltpMaxRetriesPerSerialNumber": 500, "ltpRandomNumberSizeBits": 64, "ltpRemoteUdpHostname": "nuc3", "ltpRemoteUdpPort": 1113, "ltpRxDataSegmentSessionNumberRecreationPreventerHistorySize": 1000, "ltpMaxExpectedSimultaneousSessions": 500, "ltpMaxUdpPacketsToSendPerSystemCall": 1, "delaySendingOfReportSegmentsTimeMsOrZeroToDisable": 20, "keepActiveSessionDataOnDisk": false, "activeSessionDataOnDiskNewFileDurationMs": 2000, "activeSessionDataOnDiskDirectory": "./"}]}, "outductsConfig": {"outductConfigName": "myconfig", "outductVector": [{"name": "to localhost bpsink", "convergenceLayer": "stcp", "nextHopNodeId": 2, "remoteHostname": "localhost", "remotePort": 4556, "maxNumberOfBundlesInPipeline": 50, "maxSumOfBundleBytesInPipeline": 50000000, "keepAliveIntervalSeconds": 15}]}, "storageConfig": {"storageImplementation": "asio_single_threaded", "tryToRestoreFromDisk": false, "autoDeleteFilesOnExit": true, "totalStorageCapacityBytes": 81920000000, "storageDeletionPolicy": "never", "storageDiskConfigVector": [{"name": "d1", "storeFilePath": "./store1.bin"}, {"name": "d2", "storeFilePath": "./store2.bin"}]}}