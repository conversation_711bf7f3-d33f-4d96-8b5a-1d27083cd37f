<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_class_timestamp_util" xml:lang="en-US">
<title>TimestampUtil Class Reference</title>
<indexterm><primary>TimestampUtil</primary></indexterm>
<simplesect>
    <title>Classes    </title>
        <itemizedlist>
            <listitem><para>struct <link linkend="_struct_timestamp_util_1_1bpv6__creation__timestamp__t">bpv6_creation_timestamp_t</link></para>
</listitem>
            <listitem><para>struct <link linkend="_struct_timestamp_util_1_1bpv7__creation__timestamp__t">bpv7_creation_timestamp_t</link></para>
</listitem>
            <listitem><para>struct <link linkend="_struct_timestamp_util_1_1dtn__time__t">dtn_time_t</link></para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Static Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a805fd4c67546120c500e8073d92256f2"/>static const boost::posix_time::ptime &amp; <emphasis role="strong">GetRfc5050Epoch</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1ae425a71c2c839c5a65268665b79cad2d"/>static const boost::posix_time::ptime &amp; <emphasis role="strong">GetUnixEpoch</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a079c808d86abf4909b62ffeb80eae7d9"/>static uint64_t <emphasis role="strong">GetMillisecondsSinceEpochUnix</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a5bf3781fc318222d8ad64aeeb01003fb"/>static uint64_t <emphasis role="strong">GetMillisecondsSinceEpochUnix</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a48adda43f5f32341582bbaa17462fdef"/>static uint64_t <emphasis role="strong">GetMillisecondsSinceEpochRfc5050</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1acbb96204159cbbf0a3c4e9d9ad530834"/>static uint64_t <emphasis role="strong">GetMillisecondsSinceEpochRfc5050</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a0705a1873aede6749e2c1edb877fe1bd"/>static uint64_t <emphasis role="strong">GetMillisecondsSinceEpoch</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue, const boost::posix_time::ptime &amp;epochStartTime)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1abb07701a86d3248b38723cd0eeaea165"/>static uint64_t <emphasis role="strong">GetSecondsSinceEpochUnix</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a0b90e59b305059d5de45fe2cf5cef7d6"/>static uint64_t <emphasis role="strong">GetSecondsSinceEpochUnix</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1af69ead667a2d67b6e8bd7826b11ed85a"/>static uint64_t <emphasis role="strong">GetSecondsSinceEpochRfc5050</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a703ca1a6b34950e797a2c9371840fc63"/>static uint64_t <emphasis role="strong">GetSecondsSinceEpochRfc5050</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a3cf73d1fe59e599fd1c1dfd1f952712a"/>static uint64_t <emphasis role="strong">GetSecondsSinceEpoch</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue, const boost::posix_time::ptime &amp;epochStartTime)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a1ec1ecabeeca6bae4266888dd4074602"/>static uint64_t <emphasis role="strong">GetMicrosecondsSinceEpochUnix</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a7a8e76ca37df04687a0d3a7065c4177f"/>static uint64_t <emphasis role="strong">GetMicrosecondsSinceEpochUnix</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1afbf432a5669a70c32b264ee8f368d6b8"/>static uint64_t <emphasis role="strong">GetMicrosecondsSinceEpochRfc5050</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1ace2ce3a2f3b7f089dc720bab10c88398"/>static uint64_t <emphasis role="strong">GetMicrosecondsSinceEpochRfc5050</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a55289c333fadf2e712e67ca9c204f2f5"/>static uint64_t <emphasis role="strong">GetMicrosecondsSinceEpoch</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue, const boost::posix_time::ptime &amp;epochStartTime)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a9b5e365fc85bb900be70334878ac3b0e"/>static std::string <emphasis role="strong">GetUtcTimestampStringNow</emphasis> (bool forFileName)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1aab6f324275bfc9522a810b41a9ff3553"/>static std::string <emphasis role="strong">GetUtcTimestampStringFromPtime</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue, bool forFileName)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a00f1a41c0b95c345f870d24606448c65"/>static bool <emphasis role="strong">SetPtimeFromUtcTimestampString</emphasis> (const std::string &amp;stringvalue, boost::posix_time::ptime &amp;pt)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a7ec6e3240eccc924cc4dab58fb0d54ae"/>static boost::posix_time::ptime <emphasis role="strong">DtnTimeToPtimeLossy</emphasis> (const <link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link> &amp;dtnTime)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1ad87515626fce7c4f0a197a0cd6416474"/>static <link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link> <emphasis role="strong">PtimeToDtnTime</emphasis> (const boost::posix_time::ptime &amp;posixTimeValue)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1addcfee75d49e67ce11cbdc6b181ce1af"/>static std::string <emphasis role="strong">GetUtcTimestampStringFromDtnTimeLossy</emphasis> (const <link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link> &amp;dtnTime, bool forFileName)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_timestamp_util_1a8fcc09516b90f4a1cb581932a289aff4"/>static <link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link> <emphasis role="strong">GenerateDtnTimeNow</emphasis> ()</para>
</listitem>
        </itemizedlist>
</simplesect>
<para>
The documentation for this class was generated from the following files:</para>
common/util/include/<link linkend="__timestamp_util_8h">TimestampUtil.h</link>common/util/src/<link linkend="__timestamp_util_8cpp">TimestampUtil.cpp</link></section>
