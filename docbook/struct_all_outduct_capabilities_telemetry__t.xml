<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_struct_all_outduct_capabilities_telemetry__t" xml:lang="en-US">
<title>AllOutductCapabilitiesTelemetry_t Struct Reference</title>
<indexterm><primary>AllOutductCapabilitiesTelemetry_t</primary></indexterm>
<para>Inheritance diagram for AllOutductCapabilitiesTelemetry_t:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="struct_all_outduct_capabilities_telemetry__t.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_struct_all_outduct_capabilities_telemetry__t_1a7b400ba8bbb37d78e210829b80391c78"/>TELEMETRY_DEFINITIONS_EXPORT <emphasis role="strong">AllOutductCapabilitiesTelemetry_t</emphasis> (const <link linkend="_struct_all_outduct_capabilities_telemetry__t">AllOutductCapabilitiesTelemetry_t</link> &amp;o)</para>
</listitem>
            <listitem><para><anchor xml:id="_struct_all_outduct_capabilities_telemetry__t_1a7003334944c4244250ccb3191332b93b"/>TELEMETRY_DEFINITIONS_EXPORT <emphasis role="strong">AllOutductCapabilitiesTelemetry_t</emphasis> (<link linkend="_struct_all_outduct_capabilities_telemetry__t">AllOutductCapabilitiesTelemetry_t</link> &amp;&amp;o) noexcept</para>
</listitem>
            <listitem><para><anchor xml:id="_struct_all_outduct_capabilities_telemetry__t_1a890a0faa7c9c38648864ae3fd49d9294"/>TELEMETRY_DEFINITIONS_EXPORT <link linkend="_struct_all_outduct_capabilities_telemetry__t">AllOutductCapabilitiesTelemetry_t</link> &amp; <emphasis role="strong">operator=</emphasis> (const <link linkend="_struct_all_outduct_capabilities_telemetry__t">AllOutductCapabilitiesTelemetry_t</link> &amp;o)</para>
</listitem>
            <listitem><para><anchor xml:id="_struct_all_outduct_capabilities_telemetry__t_1a005d9257dee330766bef0d76c0232edd"/>TELEMETRY_DEFINITIONS_EXPORT <link linkend="_struct_all_outduct_capabilities_telemetry__t">AllOutductCapabilitiesTelemetry_t</link> &amp; <emphasis role="strong">operator=</emphasis> (<link linkend="_struct_all_outduct_capabilities_telemetry__t">AllOutductCapabilitiesTelemetry_t</link> &amp;&amp;o) noexcept</para>
</listitem>
            <listitem><para><anchor xml:id="_struct_all_outduct_capabilities_telemetry__t_1a65331f2e52fc3b02a55e795d0bb16a21"/>TELEMETRY_DEFINITIONS_EXPORT bool <emphasis role="strong">operator==</emphasis> (const <link linkend="_struct_all_outduct_capabilities_telemetry__t">AllOutductCapabilitiesTelemetry_t</link> &amp;o) const</para>
</listitem>
            <listitem><para><anchor xml:id="_struct_all_outduct_capabilities_telemetry__t_1ac56de0ada71281b7c2c8bfc8a31c323b"/>TELEMETRY_DEFINITIONS_EXPORT bool <emphasis role="strong">operator!=</emphasis> (const <link linkend="_struct_all_outduct_capabilities_telemetry__t">AllOutductCapabilitiesTelemetry_t</link> &amp;o) const</para>
</listitem>
            <listitem><para>virtual TELEMETRY_DEFINITIONS_EXPORT boost::property_tree::ptree <link linkend="_struct_all_outduct_capabilities_telemetry__t_1aa1b04d05f82955403ba20eaf164073d7">GetNewPropertyTree</link> () const override</para>
</listitem>
            <listitem><para>virtual TELEMETRY_DEFINITIONS_EXPORT bool <link linkend="_struct_all_outduct_capabilities_telemetry__t_1a71c060e026b78155822a0bd119f7a55f">SetValuesFromPropertyTree</link> (const boost::property_tree::ptree &amp;pt) override</para>
</listitem>
        </itemizedlist>
</simplesect>
Public Member Functions inherited from <link linkend="_class_json_serializable">JsonSerializable</link>        <itemizedlist>
            <listitem><para>std::string <emphasis role="strong">ToJson</emphasis> (bool pretty=true) const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">ToJsonFile</emphasis> (const boost::filesystem::path &amp;filePath, bool pretty=true) const</para>
</listitem>
            <listitem><para>std::string <emphasis role="strong">ToXml</emphasis> () const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">ToXmlFile</emphasis> (const std::string &amp;fileName, char indentCharacter=&apos; &apos;, int indentCount=2) const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">SetValuesFromJson</emphasis> (const std::string &amp;jsonString)</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">SetValuesFromJsonCharArray</emphasis> (const char *data, const std::size_t size)</para>
</listitem>
        </itemizedlist>
<simplesect>
    <title>Public Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_struct_all_outduct_capabilities_telemetry__t_1af2bec1ba32e7338608ec6793360ae09a"/>std::list&lt; <link linkend="_struct_outduct_capability_telemetry__t">OutductCapabilityTelemetry_t</link> &gt; <emphasis role="strong">outductCapabilityTelemetryList</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Additional Inherited Members    </title>
Static Public Member Functions inherited from <link linkend="_class_json_serializable">JsonSerializable</link>        <itemizedlist>
            <listitem><para>static bool <emphasis role="strong">LoadTextFileIntoString</emphasis> (const boost::filesystem::path &amp;filePath, std::string &amp;fileContentsAsString)</para>
</listitem>
            <listitem><para>static void <emphasis role="strong">GetAllJsonKeys</emphasis> (const std::string &amp;jsonText, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</para>
</listitem>
            <listitem><para>static void <emphasis role="strong">GetAllJsonKeysLineByLine</emphasis> (std::istream &amp;stream, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInFilePath</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, const boost::filesystem::path &amp;originalUserJsonFilePath, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInString</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, const std::string &amp;originalUserJsonString, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInStream</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, std::istream &amp;originalUserJsonStream, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static std::string <emphasis role="strong">PtToJsonString</emphasis> (const boost::property_tree::ptree &amp;pt, bool pretty=true)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonCharArray</emphasis> (char *data, const std::size_t size, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonStream</emphasis> (std::istream &amp;jsonStream, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonString</emphasis> (const std::string &amp;jsonStr, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonFilePath</emphasis> (const boost::filesystem::path &amp;jsonFilePath, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static std::string <emphasis role="strong">PtToXmlString</emphasis> (const boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static boost::property_tree::ptree <emphasis role="strong">GetPropertyTreeFromXmlString</emphasis> (const std::string &amp;jsonStr)</para>
</listitem>
            <listitem><para>static boost::property_tree::ptree <emphasis role="strong">GetPropertyTreeFromXmlFile</emphasis> (const std::string &amp;xmlFileName)</para>
</listitem>
        </itemizedlist>
</simplesect>
<section>
<title>Member Function Documentation</title>
<anchor xml:id="_struct_all_outduct_capabilities_telemetry__t_1aa1b04d05f82955403ba20eaf164073d7"/><section>
    <title>GetNewPropertyTree()</title>
<indexterm><primary>GetNewPropertyTree</primary><secondary>AllOutductCapabilitiesTelemetry_t</secondary></indexterm>
<indexterm><primary>AllOutductCapabilitiesTelemetry_t</primary><secondary>GetNewPropertyTree</secondary></indexterm>
<para><computeroutput>boost::property_tree::ptree AllOutductCapabilitiesTelemetry_t::GetNewPropertyTree ( ) const<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_json_serializable">JsonSerializable</link>.</para>
</section>
<anchor xml:id="_struct_all_outduct_capabilities_telemetry__t_1a71c060e026b78155822a0bd119f7a55f"/><section>
    <title>SetValuesFromPropertyTree()</title>
<indexterm><primary>SetValuesFromPropertyTree</primary><secondary>AllOutductCapabilitiesTelemetry_t</secondary></indexterm>
<indexterm><primary>AllOutductCapabilitiesTelemetry_t</primary><secondary>SetValuesFromPropertyTree</secondary></indexterm>
<para><computeroutput>bool AllOutductCapabilitiesTelemetry_t::SetValuesFromPropertyTree (const boost::property_tree::ptree &amp; pt)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_json_serializable">JsonSerializable</link>.</para>
</section>
<para>
The documentation for this struct was generated from the following files:</para>
common/telemetry_definitions/include/<link linkend="__telemetry_definitions_8h">TelemetryDefinitions.h</link>common/telemetry_definitions/src/TelemetryDefinitions.cpp</section>
</section>
