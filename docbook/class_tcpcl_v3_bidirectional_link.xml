<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_class_tcpcl_v3_bidirectional_link" xml:lang="en-US">
<title>TcpclV3BidirectionalLink Class Reference</title>
<indexterm><primary>TcpclV3BidirectionalLink</primary></indexterm>
<para>Inheritance diagram for TcpclV3BidirectionalLink:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="class_tcpcl_v3_bidirectional_link.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a680f46bb021f5fe3faa2712e17b4ec65"/>TCPCL_LIB_EXPORT <emphasis role="strong">TcpclV3BidirectionalLink</emphasis> (const std::string &amp;implementationStringForCout, const uint64_t shutdownMessageReconnectionDelaySecondsToSend, const bool deleteSocketAfterShutdown, const bool contactHeaderMustReply, const uint16_t desiredKeepAliveIntervalSeconds, boost::asio::io_service *externalIoServicePtr, const unsigned int maxUnacked, const uint64_t maxBundleSizeBytes, const uint64_t maxFragmentSize, const uint64_t myNodeId, const std::string &amp;expectedRemoteEidUriStringIfNotEmpty)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a696af74cdff78dda7dc95553fbf58155"/>TCPCL_LIB_EXPORT bool <emphasis role="strong">BaseClass_Forward</emphasis> (const uint8_t *bundleData, const std::size_t size, std::vector&lt; uint8_t &gt; &amp;&amp;userData)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a44684ffad28b872eec2aa471fb9634f2"/>TCPCL_LIB_EXPORT bool <emphasis role="strong">BaseClass_Forward</emphasis> (padded_vector_uint8_t &amp;dataVec, std::vector&lt; uint8_t &gt; &amp;&amp;userData)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a1f3bbd6422df398fc5c7f6aa22237b89"/>TCPCL_LIB_EXPORT bool <emphasis role="strong">BaseClass_Forward</emphasis> (<link linkend="_classzmq_1_1message__t">zmq::message_t</link> &amp;dataZmq, std::vector&lt; uint8_t &gt; &amp;&amp;userData)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ad631c9fa370205ed7ed8368115757215"/>TCPCL_LIB_EXPORT bool <emphasis role="strong">BaseClass_Forward</emphasis> (std::unique_ptr&lt; <link linkend="_classzmq_1_1message__t">zmq::message_t</link> &gt; &amp;zmqMessageUniquePtr, padded_vector_uint8_t &amp;vecMessage, const bool usingZmqData, std::vector&lt; uint8_t &gt; &amp;&amp;userData)</para>
</listitem>
            <listitem><para>virtual TCPCL_LIB_EXPORT unsigned int <link linkend="_class_tcpcl_v3_bidirectional_link_1ade91c38c0aba0acd5dc8727a3f196dd1">Virtual_GetMaxTxBundlesInPipeline</link> () override</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1acb3d1904a6d482d5d2634daa0c5c97c9"/>TCPCL_LIB_EXPORT void <emphasis role="strong">BaseClass_SetOnFailedBundleVecSendCallback</emphasis> (const OnFailedBundleVecSendCallback_t &amp;callback)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a4b266248758835f3e703d25ce77ce766"/>TCPCL_LIB_EXPORT void <emphasis role="strong">BaseClass_SetOnFailedBundleZmqSendCallback</emphasis> (const OnFailedBundleZmqSendCallback_t &amp;callback)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a127feec23af3ccd07e63ca20b4851da4"/>TCPCL_LIB_EXPORT void <emphasis role="strong">BaseClass_SetOnSuccessfulBundleSendCallback</emphasis> (const OnSuccessfulBundleSendCallback_t &amp;callback)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a77bed4a9389d6b8eefa7d7d0eddca57e"/>TCPCL_LIB_EXPORT void <emphasis role="strong">BaseClass_SetOnOutductLinkStatusChangedCallback</emphasis> (const OnOutductLinkStatusChangedCallback_t &amp;callback)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ab72ff48485aea2e2ae6288ca5f13d627"/>TCPCL_LIB_EXPORT void <emphasis role="strong">BaseClass_SetUserAssignedUuid</emphasis> (uint64_t userAssignedUuid)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a2c7a58c063b64714156ecad732ffc922"/>TCPCL_LIB_EXPORT void <emphasis role="strong">BaseClass_GetTelemetry</emphasis> (<link linkend="_struct_tcpcl_v3_induct_connection_telemetry__t">TcpclV3InductConnectionTelemetry_t</link> &amp;telem) const</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a27e9129a3a11749bd00718c58dd8e71f"/>TCPCL_LIB_EXPORT void <emphasis role="strong">BaseClass_GetTelemetry</emphasis> (<link linkend="_struct_tcpcl_v3_outduct_telemetry__t">TcpclV3OutductTelemetry_t</link> &amp;telem) const</para>
</listitem>
        </itemizedlist>
</simplesect>
Public Member Functions inherited from <link linkend="_class_bidirectional_link">BidirectionalLink</link>        <itemizedlist>
            <listitem><para>BOOST_FORCEINLINE std::size_t <emphasis role="strong">BaseClass_GetTotalBundlesAcked</emphasis> () const noexcept</para>
</listitem>
            <listitem><para>BOOST_FORCEINLINE std::size_t <emphasis role="strong">BaseClass_GetTotalBundlesSent</emphasis> () const noexcept</para>
</listitem>
            <listitem><para>BOOST_FORCEINLINE std::size_t <emphasis role="strong">BaseClass_GetTotalBundlesUnacked</emphasis> () const noexcept</para>
</listitem>
            <listitem><para>BOOST_FORCEINLINE std::size_t <emphasis role="strong">BaseClass_GetTotalBundleBytesAcked</emphasis> () const noexcept</para>
</listitem>
            <listitem><para>BOOST_FORCEINLINE std::size_t <emphasis role="strong">BaseClass_GetTotalBundleBytesSent</emphasis> () const noexcept</para>
</listitem>
            <listitem><para>BOOST_FORCEINLINE std::size_t <emphasis role="strong">BaseClass_GetTotalBundleBytesUnacked</emphasis> () const noexcept</para>
</listitem>
        </itemizedlist>
<simplesect>
    <title>Protected Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a75a5f1cafecbde940a9faae7c6c0706e"/>TCPCL_LIB_EXPORT void <emphasis role="strong">BaseClass_TryToWaitForAllBundlesToFinishSending</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a36946841371073e318314e2c8b0cd8a9"/>TCPCL_LIB_EXPORT void <emphasis role="strong">BaseClass_DoTcpclShutdown</emphasis> (bool sendShutdownMessage, bool reasonWasTimeOut)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ab7d02221a0a3c848894eeef96d265be5"/>virtual void <emphasis role="strong">Virtual_OnTcpclShutdownComplete_CalledFromIoServiceThread</emphasis> ()=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a27f844cef5e4e2f9496c7a397267d9dc"/>virtual void <emphasis role="strong">Virtual_OnSuccessfulWholeBundleAcknowledged</emphasis> ()=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a718239f13916157542fb35e52faa1f25"/>virtual void <emphasis role="strong">Virtual_WholeBundleReady</emphasis> (padded_vector_uint8_t &amp;wholeBundleVec)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a2475a8d646d373dfaeeb2dff992a252a"/>virtual TCPCL_LIB_EXPORT void <emphasis role="strong">Virtual_OnTcpSendSuccessful_CalledFromIoServiceThread</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a44ed8eeec764005e52b6cea46bf98754"/>virtual TCPCL_LIB_EXPORT void <emphasis role="strong">Virtual_OnContactHeaderCompletedSuccessfully</emphasis> ()</para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Protected Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a30f0fb11b7bbda99c14da670605fde5e"/>const std::string <emphasis role="strong">M_BASE_IMPLEMENTATION_STRING_FOR_COUT</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ab181a8f6ad864dc44e0b353d76755d1c"/>const uint64_t <emphasis role="strong">M_BASE_SHUTDOWN_MESSAGE_RECONNECTION_DELAY_SECONDS_TO_SEND</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1af1d7b9661169a39282d514774efffcc4"/>const uint16_t <emphasis role="strong">M_BASE_DESIRED_KEEPALIVE_INTERVAL_SECONDS</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a9b55860fe7fbf1b0b0e0b4447e1441d2"/>const bool <emphasis role="strong">M_BASE_DELETE_SOCKET_AFTER_SHUTDOWN</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a541add3496a7e2e1b74a67e4b8ca4f6f"/>const bool <emphasis role="strong">M_BASE_CONTACT_HEADER_MUST_REPLY</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ab034bcb2d8e8c389869b6afe8de634c8"/>const std::string <emphasis role="strong">M_BASE_THIS_TCPCL_EID_STRING</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a147f123495c5c10291126503e17da174"/>std::string <emphasis role="strong">M_BASE_EXPECTED_REMOTE_CONTACT_HEADER_EID_STRING_IF_NOT_EMPTY</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1aaa34630a7389c218639eeafcd104d1a0"/>uint16_t <emphasis role="strong">m_base_keepAliveIntervalSeconds</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a1524d3621e14da9bfcfac0ef4a5acc9a"/>std::unique_ptr&lt; boost::asio::io_service &gt; <emphasis role="strong">m_base_localIoServiceUniquePtr</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ab89754b8cbaed12f248a4824a5afaee8"/>boost::asio::io_service &amp; <emphasis role="strong">m_base_ioServiceRef</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ada5d1849d4129d1a229e7954c55c37fb"/>boost::asio::deadline_timer <emphasis role="strong">m_base_noKeepAlivePacketReceivedTimer</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ab71bfb789bd8981e2a8e34701d687b28"/>boost::asio::deadline_timer <emphasis role="strong">m_base_needToSendKeepAliveMessageTimer</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a83d7b58c45eb1ecf48d9826bd6b4b351"/>boost::asio::deadline_timer <emphasis role="strong">m_base_sendShutdownMessageTimeoutTimer</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ae4e574b1ac0928919bd6d594b0003b63"/>bool <emphasis role="strong">m_base_shutdownCalled</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a7a33214ec287c4e516d71de8bab47e21"/>std::atomic&lt; bool &gt; <emphasis role="strong">m_base_readyToForward</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ae7e06abb791a64b64581bef0f810d19b"/>std::atomic&lt; bool &gt; <emphasis role="strong">m_base_sinkIsSafeToDelete</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a66b64821997a479d927068e71519c149"/>std::atomic&lt; bool &gt; <emphasis role="strong">m_base_tcpclShutdownComplete</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a0367bcaf2a3f5e885f97090f3dab0dc8"/>std::atomic&lt; bool &gt; <emphasis role="strong">m_base_useLocalConditionVariableAckReceived</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a7b33bfababc84930663a0b5e5051368f"/>std::atomic&lt; bool &gt; <emphasis role="strong">m_base_dataReceivedServedAsKeepaliveReceived</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1aad93934ed1f035468746c66f29a1fa50"/>std::atomic&lt; bool &gt; <emphasis role="strong">m_base_dataSentServedAsKeepaliveSent</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a7a0e09cd6f42c4f387cd9c60238bc018"/>boost::condition_variable <emphasis role="strong">m_base_localConditionVariableAckReceived</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1af77443f41a19c62a9942db557e2576f6"/>uint64_t <emphasis role="strong">m_base_reconnectionDelaySecondsIfNotZero</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a2b17a85a77fc13c14776d07666835eb6"/><link linkend="_class_tcpcl">Tcpcl</link> <emphasis role="strong">m_base_tcpclV3RxStateMachine</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a5df17c6bbe3e7090811abbb92b1481dc"/>CONTACT_HEADER_FLAGS <emphasis role="strong">m_base_contactHeaderFlags</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a478d61db501ddee120bbbaa461ade6f0"/>std::string <emphasis role="strong">m_base_tcpclRemoteEidString</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a6fea5046f845596705779b1460d6e1a7"/>uint64_t <emphasis role="strong">m_base_tcpclRemoteNodeId</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a1972095b63829cde29a77c24ab5c2649"/>std::shared_ptr&lt; boost::asio::ip::tcp::socket &gt; <emphasis role="strong">m_base_tcpSocketPtr</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ad12bf6c51fb4ae3a43e4c2b6c031a7f4"/>std::unique_ptr&lt; <link linkend="_class_tcp_async_sender">TcpAsyncSender</link> &gt; <emphasis role="strong">m_base_tcpAsyncSenderPtr</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a1d18190cfd289434d778d27b3a799a72"/>TcpAsyncSenderElement::OnSuccessfulSendCallbackByIoServiceThread_t <emphasis role="strong">m_base_handleTcpSendCallback</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a0d6c1f724cb5fb7fa03f380da166539c"/>TcpAsyncSenderElement::OnSuccessfulSendCallbackByIoServiceThread_t <emphasis role="strong">m_base_handleTcpSendShutdownCallback</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a7f4709310bc1c6075e7fdeccea78bfdd"/>padded_vector_uint8_t <emphasis role="strong">m_base_fragmentedBundleRxConcat</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a2870489ab3bd1d3d3db273693a850125"/>const unsigned int <emphasis role="strong">M_BASE_MAX_UNACKED_BUNDLES_IN_PIPELINE</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ae3b430113a7eb7e2d26422839e8bb489"/>const unsigned int <emphasis role="strong">M_BASE_UNACKED_BUNDLE_CB_SIZE</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a2d73ad4019c8db9e3a27db8e7a91e279"/><link linkend="_class_circular_index_buffer_single_producer_single_consumer_configurable">CircularIndexBufferSingleProducerSingleConsumerConfigurable</link> <emphasis role="strong">m_base_bytesToAckCb</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1afeca26e4850a077b82e0e576c68c0584"/>std::vector&lt; uint64_t &gt; <emphasis role="strong">m_base_bytesToAckCbVec</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a1e903e83f7f0c0d2809a77fecb5eb2a5"/>std::vector&lt; std::vector&lt; uint64_t &gt; &gt; <emphasis role="strong">m_base_fragmentBytesToAckCbVec</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ac0adfafd2f12e8d950fc5cd473f6d901"/>std::vector&lt; uint64_t &gt; <emphasis role="strong">m_base_fragmentVectorIndexCbVec</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ae7b24a66554d4877f31dc993b493a0c9"/>std::vector&lt; std::vector&lt; uint8_t &gt; &gt; <emphasis role="strong">m_base_userDataCbVec</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1afaa038b5e1f253857397836e99df549d"/>const uint64_t <emphasis role="strong">M_BASE_MAX_FRAGMENT_SIZE</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ac9396684ec1cf948d3e1efa87978dc85"/>OnFailedBundleVecSendCallback_t <emphasis role="strong">m_base_onFailedBundleVecSendCallback</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a4feec10b150500bb7a0a633fb2a5621d"/>OnFailedBundleZmqSendCallback_t <emphasis role="strong">m_base_onFailedBundleZmqSendCallback</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1aa8fd3569e50f97b4dd8bae4f3612ad0f"/>OnSuccessfulBundleSendCallback_t <emphasis role="strong">m_base_onSuccessfulBundleSendCallback</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ad799a8a9cf01f40d2273f757068f5186"/>OnOutductLinkStatusChangedCallback_t <emphasis role="strong">m_base_onOutductLinkStatusChangedCallback</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1af63f1a01cbded7e807dbae4032efdbf9"/>uint64_t <emphasis role="strong">m_base_userAssignedUuid</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a91e4592f18087874868e0060751a946d"/>std::string <emphasis role="strong">m_base_inductConnectionName</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_tcpcl_v3_bidirectional_link_1a4fd7ecf8ca14cacd7878387a916387af"/>std::string <emphasis role="strong">m_base_inductInputName</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Additional Inherited Members    </title>
Public Attributes inherited from <link linkend="_class_bidirectional_link">BidirectionalLink</link>        <itemizedlist>
            <listitem><para><link linkend="_struct_bidirectional_link_atomic_telem__t">BidirectionalLinkAtomicTelem_t</link> <emphasis role="strong">m_base_telem</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
<section>
<title>Member Function Documentation</title>
<anchor xml:id="_class_tcpcl_v3_bidirectional_link_1ade91c38c0aba0acd5dc8727a3f196dd1"/><section>
    <title>Virtual_GetMaxTxBundlesInPipeline()</title>
<indexterm><primary>Virtual_GetMaxTxBundlesInPipeline</primary><secondary>TcpclV3BidirectionalLink</secondary></indexterm>
<indexterm><primary>TcpclV3BidirectionalLink</primary><secondary>Virtual_GetMaxTxBundlesInPipeline</secondary></indexterm>
<para><computeroutput>unsigned int TcpclV3BidirectionalLink::Virtual_GetMaxTxBundlesInPipeline ( )<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_bidirectional_link">BidirectionalLink</link>.</para>
</section>
<para>
The documentation for this class was generated from the following files:</para>
common/tcpcl/include/<link linkend="__tcpcl_v3_bidirectional_link_8h">TcpclV3BidirectionalLink.h</link>common/tcpcl/src/<link linkend="__tcpcl_v3_bidirectional_link_8cpp">TcpclV3BidirectionalLink.cpp</link></section>
</section>
