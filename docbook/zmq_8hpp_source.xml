<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_zmq_8hpp_source" xml:lang="en-US">
<title>zmq.hpp</title>
<indexterm><primary>common/util/include/zmq.hpp</primary></indexterm>
<programlisting linenumbering="unnumbered">1 <emphasis role="comment">/*</emphasis>
2 <emphasis role="comment">&#32;&#32;&#32;&#32;Copyright&#32;(c)&#32;2016-2017&#32;ZeroMQ&#32;community</emphasis>
3 <emphasis role="comment">&#32;&#32;&#32;&#32;Copyright&#32;(c)&#32;2009-2011&#32;250bpm&#32;s.r.o.</emphasis>
4 <emphasis role="comment">&#32;&#32;&#32;&#32;Copyright&#32;(c)&#32;2011&#32;Botond&#32;Ballo</emphasis>
5 <emphasis role="comment">&#32;&#32;&#32;&#32;Copyright&#32;(c)&#32;2007-2009&#32;iMatix&#32;Corporation</emphasis>
6 <emphasis role="comment"></emphasis>
7 <emphasis role="comment">&#32;&#32;&#32;&#32;Permission&#32;is&#32;hereby&#32;granted,&#32;free&#32;of&#32;charge,&#32;to&#32;any&#32;person&#32;obtaining&#32;a&#32;copy</emphasis>
8 <emphasis role="comment">&#32;&#32;&#32;&#32;of&#32;this&#32;software&#32;and&#32;associated&#32;documentation&#32;files&#32;(the&#32;&quot;Software&quot;),&#32;to</emphasis>
9 <emphasis role="comment">&#32;&#32;&#32;&#32;deal&#32;in&#32;the&#32;Software&#32;without&#32;restriction,&#32;including&#32;without&#32;limitation&#32;the</emphasis>
10 <emphasis role="comment">&#32;&#32;&#32;&#32;rights&#32;to&#32;use,&#32;copy,&#32;modify,&#32;merge,&#32;publish,&#32;distribute,&#32;sublicense,&#32;and/or</emphasis>
11 <emphasis role="comment">&#32;&#32;&#32;&#32;sell&#32;copies&#32;of&#32;the&#32;Software,&#32;and&#32;to&#32;permit&#32;persons&#32;to&#32;whom&#32;the&#32;Software&#32;is</emphasis>
12 <emphasis role="comment">&#32;&#32;&#32;&#32;furnished&#32;to&#32;do&#32;so,&#32;subject&#32;to&#32;the&#32;following&#32;conditions:</emphasis>
13 <emphasis role="comment"></emphasis>
14 <emphasis role="comment">&#32;&#32;&#32;&#32;The&#32;above&#32;copyright&#32;notice&#32;and&#32;this&#32;permission&#32;notice&#32;shall&#32;be&#32;included&#32;in</emphasis>
15 <emphasis role="comment">&#32;&#32;&#32;&#32;all&#32;copies&#32;or&#32;substantial&#32;portions&#32;of&#32;the&#32;Software.</emphasis>
16 <emphasis role="comment"></emphasis>
17 <emphasis role="comment">&#32;&#32;&#32;&#32;THE&#32;SOFTWARE&#32;IS&#32;PROVIDED&#32;&quot;AS&#32;IS&quot;,&#32;WITHOUT&#32;WARRANTY&#32;OF&#32;ANY&#32;KIND,&#32;EXPRESS&#32;OR</emphasis>
18 <emphasis role="comment">&#32;&#32;&#32;&#32;IMPLIED,&#32;INCLUDING&#32;BUT&#32;NOT&#32;LIMITED&#32;TO&#32;THE&#32;WARRANTIES&#32;OF&#32;MERCHANTABILITY,</emphasis>
19 <emphasis role="comment">&#32;&#32;&#32;&#32;FITNESS&#32;FOR&#32;A&#32;PARTICULAR&#32;PURPOSE&#32;AND&#32;NONINFRINGEMENT.&#32;IN&#32;NO&#32;EVENT&#32;SHALL&#32;THE</emphasis>
20 <emphasis role="comment">&#32;&#32;&#32;&#32;AUTHORS&#32;OR&#32;COPYRIGHT&#32;HOLDERS&#32;BE&#32;LIABLE&#32;FOR&#32;ANY&#32;CLAIM,&#32;DAMAGES&#32;OR&#32;OTHER</emphasis>
21 <emphasis role="comment">&#32;&#32;&#32;&#32;LIABILITY,&#32;WHETHER&#32;IN&#32;AN&#32;ACTION&#32;OF&#32;CONTRACT,&#32;TORT&#32;OR&#32;OTHERWISE,&#32;ARISING</emphasis>
22 <emphasis role="comment">&#32;&#32;&#32;&#32;FROM,&#32;OUT&#32;OF&#32;OR&#32;IN&#32;CONNECTION&#32;WITH&#32;THE&#32;SOFTWARE&#32;OR&#32;THE&#32;USE&#32;OR&#32;OTHER&#32;DEALINGS</emphasis>
23 <emphasis role="comment">&#32;&#32;&#32;&#32;IN&#32;THE&#32;SOFTWARE.</emphasis>
24 <emphasis role="comment">*/</emphasis>
25 
26 <emphasis role="preprocessor">#ifndef&#32;__ZMQ_HPP_INCLUDED__</emphasis>
27 <emphasis role="preprocessor">#define&#32;__ZMQ_HPP_INCLUDED__</emphasis>
28 
29 <emphasis role="preprocessor">#ifdef&#32;_WIN32</emphasis>
30 <emphasis role="preprocessor">#ifndef&#32;NOMINMAX</emphasis>
31 <emphasis role="preprocessor">#define&#32;NOMINMAX</emphasis>
32 <emphasis role="preprocessor">#endif</emphasis>
33 <emphasis role="preprocessor">#endif</emphasis>
34 
35 <emphasis role="comment">//&#32;included&#32;here&#32;for&#32;_HAS_CXX*&#32;macros</emphasis>
36 <emphasis role="preprocessor">#include&#32;&lt;zmq.h&gt;</emphasis>
37 
38 <emphasis role="preprocessor">#if&#32;defined(_MSVC_LANG)</emphasis>
39 <emphasis role="preprocessor">#define&#32;CPPZMQ_LANG&#32;_MSVC_LANG</emphasis>
40 <emphasis role="preprocessor">#else</emphasis>
41 <emphasis role="preprocessor">#define&#32;CPPZMQ_LANG&#32;__cplusplus</emphasis>
42 <emphasis role="preprocessor">#endif</emphasis>
43 <emphasis role="comment">//&#32;overwrite&#32;if&#32;specific&#32;language&#32;macros&#32;indicate&#32;higher&#32;version</emphasis>
44 <emphasis role="preprocessor">#if&#32;defined(_HAS_CXX14)&#32;&amp;&amp;&#32;_HAS_CXX14&#32;&amp;&amp;&#32;CPPZMQ_LANG&#32;&lt;&#32;201402L</emphasis>
45 <emphasis role="preprocessor">#undef&#32;CPPZMQ_LANG</emphasis>
46 <emphasis role="preprocessor">#define&#32;CPPZMQ_LANG&#32;201402L</emphasis>
47 <emphasis role="preprocessor">#endif</emphasis>
48 <emphasis role="preprocessor">#if&#32;defined(_HAS_CXX17)&#32;&amp;&amp;&#32;_HAS_CXX17&#32;&amp;&amp;&#32;CPPZMQ_LANG&#32;&lt;&#32;201703L</emphasis>
49 <emphasis role="preprocessor">#undef&#32;CPPZMQ_LANG</emphasis>
50 <emphasis role="preprocessor">#define&#32;CPPZMQ_LANG&#32;201703L</emphasis>
51 <emphasis role="preprocessor">#endif</emphasis>
52 
53 <emphasis role="comment">//&#32;macros&#32;defined&#32;if&#32;has&#32;a&#32;specific&#32;standard&#32;or&#32;greater</emphasis>
54 <emphasis role="preprocessor">#if&#32;CPPZMQ_LANG&#32;&gt;=&#32;201103L&#32;||&#32;(defined(_MSC_VER)&#32;&amp;&amp;&#32;_MSC_VER&#32;&gt;=&#32;1900)</emphasis>
55 <emphasis role="preprocessor">#define&#32;ZMQ_CPP11</emphasis>
56 <emphasis role="preprocessor">#endif</emphasis>
57 <emphasis role="preprocessor">#if&#32;CPPZMQ_LANG&#32;&gt;=&#32;201402L</emphasis>
58 <emphasis role="preprocessor">#define&#32;ZMQ_CPP14</emphasis>
59 <emphasis role="preprocessor">#endif</emphasis>
60 <emphasis role="preprocessor">#if&#32;CPPZMQ_LANG&#32;&gt;=&#32;201703L</emphasis>
61 <emphasis role="preprocessor">#define&#32;ZMQ_CPP17</emphasis>
62 <emphasis role="preprocessor">#endif</emphasis>
63 
64 <emphasis role="preprocessor">#if&#32;defined(ZMQ_CPP14)&#32;&amp;&amp;&#32;!defined(_MSC_VER)</emphasis>
65 <emphasis role="preprocessor">#define&#32;ZMQ_DEPRECATED(msg)&#32;[[deprecated(msg)]]</emphasis>
66 <emphasis role="preprocessor">#elif&#32;defined(_MSC_VER)</emphasis>
67 <emphasis role="preprocessor">#define&#32;ZMQ_DEPRECATED(msg)&#32;__declspec(deprecated(msg))</emphasis>
68 <emphasis role="preprocessor">#elif&#32;defined(__GNUC__)</emphasis>
69 <emphasis role="preprocessor">#define&#32;ZMQ_DEPRECATED(msg)&#32;__attribute__((deprecated(msg)))</emphasis>
70 <emphasis role="preprocessor">#endif</emphasis>
71 
72 <emphasis role="preprocessor">#if&#32;defined(ZMQ_CPP17)</emphasis>
73 <emphasis role="preprocessor">#define&#32;ZMQ_NODISCARD&#32;[[nodiscard]]</emphasis>
74 <emphasis role="preprocessor">#else</emphasis>
75 <emphasis role="preprocessor">#define&#32;ZMQ_NODISCARD</emphasis>
76 <emphasis role="preprocessor">#endif</emphasis>
77 
78 <emphasis role="preprocessor">#if&#32;defined(ZMQ_CPP11)</emphasis>
79 <emphasis role="preprocessor">#define&#32;ZMQ_NOTHROW&#32;noexcept</emphasis>
80 <emphasis role="preprocessor">#define&#32;ZMQ_EXPLICIT&#32;explicit</emphasis>
81 <emphasis role="preprocessor">#define&#32;ZMQ_OVERRIDE&#32;override</emphasis>
82 <emphasis role="preprocessor">#define&#32;ZMQ_NULLPTR&#32;nullptr</emphasis>
83 <emphasis role="preprocessor">#define&#32;ZMQ_CONSTEXPR_FN&#32;constexpr</emphasis>
84 <emphasis role="preprocessor">#define&#32;ZMQ_CONSTEXPR_VAR&#32;constexpr</emphasis>
85 <emphasis role="preprocessor">#define&#32;ZMQ_CPP11_DEPRECATED(msg)&#32;ZMQ_DEPRECATED(msg)</emphasis>
86 <emphasis role="preprocessor">#else</emphasis>
87 <emphasis role="preprocessor">#define&#32;ZMQ_NOTHROW&#32;throw()</emphasis>
88 <emphasis role="preprocessor">#define&#32;ZMQ_EXPLICIT</emphasis>
89 <emphasis role="preprocessor">#define&#32;ZMQ_OVERRIDE</emphasis>
90 <emphasis role="preprocessor">#define&#32;ZMQ_NULLPTR&#32;0</emphasis>
91 <emphasis role="preprocessor">#define&#32;ZMQ_CONSTEXPR_FN</emphasis>
92 <emphasis role="preprocessor">#define&#32;ZMQ_CONSTEXPR_VAR&#32;const</emphasis>
93 <emphasis role="preprocessor">#define&#32;ZMQ_CPP11_DEPRECATED(msg)</emphasis>
94 <emphasis role="preprocessor">#endif</emphasis>
95 <emphasis role="preprocessor">#if&#32;defined(ZMQ_CPP14)&#32;&amp;&amp;&#32;(!defined(_MSC_VER)&#32;||&#32;_MSC_VER&#32;&gt;&#32;1900)</emphasis>
96 <emphasis role="preprocessor">#define&#32;ZMQ_EXTENDED_CONSTEXPR</emphasis>
97 <emphasis role="preprocessor">#endif</emphasis>
98 <emphasis role="preprocessor">#if&#32;defined(ZMQ_CPP17)</emphasis>
99 <emphasis role="preprocessor">#define&#32;ZMQ_INLINE_VAR&#32;inline</emphasis>
100 <emphasis role="preprocessor">#else</emphasis>
101 <emphasis role="preprocessor">#define&#32;ZMQ_INLINE_VAR</emphasis>
102 <emphasis role="preprocessor">#endif</emphasis>
103 
104 <emphasis role="preprocessor">#include&#32;&lt;cassert&gt;</emphasis>
105 <emphasis role="preprocessor">#include&#32;&lt;cstring&gt;</emphasis>
106 
107 <emphasis role="preprocessor">#include&#32;&lt;algorithm&gt;</emphasis>
108 <emphasis role="preprocessor">#include&#32;&lt;exception&gt;</emphasis>
109 <emphasis role="preprocessor">#include&#32;&lt;iomanip&gt;</emphasis>
110 <emphasis role="preprocessor">#include&#32;&lt;sstream&gt;</emphasis>
111 <emphasis role="preprocessor">#include&#32;&lt;string&gt;</emphasis>
112 <emphasis role="preprocessor">#include&#32;&lt;vector&gt;</emphasis>
113 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
114 <emphasis role="preprocessor">#include&#32;&lt;array&gt;</emphasis>
115 <emphasis role="preprocessor">#include&#32;&lt;chrono&gt;</emphasis>
116 <emphasis role="preprocessor">#include&#32;&lt;tuple&gt;</emphasis>
117 <emphasis role="preprocessor">#include&#32;&lt;memory&gt;</emphasis>
118 <emphasis role="preprocessor">#endif</emphasis>
119 
120 <emphasis role="preprocessor">#if&#32;defined(__has_include)&#32;&amp;&amp;&#32;defined(ZMQ_CPP17)</emphasis>
121 <emphasis role="preprocessor">#define&#32;CPPZMQ_HAS_INCLUDE_CPP17(X)&#32;__has_include(X)</emphasis>
122 <emphasis role="preprocessor">#else</emphasis>
123 <emphasis role="preprocessor">#define&#32;CPPZMQ_HAS_INCLUDE_CPP17(X)&#32;0</emphasis>
124 <emphasis role="preprocessor">#endif</emphasis>
125 
126 <emphasis role="preprocessor">#if&#32;CPPZMQ_HAS_INCLUDE_CPP17(&lt;optional&gt;)&#32;&amp;&amp;&#32;!defined(CPPZMQ_HAS_OPTIONAL)</emphasis>
127 <emphasis role="preprocessor">#define&#32;CPPZMQ_HAS_OPTIONAL&#32;1</emphasis>
128 <emphasis role="preprocessor">#endif</emphasis>
129 <emphasis role="preprocessor">#ifndef&#32;CPPZMQ_HAS_OPTIONAL</emphasis>
130 <emphasis role="preprocessor">#define&#32;CPPZMQ_HAS_OPTIONAL&#32;0</emphasis>
131 <emphasis role="preprocessor">#elif&#32;CPPZMQ_HAS_OPTIONAL</emphasis>
132 <emphasis role="preprocessor">#include&#32;&lt;optional&gt;</emphasis>
133 <emphasis role="preprocessor">#endif</emphasis>
134 
135 <emphasis role="preprocessor">#if&#32;CPPZMQ_HAS_INCLUDE_CPP17(&lt;string_view&gt;)&#32;&amp;&amp;&#32;!defined(CPPZMQ_HAS_STRING_VIEW)</emphasis>
136 <emphasis role="preprocessor">#define&#32;CPPZMQ_HAS_STRING_VIEW&#32;1</emphasis>
137 <emphasis role="preprocessor">#endif</emphasis>
138 <emphasis role="preprocessor">#ifndef&#32;CPPZMQ_HAS_STRING_VIEW</emphasis>
139 <emphasis role="preprocessor">#define&#32;CPPZMQ_HAS_STRING_VIEW&#32;0</emphasis>
140 <emphasis role="preprocessor">#elif&#32;CPPZMQ_HAS_STRING_VIEW</emphasis>
141 <emphasis role="preprocessor">#include&#32;&lt;string_view&gt;</emphasis>
142 <emphasis role="preprocessor">#endif</emphasis>
143 
144 <emphasis role="comment">/*&#32;&#32;Version&#32;macros&#32;for&#32;compile-time&#32;API&#32;version&#32;detection&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;*/</emphasis>
145 <emphasis role="preprocessor">#define&#32;CPPZMQ_VERSION_MAJOR&#32;4</emphasis>
146 <emphasis role="preprocessor">#define&#32;CPPZMQ_VERSION_MINOR&#32;7</emphasis>
147 <emphasis role="preprocessor">#define&#32;CPPZMQ_VERSION_PATCH&#32;1</emphasis>
148 
149 <emphasis role="preprocessor">#define&#32;CPPZMQ_VERSION&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
150 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;ZMQ_MAKE_VERSION(CPPZMQ_VERSION_MAJOR,&#32;CPPZMQ_VERSION_MINOR,&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
151 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;CPPZMQ_VERSION_PATCH)</emphasis>
152 
153 <emphasis role="comment">//&#32;&#32;Detect&#32;whether&#32;the&#32;compiler&#32;supports&#32;C++11&#32;rvalue&#32;references.</emphasis>
154 <emphasis role="preprocessor">#if&#32;(defined(__GNUC__)&#32;&amp;&amp;&#32;(__GNUC__&#32;&gt;&#32;4&#32;||&#32;(__GNUC__&#32;==&#32;4&#32;&amp;&amp;&#32;__GNUC_MINOR__&#32;&gt;&#32;2))&#32;&#32;&#32;\</emphasis>
155 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;&#32;&amp;&amp;&#32;defined(__GXX_EXPERIMENTAL_CXX0X__))</emphasis>
156 <emphasis role="preprocessor">#define&#32;ZMQ_HAS_RVALUE_REFS</emphasis>
157 <emphasis role="preprocessor">#define&#32;ZMQ_DELETED_FUNCTION&#32;=&#32;delete</emphasis>
158 <emphasis role="preprocessor">#elif&#32;defined(__clang__)</emphasis>
159 <emphasis role="preprocessor">#if&#32;__has_feature(cxx_rvalue_references)</emphasis>
160 <emphasis role="preprocessor">#define&#32;ZMQ_HAS_RVALUE_REFS</emphasis>
161 <emphasis role="preprocessor">#endif</emphasis>
162 
163 <emphasis role="preprocessor">#if&#32;__has_feature(cxx_deleted_functions)</emphasis>
164 <emphasis role="preprocessor">#define&#32;ZMQ_DELETED_FUNCTION&#32;=&#32;delete</emphasis>
165 <emphasis role="preprocessor">#else</emphasis>
166 <emphasis role="preprocessor">#define&#32;ZMQ_DELETED_FUNCTION</emphasis>
167 <emphasis role="preprocessor">#endif</emphasis>
168 <emphasis role="preprocessor">#elif&#32;defined(_MSC_VER)&#32;&amp;&amp;&#32;(_MSC_VER&#32;&gt;=&#32;1900)</emphasis>
169 <emphasis role="preprocessor">#define&#32;ZMQ_HAS_RVALUE_REFS</emphasis>
170 <emphasis role="preprocessor">#define&#32;ZMQ_DELETED_FUNCTION&#32;=&#32;delete</emphasis>
171 <emphasis role="preprocessor">#elif&#32;defined(_MSC_VER)&#32;&amp;&amp;&#32;(_MSC_VER&#32;&gt;=&#32;1600)</emphasis>
172 <emphasis role="preprocessor">#define&#32;ZMQ_HAS_RVALUE_REFS</emphasis>
173 <emphasis role="preprocessor">#define&#32;ZMQ_DELETED_FUNCTION</emphasis>
174 <emphasis role="preprocessor">#else</emphasis>
175 <emphasis role="preprocessor">#define&#32;ZMQ_DELETED_FUNCTION</emphasis>
176 <emphasis role="preprocessor">#endif</emphasis>
177 
178 <emphasis role="preprocessor">#if&#32;defined(ZMQ_CPP11)&#32;&amp;&amp;&#32;!defined(__llvm__)&#32;&amp;&amp;&#32;!defined(__INTEL_COMPILER)&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
179 <emphasis role="preprocessor">&#32;&#32;&amp;&amp;&#32;defined(__GNUC__)&#32;&amp;&amp;&#32;__GNUC__&#32;&lt;&#32;5</emphasis>
180 <emphasis role="preprocessor">#define&#32;ZMQ_CPP11_PARTIAL</emphasis>
181 <emphasis role="preprocessor">#elif&#32;defined(__GLIBCXX__)&#32;&amp;&amp;&#32;__GLIBCXX__&#32;&lt;&#32;20160805</emphasis>
182 <emphasis role="comment">//the&#32;date&#32;here&#32;is&#32;the&#32;last&#32;date&#32;of&#32;gcc&#32;4.9.4,&#32;which</emphasis>
183 <emphasis role="comment">//&#32;effectively&#32;means&#32;libstdc++&#32;from&#32;gcc&#32;5.5&#32;and&#32;higher&#32;won&apos;t&#32;trigger&#32;this&#32;branch</emphasis>
184 <emphasis role="preprocessor">#define&#32;ZMQ_CPP11_PARTIAL</emphasis>
185 <emphasis role="preprocessor">#endif</emphasis>
186 
187 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
188 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11_PARTIAL</emphasis>
189 <emphasis role="preprocessor">#define&#32;ZMQ_IS_TRIVIALLY_COPYABLE(T)&#32;__has_trivial_copy(T)</emphasis>
190 <emphasis role="preprocessor">#else</emphasis>
191 <emphasis role="preprocessor">#include&#32;&lt;type_traits&gt;</emphasis>
192 <emphasis role="preprocessor">#define&#32;ZMQ_IS_TRIVIALLY_COPYABLE(T)&#32;std::is_trivially_copyable&lt;T&gt;::value</emphasis>
193 <emphasis role="preprocessor">#endif</emphasis>
194 <emphasis role="preprocessor">#endif</emphasis>
195 
196 <emphasis role="preprocessor">#if&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(3,&#32;3,&#32;0)</emphasis>
197 <emphasis role="preprocessor">#define&#32;ZMQ_NEW_MONITOR_EVENT_LAYOUT</emphasis>
198 <emphasis role="preprocessor">#endif</emphasis>
199 
200 <emphasis role="preprocessor">#if&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(4,&#32;1,&#32;0)</emphasis>
201 <emphasis role="preprocessor">#define&#32;ZMQ_HAS_PROXY_STEERABLE</emphasis>
202 <emphasis role="comment">/*&#32;&#32;Socket&#32;event&#32;data&#32;&#32;*/</emphasis>
203 <emphasis role="keyword">typedef</emphasis>&#32;<emphasis role="keyword">struct</emphasis>
204 {
205 &#32;&#32;&#32;&#32;uint16_t&#32;event;&#32;<emphasis role="comment">//&#32;id&#32;of&#32;the&#32;event&#32;as&#32;bitfield</emphasis>
206 &#32;&#32;&#32;&#32;int32_t&#32;value;&#32;&#32;<emphasis role="comment">//&#32;value&#32;is&#32;either&#32;error&#32;code,&#32;fd&#32;or&#32;reconnect&#32;interval</emphasis>
207 }&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>;
208 <emphasis role="preprocessor">#endif</emphasis>
209 
210 <emphasis role="comment">//&#32;Avoid&#32;using&#32;deprecated&#32;message&#32;receive&#32;function&#32;when&#32;possible</emphasis>
211 <emphasis role="preprocessor">#if&#32;ZMQ_VERSION&#32;&lt;&#32;ZMQ_MAKE_VERSION(3,&#32;2,&#32;0)</emphasis>
212 <emphasis role="preprocessor">#define&#32;zmq_msg_recv(msg,&#32;socket,&#32;flags)&#32;zmq_recvmsg(socket,&#32;msg,&#32;flags)</emphasis>
213 <emphasis role="preprocessor">#endif</emphasis>
214 
215 
216 <emphasis role="comment">//&#32;In&#32;order&#32;to&#32;prevent&#32;unused&#32;variable&#32;warnings&#32;when&#32;building&#32;in&#32;non-debug</emphasis>
217 <emphasis role="comment">//&#32;mode&#32;use&#32;this&#32;macro&#32;to&#32;make&#32;assertions.</emphasis>
218 <emphasis role="preprocessor">#ifndef&#32;NDEBUG</emphasis>
219 <emphasis role="preprocessor">#define&#32;ZMQ_ASSERT(expression)&#32;assert(expression)</emphasis>
220 <emphasis role="preprocessor">#else</emphasis>
221 <emphasis role="preprocessor">#define&#32;ZMQ_ASSERT(expression)&#32;(void)&#32;(expression)</emphasis>
222 <emphasis role="preprocessor">#endif</emphasis>
223 
224 <emphasis role="keyword">namespace&#32;</emphasis>zmq
225 {
226 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
227 <emphasis role="keyword">namespace&#32;</emphasis>detail
228 {
229 <emphasis role="keyword">namespace&#32;</emphasis>ranges
230 {
231 <emphasis role="keyword">using&#32;</emphasis>std::begin;
232 <emphasis role="keyword">using&#32;</emphasis>std::end;
233 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">auto</emphasis>&#32;begin(T&#32;&amp;&amp;r)&#32;-&gt;&#32;<emphasis role="keyword">decltype</emphasis>(begin(std::forward&lt;T&gt;(r)))
234 {
235 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;begin(std::forward&lt;T&gt;(r));
236 }
237 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">auto</emphasis>&#32;end(T&#32;&amp;&amp;r)&#32;-&gt;&#32;<emphasis role="keyword">decltype</emphasis>(end(std::forward&lt;T&gt;(r)))
238 {
239 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;end(std::forward&lt;T&gt;(r));
240 }
241 }&#32;<emphasis role="comment">//&#32;namespace&#32;ranges</emphasis>
242 
243 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">using&#32;</emphasis>void_t&#32;=&#32;void;
244 
245 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;Iter&gt;
246 <emphasis role="keyword">using&#32;</emphasis>iter_value_t&#32;=&#32;<emphasis role="keyword">typename</emphasis>&#32;std::iterator_traits&lt;Iter&gt;::value_type;
247 
248 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;Range&gt;
249 <emphasis role="keyword">using&#32;</emphasis>range_iter_t&#32;=&#32;<emphasis role="keyword">decltype</emphasis>(
250 &#32;&#32;ranges::begin(std::declval&lt;<emphasis role="keyword">typename</emphasis>&#32;std::remove_reference&lt;Range&gt;::type&#32;&amp;&gt;()));
251 
252 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;Range&gt;&#32;<emphasis role="keyword">using&#32;</emphasis>range_value_t&#32;=&#32;iter_value_t&lt;range_iter_t&lt;Range&gt;&gt;;
253 
254 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;=&#32;<emphasis role="keywordtype">void</emphasis>&gt;&#32;<emphasis role="keyword">struct&#32;</emphasis>is_range&#32;:&#32;std::false_type
255 {
256 };
257 
258 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;
259 <emphasis role="keyword">struct&#32;</emphasis>is_range&lt;
260 &#32;&#32;T,
261 &#32;&#32;void_t&lt;decltype(
262 &#32;&#32;&#32;&#32;ranges::begin(std::declval&lt;typename&#32;std::remove_reference&lt;T&gt;::type&#32;&amp;&gt;())
263 &#32;&#32;&#32;&#32;==&#32;ranges::end(std::declval&lt;typename&#32;std::remove_reference&lt;T&gt;::type&#32;&amp;&gt;()))&gt;&gt;
264 &#32;&#32;&#32;&#32;:&#32;std::true_type
265 {
266 };
267 
268 }&#32;<emphasis role="comment">//&#32;namespace&#32;detail</emphasis>
269 <emphasis role="preprocessor">#endif</emphasis>
270 
271 <emphasis role="keyword">typedef</emphasis>&#32;zmq_free_fn&#32;free_fn;
272 <emphasis role="keyword">typedef</emphasis>&#32;zmq_pollitem_t&#32;pollitem_t;
273 
274 <emphasis role="keyword">class&#32;</emphasis>error_t&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;std::exception
275 {
276 &#32;&#32;<emphasis role="keyword">public</emphasis>:
277 &#32;&#32;&#32;&#32;error_t()&#32;ZMQ_NOTHROW&#32;:&#32;errnum(zmq_errno())&#32;{}
278 &#32;&#32;&#32;&#32;<emphasis role="keyword">explicit</emphasis>&#32;error_t(<emphasis role="keywordtype">int</emphasis>&#32;err)&#32;ZMQ_NOTHROW&#32;:&#32;errnum(err)&#32;{}
279 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*what()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW&#32;ZMQ_OVERRIDE
280 &#32;&#32;&#32;&#32;{
281 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;zmq_strerror(errnum);
282 &#32;&#32;&#32;&#32;}
283 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;num()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;errnum;&#32;}
284 
285 &#32;&#32;<emphasis role="keyword">private</emphasis>:
286 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;errnum;
287 };
288 
289 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;poll(zmq_pollitem_t&#32;*items_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;nitems_,&#32;<emphasis role="keywordtype">long</emphasis>&#32;timeout_&#32;=&#32;-1)
290 {
291 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_poll(items_,&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">int</emphasis><emphasis role="keyword">&gt;</emphasis>(nitems_),&#32;timeout_);
292 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;&lt;&#32;0)
293 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
294 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;rc;
295 }
296 
297 ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;poll&#32;taking&#32;non-const&#32;items&quot;</emphasis>)
298 inline&#32;<emphasis role="keywordtype">int</emphasis>&#32;poll(zmq_pollitem_t&#32;const&#32;*items_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;nitems_,&#32;<emphasis role="keywordtype">long</emphasis>&#32;timeout_&#32;=&#32;-1)
299 {
300 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;poll(<emphasis role="keyword">const_cast&lt;</emphasis>zmq_pollitem_t&#32;*<emphasis role="keyword">&gt;</emphasis>(items_),&#32;nitems_,&#32;timeout_);
301 }
302 
303 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
304 ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;poll&#32;taking&#32;non-const&#32;items&quot;</emphasis>)
305 inline&#32;<emphasis role="keywordtype">int</emphasis>
306 poll(zmq_pollitem_t&#32;const&#32;*items,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;nitems,&#32;std::chrono::milliseconds&#32;timeout)
307 {
308 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;poll(<emphasis role="keyword">const_cast&lt;</emphasis>zmq_pollitem_t&#32;*<emphasis role="keyword">&gt;</emphasis>(items),&#32;nitems,
309 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">long</emphasis><emphasis role="keyword">&gt;</emphasis>(timeout.count()));
310 }
311 
312 ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;poll&#32;taking&#32;non-const&#32;items&quot;</emphasis>)
313 inline&#32;<emphasis role="keywordtype">int</emphasis>&#32;poll(std::vector&lt;zmq_pollitem_t&gt;&#32;const&#32;&amp;items,
314 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::chrono::milliseconds&#32;timeout)
315 {
316 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;poll(<emphasis role="keyword">const_cast&lt;</emphasis>zmq_pollitem_t&#32;*<emphasis role="keyword">&gt;</emphasis>(items.data()),&#32;items.size(),
317 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">long</emphasis><emphasis role="keyword">&gt;</emphasis>(timeout.count()));
318 }
319 
320 ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;poll&#32;taking&#32;non-const&#32;items&quot;</emphasis>)
321 inline&#32;<emphasis role="keywordtype">int</emphasis>&#32;poll(std::vector&lt;zmq_pollitem_t&gt;&#32;const&#32;&amp;items,&#32;<emphasis role="keywordtype">long</emphasis>&#32;timeout_&#32;=&#32;-1)
322 {
323 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;poll(<emphasis role="keyword">const_cast&lt;</emphasis>zmq_pollitem_t&#32;*<emphasis role="keyword">&gt;</emphasis>(items.data()),&#32;items.size(),&#32;timeout_);
324 }
325 
326 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>
327 poll(zmq_pollitem_t&#32;*items,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;nitems,&#32;std::chrono::milliseconds&#32;timeout)
328 {
329 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;poll(items,&#32;nitems,&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">long</emphasis><emphasis role="keyword">&gt;</emphasis>(timeout.count()));
330 }
331 
332 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;poll(std::vector&lt;zmq_pollitem_t&gt;&#32;&amp;items,
333 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::chrono::milliseconds&#32;timeout)
334 {
335 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;poll(items.data(),&#32;items.size(),&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">long</emphasis><emphasis role="keyword">&gt;</emphasis>(timeout.count()));
336 }
337 
338 ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;poll&#32;taking&#32;std::chrono&#32;instead&#32;of&#32;long&quot;</emphasis>)
339 inline&#32;<emphasis role="keywordtype">int</emphasis>&#32;poll(std::vector&lt;zmq_pollitem_t&gt;&#32;&amp;items,&#32;<emphasis role="keywordtype">long</emphasis>&#32;timeout_&#32;=&#32;-1)
340 {
341 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;poll(items.data(),&#32;items.size(),&#32;timeout_);
342 }
343 
344 <emphasis role="keyword">template</emphasis>&lt;std::<emphasis role="keywordtype">size_t</emphasis>&#32;SIZE&gt;
345 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;poll(std::array&lt;zmq_pollitem_t,&#32;SIZE&gt;&#32;&amp;items,
346 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::chrono::milliseconds&#32;timeout)
347 {
348 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;poll(items.data(),&#32;items.size(),&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">long</emphasis><emphasis role="keyword">&gt;</emphasis>(timeout.count()));
349 }
350 <emphasis role="preprocessor">#endif</emphasis>
351 
352 
353 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;version(<emphasis role="keywordtype">int</emphasis>&#32;*major_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;*minor_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;*patch_)
354 {
355 &#32;&#32;&#32;&#32;zmq_version(major_,&#32;minor_,&#32;patch_);
356 }
357 
358 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
359 <emphasis role="keyword">inline</emphasis>&#32;std::tuple&lt;int,&#32;int,&#32;int&gt;&#32;version()
360 {
361 &#32;&#32;&#32;&#32;std::tuple&lt;int,&#32;int,&#32;int&gt;&#32;v;
362 &#32;&#32;&#32;&#32;zmq_version(&amp;std::get&lt;0&gt;(v),&#32;&amp;std::get&lt;1&gt;(v),&#32;&amp;std::get&lt;2&gt;(v));
363 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;v;
364 }
365 
366 <emphasis role="preprocessor">#if&#32;!defined(ZMQ_CPP11_PARTIAL)</emphasis>
367 <emphasis role="keyword">namespace&#32;</emphasis>detail
368 {
369 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">struct&#32;</emphasis>is_char_type
370 {
371 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;true&#32;if&#32;character&#32;type&#32;for&#32;string&#32;literals&#32;in&#32;C++11</emphasis>
372 &#32;&#32;&#32;&#32;<emphasis role="keyword">static</emphasis>&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;value&#32;=
373 &#32;&#32;&#32;&#32;&#32;&#32;std::is_same&lt;T,&#32;char&gt;::value&#32;||&#32;std::is_same&lt;T,&#32;wchar_t&gt;::value
374 &#32;&#32;&#32;&#32;&#32;&#32;||&#32;std::is_same&lt;T,&#32;char16_t&gt;::value&#32;||&#32;std::is_same&lt;T,&#32;char32_t&gt;::value;
375 };
376 }
377 <emphasis role="preprocessor">#endif</emphasis>
378 
379 <emphasis role="preprocessor">#endif</emphasis>
380 
381 <emphasis role="keyword">class&#32;</emphasis>message_t
382 {
383 &#32;&#32;<emphasis role="keyword">public</emphasis>:
384 &#32;&#32;&#32;&#32;message_t()&#32;ZMQ_NOTHROW
385 &#32;&#32;&#32;&#32;{
386 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_init(&amp;msg);
387 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
388 &#32;&#32;&#32;&#32;}
389 
390 &#32;&#32;&#32;&#32;<emphasis role="keyword">explicit</emphasis>&#32;message_t(<emphasis role="keywordtype">size_t</emphasis>&#32;size_)
391 &#32;&#32;&#32;&#32;{
392 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_init_size(&amp;msg,&#32;size_);
393 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
394 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
395 &#32;&#32;&#32;&#32;}
396 
397 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;ForwardIter&gt;&#32;message_t(ForwardIter&#32;first,&#32;ForwardIter&#32;last)
398 &#32;&#32;&#32;&#32;{
399 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;<emphasis role="keyword">typename</emphasis>&#32;std::iterator_traits&lt;ForwardIter&gt;::value_type&#32;value_t;
400 
401 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(std::distance(first,&#32;last)&#32;&gt;=&#32;0);
402 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;<emphasis role="keyword">const</emphasis>&#32;size_&#32;=
403 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">size_t</emphasis><emphasis role="keyword">&gt;</emphasis>(std::distance(first,&#32;last))&#32;*&#32;<emphasis role="keyword">sizeof</emphasis>(value_t);
404 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;<emphasis role="keyword">const</emphasis>&#32;rc&#32;=&#32;zmq_msg_init_size(&amp;msg,&#32;size_);
405 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
406 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
407 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::copy(first,&#32;last,&#32;data&lt;value_t&gt;());
408 &#32;&#32;&#32;&#32;}
409 
410 &#32;&#32;&#32;&#32;message_t(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*data_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size_)
411 &#32;&#32;&#32;&#32;{
412 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_init_size(&amp;msg,&#32;size_);
413 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
414 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
415 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(size_)&#32;{
416 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;this&#32;constructor&#32;allows&#32;(nullptr,&#32;0),</emphasis>
417 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;memcpy&#32;with&#32;a&#32;null&#32;pointer&#32;is&#32;UB</emphasis>
418 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;memcpy(data(),&#32;data_,&#32;size_);
419 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
420 &#32;&#32;&#32;&#32;}
421 
422 &#32;&#32;&#32;&#32;message_t(<emphasis role="keywordtype">void</emphasis>&#32;*data_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size_,&#32;free_fn&#32;*ffn_,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*hint_&#32;=&#32;ZMQ_NULLPTR)
423 &#32;&#32;&#32;&#32;{
424 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_init_data(&amp;msg,&#32;data_,&#32;size_,&#32;ffn_,&#32;hint_);
425 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
426 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
427 &#32;&#32;&#32;&#32;}
428 
429 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;overload&#32;set&#32;of&#32;string-like&#32;types&#32;and&#32;generic&#32;containers</emphasis>
430 <emphasis role="preprocessor">#if&#32;defined(ZMQ_CPP11)&#32;&amp;&amp;&#32;!defined(ZMQ_CPP11_PARTIAL)</emphasis>
431 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;NOTE&#32;this&#32;constructor&#32;will&#32;include&#32;the&#32;null&#32;terminator</emphasis>
432 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;when&#32;called&#32;with&#32;a&#32;string&#32;literal.</emphasis>
433 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;An&#32;overload&#32;taking&#32;const&#32;char*&#32;can&#32;not&#32;be&#32;added&#32;because</emphasis>
434 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;it&#32;would&#32;be&#32;preferred&#32;over&#32;this&#32;function&#32;and&#32;break&#32;compatiblity.</emphasis>
435 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;
436 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">class&#32;</emphasis>Char,
437 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N,
438 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typename</emphasis>&#32;=&#32;<emphasis role="keyword">typename</emphasis>&#32;std::enable_if&lt;detail::is_char_type&lt;Char&gt;::value&gt;::type&gt;
439 &#32;&#32;&#32;&#32;ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.7.0,&#32;use&#32;constructors&#32;taking&#32;iterators,&#32;(pointer,&#32;size)&#32;&quot;</emphasis>
440 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="stringliteral">&quot;or&#32;strings&#32;instead&quot;</emphasis>)
441 &#32;&#32;&#32;&#32;<emphasis role="keyword">explicit</emphasis>&#32;message_t(<emphasis role="keyword">const</emphasis>&#32;Char&#32;(&amp;data)[N])&#32;:
442 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;message_t(detail::ranges::begin(data),&#32;detail::ranges::end(data))
443 &#32;&#32;&#32;&#32;{
444 &#32;&#32;&#32;&#32;}
445 
446 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class&#32;</emphasis>Range,
447 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typename</emphasis>&#32;=&#32;<emphasis role="keyword">typename</emphasis>&#32;std::enable_if&lt;
448 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;detail::is_range&lt;Range&gt;::value
449 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&amp;&amp;&#32;ZMQ_IS_TRIVIALLY_COPYABLE(detail::range_value_t&lt;Range&gt;)
450 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&amp;&amp;&#32;!detail::is_char_type&lt;detail::range_value_t&lt;Range&gt;&gt;::value
451 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&amp;&amp;&#32;!std::is_same&lt;Range,&#32;message_t&gt;::value&gt;::type&gt;
452 &#32;&#32;&#32;&#32;<emphasis role="keyword">explicit</emphasis>&#32;message_t(<emphasis role="keyword">const</emphasis>&#32;Range&#32;&amp;rng)&#32;:
453 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;message_t(detail::ranges::begin(rng),&#32;detail::ranges::end(rng))
454 &#32;&#32;&#32;&#32;{
455 &#32;&#32;&#32;&#32;}
456 
457 &#32;&#32;&#32;&#32;<emphasis role="keyword">explicit</emphasis>&#32;message_t(<emphasis role="keyword">const</emphasis>&#32;std::string&#32;&amp;<link linkend="_classzmq_1_1message__t_1aed6502a922a73cda7f47d47689617d03">str</link>)&#32;:&#32;message_t(<link linkend="_classzmq_1_1message__t_1aed6502a922a73cda7f47d47689617d03">str</link>.data(),&#32;<link linkend="_classzmq_1_1message__t_1aed6502a922a73cda7f47d47689617d03">str</link>.size())&#32;{}
458 
459 <emphasis role="preprocessor">#if&#32;CPPZMQ_HAS_STRING_VIEW</emphasis>
460 &#32;&#32;&#32;&#32;<emphasis role="keyword">explicit</emphasis>&#32;message_t(std::string_view&#32;<link linkend="_classzmq_1_1message__t_1aed6502a922a73cda7f47d47689617d03">str</link>)&#32;:&#32;message_t(<link linkend="_classzmq_1_1message__t_1aed6502a922a73cda7f47d47689617d03">str</link>.data(),&#32;<link linkend="_classzmq_1_1message__t_1aed6502a922a73cda7f47d47689617d03">str</link>.size())&#32;{}
461 <emphasis role="preprocessor">#endif</emphasis>
462 
463 <emphasis role="preprocessor">#endif</emphasis>
464 
465 <emphasis role="preprocessor">#ifdef&#32;ZMQ_HAS_RVALUE_REFS</emphasis>
466 &#32;&#32;&#32;&#32;message_t(message_t&#32;&amp;&amp;rhs)&#32;ZMQ_NOTHROW&#32;:&#32;msg(rhs.msg)
467 &#32;&#32;&#32;&#32;{
468 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_init(&amp;rhs.msg);
469 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
470 &#32;&#32;&#32;&#32;}
471 
472 &#32;&#32;&#32;&#32;message_t&#32;&amp;operator=(message_t&#32;&amp;&amp;rhs)&#32;ZMQ_NOTHROW
473 &#32;&#32;&#32;&#32;{
474 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(msg,&#32;rhs.msg);
475 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>;
476 &#32;&#32;&#32;&#32;}
477 <emphasis role="preprocessor">#endif</emphasis>
478 
479 &#32;&#32;&#32;&#32;~message_t()&#32;ZMQ_NOTHROW
480 &#32;&#32;&#32;&#32;{
481 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_close(&amp;msg);
482 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
483 &#32;&#32;&#32;&#32;}
484 
485 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;rebuild()
486 &#32;&#32;&#32;&#32;{
487 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_close(&amp;msg);
488 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
489 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
490 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;rc&#32;=&#32;zmq_msg_init(&amp;msg);
491 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
492 &#32;&#32;&#32;&#32;}
493 
494 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;rebuild(<emphasis role="keywordtype">size_t</emphasis>&#32;size_)
495 &#32;&#32;&#32;&#32;{
496 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_close(&amp;msg);
497 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
498 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
499 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;rc&#32;=&#32;zmq_msg_init_size(&amp;msg,&#32;size_);
500 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
501 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
502 &#32;&#32;&#32;&#32;}
503 
504 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;rebuild(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*data_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size_)
505 &#32;&#32;&#32;&#32;{
506 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_close(&amp;msg);
507 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
508 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
509 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;rc&#32;=&#32;zmq_msg_init_size(&amp;msg,&#32;size_);
510 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
511 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
512 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;memcpy(data(),&#32;data_,&#32;size_);
513 &#32;&#32;&#32;&#32;}
514 
515 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;rebuild(<emphasis role="keywordtype">void</emphasis>&#32;*data_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size_,&#32;free_fn&#32;*ffn_,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*hint_&#32;=&#32;ZMQ_NULLPTR)
516 &#32;&#32;&#32;&#32;{
517 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_close(&amp;msg);
518 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
519 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
520 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;rc&#32;=&#32;zmq_msg_init_data(&amp;msg,&#32;data_,&#32;size_,&#32;ffn_,&#32;hint_);
521 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
522 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
523 &#32;&#32;&#32;&#32;}
524 
525 &#32;&#32;&#32;&#32;ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;move&#32;taking&#32;non-const&#32;reference&#32;instead&quot;</emphasis>)
526 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;move(message_t&#32;<emphasis role="keyword">const</emphasis>&#32;*msg_)
527 &#32;&#32;&#32;&#32;{
528 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_move(&amp;msg,&#32;<emphasis role="keyword">const_cast&lt;</emphasis>zmq_msg_t&#32;*<emphasis role="keyword">&gt;</emphasis>(msg_-&gt;handle()));
529 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
530 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
531 &#32;&#32;&#32;&#32;}
532 
533 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;move(message_t&#32;&amp;msg_)
534 &#32;&#32;&#32;&#32;{
535 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_move(&amp;msg,&#32;msg_.handle());
536 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
537 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
538 &#32;&#32;&#32;&#32;}
539 
540 &#32;&#32;&#32;&#32;ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;copy&#32;taking&#32;non-const&#32;reference&#32;instead&quot;</emphasis>)
541 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;copy(message_t&#32;<emphasis role="keyword">const</emphasis>&#32;*msg_)
542 &#32;&#32;&#32;&#32;{
543 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_copy(&amp;msg,&#32;<emphasis role="keyword">const_cast&lt;</emphasis>zmq_msg_t&#32;*<emphasis role="keyword">&gt;</emphasis>(msg_-&gt;handle()));
544 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
545 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
546 &#32;&#32;&#32;&#32;}
547 
548 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;copy(message_t&#32;&amp;msg_)
549 &#32;&#32;&#32;&#32;{
550 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_copy(&amp;msg,&#32;msg_.handle());
551 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
552 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
553 &#32;&#32;&#32;&#32;}
554 
555 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;more()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW
556 &#32;&#32;&#32;&#32;{
557 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_more(<emphasis role="keyword">const_cast&lt;</emphasis>zmq_msg_t&#32;*<emphasis role="keyword">&gt;</emphasis>(&amp;msg));
558 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;rc&#32;!=&#32;0;
559 &#32;&#32;&#32;&#32;}
560 
561 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;*data()&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;zmq_msg_data(&amp;msg);&#32;}
562 
563 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*data()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW
564 &#32;&#32;&#32;&#32;{
565 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;zmq_msg_data(<emphasis role="keyword">const_cast&lt;</emphasis>zmq_msg_t&#32;*<emphasis role="keyword">&gt;</emphasis>(&amp;msg));
566 &#32;&#32;&#32;&#32;}
567 
568 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW
569 &#32;&#32;&#32;&#32;{
570 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;zmq_msg_size(<emphasis role="keyword">const_cast&lt;</emphasis>zmq_msg_t&#32;*<emphasis role="keyword">&gt;</emphasis>(&amp;msg));
571 &#32;&#32;&#32;&#32;}
572 
573 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;<emphasis role="keywordtype">bool</emphasis>&#32;empty()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;size()&#32;==&#32;0u;&#32;}
574 
575 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">typename</emphasis>&#32;T&gt;&#32;T&#32;*data()&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis>T&#32;*<emphasis role="keyword">&gt;</emphasis>(data());&#32;}
576 
577 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">typename</emphasis>&#32;T&gt;&#32;T&#32;<emphasis role="keyword">const</emphasis>&#32;*data()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW
578 &#32;&#32;&#32;&#32;{
579 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis>T&#32;<emphasis role="keyword">const&#32;</emphasis>*<emphasis role="keyword">&gt;</emphasis>(data());
580 &#32;&#32;&#32;&#32;}
581 
582 &#32;&#32;&#32;&#32;ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.0,&#32;use&#32;operator==&#32;instead&quot;</emphasis>)
583 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;equal(<emphasis role="keyword">const</emphasis>&#32;message_t&#32;*other)&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>&#32;==&#32;*other;&#32;}
584 
585 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;message_t&#32;&amp;other)&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW
586 &#32;&#32;&#32;&#32;{
587 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;my_size&#32;=&#32;size();
588 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;my_size&#32;==&#32;other.size()&#32;&amp;&amp;&#32;0&#32;==&#32;memcmp(data(),&#32;other.data(),&#32;my_size);
589 &#32;&#32;&#32;&#32;}
590 
591 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;message_t&#32;&amp;other)&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW
592 &#32;&#32;&#32;&#32;{
593 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;!(*<emphasis role="keyword">this</emphasis>&#32;==&#32;other);
594 &#32;&#32;&#32;&#32;}
595 
596 <emphasis role="preprocessor">#if&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(3,&#32;2,&#32;0)</emphasis>
597 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;get(<emphasis role="keywordtype">int</emphasis>&#32;property_)
598 &#32;&#32;&#32;&#32;{
599 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;value&#32;=&#32;zmq_msg_get(&amp;msg,&#32;property_);
600 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(value&#32;==&#32;-1)
601 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
602 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;value;
603 &#32;&#32;&#32;&#32;}
604 <emphasis role="preprocessor">#endif</emphasis>
605 
606 <emphasis role="preprocessor">#if&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(4,&#32;1,&#32;0)</emphasis>
607 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*gets(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*property_)
608 &#32;&#32;&#32;&#32;{
609 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*value&#32;=&#32;zmq_msg_gets(&amp;msg,&#32;property_);
610 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(value&#32;==&#32;ZMQ_NULLPTR)
611 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
612 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;value;
613 &#32;&#32;&#32;&#32;}
614 <emphasis role="preprocessor">#endif</emphasis>
615 
616 <emphasis role="preprocessor">#if&#32;defined(ZMQ_BUILD_DRAFT_API)&#32;&amp;&amp;&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(4,&#32;2,&#32;0)</emphasis>
617 &#32;&#32;&#32;&#32;uint32_t&#32;routing_id()<emphasis role="keyword">&#32;const</emphasis>
618 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
619 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;zmq_msg_routing_id(<emphasis role="keyword">const_cast&lt;</emphasis>zmq_msg_t&#32;*<emphasis role="keyword">&gt;</emphasis>(&amp;msg));
620 &#32;&#32;&#32;&#32;}
621 
622 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;set_routing_id(uint32_t&#32;routing_id)
623 &#32;&#32;&#32;&#32;{
624 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_set_routing_id(&amp;msg,&#32;routing_id);
625 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
626 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
627 &#32;&#32;&#32;&#32;}
628 
629 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*group()<emphasis role="keyword">&#32;const</emphasis>
630 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
631 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;zmq_msg_group(<emphasis role="keyword">const_cast&lt;</emphasis>zmq_msg_t&#32;*<emphasis role="keyword">&gt;</emphasis>(&amp;msg));
632 &#32;&#32;&#32;&#32;}
633 
634 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;set_group(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*group)
635 &#32;&#32;&#32;&#32;{
636 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_set_group(&amp;msg,&#32;group);
637 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
638 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
639 &#32;&#32;&#32;&#32;}
640 <emphasis role="preprocessor">#endif</emphasis>
641 
642 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;interpret&#32;message&#32;content&#32;as&#32;a&#32;string</emphasis>
643 &#32;&#32;&#32;&#32;std::string&#32;to_string()<emphasis role="keyword">&#32;const</emphasis>
644 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
645 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;std::string(<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keyword">const&#32;</emphasis><emphasis role="keywordtype">char</emphasis>&#32;*<emphasis role="keyword">&gt;</emphasis>(data()),&#32;size());
646 &#32;&#32;&#32;&#32;}
647 <emphasis role="preprocessor">#if&#32;CPPZMQ_HAS_STRING_VIEW</emphasis>
648 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;interpret&#32;message&#32;content&#32;as&#32;a&#32;string</emphasis>
649 &#32;&#32;&#32;&#32;std::string_view&#32;to_string_view()&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">noexcept</emphasis>
650 &#32;&#32;&#32;&#32;{
651 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;std::string_view(<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keyword">const&#32;</emphasis><emphasis role="keywordtype">char</emphasis>&#32;*<emphasis role="keyword">&gt;</emphasis>(data()),&#32;size());
652 &#32;&#32;&#32;&#32;}
653 <emphasis role="preprocessor">#endif</emphasis>
654 
661 &#32;&#32;&#32;&#32;std::string&#32;<link linkend="_classzmq_1_1message__t_1aed6502a922a73cda7f47d47689617d03">str</link>()<emphasis role="keyword">&#32;const</emphasis>
662 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
663 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Partly&#32;mutuated&#32;from&#32;the&#32;same&#32;method&#32;in&#32;zmq::multipart_t</emphasis>
664 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::stringstream&#32;os;
665 
666 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">unsigned</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*msg_data&#32;=&#32;this-&gt;data&lt;unsigned&#32;char&gt;();
667 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">unsigned</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;byte;
668 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size&#32;=&#32;this-&gt;size();
669 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;is_ascii[2]&#32;=&#32;{0,&#32;0};
670 
671 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;os&#32;&lt;&lt;&#32;<emphasis role="stringliteral">&quot;zmq::message_t&#32;[size&#32;&quot;</emphasis>&#32;&lt;&lt;&#32;std::dec&#32;&lt;&lt;&#32;std::setw(3)
672 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&lt;&lt;&#32;std::setfill(<emphasis role="charliteral">&apos;0&apos;</emphasis>)&#32;&lt;&lt;&#32;size&#32;&lt;&lt;&#32;<emphasis role="stringliteral">&quot;]&#32;(&quot;</emphasis>;
673 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Totally&#32;arbitrary</emphasis>
674 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(size&#32;&gt;=&#32;1000)&#32;{
675 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;os&#32;&lt;&lt;&#32;<emphasis role="stringliteral">&quot;...&#32;too&#32;big&#32;to&#32;print)&quot;</emphasis>;
676 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}&#32;<emphasis role="keywordflow">else</emphasis>&#32;{
677 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">while</emphasis>&#32;(size--)&#32;{
678 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">byte</emphasis>&#32;=&#32;*msg_data++;
679 
680 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;is_ascii[1]&#32;=&#32;(<emphasis role="keywordtype">byte</emphasis>&#32;&gt;=&#32;32&#32;&amp;&amp;&#32;<emphasis role="keywordtype">byte</emphasis>&#32;&lt;&#32;127);
681 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(is_ascii[1]&#32;!=&#32;is_ascii[0])
682 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;os&#32;&lt;&lt;&#32;<emphasis role="stringliteral">&quot;&#32;&quot;</emphasis>;&#32;<emphasis role="comment">//&#32;Separate&#32;text/non&#32;text</emphasis>
683 
684 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(is_ascii[1])&#32;{
685 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;os&#32;&lt;&lt;&#32;byte;
686 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}&#32;<emphasis role="keywordflow">else</emphasis>&#32;{
687 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;os&#32;&lt;&lt;&#32;std::hex&#32;&lt;&lt;&#32;std::uppercase&#32;&lt;&lt;&#32;std::setw(2)
688 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&lt;&lt;&#32;std::setfill(<emphasis role="charliteral">&apos;0&apos;</emphasis>)&#32;&lt;&lt;&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">short</emphasis><emphasis role="keyword">&gt;</emphasis>(byte);
689 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
690 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;is_ascii[0]&#32;=&#32;is_ascii[1];
691 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
692 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;os&#32;&lt;&lt;&#32;<emphasis role="stringliteral">&quot;)&quot;</emphasis>;
693 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
694 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;os.str();
695 &#32;&#32;&#32;&#32;}
696 
697 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;swap(<link linkend="_classzmq_1_1message__t">message_t</link>&#32;&amp;other)&#32;ZMQ_NOTHROW
698 &#32;&#32;&#32;&#32;{
699 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;this&#32;assumes&#32;zmq::msg_t&#32;from&#32;libzmq&#32;is&#32;trivially&#32;relocatable</emphasis>
700 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(msg,&#32;other.msg);
701 &#32;&#32;&#32;&#32;}
702 
703 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;zmq_msg_t&#32;*handle()&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;&amp;msg;&#32;}
704 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;<emphasis role="keyword">const</emphasis>&#32;zmq_msg_t&#32;*handle()&#32;const&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;&amp;msg;&#32;}
705 
706 &#32;&#32;<emphasis role="keyword">private</emphasis>:
707 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;&#32;The&#32;underlying&#32;message</emphasis>
708 &#32;&#32;&#32;&#32;zmq_msg_t&#32;msg;
709 
710 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;&#32;Disable&#32;implicit&#32;message&#32;copying,&#32;so&#32;that&#32;users&#32;won&apos;t&#32;use&#32;shared</emphasis>
711 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;&#32;messages&#32;(less&#32;efficient)&#32;without&#32;being&#32;aware&#32;of&#32;the&#32;fact.</emphasis>
712 &#32;&#32;&#32;&#32;message_t(<emphasis role="keyword">const</emphasis>&#32;message_t&#32;&amp;)&#32;ZMQ_DELETED_FUNCTION;
713 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;message_t&#32;&amp;)&#32;ZMQ_DELETED_FUNCTION;
714 };
715 
716 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;swap(<link linkend="_classzmq_1_1message__t">message_t</link>&#32;&amp;a,&#32;<link linkend="_classzmq_1_1message__t">message_t</link>&#32;&amp;b)&#32;ZMQ_NOTHROW
717 {
718 &#32;&#32;&#32;&#32;a.swap(b);
719 }
720 
721 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
722 <emphasis role="keyword">enum&#32;class</emphasis>&#32;ctxopt
723 {
724 <emphasis role="preprocessor">#ifdef&#32;ZMQ_BLOCKY</emphasis>
725 &#32;&#32;&#32;&#32;blocky&#32;=&#32;ZMQ_BLOCKY,
726 <emphasis role="preprocessor">#endif</emphasis>
727 <emphasis role="preprocessor">#ifdef&#32;ZMQ_IO_THREADS</emphasis>
728 &#32;&#32;&#32;&#32;io_threads&#32;=&#32;ZMQ_IO_THREADS,
729 <emphasis role="preprocessor">#endif</emphasis>
730 <emphasis role="preprocessor">#ifdef&#32;ZMQ_THREAD_SCHED_POLICY</emphasis>
731 &#32;&#32;&#32;&#32;thread_sched_policy&#32;=&#32;ZMQ_THREAD_SCHED_POLICY,
732 <emphasis role="preprocessor">#endif</emphasis>
733 <emphasis role="preprocessor">#ifdef&#32;ZMQ_THREAD_PRIORITY</emphasis>
734 &#32;&#32;&#32;&#32;thread_priority&#32;=&#32;ZMQ_THREAD_PRIORITY,
735 <emphasis role="preprocessor">#endif</emphasis>
736 <emphasis role="preprocessor">#ifdef&#32;ZMQ_THREAD_AFFINITY_CPU_ADD</emphasis>
737 &#32;&#32;&#32;&#32;thread_affinity_cpu_add&#32;=&#32;ZMQ_THREAD_AFFINITY_CPU_ADD,
738 <emphasis role="preprocessor">#endif</emphasis>
739 <emphasis role="preprocessor">#ifdef&#32;ZMQ_THREAD_AFFINITY_CPU_REMOVE</emphasis>
740 &#32;&#32;&#32;&#32;thread_affinity_cpu_remove&#32;=&#32;ZMQ_THREAD_AFFINITY_CPU_REMOVE,
741 <emphasis role="preprocessor">#endif</emphasis>
742 <emphasis role="preprocessor">#ifdef&#32;ZMQ_THREAD_NAME_PREFIX</emphasis>
743 &#32;&#32;&#32;&#32;thread_name_prefix&#32;=&#32;ZMQ_THREAD_NAME_PREFIX,
744 <emphasis role="preprocessor">#endif</emphasis>
745 <emphasis role="preprocessor">#ifdef&#32;ZMQ_MAX_MSGSZ</emphasis>
746 &#32;&#32;&#32;&#32;max_msgsz&#32;=&#32;ZMQ_MAX_MSGSZ,
747 <emphasis role="preprocessor">#endif</emphasis>
748 <emphasis role="preprocessor">#ifdef&#32;ZMQ_ZERO_COPY_RECV</emphasis>
749 &#32;&#32;&#32;&#32;zero_copy_recv&#32;=&#32;ZMQ_ZERO_COPY_RECV,
750 <emphasis role="preprocessor">#endif</emphasis>
751 <emphasis role="preprocessor">#ifdef&#32;ZMQ_MAX_SOCKETS</emphasis>
752 &#32;&#32;&#32;&#32;max_sockets&#32;=&#32;ZMQ_MAX_SOCKETS,
753 <emphasis role="preprocessor">#endif</emphasis>
754 <emphasis role="preprocessor">#ifdef&#32;ZMQ_SOCKET_LIMIT</emphasis>
755 &#32;&#32;&#32;&#32;socket_limit&#32;=&#32;ZMQ_SOCKET_LIMIT,
756 <emphasis role="preprocessor">#endif</emphasis>
757 <emphasis role="preprocessor">#ifdef&#32;ZMQ_IPV6</emphasis>
758 &#32;&#32;&#32;&#32;ipv6&#32;=&#32;ZMQ_IPV6,
759 <emphasis role="preprocessor">#endif</emphasis>
760 <emphasis role="preprocessor">#ifdef&#32;ZMQ_MSG_T_SIZE</emphasis>
761 &#32;&#32;&#32;&#32;msg_t_size&#32;=&#32;ZMQ_MSG_T_SIZE
762 <emphasis role="preprocessor">#endif</emphasis>
763 };
764 <emphasis role="preprocessor">#endif</emphasis>
765 
766 <emphasis role="keyword">class&#32;</emphasis>context_t
767 {
768 &#32;&#32;<emphasis role="keyword">public</emphasis>:
769 &#32;&#32;&#32;&#32;context_t()
770 &#32;&#32;&#32;&#32;{
771 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ptr&#32;=&#32;zmq_ctx_new();
772 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(ptr&#32;==&#32;ZMQ_NULLPTR)
773 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
774 &#32;&#32;&#32;&#32;}
775 
776 
777 &#32;&#32;&#32;&#32;<emphasis role="keyword">explicit</emphasis>&#32;context_t(<emphasis role="keywordtype">int</emphasis>&#32;io_threads_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;max_sockets_&#32;=&#32;ZMQ_MAX_SOCKETS_DFLT)
778 &#32;&#32;&#32;&#32;{
779 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ptr&#32;=&#32;zmq_ctx_new();
780 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(ptr&#32;==&#32;ZMQ_NULLPTR)
781 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
782 
783 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_ctx_set(ptr,&#32;ZMQ_IO_THREADS,&#32;io_threads_);
784 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
785 
786 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;rc&#32;=&#32;zmq_ctx_set(ptr,&#32;ZMQ_MAX_SOCKETS,&#32;max_sockets_);
787 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
788 &#32;&#32;&#32;&#32;}
789 
790 <emphasis role="preprocessor">#ifdef&#32;ZMQ_HAS_RVALUE_REFS</emphasis>
791 &#32;&#32;&#32;&#32;context_t(context_t&#32;&amp;&amp;rhs)&#32;ZMQ_NOTHROW&#32;:&#32;ptr(rhs.ptr)&#32;{&#32;rhs.ptr&#32;=&#32;ZMQ_NULLPTR;&#32;}
792 &#32;&#32;&#32;&#32;context_t&#32;&amp;operator=(context_t&#32;&amp;&amp;rhs)&#32;ZMQ_NOTHROW
793 &#32;&#32;&#32;&#32;{
794 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;close();
795 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(ptr,&#32;rhs.ptr);
796 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>;
797 &#32;&#32;&#32;&#32;}
798 <emphasis role="preprocessor">#endif</emphasis>
799 
800 &#32;&#32;&#32;&#32;~context_t()&#32;ZMQ_NOTHROW&#32;{&#32;close();&#32;}
801 
802 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.7.0,&#32;use&#32;set&#32;taking&#32;zmq::ctxopt&#32;instead&quot;</emphasis>)
803 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;setctxopt(<emphasis role="keywordtype">int</emphasis>&#32;option_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;optval_)
804 &#32;&#32;&#32;&#32;{
805 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_ctx_set(ptr,&#32;option_,&#32;optval_);
806 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
807 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;rc;
808 &#32;&#32;&#32;&#32;}
809 
810 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.7.0,&#32;use&#32;get&#32;taking&#32;zmq::ctxopt&#32;instead&quot;</emphasis>)
811 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;getctxopt(<emphasis role="keywordtype">int</emphasis>&#32;option_)&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;zmq_ctx_get(ptr,&#32;option_);&#32;}
812 
813 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
814 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;set(ctxopt&#32;option,&#32;<emphasis role="keywordtype">int</emphasis>&#32;optval)
815 &#32;&#32;&#32;&#32;{
816 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_ctx_set(ptr,&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">int</emphasis><emphasis role="keyword">&gt;</emphasis>(option),&#32;optval);
817 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;==&#32;-1)
818 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
819 &#32;&#32;&#32;&#32;}
820 
821 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;<emphasis role="keywordtype">int</emphasis>&#32;get(ctxopt&#32;option)
822 &#32;&#32;&#32;&#32;{
823 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_ctx_get(ptr,&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">int</emphasis><emphasis role="keyword">&gt;</emphasis>(option));
824 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;some&#32;options&#32;have&#32;a&#32;default&#32;value&#32;of&#32;-1</emphasis>
825 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;which&#32;is&#32;unfortunate,&#32;and&#32;may&#32;result&#32;in&#32;errors</emphasis>
826 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;that&#32;don&apos;t&#32;make&#32;sense</emphasis>
827 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;==&#32;-1)
828 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
829 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;rc;
830 &#32;&#32;&#32;&#32;}
831 <emphasis role="preprocessor">#endif</emphasis>
832 
833 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Terminates&#32;context&#32;(see&#32;also&#32;shutdown()).</emphasis>
834 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;close()&#32;ZMQ_NOTHROW
835 &#32;&#32;&#32;&#32;{
836 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(ptr&#32;==&#32;ZMQ_NULLPTR)
837 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>;
838 
839 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc;
840 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">do</emphasis>&#32;{
841 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;rc&#32;=&#32;zmq_ctx_destroy(ptr);
842 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}&#32;<emphasis role="keywordflow">while</emphasis>&#32;(rc&#32;==&#32;-1&#32;&amp;&amp;&#32;errno&#32;==&#32;EINTR);
843 
844 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
845 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ptr&#32;=&#32;ZMQ_NULLPTR;
846 &#32;&#32;&#32;&#32;}
847 
848 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Shutdown&#32;context&#32;in&#32;preparation&#32;for&#32;termination&#32;(close()).</emphasis>
849 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Causes&#32;all&#32;blocking&#32;socket&#32;operations&#32;and&#32;any&#32;further</emphasis>
850 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;socket&#32;operations&#32;to&#32;return&#32;with&#32;ETERM.</emphasis>
851 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;shutdown()&#32;ZMQ_NOTHROW
852 &#32;&#32;&#32;&#32;{
853 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(ptr&#32;==&#32;ZMQ_NULLPTR)
854 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>;
855 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_ctx_shutdown(ptr);
856 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
857 &#32;&#32;&#32;&#32;}
858 
859 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;&#32;Be&#32;careful&#32;with&#32;this,&#32;it&apos;s&#32;probably&#32;only&#32;useful&#32;for</emphasis>
860 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;&#32;using&#32;the&#32;C&#32;api&#32;together&#32;with&#32;an&#32;existing&#32;C++&#32;api.</emphasis>
861 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;&#32;Normally&#32;you&#32;should&#32;never&#32;need&#32;to&#32;use&#32;this.</emphasis>
862 &#32;&#32;&#32;&#32;ZMQ_EXPLICIT&#32;<emphasis role="keyword">operator</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*()&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;ptr;&#32;}
863 
864 &#32;&#32;&#32;&#32;ZMQ_EXPLICIT&#32;<emphasis role="keyword">operator</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;<emphasis role="keyword">const</emphasis>&#32;*()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;ptr;&#32;}
865 
866 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;<emphasis role="keywordtype">void</emphasis>&#32;*handle()&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;ptr;&#32;}
867 
868 &#32;&#32;&#32;&#32;ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.7.0,&#32;use&#32;handle()&#32;!=&#32;nullptr&#32;instead&quot;</emphasis>)
869 &#32;&#32;&#32;&#32;<emphasis role="keyword">operator</emphasis>&#32;bool()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;ptr&#32;!=&#32;ZMQ_NULLPTR;&#32;}
870 
871 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;swap(context_t&#32;&amp;other)&#32;ZMQ_NOTHROW&#32;{&#32;std::swap(ptr,&#32;other.ptr);&#32;}
872 
873 &#32;&#32;<emphasis role="keyword">private</emphasis>:
874 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;*ptr;
875 
876 &#32;&#32;&#32;&#32;context_t(<emphasis role="keyword">const</emphasis>&#32;context_t&#32;&amp;)&#32;ZMQ_DELETED_FUNCTION;
877 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;context_t&#32;&amp;)&#32;ZMQ_DELETED_FUNCTION;
878 };
879 
880 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;swap(<link linkend="_classzmq_1_1context__t">context_t</link>&#32;&amp;a,&#32;<link linkend="_classzmq_1_1context__t">context_t</link>&#32;&amp;b)&#32;ZMQ_NOTHROW
881 {
882 &#32;&#32;&#32;&#32;a.swap(b);
883 }
884 
885 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
886 
887 <emphasis role="keyword">struct&#32;</emphasis>recv_buffer_size
888 {
889 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;number&#32;of&#32;bytes&#32;written&#32;to&#32;buffer</emphasis>
890 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;untruncated_size;&#32;<emphasis role="comment">//&#32;untruncated&#32;message&#32;size&#32;in&#32;bytes</emphasis>
891 
892 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;<emphasis role="keywordtype">bool</emphasis>&#32;truncated()&#32;const&#32;noexcept
893 &#32;&#32;&#32;&#32;{
894 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;size&#32;!=&#32;untruncated_size;
895 &#32;&#32;&#32;&#32;}
896 };
897 
898 <emphasis role="preprocessor">#if&#32;CPPZMQ_HAS_OPTIONAL</emphasis>
899 
900 <emphasis role="keyword">using&#32;</emphasis>send_result_t&#32;=&#32;std::optional&lt;size_t&gt;;
901 <emphasis role="keyword">using&#32;</emphasis>recv_result_t&#32;=&#32;std::optional&lt;size_t&gt;;
902 <emphasis role="keyword">using&#32;</emphasis>recv_buffer_result_t&#32;=&#32;std::optional&lt;recv_buffer_size&gt;;
903 
904 <emphasis role="preprocessor">#else</emphasis>
905 
906 <emphasis role="keyword">namespace&#32;</emphasis>detail
907 {
908 <emphasis role="comment">//&#32;A&#32;C++11&#32;type&#32;emulating&#32;the&#32;most&#32;basic</emphasis>
909 <emphasis role="comment">//&#32;operations&#32;of&#32;std::optional&#32;for&#32;trivial&#32;types</emphasis>
910 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">class&#32;</emphasis>trivial_optional
911 {
912 &#32;&#32;<emphasis role="keyword">public</emphasis>:
913 &#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(std::is_trivial&lt;T&gt;::value,&#32;<emphasis role="stringliteral">&quot;T&#32;must&#32;be&#32;trivial&quot;</emphasis>);
914 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>value_type&#32;=&#32;T;
915 
916 &#32;&#32;&#32;&#32;trivial_optional()&#32;=&#32;<emphasis role="keywordflow">default</emphasis>;
917 &#32;&#32;&#32;&#32;trivial_optional(T&#32;value)&#32;noexcept&#32;:&#32;_value(value),&#32;_has_value(<emphasis role="keyword">true</emphasis>)&#32;{}
918 
919 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;T&#32;*operator-&gt;()&#32;const&#32;noexcept
920 &#32;&#32;&#32;&#32;{
921 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(_has_value);
922 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;&amp;_value;
923 &#32;&#32;&#32;&#32;}
924 &#32;&#32;&#32;&#32;T&#32;*operator-&gt;()&#32;noexcept
925 &#32;&#32;&#32;&#32;{
926 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(_has_value);
927 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;&amp;_value;
928 &#32;&#32;&#32;&#32;}
929 
930 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;T&#32;&amp;operator*()&#32;const&#32;noexcept
931 &#32;&#32;&#32;&#32;{
932 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(_has_value);
933 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;_value;
934 &#32;&#32;&#32;&#32;}
935 &#32;&#32;&#32;&#32;T&#32;&amp;operator*()&#32;noexcept
936 &#32;&#32;&#32;&#32;{
937 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(_has_value);
938 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;_value;
939 &#32;&#32;&#32;&#32;}
940 
941 &#32;&#32;&#32;&#32;T&#32;&amp;value()
942 &#32;&#32;&#32;&#32;{
943 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(!_has_value)
944 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;std::exception();
945 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;_value;
946 &#32;&#32;&#32;&#32;}
947 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;T&#32;&amp;value()<emphasis role="keyword">&#32;const</emphasis>
948 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
949 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(!_has_value)
950 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;std::exception();
951 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;_value;
952 &#32;&#32;&#32;&#32;}
953 
954 &#32;&#32;&#32;&#32;<emphasis role="keyword">explicit</emphasis>&#32;<emphasis role="keyword">operator</emphasis>&#32;bool()&#32;const&#32;noexcept&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_has_value;&#32;}
955 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;has_value()&#32;const&#32;noexcept&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_has_value;&#32;}
956 
957 &#32;&#32;<emphasis role="keyword">private</emphasis>:
958 &#32;&#32;&#32;&#32;T&#32;_value{};
959 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;_has_value{<emphasis role="keyword">false</emphasis>};
960 };
961 }&#32;<emphasis role="comment">//&#32;namespace&#32;detail</emphasis>
962 
963 <emphasis role="keyword">using&#32;</emphasis>send_result_t&#32;=&#32;detail::trivial_optional&lt;size_t&gt;;
964 <emphasis role="keyword">using&#32;</emphasis>recv_result_t&#32;=&#32;detail::trivial_optional&lt;size_t&gt;;
965 <emphasis role="keyword">using&#32;</emphasis>recv_buffer_result_t&#32;=&#32;detail::trivial_optional&lt;recv_buffer_size&gt;;
966 
967 <emphasis role="preprocessor">#endif</emphasis>
968 
969 <emphasis role="keyword">namespace&#32;</emphasis>detail
970 {
971 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;T&#32;enum_bit_or(T&#32;a,&#32;T&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
972 {
973 &#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(std::is_enum&lt;T&gt;::value,&#32;<emphasis role="stringliteral">&quot;must&#32;be&#32;enum&quot;</emphasis>);
974 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>U&#32;=&#32;<emphasis role="keyword">typename</emphasis>&#32;std::underlying_type&lt;T&gt;::type;
975 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis>T<emphasis role="keyword">&gt;</emphasis>(<emphasis role="keyword">static_cast&lt;</emphasis>U<emphasis role="keyword">&gt;</emphasis>(a)&#32;|&#32;<emphasis role="keyword">static_cast&lt;</emphasis>U<emphasis role="keyword">&gt;</emphasis>(b));
976 }
977 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;T&#32;enum_bit_and(T&#32;a,&#32;T&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
978 {
979 &#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(std::is_enum&lt;T&gt;::value,&#32;<emphasis role="stringliteral">&quot;must&#32;be&#32;enum&quot;</emphasis>);
980 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>U&#32;=&#32;<emphasis role="keyword">typename</emphasis>&#32;std::underlying_type&lt;T&gt;::type;
981 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis>T<emphasis role="keyword">&gt;</emphasis>(<emphasis role="keyword">static_cast&lt;</emphasis>U<emphasis role="keyword">&gt;</emphasis>(a)&#32;&amp;&#32;<emphasis role="keyword">static_cast&lt;</emphasis>U<emphasis role="keyword">&gt;</emphasis>(b));
982 }
983 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;T&#32;enum_bit_xor(T&#32;a,&#32;T&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
984 {
985 &#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(std::is_enum&lt;T&gt;::value,&#32;<emphasis role="stringliteral">&quot;must&#32;be&#32;enum&quot;</emphasis>);
986 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>U&#32;=&#32;<emphasis role="keyword">typename</emphasis>&#32;std::underlying_type&lt;T&gt;::type;
987 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis>T<emphasis role="keyword">&gt;</emphasis>(<emphasis role="keyword">static_cast&lt;</emphasis>U<emphasis role="keyword">&gt;</emphasis>(a)&#32;^&#32;<emphasis role="keyword">static_cast&lt;</emphasis>U<emphasis role="keyword">&gt;</emphasis>(b));
988 }
989 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;T&#32;enum_bit_not(T&#32;a)&#32;<emphasis role="keyword">noexcept</emphasis>
990 {
991 &#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(std::is_enum&lt;T&gt;::value,&#32;<emphasis role="stringliteral">&quot;must&#32;be&#32;enum&quot;</emphasis>);
992 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>U&#32;=&#32;<emphasis role="keyword">typename</emphasis>&#32;std::underlying_type&lt;T&gt;::type;
993 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis>T<emphasis role="keyword">&gt;</emphasis>(~static_cast&lt;U&gt;(a));
994 }
995 }&#32;<emphasis role="comment">//&#32;namespace&#32;detail</emphasis>
996 
997 <emphasis role="comment">//&#32;partially&#32;satisfies&#32;named&#32;requirement&#32;BitmaskType</emphasis>
998 <emphasis role="keyword">enum&#32;class</emphasis>&#32;send_flags&#32;:&#32;<emphasis role="keywordtype">int</emphasis>
999 {
1000 &#32;&#32;&#32;&#32;none&#32;=&#32;0,
1001 &#32;&#32;&#32;&#32;dontwait&#32;=&#32;ZMQ_DONTWAIT,
1002 &#32;&#32;&#32;&#32;sndmore&#32;=&#32;ZMQ_SNDMORE
1003 };
1004 
1005 <emphasis role="keyword">constexpr</emphasis>&#32;send_flags&#32;operator|(send_flags&#32;a,&#32;send_flags&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
1006 {
1007 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_or(a,&#32;b);
1008 }
1009 <emphasis role="keyword">constexpr</emphasis>&#32;send_flags&#32;operator&amp;(send_flags&#32;a,&#32;send_flags&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
1010 {
1011 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_and(a,&#32;b);
1012 }
1013 <emphasis role="keyword">constexpr</emphasis>&#32;send_flags&#32;operator^(send_flags&#32;a,&#32;send_flags&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
1014 {
1015 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_xor(a,&#32;b);
1016 }
1017 <emphasis role="keyword">constexpr</emphasis>&#32;send_flags&#32;operator~(send_flags&#32;a)&#32;<emphasis role="keyword">noexcept</emphasis>
1018 {
1019 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_not(a);
1020 }
1021 
1022 <emphasis role="comment">//&#32;partially&#32;satisfies&#32;named&#32;requirement&#32;BitmaskType</emphasis>
1023 <emphasis role="keyword">enum&#32;class</emphasis>&#32;recv_flags&#32;:&#32;<emphasis role="keywordtype">int</emphasis>
1024 {
1025 &#32;&#32;&#32;&#32;none&#32;=&#32;0,
1026 &#32;&#32;&#32;&#32;dontwait&#32;=&#32;ZMQ_DONTWAIT
1027 };
1028 
1029 <emphasis role="keyword">constexpr</emphasis>&#32;recv_flags&#32;operator|(recv_flags&#32;a,&#32;recv_flags&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
1030 {
1031 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_or(a,&#32;b);
1032 }
1033 <emphasis role="keyword">constexpr</emphasis>&#32;recv_flags&#32;operator&amp;(recv_flags&#32;a,&#32;recv_flags&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
1034 {
1035 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_and(a,&#32;b);
1036 }
1037 <emphasis role="keyword">constexpr</emphasis>&#32;recv_flags&#32;operator^(recv_flags&#32;a,&#32;recv_flags&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
1038 {
1039 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_xor(a,&#32;b);
1040 }
1041 <emphasis role="keyword">constexpr</emphasis>&#32;recv_flags&#32;operator~(recv_flags&#32;a)&#32;<emphasis role="keyword">noexcept</emphasis>
1042 {
1043 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_not(a);
1044 }
1045 
1046 
1047 <emphasis role="comment">//&#32;mutable_buffer,&#32;const_buffer&#32;and&#32;buffer&#32;are&#32;based&#32;on</emphasis>
1048 <emphasis role="comment">//&#32;the&#32;Networking&#32;TS&#32;specification,&#32;draft:</emphasis>
1049 <emphasis role="comment">//&#32;http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2018/n4771.pdf</emphasis>
1050 
1051 <emphasis role="keyword">class&#32;</emphasis>mutable_buffer
1052 {
1053 &#32;&#32;<emphasis role="keyword">public</emphasis>:
1054 &#32;&#32;&#32;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;mutable_buffer()&#32;noexcept&#32;:&#32;_data(<emphasis role="keywordtype">nullptr</emphasis>),&#32;_size(0)&#32;{}
1055 &#32;&#32;&#32;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;mutable_buffer(<emphasis role="keywordtype">void</emphasis>&#32;*p,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n)&#32;noexcept&#32;:&#32;_data(p),&#32;_size(n)
1056 &#32;&#32;&#32;&#32;{
1057 <emphasis role="preprocessor">#ifdef&#32;ZMQ_EXTENDED_CONSTEXPR</emphasis>
1058 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(p&#32;!=&#32;<emphasis role="keyword">nullptr</emphasis>&#32;||&#32;n&#32;==&#32;0);
1059 <emphasis role="preprocessor">#endif</emphasis>
1060 &#32;&#32;&#32;&#32;}
1061 
1062 &#32;&#32;&#32;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*data()&#32;const&#32;noexcept&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_data;&#32;}
1063 &#32;&#32;&#32;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size()&#32;const&#32;noexcept&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_size;&#32;}
1064 &#32;&#32;&#32;&#32;mutable_buffer&#32;&amp;operator+=(<emphasis role="keywordtype">size_t</emphasis>&#32;n)&#32;<emphasis role="keyword">noexcept</emphasis>
1065 &#32;&#32;&#32;&#32;{
1066 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;(std::min)&#32;is&#32;a&#32;workaround&#32;for&#32;when&#32;a&#32;min&#32;macro&#32;is&#32;defined</emphasis>
1067 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">auto</emphasis>&#32;shift&#32;=&#32;(std::min)(n,&#32;_size);
1068 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_data&#32;=&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">char</emphasis>&#32;*<emphasis role="keyword">&gt;</emphasis>(_data)&#32;+&#32;shift;
1069 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_size&#32;-=&#32;shift;
1070 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>;
1071 &#32;&#32;&#32;&#32;}
1072 
1073 &#32;&#32;<emphasis role="keyword">private</emphasis>:
1074 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;*_data;
1075 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;_size;
1076 };
1077 
1078 <emphasis role="keyword">inline</emphasis>&#32;mutable_buffer&#32;operator+(<emphasis role="keyword">const</emphasis>&#32;mutable_buffer&#32;&amp;mb,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n)&#32;<emphasis role="keyword">noexcept</emphasis>
1079 {
1080 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;mutable_buffer(<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">char</emphasis>&#32;*<emphasis role="keyword">&gt;</emphasis>(mb.data())&#32;+&#32;(std::min)(n,&#32;mb.size()),
1081 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;mb.size()&#32;-&#32;(std::min)(n,&#32;mb.size()));
1082 }
1083 <emphasis role="keyword">inline</emphasis>&#32;mutable_buffer&#32;operator+(<emphasis role="keywordtype">size_t</emphasis>&#32;n,&#32;<emphasis role="keyword">const</emphasis>&#32;mutable_buffer&#32;&amp;mb)&#32;<emphasis role="keyword">noexcept</emphasis>
1084 {
1085 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;mb&#32;+&#32;n;
1086 }
1087 
1088 <emphasis role="keyword">class&#32;</emphasis>const_buffer
1089 {
1090 &#32;&#32;<emphasis role="keyword">public</emphasis>:
1091 &#32;&#32;&#32;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;const_buffer()&#32;noexcept&#32;:&#32;_data(<emphasis role="keywordtype">nullptr</emphasis>),&#32;_size(0)&#32;{}
1092 &#32;&#32;&#32;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;const_buffer(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*p,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n)&#32;noexcept&#32;:&#32;_data(p),&#32;_size(n)
1093 &#32;&#32;&#32;&#32;{
1094 <emphasis role="preprocessor">#ifdef&#32;ZMQ_EXTENDED_CONSTEXPR</emphasis>
1095 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(p&#32;!=&#32;<emphasis role="keyword">nullptr</emphasis>&#32;||&#32;n&#32;==&#32;0);
1096 <emphasis role="preprocessor">#endif</emphasis>
1097 &#32;&#32;&#32;&#32;}
1098 &#32;&#32;&#32;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;const_buffer(<emphasis role="keyword">const</emphasis>&#32;mutable_buffer&#32;&amp;mb)&#32;noexcept&#32;:
1099 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_data(mb.data()),
1100 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_size(mb.size())
1101 &#32;&#32;&#32;&#32;{
1102 &#32;&#32;&#32;&#32;}
1103 
1104 &#32;&#32;&#32;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*data()&#32;const&#32;noexcept&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_data;&#32;}
1105 &#32;&#32;&#32;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size()&#32;const&#32;noexcept&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_size;&#32;}
1106 &#32;&#32;&#32;&#32;const_buffer&#32;&amp;operator+=(<emphasis role="keywordtype">size_t</emphasis>&#32;n)&#32;<emphasis role="keyword">noexcept</emphasis>
1107 &#32;&#32;&#32;&#32;{
1108 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">auto</emphasis>&#32;shift&#32;=&#32;(std::min)(n,&#32;_size);
1109 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_data&#32;=&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keyword">const&#32;</emphasis><emphasis role="keywordtype">char</emphasis>&#32;*<emphasis role="keyword">&gt;</emphasis>(_data)&#32;+&#32;shift;
1110 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_size&#32;-=&#32;shift;
1111 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>;
1112 &#32;&#32;&#32;&#32;}
1113 
1114 &#32;&#32;<emphasis role="keyword">private</emphasis>:
1115 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*_data;
1116 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;_size;
1117 };
1118 
1119 <emphasis role="keyword">inline</emphasis>&#32;const_buffer&#32;operator+(<emphasis role="keyword">const</emphasis>&#32;const_buffer&#32;&amp;cb,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n)&#32;<emphasis role="keyword">noexcept</emphasis>
1120 {
1121 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;const_buffer(<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keyword">const&#32;</emphasis><emphasis role="keywordtype">char</emphasis>&#32;*<emphasis role="keyword">&gt;</emphasis>(cb.data())
1122 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;+&#32;(std::min)(n,&#32;cb.size()),
1123 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;cb.size()&#32;-&#32;(std::min)(n,&#32;cb.size()));
1124 }
1125 <emphasis role="keyword">inline</emphasis>&#32;const_buffer&#32;operator+(<emphasis role="keywordtype">size_t</emphasis>&#32;n,&#32;<emphasis role="keyword">const</emphasis>&#32;const_buffer&#32;&amp;cb)&#32;<emphasis role="keyword">noexcept</emphasis>
1126 {
1127 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;cb&#32;+&#32;n;
1128 }
1129 
1130 <emphasis role="comment">//&#32;buffer&#32;creation</emphasis>
1131 
1132 <emphasis role="keyword">constexpr</emphasis>&#32;mutable_buffer&#32;buffer(<emphasis role="keywordtype">void</emphasis>&#32;*p,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n)&#32;<emphasis role="keyword">noexcept</emphasis>
1133 {
1134 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;mutable_buffer(p,&#32;n);
1135 }
1136 <emphasis role="keyword">constexpr</emphasis>&#32;const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*p,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n)&#32;<emphasis role="keyword">noexcept</emphasis>
1137 {
1138 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;const_buffer(p,&#32;n);
1139 }
1140 <emphasis role="keyword">constexpr</emphasis>&#32;mutable_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;mutable_buffer&#32;&amp;mb)&#32;<emphasis role="keyword">noexcept</emphasis>
1141 {
1142 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;mb;
1143 }
1144 <emphasis role="keyword">inline</emphasis>&#32;mutable_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;mutable_buffer&#32;&amp;mb,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n)&#32;<emphasis role="keyword">noexcept</emphasis>
1145 {
1146 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;mutable_buffer(mb.data(),&#32;(std::min)(mb.size(),&#32;n));
1147 }
1148 <emphasis role="keyword">constexpr</emphasis>&#32;const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;const_buffer&#32;&amp;cb)&#32;<emphasis role="keyword">noexcept</emphasis>
1149 {
1150 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;cb;
1151 }
1152 <emphasis role="keyword">inline</emphasis>&#32;const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;const_buffer&#32;&amp;cb,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n)&#32;<emphasis role="keyword">noexcept</emphasis>
1153 {
1154 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;const_buffer(cb.data(),&#32;(std::min)(cb.size(),&#32;n));
1155 }
1156 
1157 <emphasis role="keyword">namespace&#32;</emphasis>detail
1158 {
1159 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">struct&#32;</emphasis>is_buffer
1160 {
1161 &#32;&#32;&#32;&#32;<emphasis role="keyword">static</emphasis>&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;value&#32;=
1162 &#32;&#32;&#32;&#32;&#32;&#32;std::is_same&lt;T,&#32;const_buffer&gt;::value&#32;||&#32;std::is_same&lt;T,&#32;mutable_buffer&gt;::value;
1163 };
1164 
1165 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&gt;&#32;<emphasis role="keyword">struct&#32;</emphasis>is_pod_like
1166 {
1167 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;NOTE:&#32;The&#32;networking&#32;draft&#32;N4771&#32;section&#32;16.11&#32;requires</emphasis>
1168 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;T&#32;in&#32;the&#32;buffer&#32;functions&#32;below&#32;to&#32;be</emphasis>
1169 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;trivially&#32;copyable&#32;OR&#32;standard&#32;layout.</emphasis>
1170 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Here&#32;we&#32;decide&#32;to&#32;be&#32;conservative&#32;and&#32;require&#32;both.</emphasis>
1171 &#32;&#32;&#32;&#32;<emphasis role="keyword">static</emphasis>&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;value&#32;=
1172 &#32;&#32;&#32;&#32;&#32;&#32;ZMQ_IS_TRIVIALLY_COPYABLE(T)&#32;&amp;&amp;&#32;std::is_standard_layout&lt;T&gt;::value;
1173 };
1174 
1175 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;C&gt;&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keyword">auto</emphasis>&#32;seq_size(<emphasis role="keyword">const</emphasis>&#32;C&#32;&amp;c)&#32;<emphasis role="keyword">noexcept</emphasis>&#32;-&gt;&#32;<emphasis role="keyword">decltype</emphasis>(c.size())
1176 {
1177 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;c.size();
1178 }
1179 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;
1180 <emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;seq_size(<emphasis role="keyword">const</emphasis>&#32;T&#32;(&amp;<emphasis role="comment">/*array*/</emphasis>)[N])&#32;<emphasis role="keyword">noexcept</emphasis>
1181 {
1182 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;N;
1183 }
1184 
1185 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;Seq&gt;
1186 <emphasis role="keyword">auto</emphasis>&#32;buffer_contiguous_sequence(Seq&#32;&amp;&amp;seq)&#32;<emphasis role="keyword">noexcept</emphasis>
1187 &#32;&#32;-&gt;&#32;<emphasis role="keyword">decltype</emphasis>(buffer(std::addressof(*std::begin(seq)),&#32;<emphasis role="keywordtype">size_t</emphasis>{}))
1188 {
1189 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>T&#32;=&#32;<emphasis role="keyword">typename</emphasis>&#32;std::remove_cv&lt;
1190 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typename</emphasis>&#32;std::remove_reference&lt;<emphasis role="keyword">decltype</emphasis>(*std::begin(seq))&gt;::type&gt;::type;
1191 &#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(detail::is_pod_like&lt;T&gt;::value,&#32;<emphasis role="stringliteral">&quot;T&#32;must&#32;be&#32;POD&quot;</emphasis>);
1192 
1193 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">auto</emphasis>&#32;size&#32;=&#32;seq_size(seq);
1194 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;buffer(size&#32;!=&#32;0u&#32;?&#32;std::addressof(*std::begin(seq))&#32;:&#32;<emphasis role="keyword">nullptr</emphasis>,
1195 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;size&#32;*&#32;<emphasis role="keyword">sizeof</emphasis>(T));
1196 }
1197 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;Seq&gt;
1198 <emphasis role="keyword">auto</emphasis>&#32;buffer_contiguous_sequence(Seq&#32;&amp;&amp;seq,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1199 &#32;&#32;-&gt;&#32;<emphasis role="keyword">decltype</emphasis>(buffer_contiguous_sequence(seq))
1200 {
1201 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>T&#32;=&#32;<emphasis role="keyword">typename</emphasis>&#32;std::remove_cv&lt;
1202 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typename</emphasis>&#32;std::remove_reference&lt;<emphasis role="keyword">decltype</emphasis>(*std::begin(seq))&gt;::type&gt;::type;
1203 &#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(detail::is_pod_like&lt;T&gt;::value,&#32;<emphasis role="stringliteral">&quot;T&#32;must&#32;be&#32;POD&quot;</emphasis>);
1204 
1205 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">auto</emphasis>&#32;size&#32;=&#32;seq_size(seq);
1206 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;buffer(size&#32;!=&#32;0u&#32;?&#32;std::addressof(*std::begin(seq))&#32;:&#32;<emphasis role="keyword">nullptr</emphasis>,
1207 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(std::min)(size&#32;*&#32;<emphasis role="keyword">sizeof</emphasis>(T),&#32;n_bytes));
1208 }
1209 
1210 }&#32;<emphasis role="comment">//&#32;namespace&#32;detail</emphasis>
1211 
1212 <emphasis role="comment">//&#32;C&#32;array</emphasis>
1213 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;&#32;mutable_buffer&#32;buffer(T&#32;(&amp;data)[N])&#32;<emphasis role="keyword">noexcept</emphasis>
1214 {
1215 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data);
1216 }
1217 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;
1218 mutable_buffer&#32;buffer(T&#32;(&amp;data)[N],&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1219 {
1220 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data,&#32;n_bytes);
1221 }
1222 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;&#32;const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;T&#32;(&amp;data)[N])&#32;<emphasis role="keyword">noexcept</emphasis>
1223 {
1224 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data);
1225 }
1226 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;
1227 const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;T&#32;(&amp;data)[N],&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1228 {
1229 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data,&#32;n_bytes);
1230 }
1231 <emphasis role="comment">//&#32;std::array</emphasis>
1232 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;&#32;mutable_buffer&#32;buffer(std::array&lt;T,&#32;N&gt;&#32;&amp;data)&#32;<emphasis role="keyword">noexcept</emphasis>
1233 {
1234 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data);
1235 }
1236 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;
1237 mutable_buffer&#32;buffer(std::array&lt;T,&#32;N&gt;&#32;&amp;data,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1238 {
1239 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data,&#32;n_bytes);
1240 }
1241 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;
1242 const_buffer&#32;buffer(std::array&lt;const&#32;T,&#32;N&gt;&#32;&amp;data)&#32;<emphasis role="keyword">noexcept</emphasis>
1243 {
1244 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data);
1245 }
1246 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;
1247 const_buffer&#32;buffer(std::array&lt;const&#32;T,&#32;N&gt;&#32;&amp;data,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1248 {
1249 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data,&#32;n_bytes);
1250 }
1251 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;
1252 const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;std::array&lt;T,&#32;N&gt;&#32;&amp;data)&#32;<emphasis role="keyword">noexcept</emphasis>
1253 {
1254 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data);
1255 }
1256 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;
1257 const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;std::array&lt;T,&#32;N&gt;&#32;&amp;data,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1258 {
1259 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data,&#32;n_bytes);
1260 }
1261 <emphasis role="comment">//&#32;std::vector</emphasis>
1262 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;Allocator&gt;
1263 mutable_buffer&#32;buffer(std::vector&lt;T,&#32;Allocator&gt;&#32;&amp;data)&#32;<emphasis role="keyword">noexcept</emphasis>
1264 {
1265 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data);
1266 }
1267 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;Allocator&gt;
1268 mutable_buffer&#32;buffer(std::vector&lt;T,&#32;Allocator&gt;&#32;&amp;data,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1269 {
1270 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data,&#32;n_bytes);
1271 }
1272 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;Allocator&gt;
1273 const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;std::vector&lt;T,&#32;Allocator&gt;&#32;&amp;data)&#32;<emphasis role="keyword">noexcept</emphasis>
1274 {
1275 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data);
1276 }
1277 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;Allocator&gt;
1278 const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;std::vector&lt;T,&#32;Allocator&gt;&#32;&amp;data,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1279 {
1280 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data,&#32;n_bytes);
1281 }
1282 <emphasis role="comment">//&#32;std::basic_string</emphasis>
1283 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;Traits,&#32;<emphasis role="keyword">class</emphasis>&#32;Allocator&gt;
1284 mutable_buffer&#32;buffer(std::basic_string&lt;T,&#32;Traits,&#32;Allocator&gt;&#32;&amp;data)&#32;<emphasis role="keyword">noexcept</emphasis>
1285 {
1286 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data);
1287 }
1288 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;Traits,&#32;<emphasis role="keyword">class</emphasis>&#32;Allocator&gt;
1289 mutable_buffer&#32;buffer(std::basic_string&lt;T,&#32;Traits,&#32;Allocator&gt;&#32;&amp;data,
1290 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1291 {
1292 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data,&#32;n_bytes);
1293 }
1294 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;Traits,&#32;<emphasis role="keyword">class</emphasis>&#32;Allocator&gt;
1295 const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;std::basic_string&lt;T,&#32;Traits,&#32;Allocator&gt;&#32;&amp;data)&#32;<emphasis role="keyword">noexcept</emphasis>
1296 {
1297 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data);
1298 }
1299 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;Traits,&#32;<emphasis role="keyword">class</emphasis>&#32;Allocator&gt;
1300 const_buffer&#32;buffer(<emphasis role="keyword">const</emphasis>&#32;std::basic_string&lt;T,&#32;Traits,&#32;Allocator&gt;&#32;&amp;data,
1301 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1302 {
1303 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data,&#32;n_bytes);
1304 }
1305 
1306 <emphasis role="preprocessor">#if&#32;CPPZMQ_HAS_STRING_VIEW</emphasis>
1307 <emphasis role="comment">//&#32;std::basic_string_view</emphasis>
1308 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;Traits&gt;
1309 const_buffer&#32;buffer(std::basic_string_view&lt;T,&#32;Traits&gt;&#32;data)&#32;<emphasis role="keyword">noexcept</emphasis>
1310 {
1311 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data);
1312 }
1313 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keyword">class</emphasis>&#32;Traits&gt;
1314 const_buffer&#32;buffer(std::basic_string_view&lt;T,&#32;Traits&gt;&#32;data,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;n_bytes)&#32;<emphasis role="keyword">noexcept</emphasis>
1315 {
1316 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::buffer_contiguous_sequence(data,&#32;n_bytes);
1317 }
1318 <emphasis role="preprocessor">#endif</emphasis>
1319 
1320 <emphasis role="comment">//&#32;Buffer&#32;for&#32;a&#32;string&#32;literal&#32;(null&#32;terminated)</emphasis>
1321 <emphasis role="comment">//&#32;where&#32;the&#32;buffer&#32;size&#32;excludes&#32;the&#32;terminating&#32;character.</emphasis>
1322 <emphasis role="comment">//&#32;Equivalent&#32;to&#32;zmq::buffer(std::string_view(&quot;...&quot;)).</emphasis>
1323 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;Char,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;N&gt;
1324 <emphasis role="keyword">constexpr</emphasis>&#32;const_buffer&#32;str_buffer(<emphasis role="keyword">const</emphasis>&#32;Char&#32;(&amp;data)[N])&#32;<emphasis role="keyword">noexcept</emphasis>
1325 {
1326 &#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(detail::is_pod_like&lt;Char&gt;::value,&#32;<emphasis role="stringliteral">&quot;Char&#32;must&#32;be&#32;POD&quot;</emphasis>);
1327 <emphasis role="preprocessor">#ifdef&#32;ZMQ_EXTENDED_CONSTEXPR</emphasis>
1328 &#32;&#32;&#32;&#32;assert(data[N&#32;-&#32;1]&#32;==&#32;Char{0});
1329 <emphasis role="preprocessor">#endif</emphasis>
1330 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;const_buffer(<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keyword">const&#32;</emphasis>Char&#32;*<emphasis role="keyword">&gt;</emphasis>(data),&#32;(N&#32;-&#32;1)&#32;*&#32;<emphasis role="keyword">sizeof</emphasis>(Char));
1331 }
1332 
1333 <emphasis role="keyword">namespace&#32;</emphasis>literals
1334 {
1335 <emphasis role="keyword">constexpr</emphasis>&#32;const_buffer&#32;<emphasis role="keyword">operator</emphasis><emphasis role="stringliteral">&quot;&quot;</emphasis>&#32;_zbuf(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*str,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;len)&#32;<emphasis role="keyword">noexcept</emphasis>
1336 {
1337 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;const_buffer(str,&#32;len&#32;*&#32;<emphasis role="keyword">sizeof</emphasis>(<emphasis role="keywordtype">char</emphasis>));
1338 }
1339 <emphasis role="keyword">constexpr</emphasis>&#32;const_buffer&#32;<emphasis role="keyword">operator</emphasis><emphasis role="stringliteral">&quot;&quot;</emphasis>&#32;_zbuf(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">wchar_t</emphasis>&#32;*str,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;len)&#32;<emphasis role="keyword">noexcept</emphasis>
1340 {
1341 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;const_buffer(str,&#32;len&#32;*&#32;<emphasis role="keyword">sizeof</emphasis>(<emphasis role="keywordtype">wchar_t</emphasis>));
1342 }
1343 <emphasis role="keyword">constexpr</emphasis>&#32;const_buffer&#32;<emphasis role="keyword">operator</emphasis><emphasis role="stringliteral">&quot;&quot;</emphasis>&#32;_zbuf(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char16_t</emphasis>&#32;*str,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;len)&#32;<emphasis role="keyword">noexcept</emphasis>
1344 {
1345 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;const_buffer(str,&#32;len&#32;*&#32;<emphasis role="keyword">sizeof</emphasis>(<emphasis role="keywordtype">char16_t</emphasis>));
1346 }
1347 <emphasis role="keyword">constexpr</emphasis>&#32;const_buffer&#32;<emphasis role="keyword">operator</emphasis><emphasis role="stringliteral">&quot;&quot;</emphasis>&#32;_zbuf(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char32_t</emphasis>&#32;*str,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;len)&#32;<emphasis role="keyword">noexcept</emphasis>
1348 {
1349 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;const_buffer(str,&#32;len&#32;*&#32;<emphasis role="keyword">sizeof</emphasis>(<emphasis role="keywordtype">char32_t</emphasis>));
1350 }
1351 }
1352 
1353 <emphasis role="preprocessor">#endif&#32;</emphasis><emphasis role="comment">//&#32;ZMQ_CPP11</emphasis>
1354 
1355 
1356 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
1357 <emphasis role="keyword">namespace&#32;</emphasis>sockopt
1358 {
1359 <emphasis role="comment">//&#32;There&#32;are&#32;two&#32;types&#32;of&#32;options,</emphasis>
1360 <emphasis role="comment">//&#32;integral&#32;type&#32;with&#32;known&#32;compiler&#32;time&#32;size&#32;(int,&#32;bool,&#32;int64_t,&#32;uint64_t)</emphasis>
1361 <emphasis role="comment">//&#32;and&#32;arrays&#32;with&#32;dynamic&#32;size&#32;(strings,&#32;binary&#32;data).</emphasis>
1362 
1363 <emphasis role="comment">//&#32;BoolUnit:&#32;if&#32;true&#32;accepts&#32;values&#32;of&#32;type&#32;bool&#32;(but&#32;passed&#32;as&#32;T&#32;into&#32;libzmq)</emphasis>
1364 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">bool</emphasis>&#32;BoolUnit&#32;=&#32;false&gt;&#32;<emphasis role="keyword">struct&#32;</emphasis>integral_option
1365 {
1366 };
1367 
1368 <emphasis role="comment">//&#32;NullTerm:</emphasis>
1369 <emphasis role="comment">//&#32;0:&#32;binary&#32;data</emphasis>
1370 <emphasis role="comment">//&#32;1:&#32;null-terminated&#32;string&#32;(`getsockopt`&#32;size&#32;includes&#32;null)</emphasis>
1371 <emphasis role="comment">//&#32;2:&#32;binary&#32;(size&#32;32)&#32;or&#32;Z85&#32;encoder&#32;string&#32;of&#32;size&#32;41&#32;(null&#32;included)</emphasis>
1372 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keywordtype">int</emphasis>&#32;NullTerm&#32;=&#32;1&gt;&#32;<emphasis role="keyword">struct&#32;</emphasis>array_option
1373 {
1374 };
1375 
1376 <emphasis role="preprocessor">#define&#32;ZMQ_DEFINE_INTEGRAL_OPT(OPT,&#32;NAME,&#32;TYPE)&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
1377 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;using&#32;NAME##_t&#32;=&#32;integral_option&lt;OPT,&#32;TYPE,&#32;false&gt;;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
1378 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;ZMQ_INLINE_VAR&#32;ZMQ_CONSTEXPR_VAR&#32;NAME##_t&#32;NAME&#32;{}</emphasis>
1379 <emphasis role="preprocessor">#define&#32;ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(OPT,&#32;NAME,&#32;TYPE)&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
1380 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;using&#32;NAME##_t&#32;=&#32;integral_option&lt;OPT,&#32;TYPE,&#32;true&gt;;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
1381 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;ZMQ_INLINE_VAR&#32;ZMQ_CONSTEXPR_VAR&#32;NAME##_t&#32;NAME&#32;{}</emphasis>
1382 <emphasis role="preprocessor">#define&#32;ZMQ_DEFINE_ARRAY_OPT(OPT,&#32;NAME)&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
1383 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;using&#32;NAME##_t&#32;=&#32;array_option&lt;OPT&gt;;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
1384 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;ZMQ_INLINE_VAR&#32;ZMQ_CONSTEXPR_VAR&#32;NAME##_t&#32;NAME&#32;{}</emphasis>
1385 <emphasis role="preprocessor">#define&#32;ZMQ_DEFINE_ARRAY_OPT_BINARY(OPT,&#32;NAME)&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
1386 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;using&#32;NAME##_t&#32;=&#32;array_option&lt;OPT,&#32;0&gt;;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
1387 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;ZMQ_INLINE_VAR&#32;ZMQ_CONSTEXPR_VAR&#32;NAME##_t&#32;NAME&#32;{}</emphasis>
1388 <emphasis role="preprocessor">#define&#32;ZMQ_DEFINE_ARRAY_OPT_BIN_OR_Z85(OPT,&#32;NAME)&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
1389 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;using&#32;NAME##_t&#32;=&#32;array_option&lt;OPT,&#32;2&gt;;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;\</emphasis>
1390 <emphasis role="preprocessor">&#32;&#32;&#32;&#32;ZMQ_INLINE_VAR&#32;ZMQ_CONSTEXPR_VAR&#32;NAME##_t&#32;NAME&#32;{}</emphasis>
1391 
1392 <emphasis role="comment">//&#32;duplicate&#32;definition&#32;from&#32;libzmq&#32;4.3.3</emphasis>
1393 <emphasis role="preprocessor">#if&#32;defined&#32;_WIN32</emphasis>
1394 <emphasis role="preprocessor">#if&#32;defined&#32;_WIN64</emphasis>
1395 <emphasis role="keyword">typedef</emphasis>&#32;<emphasis role="keywordtype">unsigned</emphasis>&#32;__int64&#32;cppzmq_fd_t;
1396 <emphasis role="preprocessor">#else</emphasis>
1397 <emphasis role="keyword">typedef</emphasis>&#32;<emphasis role="keywordtype">unsigned</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;cppzmq_fd_t;
1398 <emphasis role="preprocessor">#endif</emphasis>
1399 <emphasis role="preprocessor">#else</emphasis>
1400 <emphasis role="keyword">typedef</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;cppzmq_fd_t;
1401 <emphasis role="preprocessor">#endif</emphasis>
1402 
1403 <emphasis role="preprocessor">#ifdef&#32;ZMQ_AFFINITY</emphasis>
1404 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_AFFINITY,&#32;affinity,&#32;uint64_t);
1405 <emphasis role="preprocessor">#endif</emphasis>
1406 <emphasis role="preprocessor">#ifdef&#32;ZMQ_BACKLOG</emphasis>
1407 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_BACKLOG,&#32;backlog,&#32;<emphasis role="keywordtype">int</emphasis>);
1408 <emphasis role="preprocessor">#endif</emphasis>
1409 <emphasis role="preprocessor">#ifdef&#32;ZMQ_BINDTODEVICE</emphasis>
1410 ZMQ_DEFINE_ARRAY_OPT_BINARY(ZMQ_BINDTODEVICE,&#32;bindtodevice);
1411 <emphasis role="preprocessor">#endif</emphasis>
1412 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CONFLATE</emphasis>
1413 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_CONFLATE,&#32;conflate,&#32;<emphasis role="keywordtype">int</emphasis>);
1414 <emphasis role="preprocessor">#endif</emphasis>
1415 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CONNECT_ROUTING_ID</emphasis>
1416 ZMQ_DEFINE_ARRAY_OPT(ZMQ_CONNECT_ROUTING_ID,&#32;connect_routing_id);
1417 <emphasis role="preprocessor">#endif</emphasis>
1418 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CONNECT_TIMEOUT</emphasis>
1419 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_CONNECT_TIMEOUT,&#32;connect_timeout,&#32;<emphasis role="keywordtype">int</emphasis>);
1420 <emphasis role="preprocessor">#endif</emphasis>
1421 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CURVE_PUBLICKEY</emphasis>
1422 ZMQ_DEFINE_ARRAY_OPT_BIN_OR_Z85(ZMQ_CURVE_PUBLICKEY,&#32;curve_publickey);
1423 <emphasis role="preprocessor">#endif</emphasis>
1424 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CURVE_SECRETKEY</emphasis>
1425 ZMQ_DEFINE_ARRAY_OPT_BIN_OR_Z85(ZMQ_CURVE_SECRETKEY,&#32;curve_secretkey);
1426 <emphasis role="preprocessor">#endif</emphasis>
1427 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CURVE_SERVER</emphasis>
1428 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_CURVE_SERVER,&#32;curve_server,&#32;<emphasis role="keywordtype">int</emphasis>);
1429 <emphasis role="preprocessor">#endif</emphasis>
1430 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CURVE_SERVERKEY</emphasis>
1431 ZMQ_DEFINE_ARRAY_OPT_BIN_OR_Z85(ZMQ_CURVE_SERVERKEY,&#32;curve_serverkey);
1432 <emphasis role="preprocessor">#endif</emphasis>
1433 <emphasis role="preprocessor">#ifdef&#32;ZMQ_EVENTS</emphasis>
1434 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_EVENTS,&#32;events,&#32;<emphasis role="keywordtype">int</emphasis>);
1435 <emphasis role="preprocessor">#endif</emphasis>
1436 <emphasis role="preprocessor">#ifdef&#32;ZMQ_FD</emphasis>
1437 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_FD,&#32;fd,&#32;cppzmq_fd_t);
1438 <emphasis role="preprocessor">#endif</emphasis>
1439 <emphasis role="preprocessor">#ifdef&#32;ZMQ_GSSAPI_PLAINTEXT</emphasis>
1440 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_GSSAPI_PLAINTEXT,&#32;gssapi_plaintext,&#32;<emphasis role="keywordtype">int</emphasis>);
1441 <emphasis role="preprocessor">#endif</emphasis>
1442 <emphasis role="preprocessor">#ifdef&#32;ZMQ_GSSAPI_SERVER</emphasis>
1443 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_GSSAPI_SERVER,&#32;gssapi_server,&#32;<emphasis role="keywordtype">int</emphasis>);
1444 <emphasis role="preprocessor">#endif</emphasis>
1445 <emphasis role="preprocessor">#ifdef&#32;ZMQ_GSSAPI_SERVICE_PRINCIPAL</emphasis>
1446 ZMQ_DEFINE_ARRAY_OPT(ZMQ_GSSAPI_SERVICE_PRINCIPAL,&#32;gssapi_service_principal);
1447 <emphasis role="preprocessor">#endif</emphasis>
1448 <emphasis role="preprocessor">#ifdef&#32;ZMQ_GSSAPI_SERVICE_PRINCIPAL_NAMETYPE</emphasis>
1449 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_GSSAPI_SERVICE_PRINCIPAL_NAMETYPE,
1450 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;gssapi_service_principal_nametype,
1451 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>);
1452 <emphasis role="preprocessor">#endif</emphasis>
1453 <emphasis role="preprocessor">#ifdef&#32;ZMQ_GSSAPI_PRINCIPAL</emphasis>
1454 ZMQ_DEFINE_ARRAY_OPT(ZMQ_GSSAPI_PRINCIPAL,&#32;gssapi_principal);
1455 <emphasis role="preprocessor">#endif</emphasis>
1456 <emphasis role="preprocessor">#ifdef&#32;ZMQ_GSSAPI_PRINCIPAL_NAMETYPE</emphasis>
1457 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_GSSAPI_PRINCIPAL_NAMETYPE,
1458 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;gssapi_principal_nametype,
1459 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>);
1460 <emphasis role="preprocessor">#endif</emphasis>
1461 <emphasis role="preprocessor">#ifdef&#32;ZMQ_HANDSHAKE_IVL</emphasis>
1462 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_HANDSHAKE_IVL,&#32;handshake_ivl,&#32;<emphasis role="keywordtype">int</emphasis>);
1463 <emphasis role="preprocessor">#endif</emphasis>
1464 <emphasis role="preprocessor">#ifdef&#32;ZMQ_HEARTBEAT_IVL</emphasis>
1465 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_HEARTBEAT_IVL,&#32;heartbeat_ivl,&#32;<emphasis role="keywordtype">int</emphasis>);
1466 <emphasis role="preprocessor">#endif</emphasis>
1467 <emphasis role="preprocessor">#ifdef&#32;ZMQ_HEARTBEAT_TIMEOUT</emphasis>
1468 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_HEARTBEAT_TIMEOUT,&#32;heartbeat_timeout,&#32;<emphasis role="keywordtype">int</emphasis>);
1469 <emphasis role="preprocessor">#endif</emphasis>
1470 <emphasis role="preprocessor">#ifdef&#32;ZMQ_HEARTBEAT_TTL</emphasis>
1471 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_HEARTBEAT_TTL,&#32;heartbeat_ttl,&#32;<emphasis role="keywordtype">int</emphasis>);
1472 <emphasis role="preprocessor">#endif</emphasis>
1473 <emphasis role="preprocessor">#ifdef&#32;ZMQ_IMMEDIATE</emphasis>
1474 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_IMMEDIATE,&#32;immediate,&#32;<emphasis role="keywordtype">int</emphasis>);
1475 <emphasis role="preprocessor">#endif</emphasis>
1476 <emphasis role="preprocessor">#ifdef&#32;ZMQ_INVERT_MATCHING</emphasis>
1477 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_INVERT_MATCHING,&#32;invert_matching,&#32;<emphasis role="keywordtype">int</emphasis>);
1478 <emphasis role="preprocessor">#endif</emphasis>
1479 <emphasis role="preprocessor">#ifdef&#32;ZMQ_IPV6</emphasis>
1480 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_IPV6,&#32;ipv6,&#32;<emphasis role="keywordtype">int</emphasis>);
1481 <emphasis role="preprocessor">#endif</emphasis>
1482 <emphasis role="preprocessor">#ifdef&#32;ZMQ_LAST_ENDPOINT</emphasis>
1483 ZMQ_DEFINE_ARRAY_OPT(ZMQ_LAST_ENDPOINT,&#32;last_endpoint);
1484 <emphasis role="preprocessor">#endif</emphasis>
1485 <emphasis role="preprocessor">#ifdef&#32;ZMQ_LINGER</emphasis>
1486 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_LINGER,&#32;linger,&#32;<emphasis role="keywordtype">int</emphasis>);
1487 <emphasis role="preprocessor">#endif</emphasis>
1488 <emphasis role="preprocessor">#ifdef&#32;ZMQ_MAXMSGSIZE</emphasis>
1489 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_MAXMSGSIZE,&#32;maxmsgsize,&#32;int64_t);
1490 <emphasis role="preprocessor">#endif</emphasis>
1491 <emphasis role="preprocessor">#ifdef&#32;ZMQ_MECHANISM</emphasis>
1492 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_MECHANISM,&#32;mechanism,&#32;<emphasis role="keywordtype">int</emphasis>);
1493 <emphasis role="preprocessor">#endif</emphasis>
1494 <emphasis role="preprocessor">#ifdef&#32;ZMQ_METADATA</emphasis>
1495 ZMQ_DEFINE_ARRAY_OPT(ZMQ_METADATA,&#32;metadata);
1496 <emphasis role="preprocessor">#endif</emphasis>
1497 <emphasis role="preprocessor">#ifdef&#32;ZMQ_MULTICAST_HOPS</emphasis>
1498 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_MULTICAST_HOPS,&#32;multicast_hops,&#32;<emphasis role="keywordtype">int</emphasis>);
1499 <emphasis role="preprocessor">#endif</emphasis>
1500 <emphasis role="preprocessor">#ifdef&#32;ZMQ_MULTICAST_LOOP</emphasis>
1501 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_MULTICAST_LOOP,&#32;multicast_loop,&#32;<emphasis role="keywordtype">int</emphasis>);
1502 <emphasis role="preprocessor">#endif</emphasis>
1503 <emphasis role="preprocessor">#ifdef&#32;ZMQ_MULTICAST_MAXTPDU</emphasis>
1504 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_MULTICAST_MAXTPDU,&#32;multicast_maxtpdu,&#32;<emphasis role="keywordtype">int</emphasis>);
1505 <emphasis role="preprocessor">#endif</emphasis>
1506 <emphasis role="preprocessor">#ifdef&#32;ZMQ_PLAIN_SERVER</emphasis>
1507 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_PLAIN_SERVER,&#32;plain_server,&#32;<emphasis role="keywordtype">int</emphasis>);
1508 <emphasis role="preprocessor">#endif</emphasis>
1509 <emphasis role="preprocessor">#ifdef&#32;ZMQ_PLAIN_PASSWORD</emphasis>
1510 ZMQ_DEFINE_ARRAY_OPT(ZMQ_PLAIN_PASSWORD,&#32;plain_password);
1511 <emphasis role="preprocessor">#endif</emphasis>
1512 <emphasis role="preprocessor">#ifdef&#32;ZMQ_PLAIN_USERNAME</emphasis>
1513 ZMQ_DEFINE_ARRAY_OPT(ZMQ_PLAIN_USERNAME,&#32;plain_username);
1514 <emphasis role="preprocessor">#endif</emphasis>
1515 <emphasis role="preprocessor">#ifdef&#32;ZMQ_USE_FD</emphasis>
1516 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_USE_FD,&#32;use_fd,&#32;<emphasis role="keywordtype">int</emphasis>);
1517 <emphasis role="preprocessor">#endif</emphasis>
1518 <emphasis role="preprocessor">#ifdef&#32;ZMQ_PROBE_ROUTER</emphasis>
1519 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_PROBE_ROUTER,&#32;probe_router,&#32;<emphasis role="keywordtype">int</emphasis>);
1520 <emphasis role="preprocessor">#endif</emphasis>
1521 <emphasis role="preprocessor">#ifdef&#32;ZMQ_RATE</emphasis>
1522 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RATE,&#32;rate,&#32;<emphasis role="keywordtype">int</emphasis>);
1523 <emphasis role="preprocessor">#endif</emphasis>
1524 <emphasis role="preprocessor">#ifdef&#32;ZMQ_RCVBUF</emphasis>
1525 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RCVBUF,&#32;rcvbuf,&#32;<emphasis role="keywordtype">int</emphasis>);
1526 <emphasis role="preprocessor">#endif</emphasis>
1527 <emphasis role="preprocessor">#ifdef&#32;ZMQ_RCVHWM</emphasis>
1528 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RCVHWM,&#32;rcvhwm,&#32;<emphasis role="keywordtype">int</emphasis>);
1529 <emphasis role="preprocessor">#endif</emphasis>
1530 <emphasis role="preprocessor">#ifdef&#32;ZMQ_RCVMORE</emphasis>
1531 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_RCVMORE,&#32;rcvmore,&#32;<emphasis role="keywordtype">int</emphasis>);
1532 <emphasis role="preprocessor">#endif</emphasis>
1533 <emphasis role="preprocessor">#ifdef&#32;ZMQ_RCVTIMEO</emphasis>
1534 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RCVTIMEO,&#32;rcvtimeo,&#32;<emphasis role="keywordtype">int</emphasis>);
1535 <emphasis role="preprocessor">#endif</emphasis>
1536 <emphasis role="preprocessor">#ifdef&#32;ZMQ_RECONNECT_IVL</emphasis>
1537 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RECONNECT_IVL,&#32;reconnect_ivl,&#32;<emphasis role="keywordtype">int</emphasis>);
1538 <emphasis role="preprocessor">#endif</emphasis>
1539 <emphasis role="preprocessor">#ifdef&#32;ZMQ_RECONNECT_IVL_MAX</emphasis>
1540 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RECONNECT_IVL_MAX,&#32;reconnect_ivl_max,&#32;<emphasis role="keywordtype">int</emphasis>);
1541 <emphasis role="preprocessor">#endif</emphasis>
1542 <emphasis role="preprocessor">#ifdef&#32;ZMQ_RECOVERY_IVL</emphasis>
1543 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RECOVERY_IVL,&#32;recovery_ivl,&#32;<emphasis role="keywordtype">int</emphasis>);
1544 <emphasis role="preprocessor">#endif</emphasis>
1545 <emphasis role="preprocessor">#ifdef&#32;ZMQ_REQ_CORRELATE</emphasis>
1546 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_REQ_CORRELATE,&#32;req_correlate,&#32;<emphasis role="keywordtype">int</emphasis>);
1547 <emphasis role="preprocessor">#endif</emphasis>
1548 <emphasis role="preprocessor">#ifdef&#32;ZMQ_REQ_RELAXED</emphasis>
1549 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_REQ_RELAXED,&#32;req_relaxed,&#32;<emphasis role="keywordtype">int</emphasis>);
1550 <emphasis role="preprocessor">#endif</emphasis>
1551 <emphasis role="preprocessor">#ifdef&#32;ZMQ_ROUTER_HANDOVER</emphasis>
1552 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_ROUTER_HANDOVER,&#32;router_handover,&#32;<emphasis role="keywordtype">int</emphasis>);
1553 <emphasis role="preprocessor">#endif</emphasis>
1554 <emphasis role="preprocessor">#ifdef&#32;ZMQ_ROUTER_MANDATORY</emphasis>
1555 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_ROUTER_MANDATORY,&#32;router_mandatory,&#32;<emphasis role="keywordtype">int</emphasis>);
1556 <emphasis role="preprocessor">#endif</emphasis>
1557 <emphasis role="preprocessor">#ifdef&#32;ZMQ_ROUTER_NOTIFY</emphasis>
1558 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_ROUTER_NOTIFY,&#32;router_notify,&#32;<emphasis role="keywordtype">int</emphasis>);
1559 <emphasis role="preprocessor">#endif</emphasis>
1560 <emphasis role="preprocessor">#ifdef&#32;ZMQ_ROUTING_ID</emphasis>
1561 ZMQ_DEFINE_ARRAY_OPT_BINARY(ZMQ_ROUTING_ID,&#32;routing_id);
1562 <emphasis role="preprocessor">#endif</emphasis>
1563 <emphasis role="preprocessor">#ifdef&#32;ZMQ_SNDBUF</emphasis>
1564 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_SNDBUF,&#32;sndbuf,&#32;<emphasis role="keywordtype">int</emphasis>);
1565 <emphasis role="preprocessor">#endif</emphasis>
1566 <emphasis role="preprocessor">#ifdef&#32;ZMQ_SNDHWM</emphasis>
1567 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_SNDHWM,&#32;sndhwm,&#32;<emphasis role="keywordtype">int</emphasis>);
1568 <emphasis role="preprocessor">#endif</emphasis>
1569 <emphasis role="preprocessor">#ifdef&#32;ZMQ_SNDTIMEO</emphasis>
1570 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_SNDTIMEO,&#32;sndtimeo,&#32;<emphasis role="keywordtype">int</emphasis>);
1571 <emphasis role="preprocessor">#endif</emphasis>
1572 <emphasis role="preprocessor">#ifdef&#32;ZMQ_SOCKS_PROXY</emphasis>
1573 ZMQ_DEFINE_ARRAY_OPT(ZMQ_SOCKS_PROXY,&#32;socks_proxy);
1574 <emphasis role="preprocessor">#endif</emphasis>
1575 <emphasis role="preprocessor">#ifdef&#32;ZMQ_STREAM_NOTIFY</emphasis>
1576 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_STREAM_NOTIFY,&#32;stream_notify,&#32;<emphasis role="keywordtype">int</emphasis>);
1577 <emphasis role="preprocessor">#endif</emphasis>
1578 <emphasis role="preprocessor">#ifdef&#32;ZMQ_SUBSCRIBE</emphasis>
1579 ZMQ_DEFINE_ARRAY_OPT(ZMQ_SUBSCRIBE,&#32;subscribe);
1580 <emphasis role="preprocessor">#endif</emphasis>
1581 <emphasis role="preprocessor">#ifdef&#32;ZMQ_TCP_KEEPALIVE</emphasis>
1582 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TCP_KEEPALIVE,&#32;tcp_keepalive,&#32;<emphasis role="keywordtype">int</emphasis>);
1583 <emphasis role="preprocessor">#endif</emphasis>
1584 <emphasis role="preprocessor">#ifdef&#32;ZMQ_TCP_KEEPALIVE_CNT</emphasis>
1585 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TCP_KEEPALIVE_CNT,&#32;tcp_keepalive_cnt,&#32;<emphasis role="keywordtype">int</emphasis>);
1586 <emphasis role="preprocessor">#endif</emphasis>
1587 <emphasis role="preprocessor">#ifdef&#32;ZMQ_TCP_KEEPALIVE_IDLE</emphasis>
1588 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TCP_KEEPALIVE_IDLE,&#32;tcp_keepalive_idle,&#32;<emphasis role="keywordtype">int</emphasis>);
1589 <emphasis role="preprocessor">#endif</emphasis>
1590 <emphasis role="preprocessor">#ifdef&#32;ZMQ_TCP_KEEPALIVE_INTVL</emphasis>
1591 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TCP_KEEPALIVE_INTVL,&#32;tcp_keepalive_intvl,&#32;<emphasis role="keywordtype">int</emphasis>);
1592 <emphasis role="preprocessor">#endif</emphasis>
1593 <emphasis role="preprocessor">#ifdef&#32;ZMQ_TCP_MAXRT</emphasis>
1594 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TCP_MAXRT,&#32;tcp_maxrt,&#32;<emphasis role="keywordtype">int</emphasis>);
1595 <emphasis role="preprocessor">#endif</emphasis>
1596 <emphasis role="preprocessor">#ifdef&#32;ZMQ_THREAD_SAFE</emphasis>
1597 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_THREAD_SAFE,&#32;thread_safe,&#32;<emphasis role="keywordtype">int</emphasis>);
1598 <emphasis role="preprocessor">#endif</emphasis>
1599 <emphasis role="preprocessor">#ifdef&#32;ZMQ_TOS</emphasis>
1600 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TOS,&#32;tos,&#32;<emphasis role="keywordtype">int</emphasis>);
1601 <emphasis role="preprocessor">#endif</emphasis>
1602 <emphasis role="preprocessor">#ifdef&#32;ZMQ_TYPE</emphasis>
1603 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TYPE,&#32;type,&#32;<emphasis role="keywordtype">int</emphasis>);
1604 <emphasis role="preprocessor">#endif</emphasis>
1605 <emphasis role="preprocessor">#ifdef&#32;ZMQ_UNSUBSCRIBE</emphasis>
1606 ZMQ_DEFINE_ARRAY_OPT(ZMQ_UNSUBSCRIBE,&#32;unsubscribe);
1607 <emphasis role="preprocessor">#endif</emphasis>
1608 <emphasis role="preprocessor">#ifdef&#32;ZMQ_VMCI_BUFFER_SIZE</emphasis>
1609 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_VMCI_BUFFER_SIZE,&#32;vmci_buffer_size,&#32;uint64_t);
1610 <emphasis role="preprocessor">#endif</emphasis>
1611 <emphasis role="preprocessor">#ifdef&#32;ZMQ_VMCI_BUFFER_MIN_SIZE</emphasis>
1612 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_VMCI_BUFFER_MIN_SIZE,&#32;vmci_buffer_min_size,&#32;uint64_t);
1613 <emphasis role="preprocessor">#endif</emphasis>
1614 <emphasis role="preprocessor">#ifdef&#32;ZMQ_VMCI_BUFFER_MAX_SIZE</emphasis>
1615 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_VMCI_BUFFER_MAX_SIZE,&#32;vmci_buffer_max_size,&#32;uint64_t);
1616 <emphasis role="preprocessor">#endif</emphasis>
1617 <emphasis role="preprocessor">#ifdef&#32;ZMQ_VMCI_CONNECT_TIMEOUT</emphasis>
1618 ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_VMCI_CONNECT_TIMEOUT,&#32;vmci_connect_timeout,&#32;<emphasis role="keywordtype">int</emphasis>);
1619 <emphasis role="preprocessor">#endif</emphasis>
1620 <emphasis role="preprocessor">#ifdef&#32;ZMQ_XPUB_VERBOSE</emphasis>
1621 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_XPUB_VERBOSE,&#32;xpub_verbose,&#32;<emphasis role="keywordtype">int</emphasis>);
1622 <emphasis role="preprocessor">#endif</emphasis>
1623 <emphasis role="preprocessor">#ifdef&#32;ZMQ_XPUB_VERBOSER</emphasis>
1624 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_XPUB_VERBOSER,&#32;xpub_verboser,&#32;<emphasis role="keywordtype">int</emphasis>);
1625 <emphasis role="preprocessor">#endif</emphasis>
1626 <emphasis role="preprocessor">#ifdef&#32;ZMQ_XPUB_MANUAL</emphasis>
1627 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_XPUB_MANUAL,&#32;xpub_manual,&#32;<emphasis role="keywordtype">int</emphasis>);
1628 <emphasis role="preprocessor">#endif</emphasis>
1629 <emphasis role="preprocessor">#ifdef&#32;ZMQ_XPUB_NODROP</emphasis>
1630 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_XPUB_NODROP,&#32;xpub_nodrop,&#32;<emphasis role="keywordtype">int</emphasis>);
1631 <emphasis role="preprocessor">#endif</emphasis>
1632 <emphasis role="preprocessor">#ifdef&#32;ZMQ_XPUB_WELCOME_MSG</emphasis>
1633 ZMQ_DEFINE_ARRAY_OPT(ZMQ_XPUB_WELCOME_MSG,&#32;xpub_welcome_msg);
1634 <emphasis role="preprocessor">#endif</emphasis>
1635 <emphasis role="preprocessor">#ifdef&#32;ZMQ_ZAP_ENFORCE_DOMAIN</emphasis>
1636 ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_ZAP_ENFORCE_DOMAIN,&#32;zap_enforce_domain,&#32;<emphasis role="keywordtype">int</emphasis>);
1637 <emphasis role="preprocessor">#endif</emphasis>
1638 <emphasis role="preprocessor">#ifdef&#32;ZMQ_ZAP_DOMAIN</emphasis>
1639 ZMQ_DEFINE_ARRAY_OPT(ZMQ_ZAP_DOMAIN,&#32;zap_domain);
1640 <emphasis role="preprocessor">#endif</emphasis>
1641 
1642 }&#32;<emphasis role="comment">//&#32;namespace&#32;sockopt</emphasis>
1643 <emphasis role="preprocessor">#endif&#32;</emphasis><emphasis role="comment">//&#32;ZMQ_CPP11</emphasis>
1644 
1645 
1646 <emphasis role="keyword">namespace&#32;</emphasis>detail
1647 {
1648 <emphasis role="keyword">class&#32;</emphasis>socket_base
1649 {
1650 &#32;&#32;<emphasis role="keyword">public</emphasis>:
1651 &#32;&#32;&#32;&#32;socket_base()&#32;ZMQ_NOTHROW&#32;:&#32;_handle(ZMQ_NULLPTR)&#32;{}
1652 &#32;&#32;&#32;&#32;ZMQ_EXPLICIT&#32;socket_base(<emphasis role="keywordtype">void</emphasis>&#32;*handle)&#32;ZMQ_NOTHROW&#32;:&#32;_handle(handle)&#32;{}
1653 
1654 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">typename</emphasis>&#32;T&gt;
1655 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.7.0,&#32;use&#32;`set`&#32;taking&#32;option&#32;from&#32;zmq::sockopt&quot;</emphasis>)
1656 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;setsockopt(<emphasis role="keywordtype">int</emphasis>&#32;option_,&#32;T&#32;<emphasis role="keyword">const</emphasis>&#32;&amp;optval)
1657 &#32;&#32;&#32;&#32;{
1658 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;setsockopt(option_,&#32;&amp;optval,&#32;<emphasis role="keyword">sizeof</emphasis>(T));
1659 &#32;&#32;&#32;&#32;}
1660 
1661 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.7.0,&#32;use&#32;`set`&#32;taking&#32;option&#32;from&#32;zmq::sockopt&quot;</emphasis>)
1662 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;setsockopt(<emphasis role="keywordtype">int</emphasis>&#32;option_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*optval_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;optvallen_)
1663 &#32;&#32;&#32;&#32;{
1664 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_setsockopt(_handle,&#32;option_,&#32;optval_,&#32;optvallen_);
1665 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
1666 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1667 &#32;&#32;&#32;&#32;}
1668 
1669 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.7.0,&#32;use&#32;`get`&#32;taking&#32;option&#32;from&#32;zmq::sockopt&quot;</emphasis>)
1670 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;getsockopt(<emphasis role="keywordtype">int</emphasis>&#32;option_,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*optval_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;*optvallen_)<emphasis role="keyword">&#32;const</emphasis>
1671 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
1672 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_getsockopt(_handle,&#32;option_,&#32;optval_,&#32;optvallen_);
1673 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
1674 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1675 &#32;&#32;&#32;&#32;}
1676 
1677 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">typename</emphasis>&#32;T&gt;
1678 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.7.0,&#32;use&#32;`get`&#32;taking&#32;option&#32;from&#32;zmq::sockopt&quot;</emphasis>)
1679 &#32;&#32;&#32;&#32;T&#32;getsockopt(<emphasis role="keywordtype">int</emphasis>&#32;option_)<emphasis role="keyword">&#32;const</emphasis>
1680 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
1681 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;T&#32;optval;
1682 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;optlen&#32;=&#32;<emphasis role="keyword">sizeof</emphasis>(T);
1683 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;getsockopt(option_,&#32;&amp;optval,&#32;&amp;optlen);
1684 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;optval;
1685 &#32;&#32;&#32;&#32;}
1686 
1687 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
1688 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Set&#32;integral&#32;socket&#32;option,&#32;e.g.</emphasis>
1689 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;`socket.set(zmq::sockopt::linger,&#32;0)`</emphasis>
1690 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">bool</emphasis>&#32;BoolUnit&gt;
1691 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;set(sockopt::integral_option&lt;Opt,&#32;T,&#32;BoolUnit&gt;,&#32;<emphasis role="keyword">const</emphasis>&#32;T&#32;&amp;val)
1692 &#32;&#32;&#32;&#32;{
1693 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(std::is_integral&lt;T&gt;::value,&#32;<emphasis role="stringliteral">&quot;T&#32;must&#32;be&#32;integral&quot;</emphasis>);
1694 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;set_option(Opt,&#32;&amp;val,&#32;<emphasis role="keyword">sizeof</emphasis>&#32;val);
1695 &#32;&#32;&#32;&#32;}
1696 
1697 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Set&#32;integral&#32;socket&#32;option&#32;from&#32;boolean,&#32;e.g.</emphasis>
1698 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;`socket.set(zmq::sockopt::immediate,&#32;false)`</emphasis>
1699 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keyword">class</emphasis>&#32;T&gt;
1700 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;set(sockopt::integral_option&lt;Opt,&#32;T,&#32;true&gt;,&#32;<emphasis role="keywordtype">bool</emphasis>&#32;val)
1701 &#32;&#32;&#32;&#32;{
1702 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(std::is_integral&lt;T&gt;::value,&#32;<emphasis role="stringliteral">&quot;T&#32;must&#32;be&#32;integral&quot;</emphasis>);
1703 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;T&#32;rep_val&#32;=&#32;val;
1704 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;set_option(Opt,&#32;&amp;rep_val,&#32;<emphasis role="keyword">sizeof</emphasis>&#32;rep_val);
1705 &#32;&#32;&#32;&#32;}
1706 
1707 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Set&#32;array&#32;socket&#32;option,&#32;e.g.</emphasis>
1708 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;`socket.set(zmq::sockopt::plain_username,&#32;&quot;foo123&quot;)`</emphasis>
1709 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keywordtype">int</emphasis>&#32;NullTerm&gt;
1710 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;set(sockopt::array_option&lt;Opt,&#32;NullTerm&gt;,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*buf)
1711 &#32;&#32;&#32;&#32;{
1712 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;set_option(Opt,&#32;buf,&#32;std::strlen(buf));
1713 &#32;&#32;&#32;&#32;}
1714 
1715 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Set&#32;array&#32;socket&#32;option,&#32;e.g.</emphasis>
1716 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;`socket.set(zmq::sockopt::routing_id,&#32;zmq::buffer(id))`</emphasis>
1717 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keywordtype">int</emphasis>&#32;NullTerm&gt;
1718 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;set(sockopt::array_option&lt;Opt,&#32;NullTerm&gt;,&#32;const_buffer&#32;buf)
1719 &#32;&#32;&#32;&#32;{
1720 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;set_option(Opt,&#32;buf.data(),&#32;buf.size());
1721 &#32;&#32;&#32;&#32;}
1722 
1723 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Set&#32;array&#32;socket&#32;option,&#32;e.g.</emphasis>
1724 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;`socket.set(zmq::sockopt::routing_id,&#32;id_str)`</emphasis>
1725 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keywordtype">int</emphasis>&#32;NullTerm&gt;
1726 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;set(sockopt::array_option&lt;Opt,&#32;NullTerm&gt;,&#32;<emphasis role="keyword">const</emphasis>&#32;std::string&#32;&amp;buf)
1727 &#32;&#32;&#32;&#32;{
1728 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;set_option(Opt,&#32;buf.data(),&#32;buf.size());
1729 &#32;&#32;&#32;&#32;}
1730 
1731 <emphasis role="preprocessor">#if&#32;CPPZMQ_HAS_STRING_VIEW</emphasis>
1732 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Set&#32;array&#32;socket&#32;option,&#32;e.g.</emphasis>
1733 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;`socket.set(zmq::sockopt::routing_id,&#32;id_str)`</emphasis>
1734 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keywordtype">int</emphasis>&#32;NullTerm&gt;
1735 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;set(sockopt::array_option&lt;Opt,&#32;NullTerm&gt;,&#32;std::string_view&#32;buf)
1736 &#32;&#32;&#32;&#32;{
1737 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;set_option(Opt,&#32;buf.data(),&#32;buf.size());
1738 &#32;&#32;&#32;&#32;}
1739 <emphasis role="preprocessor">#endif</emphasis>
1740 
1741 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Get&#32;scalar&#32;socket&#32;option,&#32;e.g.</emphasis>
1742 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;`auto&#32;opt&#32;=&#32;socket.get(zmq::sockopt::linger)`</emphasis>
1743 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keyword">class</emphasis>&#32;T,&#32;<emphasis role="keywordtype">bool</emphasis>&#32;BoolUnit&gt;
1744 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;T&#32;get(sockopt::integral_option&lt;Opt,&#32;T,&#32;BoolUnit&gt;)<emphasis role="keyword">&#32;const</emphasis>
1745 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
1746 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_assert</emphasis>(std::is_integral&lt;T&gt;::value,&#32;<emphasis role="stringliteral">&quot;T&#32;must&#32;be&#32;integral&quot;</emphasis>);
1747 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;T&#32;val;
1748 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size&#32;=&#32;<emphasis role="keyword">sizeof</emphasis>&#32;val;
1749 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;get_option(Opt,&#32;&amp;val,&#32;&amp;size);
1750 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(size&#32;==&#32;<emphasis role="keyword">sizeof</emphasis>&#32;val);
1751 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;val;
1752 &#32;&#32;&#32;&#32;}
1753 
1754 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Get&#32;array&#32;socket&#32;option,&#32;writes&#32;to&#32;buf,&#32;returns&#32;option&#32;size&#32;in&#32;bytes,&#32;e.g.</emphasis>
1755 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;`size_t&#32;optsize&#32;=&#32;socket.get(zmq::sockopt::routing_id,&#32;zmq::buffer(id))`</emphasis>
1756 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keywordtype">int</emphasis>&#32;NullTerm&gt;
1757 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;get(sockopt::array_option&lt;Opt,&#32;NullTerm&gt;,
1758 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;mutable_buffer&#32;buf)<emphasis role="keyword">&#32;const</emphasis>
1759 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
1760 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size&#32;=&#32;buf.size();
1761 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;get_option(Opt,&#32;buf.data(),&#32;&amp;size);
1762 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;size;
1763 &#32;&#32;&#32;&#32;}
1764 
1765 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Get&#32;array&#32;socket&#32;option&#32;as&#32;string&#32;(initializes&#32;the&#32;string&#32;buffer&#32;size&#32;to&#32;init_size)&#32;e.g.</emphasis>
1766 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;`auto&#32;s&#32;=&#32;socket.get(zmq::sockopt::routing_id)`</emphasis>
1767 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Note:&#32;removes&#32;the&#32;null&#32;character&#32;from&#32;null-terminated&#32;string&#32;options,</emphasis>
1768 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;i.e.&#32;the&#32;string&#32;size&#32;excludes&#32;the&#32;null&#32;character.</emphasis>
1769 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keywordtype">int</emphasis>&#32;Opt,&#32;<emphasis role="keywordtype">int</emphasis>&#32;NullTerm&gt;
1770 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;std::string&#32;get(sockopt::array_option&lt;Opt,&#32;NullTerm&gt;,
1771 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;init_size&#32;=&#32;1024)<emphasis role="keyword">&#32;const</emphasis>
1772 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
1773 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(NullTerm&#32;==&#32;2&#32;&amp;&amp;&#32;init_size&#32;==&#32;1024)&#32;{
1774 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;init_size&#32;=&#32;41;&#32;<emphasis role="comment">//&#32;get&#32;as&#32;Z85&#32;string</emphasis>
1775 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
1776 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::string&#32;str(init_size,&#32;<emphasis role="charliteral">&apos;\0&apos;</emphasis>);
1777 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;size&#32;=&#32;get(sockopt::array_option&lt;Opt&gt;{},&#32;<link linkend="_structbuffer">buffer</link>(str));
1778 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(NullTerm&#32;==&#32;1)&#32;{
1779 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(size&#32;&gt;&#32;0)&#32;{
1780 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(str[size&#32;-&#32;1]&#32;==&#32;<emphasis role="charliteral">&apos;\0&apos;</emphasis>);
1781 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;--size;
1782 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
1783 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}&#32;<emphasis role="keywordflow">else</emphasis>&#32;<emphasis role="keywordflow">if</emphasis>&#32;(NullTerm&#32;==&#32;2)&#32;{
1784 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(size&#32;==&#32;32&#32;||&#32;size&#32;==&#32;41);
1785 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(size&#32;==&#32;41)&#32;{
1786 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(str[size&#32;-&#32;1]&#32;==&#32;<emphasis role="charliteral">&apos;\0&apos;</emphasis>);
1787 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;--size;
1788 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
1789 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
1790 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;str.resize(size);
1791 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;str;
1792 &#32;&#32;&#32;&#32;}
1793 <emphasis role="preprocessor">#endif</emphasis>
1794 
1795 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;bind(std::string&#32;<emphasis role="keyword">const</emphasis>&#32;&amp;addr)&#32;{&#32;bind(addr.c_str());&#32;}
1796 
1797 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;bind(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
1798 &#32;&#32;&#32;&#32;{
1799 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_bind(_handle,&#32;addr_);
1800 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
1801 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1802 &#32;&#32;&#32;&#32;}
1803 
1804 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;unbind(std::string&#32;<emphasis role="keyword">const</emphasis>&#32;&amp;addr)&#32;{&#32;unbind(addr.c_str());&#32;}
1805 
1806 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;unbind(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
1807 &#32;&#32;&#32;&#32;{
1808 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_unbind(_handle,&#32;addr_);
1809 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
1810 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1811 &#32;&#32;&#32;&#32;}
1812 
1813 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;connect(std::string&#32;<emphasis role="keyword">const</emphasis>&#32;&amp;addr)&#32;{&#32;connect(addr.c_str());&#32;}
1814 
1815 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;connect(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
1816 &#32;&#32;&#32;&#32;{
1817 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_connect(_handle,&#32;addr_);
1818 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
1819 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1820 &#32;&#32;&#32;&#32;}
1821 
1822 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;disconnect(std::string&#32;<emphasis role="keyword">const</emphasis>&#32;&amp;addr)&#32;{&#32;disconnect(addr.c_str());&#32;}
1823 
1824 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;disconnect(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
1825 &#32;&#32;&#32;&#32;{
1826 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_disconnect(_handle,&#32;addr_);
1827 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
1828 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1829 &#32;&#32;&#32;&#32;}
1830 
1831 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;connected()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;(_handle&#32;!=&#32;ZMQ_NULLPTR);&#32;}
1832 
1833 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;send&#32;taking&#32;a&#32;const_buffer&#32;and&#32;send_flags&quot;</emphasis>)
1834 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;send(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*buf_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;len_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;flags_&#32;=&#32;0)
1835 &#32;&#32;&#32;&#32;{
1836 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;nbytes&#32;=&#32;zmq_send(_handle,&#32;buf_,&#32;len_,&#32;flags_);
1837 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(nbytes&#32;&gt;=&#32;0)
1838 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">size_t</emphasis><emphasis role="keyword">&gt;</emphasis>(nbytes);
1839 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;EAGAIN)
1840 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;0;
1841 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1842 &#32;&#32;&#32;&#32;}
1843 
1844 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;send&#32;taking&#32;message_t&#32;and&#32;send_flags&quot;</emphasis>)
1845 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;send(<link linkend="_classzmq_1_1message__t">message_t</link>&#32;&amp;msg_,
1846 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;flags_&#32;=&#32;0)&#32;<emphasis role="comment">//&#32;default&#32;until&#32;removed</emphasis>
1847 &#32;&#32;&#32;&#32;{
1848 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;nbytes&#32;=&#32;zmq_msg_send(msg_.handle(),&#32;_handle,&#32;flags_);
1849 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(nbytes&#32;&gt;=&#32;0)
1850 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">true</emphasis>;
1851 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;EAGAIN)
1852 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">false</emphasis>;
1853 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1854 &#32;&#32;&#32;&#32;}
1855 
1856 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">typename</emphasis>&#32;T&gt;
1857 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(
1858 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="stringliteral">&quot;from&#32;4.4.1,&#32;use&#32;send&#32;taking&#32;message_t&#32;or&#32;buffer&#32;(for&#32;contiguous&#32;&quot;</emphasis>
1859 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="stringliteral">&quot;ranges),&#32;and&#32;send_flags&quot;</emphasis>)
1860 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;send(T&#32;first,&#32;T&#32;last,&#32;<emphasis role="keywordtype">int</emphasis>&#32;flags_&#32;=&#32;0)
1861 &#32;&#32;&#32;&#32;{
1862 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<link linkend="_classzmq_1_1message__t">zmq::message_t</link>&#32;msg(first,&#32;last);
1863 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;nbytes&#32;=&#32;zmq_msg_send(msg.handle(),&#32;_handle,&#32;flags_);
1864 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(nbytes&#32;&gt;=&#32;0)
1865 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">true</emphasis>;
1866 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;EAGAIN)
1867 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">false</emphasis>;
1868 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1869 &#32;&#32;&#32;&#32;}
1870 
1871 <emphasis role="preprocessor">#ifdef&#32;ZMQ_HAS_RVALUE_REFS</emphasis>
1872 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;send&#32;taking&#32;message_t&#32;and&#32;send_flags&quot;</emphasis>)
1873 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;send(<link linkend="_classzmq_1_1message__t">message_t</link>&#32;&amp;&amp;msg_,
1874 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;flags_&#32;=&#32;0)&#32;<emphasis role="comment">//&#32;default&#32;until&#32;removed</emphasis>
1875 &#32;&#32;&#32;&#32;{
1876 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
1877 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;send(msg_,&#32;<emphasis role="keyword">static_cast&lt;</emphasis>send_flags<emphasis role="keyword">&gt;</emphasis>(flags_)).has_value();
1878 <emphasis role="preprocessor">#else</emphasis>
1879 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;send(msg_,&#32;flags_);
1880 <emphasis role="preprocessor">#endif</emphasis>
1881 &#32;&#32;&#32;&#32;}
1882 <emphasis role="preprocessor">#endif</emphasis>
1883 
1884 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
1885 &#32;&#32;&#32;&#32;send_result_t&#32;send(const_buffer&#32;buf,&#32;send_flags&#32;flags&#32;=&#32;send_flags::none)
1886 &#32;&#32;&#32;&#32;{
1887 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;nbytes&#32;=
1888 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_send(_handle,&#32;buf.data(),&#32;buf.size(),&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">int</emphasis><emphasis role="keyword">&gt;</emphasis>(flags));
1889 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(nbytes&#32;&gt;=&#32;0)
1890 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">size_t</emphasis><emphasis role="keyword">&gt;</emphasis>(nbytes);
1891 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;EAGAIN)
1892 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;{};
1893 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1894 &#32;&#32;&#32;&#32;}
1895 
1896 &#32;&#32;&#32;&#32;send_result_t&#32;send(<link linkend="_classzmq_1_1message__t">message_t</link>&#32;&amp;msg,&#32;send_flags&#32;flags)
1897 &#32;&#32;&#32;&#32;{
1898 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;nbytes&#32;=&#32;zmq_msg_send(msg.handle(),&#32;_handle,&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">int</emphasis><emphasis role="keyword">&gt;</emphasis>(flags));
1899 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(nbytes&#32;&gt;=&#32;0)
1900 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">size_t</emphasis><emphasis role="keyword">&gt;</emphasis>(nbytes);
1901 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;EAGAIN)
1902 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;{};
1903 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1904 &#32;&#32;&#32;&#32;}
1905 
1906 &#32;&#32;&#32;&#32;send_result_t&#32;send(<link linkend="_classzmq_1_1message__t">message_t</link>&#32;&amp;&amp;msg,&#32;send_flags&#32;flags)
1907 &#32;&#32;&#32;&#32;{
1908 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;send(msg,&#32;flags);
1909 &#32;&#32;&#32;&#32;}
1910 <emphasis role="preprocessor">#endif</emphasis>
1911 
1912 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(
1913 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;recv&#32;taking&#32;a&#32;mutable_buffer&#32;and&#32;recv_flags&quot;</emphasis>)
1914 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;recv(<emphasis role="keywordtype">void</emphasis>&#32;*buf_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;len_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;flags_&#32;=&#32;0)
1915 &#32;&#32;&#32;&#32;{
1916 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;nbytes&#32;=&#32;zmq_recv(_handle,&#32;buf_,&#32;len_,&#32;flags_);
1917 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(nbytes&#32;&gt;=&#32;0)
1918 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">size_t</emphasis><emphasis role="keyword">&gt;</emphasis>(nbytes);
1919 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;EAGAIN)
1920 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;0;
1921 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1922 &#32;&#32;&#32;&#32;}
1923 
1924 &#32;&#32;&#32;&#32;ZMQ_CPP11_DEPRECATED(
1925 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;recv&#32;taking&#32;a&#32;reference&#32;to&#32;message_t&#32;and&#32;recv_flags&quot;</emphasis>)
1926 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;recv(<link linkend="_classzmq_1_1message__t">message_t</link>&#32;*msg_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;flags_&#32;=&#32;0)
1927 &#32;&#32;&#32;&#32;{
1928 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;nbytes&#32;=&#32;zmq_msg_recv(msg_-&gt;handle(),&#32;_handle,&#32;flags_);
1929 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(nbytes&#32;&gt;=&#32;0)
1930 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">true</emphasis>;
1931 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;EAGAIN)
1932 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">false</emphasis>;
1933 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1934 &#32;&#32;&#32;&#32;}
1935 
1936 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
1937 &#32;&#32;&#32;&#32;ZMQ_NODISCARD
1938 &#32;&#32;&#32;&#32;recv_buffer_result_t&#32;recv(mutable_buffer&#32;buf,
1939 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;recv_flags&#32;flags&#32;=&#32;recv_flags::none)
1940 &#32;&#32;&#32;&#32;{
1941 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;nbytes&#32;=
1942 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_recv(_handle,&#32;buf.data(),&#32;buf.size(),&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">int</emphasis><emphasis role="keyword">&gt;</emphasis>(flags));
1943 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(nbytes&#32;&gt;=&#32;0)&#32;{
1944 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;recv_buffer_size{
1945 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(std::min)(<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">size_t</emphasis><emphasis role="keyword">&gt;</emphasis>(nbytes),&#32;buf.size()),
1946 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">size_t</emphasis><emphasis role="keyword">&gt;</emphasis>(nbytes)};
1947 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
1948 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;EAGAIN)
1949 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;{};
1950 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1951 &#32;&#32;&#32;&#32;}
1952 
1953 &#32;&#32;&#32;&#32;ZMQ_NODISCARD
1954 &#32;&#32;&#32;&#32;recv_result_t&#32;recv(<link linkend="_classzmq_1_1message__t">message_t</link>&#32;&amp;msg,&#32;recv_flags&#32;flags&#32;=&#32;recv_flags::none)
1955 &#32;&#32;&#32;&#32;{
1956 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;nbytes&#32;=
1957 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_msg_recv(msg.handle(),&#32;_handle,&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">int</emphasis><emphasis role="keyword">&gt;</emphasis>(flags));
1958 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(nbytes&#32;&gt;=&#32;0)&#32;{
1959 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(msg.size()&#32;==&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">size_t</emphasis><emphasis role="keyword">&gt;</emphasis>(nbytes));
1960 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">size_t</emphasis><emphasis role="keyword">&gt;</emphasis>(nbytes);
1961 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
1962 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;EAGAIN)
1963 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;{};
1964 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1965 &#32;&#32;&#32;&#32;}
1966 <emphasis role="preprocessor">#endif</emphasis>
1967 
1968 <emphasis role="preprocessor">#if&#32;defined(ZMQ_BUILD_DRAFT_API)&#32;&amp;&amp;&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(4,&#32;2,&#32;0)</emphasis>
1969 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;join(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*group)
1970 &#32;&#32;&#32;&#32;{
1971 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_join(_handle,&#32;group);
1972 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
1973 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1974 &#32;&#32;&#32;&#32;}
1975 
1976 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;leave(<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*group)
1977 &#32;&#32;&#32;&#32;{
1978 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_leave(_handle,&#32;group);
1979 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
1980 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
1981 &#32;&#32;&#32;&#32;}
1982 <emphasis role="preprocessor">#endif</emphasis>
1983 
1984 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;<emphasis role="keywordtype">void</emphasis>&#32;*handle()&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_handle;&#32;}
1985 &#32;&#32;&#32;&#32;ZMQ_NODISCARD&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*handle()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_handle;&#32;}
1986 
1987 &#32;&#32;&#32;&#32;ZMQ_EXPLICIT&#32;<emphasis role="keyword">operator</emphasis>&#32;bool()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_handle&#32;!=&#32;ZMQ_NULLPTR;&#32;}
1988 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;note:&#32;non-const&#32;operator&#32;bool&#32;can&#32;be&#32;removed&#32;once</emphasis>
1989 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;operator&#32;void*&#32;is&#32;removed&#32;from&#32;socket_t</emphasis>
1990 &#32;&#32;&#32;&#32;ZMQ_EXPLICIT&#32;<emphasis role="keyword">operator</emphasis>&#32;bool()&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_handle&#32;!=&#32;ZMQ_NULLPTR;&#32;}
1991 
1992 &#32;&#32;<emphasis role="keyword">protected</emphasis>:
1993 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;*_handle;
1994 
1995 &#32;&#32;<emphasis role="keyword">private</emphasis>:
1996 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;set_option(<emphasis role="keywordtype">int</emphasis>&#32;option_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*optval_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;optvallen_)
1997 &#32;&#32;&#32;&#32;{
1998 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_setsockopt(_handle,&#32;option_,&#32;optval_,&#32;optvallen_);
1999 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
2000 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
2001 &#32;&#32;&#32;&#32;}
2002 
2003 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;get_option(<emphasis role="keywordtype">int</emphasis>&#32;option_,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*optval_,&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;*optvallen_)<emphasis role="keyword">&#32;const</emphasis>
2004 <emphasis role="keyword">&#32;&#32;&#32;&#32;</emphasis>{
2005 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_getsockopt(_handle,&#32;option_,&#32;optval_,&#32;optvallen_);
2006 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
2007 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
2008 &#32;&#32;&#32;&#32;}
2009 };
2010 }&#32;<emphasis role="comment">//&#32;namespace&#32;detail</emphasis>
2011 
2012 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
2013 <emphasis role="keyword">enum&#32;class</emphasis>&#32;socket_type&#32;:&#32;<emphasis role="keywordtype">int</emphasis>
2014 {
2015 &#32;&#32;&#32;&#32;req&#32;=&#32;ZMQ_REQ,
2016 &#32;&#32;&#32;&#32;rep&#32;=&#32;ZMQ_REP,
2017 &#32;&#32;&#32;&#32;dealer&#32;=&#32;ZMQ_DEALER,
2018 &#32;&#32;&#32;&#32;router&#32;=&#32;ZMQ_ROUTER,
2019 &#32;&#32;&#32;&#32;pub&#32;=&#32;ZMQ_PUB,
2020 &#32;&#32;&#32;&#32;sub&#32;=&#32;ZMQ_SUB,
2021 &#32;&#32;&#32;&#32;xpub&#32;=&#32;ZMQ_XPUB,
2022 &#32;&#32;&#32;&#32;xsub&#32;=&#32;ZMQ_XSUB,
2023 &#32;&#32;&#32;&#32;push&#32;=&#32;ZMQ_PUSH,
2024 &#32;&#32;&#32;&#32;pull&#32;=&#32;ZMQ_PULL,
2025 <emphasis role="preprocessor">#if&#32;defined(ZMQ_BUILD_DRAFT_API)&#32;&amp;&amp;&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(4,&#32;2,&#32;0)</emphasis>
2026 &#32;&#32;&#32;&#32;server&#32;=&#32;ZMQ_SERVER,
2027 &#32;&#32;&#32;&#32;client&#32;=&#32;ZMQ_CLIENT,
2028 &#32;&#32;&#32;&#32;radio&#32;=&#32;ZMQ_RADIO,
2029 &#32;&#32;&#32;&#32;dish&#32;=&#32;ZMQ_DISH,
2030 <emphasis role="preprocessor">#endif</emphasis>
2031 <emphasis role="preprocessor">#if&#32;ZMQ_VERSION_MAJOR&#32;&gt;=&#32;4</emphasis>
2032 &#32;&#32;&#32;&#32;stream&#32;=&#32;ZMQ_STREAM,
2033 <emphasis role="preprocessor">#endif</emphasis>
2034 &#32;&#32;&#32;&#32;pair&#32;=&#32;ZMQ_PAIR
2035 };
2036 <emphasis role="preprocessor">#endif</emphasis>
2037 
2038 <emphasis role="keyword">struct&#32;</emphasis>from_handle_t
2039 {
2040 &#32;&#32;&#32;&#32;<emphasis role="keyword">struct&#32;</emphasis><link linkend="_structzmq_1_1from__handle__t_1_1__private">_private</link>
2041 &#32;&#32;&#32;&#32;{
2042 &#32;&#32;&#32;&#32;};&#32;<emphasis role="comment">//&#32;disabling&#32;use&#32;other&#32;than&#32;with&#32;from_handle</emphasis>
2043 &#32;&#32;&#32;&#32;ZMQ_CONSTEXPR_FN&#32;ZMQ_EXPLICIT&#32;from_handle_t(<link linkend="_structzmq_1_1from__handle__t_1_1__private">_private</link>&#32;<emphasis role="comment">/*p*/</emphasis>)&#32;ZMQ_NOTHROW&#32;{}
2044 };
2045 
2046 ZMQ_CONSTEXPR_VAR&#32;from_handle_t&#32;from_handle&#32;=
2047 &#32;&#32;from_handle_t(from_handle_t::_private());
2048 
2049 <emphasis role="comment">//&#32;A&#32;non-owning&#32;nullable&#32;reference&#32;to&#32;a&#32;socket.</emphasis>
2050 <emphasis role="comment">//&#32;The&#32;reference&#32;is&#32;invalidated&#32;on&#32;socket&#32;close&#32;or&#32;destruction.</emphasis>
2051 <emphasis role="keyword">class&#32;</emphasis>socket_ref&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;<link linkend="_classzmq_1_1detail_1_1socket__base">detail::socket_base</link>
2052 {
2053 &#32;&#32;<emphasis role="keyword">public</emphasis>:
2054 &#32;&#32;&#32;&#32;socket_ref()&#32;ZMQ_NOTHROW&#32;:&#32;<link linkend="_classzmq_1_1detail_1_1socket__base">detail::socket_base</link>()&#32;{}
2055 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
2056 &#32;&#32;&#32;&#32;socket_ref(std::nullptr_t)&#32;ZMQ_NOTHROW&#32;:&#32;<link linkend="_classzmq_1_1detail_1_1socket__base">detail::socket_base</link>()&#32;{}
2057 <emphasis role="preprocessor">#endif</emphasis>
2058 &#32;&#32;&#32;&#32;socket_ref(<link linkend="_structzmq_1_1from__handle__t">from_handle_t</link>&#32;<emphasis role="comment">/*fh*/</emphasis>,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*handle)&#32;ZMQ_NOTHROW
2059 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;:&#32;<link linkend="_classzmq_1_1detail_1_1socket__base">detail::socket_base</link>(handle)
2060 &#32;&#32;&#32;&#32;{
2061 &#32;&#32;&#32;&#32;}
2062 };
2063 
2064 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
2065 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;sr,&#32;std::nullptr_t&#32;<emphasis role="comment">/*p*/</emphasis>)&#32;ZMQ_NOTHROW
2066 {
2067 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;sr.handle()&#32;==&#32;<emphasis role="keyword">nullptr</emphasis>;
2068 }
2069 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(std::nullptr_t&#32;<emphasis role="comment">/*p*/</emphasis>,&#32;socket_ref&#32;sr)&#32;ZMQ_NOTHROW
2070 {
2071 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;sr.handle()&#32;==&#32;<emphasis role="keyword">nullptr</emphasis>;
2072 }
2073 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;sr,&#32;std::nullptr_t&#32;<emphasis role="comment">/*p*/</emphasis>)&#32;ZMQ_NOTHROW
2074 {
2075 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;!(sr&#32;==&#32;<emphasis role="keyword">nullptr</emphasis>);
2076 }
2077 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(std::nullptr_t&#32;<emphasis role="comment">/*p*/</emphasis>,&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;sr)&#32;ZMQ_NOTHROW
2078 {
2079 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;!(sr&#32;==&#32;<emphasis role="keyword">nullptr</emphasis>);
2080 }
2081 <emphasis role="preprocessor">#endif</emphasis>
2082 
2083 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;a,&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;b)&#32;ZMQ_NOTHROW
2084 {
2085 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;std::equal_to&lt;void&#32;*&gt;()(a.handle(),&#32;b.handle());
2086 }
2087 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;a,&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;b)&#32;ZMQ_NOTHROW
2088 {
2089 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;!(a&#32;==&#32;b);
2090 }
2091 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator&lt;(<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;a,&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;b)&#32;ZMQ_NOTHROW
2092 {
2093 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;std::less&lt;void&#32;*&gt;()(a.handle(),&#32;b.handle());
2094 }
2095 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator&gt;(<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;a,&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;b)&#32;ZMQ_NOTHROW
2096 {
2097 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;b&#32;&lt;&#32;a;
2098 }
2099 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator&lt;=(<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;a,&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;b)&#32;ZMQ_NOTHROW
2100 {
2101 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;!(a&#32;&gt;&#32;b);
2102 }
2103 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator&gt;=(<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;a,&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;b)&#32;ZMQ_NOTHROW
2104 {
2105 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;!(a&#32;&lt;&#32;b);
2106 }
2107 
2108 }&#32;<emphasis role="comment">//&#32;namespace&#32;zmq</emphasis>
2109 
2110 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
2111 <emphasis role="keyword">namespace&#32;</emphasis>std
2112 {
2113 <emphasis role="keyword">template</emphasis>&lt;&gt;&#32;<emphasis role="keyword">struct&#32;</emphasis>hash&lt;zmq::socket_ref&gt;
2114 {
2115 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;operator()(zmq::socket_ref&#32;sr)&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW
2116 &#32;&#32;&#32;&#32;{
2117 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;hash&lt;void&#32;*&gt;()(sr.handle());
2118 &#32;&#32;&#32;&#32;}
2119 };
2120 }&#32;<emphasis role="comment">//&#32;namespace&#32;std</emphasis>
2121 <emphasis role="preprocessor">#endif</emphasis>
2122 
2123 <emphasis role="keyword">namespace&#32;</emphasis>zmq
2124 {
2125 <emphasis role="keyword">class&#32;</emphasis>socket_t&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;<link linkend="_classzmq_1_1detail_1_1socket__base">detail::socket_base</link>
2126 {
2127 &#32;&#32;&#32;&#32;<emphasis role="keyword">friend</emphasis>&#32;<emphasis role="keyword">class&#32;</emphasis>monitor_t;
2128 
2129 &#32;&#32;<emphasis role="keyword">public</emphasis>:
2130 &#32;&#32;&#32;&#32;socket_t()&#32;ZMQ_NOTHROW&#32;:&#32;<link linkend="_classzmq_1_1detail_1_1socket__base">detail::socket_base</link>(ZMQ_NULLPTR),&#32;ctxptr(ZMQ_NULLPTR)&#32;{}
2131 
2132 &#32;&#32;&#32;&#32;socket_t(<link linkend="_classzmq_1_1context__t">context_t</link>&#32;&amp;context_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;type_)&#32;:
2133 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<link linkend="_classzmq_1_1detail_1_1socket__base">detail::socket_base</link>(zmq_socket(context_.handle(),&#32;type_)),
2134 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ctxptr(context_.handle())
2135 &#32;&#32;&#32;&#32;{
2136 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(_handle&#32;==&#32;ZMQ_NULLPTR)
2137 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
2138 &#32;&#32;&#32;&#32;}
2139 
2140 <emphasis role="preprocessor">#ifdef&#32;ZMQ_CPP11</emphasis>
2141 &#32;&#32;&#32;&#32;socket_t(<link linkend="_classzmq_1_1context__t">context_t</link>&#32;&amp;context_,&#32;socket_type&#32;type_)&#32;:
2142 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;socket_t(context_,&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">int</emphasis><emphasis role="keyword">&gt;</emphasis>(type_))
2143 &#32;&#32;&#32;&#32;{
2144 &#32;&#32;&#32;&#32;}
2145 <emphasis role="preprocessor">#endif</emphasis>
2146 
2147 <emphasis role="preprocessor">#ifdef&#32;ZMQ_HAS_RVALUE_REFS</emphasis>
2148 &#32;&#32;&#32;&#32;socket_t(socket_t&#32;&amp;&amp;rhs)&#32;ZMQ_NOTHROW&#32;:&#32;<link linkend="_classzmq_1_1detail_1_1socket__base">detail::socket_base</link>(rhs._handle),
2149 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ctxptr(rhs.ctxptr)
2150 &#32;&#32;&#32;&#32;{
2151 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;rhs._handle&#32;=&#32;ZMQ_NULLPTR;
2152 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;rhs.ctxptr&#32;=&#32;ZMQ_NULLPTR;
2153 &#32;&#32;&#32;&#32;}
2154 &#32;&#32;&#32;&#32;socket_t&#32;&amp;operator=(socket_t&#32;&amp;&amp;rhs)&#32;ZMQ_NOTHROW
2155 &#32;&#32;&#32;&#32;{
2156 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;close();
2157 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(_handle,&#32;rhs._handle);
2158 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(ctxptr,&#32;rhs.ctxptr);
2159 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>;
2160 &#32;&#32;&#32;&#32;}
2161 <emphasis role="preprocessor">#endif</emphasis>
2162 
2163 &#32;&#32;&#32;&#32;~socket_t()&#32;ZMQ_NOTHROW&#32;{&#32;close();&#32;}
2164 
2165 &#32;&#32;&#32;&#32;<emphasis role="keyword">operator</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*()&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_handle;&#32;}
2166 
2167 &#32;&#32;&#32;&#32;<emphasis role="keyword">operator</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;<emphasis role="keyword">const</emphasis>&#32;*()&#32;<emphasis role="keyword">const</emphasis>&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;_handle;&#32;}
2168 
2169 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;close()&#32;ZMQ_NOTHROW
2170 &#32;&#32;&#32;&#32;{
2171 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(_handle&#32;==&#32;ZMQ_NULLPTR)
2172 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;already&#32;closed</emphasis>
2173 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>;
2174 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_close(_handle);
2175 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
2176 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_handle&#32;=&#32;ZMQ_NULLPTR;
2177 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ctxptr&#32;=&#32;ZMQ_NULLPTR;
2178 &#32;&#32;&#32;&#32;}
2179 
2180 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;swap(socket_t&#32;&amp;other)&#32;ZMQ_NOTHROW
2181 &#32;&#32;&#32;&#32;{
2182 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(_handle,&#32;other._handle);
2183 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(ctxptr,&#32;other.ctxptr);
2184 &#32;&#32;&#32;&#32;}
2185 
2186 &#32;&#32;&#32;&#32;<emphasis role="keyword">operator</emphasis>&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>()&#32;ZMQ_NOTHROW&#32;{&#32;<emphasis role="keywordflow">return</emphasis>&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>(from_handle,&#32;_handle);&#32;}
2187 
2188 &#32;&#32;<emphasis role="keyword">private</emphasis>:
2189 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;*ctxptr;
2190 
2191 &#32;&#32;&#32;&#32;socket_t(<emphasis role="keyword">const</emphasis>&#32;socket_t&#32;&amp;)&#32;ZMQ_DELETED_FUNCTION;
2192 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;socket_t&#32;&amp;)&#32;ZMQ_DELETED_FUNCTION;
2193 
2194 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;used&#32;by&#32;monitor_t</emphasis>
2195 &#32;&#32;&#32;&#32;socket_t(<emphasis role="keywordtype">void</emphasis>&#32;*context_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;type_)&#32;:
2196 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<link linkend="_classzmq_1_1detail_1_1socket__base">detail::socket_base</link>(zmq_socket(context_,&#32;type_)),
2197 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ctxptr(context_)
2198 &#32;&#32;&#32;&#32;{
2199 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(_handle&#32;==&#32;ZMQ_NULLPTR)
2200 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
2201 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(ctxptr&#32;==&#32;ZMQ_NULLPTR)
2202 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
2203 &#32;&#32;&#32;&#32;}
2204 };
2205 
2206 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;swap(<link linkend="_classzmq_1_1socket__t">socket_t</link>&#32;&amp;a,&#32;<link linkend="_classzmq_1_1socket__t">socket_t</link>&#32;&amp;b)&#32;ZMQ_NOTHROW
2207 {
2208 &#32;&#32;&#32;&#32;a.swap(b);
2209 }
2210 
2211 ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;proxy&#32;taking&#32;socket_t&#32;objects&quot;</emphasis>)
2212 inline&#32;<emphasis role="keywordtype">void</emphasis>&#32;proxy(<emphasis role="keywordtype">void</emphasis>&#32;*frontend,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*backend,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*capture)
2213 {
2214 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_proxy(frontend,&#32;backend,&#32;capture);
2215 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
2216 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;error_t();
2217 }
2218 
2219 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>
2220 proxy(<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;frontend,&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;backend,&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;capture&#32;=&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>())
2221 {
2222 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_proxy(frontend.handle(),&#32;backend.handle(),&#32;capture.handle());
2223 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
2224 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
2225 }
2226 
2227 <emphasis role="preprocessor">#ifdef&#32;ZMQ_HAS_PROXY_STEERABLE</emphasis>
2228 ZMQ_DEPRECATED(<emphasis role="stringliteral">&quot;from&#32;4.3.1,&#32;use&#32;proxy_steerable&#32;taking&#32;socket_t&#32;objects&quot;</emphasis>)
2229 inline&#32;<emphasis role="keywordtype">void</emphasis>
2230 proxy_steerable(<emphasis role="keywordtype">void</emphasis>&#32;*frontend,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*backend,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*capture,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*control)
2231 {
2232 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_proxy_steerable(frontend,&#32;backend,&#32;capture,&#32;control);
2233 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
2234 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
2235 }
2236 
2237 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;proxy_steerable(<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;frontend,
2238 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;backend,
2239 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;capture,
2240 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;control)
2241 {
2242 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_proxy_steerable(frontend.handle(),&#32;backend.handle(),
2243 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;capture.handle(),&#32;control.handle());
2244 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
2245 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
2246 }
2247 <emphasis role="preprocessor">#endif</emphasis>
2248 
2249 <emphasis role="keyword">class&#32;</emphasis>monitor_t
2250 {
2251 &#32;&#32;<emphasis role="keyword">public</emphasis>:
2252 &#32;&#32;&#32;&#32;monitor_t()&#32;:&#32;_socket(),&#32;_monitor_socket()&#32;{}
2253 
2254 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;~monitor_t()&#32;{&#32;close();&#32;}
2255 
2256 <emphasis role="preprocessor">#ifdef&#32;ZMQ_HAS_RVALUE_REFS</emphasis>
2257 &#32;&#32;&#32;&#32;monitor_t(monitor_t&#32;&amp;&amp;rhs)&#32;ZMQ_NOTHROW&#32;:&#32;_socket(),&#32;_monitor_socket()
2258 &#32;&#32;&#32;&#32;{
2259 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(_socket,&#32;rhs._socket);
2260 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(_monitor_socket,&#32;rhs._monitor_socket);
2261 &#32;&#32;&#32;&#32;}
2262 
2263 &#32;&#32;&#32;&#32;monitor_t&#32;&amp;operator=(monitor_t&#32;&amp;&amp;rhs)&#32;ZMQ_NOTHROW
2264 &#32;&#32;&#32;&#32;{
2265 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;close();
2266 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_socket&#32;=&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>();
2267 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(_socket,&#32;rhs._socket);
2268 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::swap(_monitor_socket,&#32;rhs._monitor_socket);
2269 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>;
2270 &#32;&#32;&#32;&#32;}
2271 <emphasis role="preprocessor">#endif</emphasis>
2272 
2273 
2274 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>
2275 &#32;&#32;&#32;&#32;monitor(<link linkend="_classzmq_1_1socket__t">socket_t</link>&#32;&amp;socket,&#32;std::string&#32;<emphasis role="keyword">const</emphasis>&#32;&amp;addr,&#32;<emphasis role="keywordtype">int</emphasis>&#32;events&#32;=&#32;ZMQ_EVENT_ALL)
2276 &#32;&#32;&#32;&#32;{
2277 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;monitor(socket,&#32;addr.c_str(),&#32;events);
2278 &#32;&#32;&#32;&#32;}
2279 
2280 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;monitor(<link linkend="_classzmq_1_1socket__t">socket_t</link>&#32;&amp;socket,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;events&#32;=&#32;ZMQ_EVENT_ALL)
2281 &#32;&#32;&#32;&#32;{
2282 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;init(socket,&#32;addr_,&#32;events);
2283 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">while</emphasis>&#32;(<emphasis role="keyword">true</emphasis>)&#32;{
2284 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;check_event(-1);
2285 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
2286 &#32;&#32;&#32;&#32;}
2287 
2288 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;init(<link linkend="_classzmq_1_1socket__t">socket_t</link>&#32;&amp;socket,&#32;std::string&#32;<emphasis role="keyword">const</emphasis>&#32;&amp;addr,&#32;<emphasis role="keywordtype">int</emphasis>&#32;events&#32;=&#32;ZMQ_EVENT_ALL)
2289 &#32;&#32;&#32;&#32;{
2290 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;init(socket,&#32;addr.c_str(),&#32;events);
2291 &#32;&#32;&#32;&#32;}
2292 
2293 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;init(<link linkend="_classzmq_1_1socket__t">socket_t</link>&#32;&amp;socket,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_,&#32;<emphasis role="keywordtype">int</emphasis>&#32;events&#32;=&#32;ZMQ_EVENT_ALL)
2294 &#32;&#32;&#32;&#32;{
2295 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_socket_monitor(socket.handle(),&#32;addr_,&#32;events);
2296 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;!=&#32;0)
2297 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;<link linkend="_classzmq_1_1error__t">error_t</link>();
2298 
2299 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_socket&#32;=&#32;socket;
2300 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_monitor_socket&#32;=&#32;<link linkend="_classzmq_1_1socket__t">socket_t</link>(socket.ctxptr,&#32;ZMQ_PAIR);
2301 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_monitor_socket.connect(addr_);
2302 
2303 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_monitor_started();
2304 &#32;&#32;&#32;&#32;}
2305 
2306 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;check_event(<emphasis role="keywordtype">int</emphasis>&#32;timeout&#32;=&#32;0)
2307 &#32;&#32;&#32;&#32;{
2308 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(_monitor_socket);
2309 
2310 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_msg_t&#32;eventMsg;
2311 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_msg_init(&amp;eventMsg);
2312 
2313 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq::pollitem_t&#32;items[]&#32;=&#32;{
2314 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;{_monitor_socket.handle(),&#32;0,&#32;ZMQ_POLLIN,&#32;0},
2315 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;};
2316 
2317 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq::poll(&amp;items[0],&#32;1,&#32;timeout);
2318 
2319 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(items[0].revents&#32;&amp;&#32;ZMQ_POLLIN)&#32;{
2320 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_recv(&amp;eventMsg,&#32;_monitor_socket.handle(),&#32;0);
2321 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;==&#32;-1&#32;&amp;&amp;&#32;zmq_errno()&#32;==&#32;ETERM)
2322 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">false</emphasis>;
2323 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(rc&#32;!=&#32;-1);
2324 
2325 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}&#32;<emphasis role="keywordflow">else</emphasis>&#32;{
2326 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_msg_close(&amp;eventMsg);
2327 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">false</emphasis>;
2328 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
2329 
2330 <emphasis role="preprocessor">#if&#32;ZMQ_VERSION_MAJOR&#32;&gt;=&#32;4</emphasis>
2331 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*data&#32;=&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keyword">const&#32;</emphasis><emphasis role="keywordtype">char</emphasis>&#32;*<emphasis role="keyword">&gt;</emphasis>(zmq_msg_data(&amp;eventMsg));
2332 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;msgEvent;
2333 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;memcpy(&amp;msgEvent.event,&#32;data,&#32;<emphasis role="keyword">sizeof</emphasis>(uint16_t));
2334 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;data&#32;+=&#32;<emphasis role="keyword">sizeof</emphasis>(uint16_t);
2335 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;memcpy(&amp;msgEvent.value,&#32;data,&#32;<emphasis role="keyword">sizeof</emphasis>(int32_t));
2336 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;*<emphasis role="keyword">event</emphasis>&#32;=&#32;&amp;msgEvent;
2337 <emphasis role="preprocessor">#else</emphasis>
2338 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;*<emphasis role="keyword">event</emphasis>&#32;=&#32;<emphasis role="keyword">static_cast&lt;</emphasis><link linkend="_structzmq__event__t">zmq_event_t</link>&#32;*<emphasis role="keyword">&gt;</emphasis>(zmq_msg_data(&amp;eventMsg));
2339 <emphasis role="preprocessor">#endif</emphasis>
2340 
2341 <emphasis role="preprocessor">#ifdef&#32;ZMQ_NEW_MONITOR_EVENT_LAYOUT</emphasis>
2342 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_msg_t&#32;addrMsg;
2343 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_msg_init(&amp;addrMsg);
2344 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_msg_recv(&amp;addrMsg,&#32;_monitor_socket.handle(),&#32;0);
2345 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;==&#32;-1&#32;&amp;&amp;&#32;zmq_errno()&#32;==&#32;ETERM)&#32;{
2346 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_msg_close(&amp;eventMsg);
2347 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">false</emphasis>;
2348 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
2349 
2350 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;assert(rc&#32;!=&#32;-1);
2351 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*str&#32;=&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keyword">const&#32;</emphasis><emphasis role="keywordtype">char</emphasis>&#32;*<emphasis role="keyword">&gt;</emphasis>(zmq_msg_data(&amp;addrMsg));
2352 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::string&#32;address(str,&#32;str&#32;+&#32;zmq_msg_size(&amp;addrMsg));
2353 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_msg_close(&amp;addrMsg);
2354 <emphasis role="preprocessor">#else</emphasis>
2355 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;Bit&#32;of&#32;a&#32;hack,&#32;but&#32;all&#32;events&#32;in&#32;the&#32;zmq_event_t&#32;union&#32;have&#32;the&#32;same&#32;layout&#32;so&#32;this&#32;will&#32;work&#32;for&#32;all&#32;event&#32;types.</emphasis>
2356 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::string&#32;address&#32;=&#32;<emphasis role="keyword">event</emphasis>-&gt;data.connected.addr;
2357 <emphasis role="preprocessor">#endif</emphasis>
2358 
2359 <emphasis role="preprocessor">#ifdef&#32;ZMQ_EVENT_MONITOR_STOPPED</emphasis>
2360 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(event-&gt;event&#32;==&#32;ZMQ_EVENT_MONITOR_STOPPED)&#32;{
2361 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_msg_close(&amp;eventMsg);
2362 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">false</emphasis>;
2363 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
2364 
2365 <emphasis role="preprocessor">#endif</emphasis>
2366 
2367 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">switch</emphasis>&#32;(event-&gt;event)&#32;{
2368 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_CONNECTED:
2369 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_connected(*event,&#32;address.c_str());
2370 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2371 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_CONNECT_DELAYED:
2372 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_connect_delayed(*event,&#32;address.c_str());
2373 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2374 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_CONNECT_RETRIED:
2375 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_connect_retried(*event,&#32;address.c_str());
2376 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2377 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_LISTENING:
2378 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_listening(*event,&#32;address.c_str());
2379 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2380 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_BIND_FAILED:
2381 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_bind_failed(*event,&#32;address.c_str());
2382 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2383 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_ACCEPTED:
2384 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_accepted(*event,&#32;address.c_str());
2385 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2386 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_ACCEPT_FAILED:
2387 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_accept_failed(*event,&#32;address.c_str());
2388 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2389 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_CLOSED:
2390 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_closed(*event,&#32;address.c_str());
2391 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2392 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_CLOSE_FAILED:
2393 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_close_failed(*event,&#32;address.c_str());
2394 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2395 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_DISCONNECTED:
2396 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_disconnected(*event,&#32;address.c_str());
2397 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2398 <emphasis role="preprocessor">#ifdef&#32;ZMQ_BUILD_DRAFT_API</emphasis>
2399 <emphasis role="preprocessor">#if&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(4,&#32;2,&#32;3)</emphasis>
2400 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_HANDSHAKE_FAILED_NO_DETAIL:
2401 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_handshake_failed_no_detail(*event,&#32;address.c_str());
2402 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2403 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_HANDSHAKE_FAILED_PROTOCOL:
2404 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_handshake_failed_protocol(*event,&#32;address.c_str());
2405 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2406 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_HANDSHAKE_FAILED_AUTH:
2407 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_handshake_failed_auth(*event,&#32;address.c_str());
2408 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2409 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_HANDSHAKE_SUCCEEDED:
2410 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_handshake_succeeded(*event,&#32;address.c_str());
2411 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2412 <emphasis role="preprocessor">#elif&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(4,&#32;2,&#32;1)</emphasis>
2413 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_HANDSHAKE_FAILED:
2414 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_handshake_failed(*event,&#32;address.c_str());
2415 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2416 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">case</emphasis>&#32;ZMQ_EVENT_HANDSHAKE_SUCCEED:
2417 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_handshake_succeed(*event,&#32;address.c_str());
2418 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2419 <emphasis role="preprocessor">#endif</emphasis>
2420 <emphasis role="preprocessor">#endif</emphasis>
2421 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">default</emphasis>:
2422 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;on_event_unknown(*event,&#32;address.c_str());
2423 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">break</emphasis>;
2424 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
2425 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_msg_close(&amp;eventMsg);
2426 
2427 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">true</emphasis>;
2428 &#32;&#32;&#32;&#32;}
2429 
2430 <emphasis role="preprocessor">#ifdef&#32;ZMQ_EVENT_MONITOR_STOPPED</emphasis>
2431 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;abort()
2432 &#32;&#32;&#32;&#32;{
2433 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(_socket)
2434 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_socket_monitor(_socket.handle(),&#32;ZMQ_NULLPTR,&#32;0);
2435 
2436 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_socket&#32;=&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>();
2437 &#32;&#32;&#32;&#32;}
2438 <emphasis role="preprocessor">#endif</emphasis>
2439 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_monitor_started()&#32;{}
2440 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_connected(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2441 &#32;&#32;&#32;&#32;{
2442 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2443 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2444 &#32;&#32;&#32;&#32;}
2445 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_connect_delayed(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,
2446 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2447 &#32;&#32;&#32;&#32;{
2448 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2449 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2450 &#32;&#32;&#32;&#32;}
2451 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_connect_retried(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,
2452 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2453 &#32;&#32;&#32;&#32;{
2454 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2455 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2456 &#32;&#32;&#32;&#32;}
2457 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_listening(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2458 &#32;&#32;&#32;&#32;{
2459 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2460 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2461 &#32;&#32;&#32;&#32;}
2462 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_bind_failed(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2463 &#32;&#32;&#32;&#32;{
2464 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2465 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2466 &#32;&#32;&#32;&#32;}
2467 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_accepted(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2468 &#32;&#32;&#32;&#32;{
2469 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2470 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2471 &#32;&#32;&#32;&#32;}
2472 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_accept_failed(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2473 &#32;&#32;&#32;&#32;{
2474 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2475 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2476 &#32;&#32;&#32;&#32;}
2477 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_closed(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2478 &#32;&#32;&#32;&#32;{
2479 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2480 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2481 &#32;&#32;&#32;&#32;}
2482 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_close_failed(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2483 &#32;&#32;&#32;&#32;{
2484 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2485 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2486 &#32;&#32;&#32;&#32;}
2487 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_disconnected(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2488 &#32;&#32;&#32;&#32;{
2489 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2490 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2491 &#32;&#32;&#32;&#32;}
2492 <emphasis role="preprocessor">#if&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(4,&#32;2,&#32;3)</emphasis>
2493 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_handshake_failed_no_detail(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,
2494 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2495 &#32;&#32;&#32;&#32;{
2496 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2497 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2498 &#32;&#32;&#32;&#32;}
2499 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_handshake_failed_protocol(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,
2500 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2501 &#32;&#32;&#32;&#32;{
2502 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2503 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2504 &#32;&#32;&#32;&#32;}
2505 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_handshake_failed_auth(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,
2506 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2507 &#32;&#32;&#32;&#32;{
2508 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2509 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2510 &#32;&#32;&#32;&#32;}
2511 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_handshake_succeeded(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,
2512 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2513 &#32;&#32;&#32;&#32;{
2514 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2515 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2516 &#32;&#32;&#32;&#32;}
2517 <emphasis role="preprocessor">#elif&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(4,&#32;2,&#32;1)</emphasis>
2518 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_handshake_failed(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,
2519 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2520 &#32;&#32;&#32;&#32;{
2521 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2522 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2523 &#32;&#32;&#32;&#32;}
2524 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_handshake_succeed(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,
2525 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2526 &#32;&#32;&#32;&#32;{
2527 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2528 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2529 &#32;&#32;&#32;&#32;}
2530 <emphasis role="preprocessor">#endif</emphasis>
2531 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;on_event_unknown(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structzmq__event__t">zmq_event_t</link>&#32;&amp;event_,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">char</emphasis>&#32;*addr_)
2532 &#32;&#32;&#32;&#32;{
2533 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;event_;
2534 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(void)&#32;addr_;
2535 &#32;&#32;&#32;&#32;}
2536 
2537 &#32;&#32;<emphasis role="keyword">private</emphasis>:
2538 &#32;&#32;&#32;&#32;monitor_t(<emphasis role="keyword">const</emphasis>&#32;monitor_t&#32;&amp;)&#32;ZMQ_DELETED_FUNCTION;
2539 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;monitor_t&#32;&amp;)&#32;ZMQ_DELETED_FUNCTION;
2540 
2541 &#32;&#32;&#32;&#32;<link linkend="_classzmq_1_1socket__ref">socket_ref</link>&#32;_socket;
2542 &#32;&#32;&#32;&#32;<link linkend="_classzmq_1_1socket__t">socket_t</link>&#32;_monitor_socket;
2543 
2544 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;close()&#32;ZMQ_NOTHROW
2545 &#32;&#32;&#32;&#32;{
2546 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(_socket)
2547 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;zmq_socket_monitor(_socket.handle(),&#32;ZMQ_NULLPTR,&#32;0);
2548 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;_monitor_socket.close();
2549 &#32;&#32;&#32;&#32;}
2550 };
2551 
2552 <emphasis role="preprocessor">#if&#32;defined(ZMQ_BUILD_DRAFT_API)&#32;&amp;&amp;&#32;defined(ZMQ_CPP11)&#32;&amp;&amp;&#32;defined(ZMQ_HAVE_POLLER)</emphasis>
2553 
2554 <emphasis role="comment">//&#32;polling&#32;events</emphasis>
2555 <emphasis role="keyword">enum&#32;class</emphasis>&#32;event_flags&#32;:&#32;<emphasis role="keywordtype">short</emphasis>
2556 {
2557 &#32;&#32;&#32;&#32;none&#32;=&#32;0,
2558 &#32;&#32;&#32;&#32;pollin&#32;=&#32;ZMQ_POLLIN,
2559 &#32;&#32;&#32;&#32;pollout&#32;=&#32;ZMQ_POLLOUT,
2560 &#32;&#32;&#32;&#32;pollerr&#32;=&#32;ZMQ_POLLERR,
2561 &#32;&#32;&#32;&#32;pollpri&#32;=&#32;ZMQ_POLLPRI
2562 };
2563 
2564 <emphasis role="keyword">constexpr</emphasis>&#32;event_flags&#32;operator|(event_flags&#32;a,&#32;event_flags&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
2565 {
2566 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_or(a,&#32;b);
2567 }
2568 <emphasis role="keyword">constexpr</emphasis>&#32;event_flags&#32;operator&amp;(event_flags&#32;a,&#32;event_flags&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
2569 {
2570 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_and(a,&#32;b);
2571 }
2572 <emphasis role="keyword">constexpr</emphasis>&#32;event_flags&#32;operator^(event_flags&#32;a,&#32;event_flags&#32;b)&#32;<emphasis role="keyword">noexcept</emphasis>
2573 {
2574 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_xor(a,&#32;b);
2575 }
2576 <emphasis role="keyword">constexpr</emphasis>&#32;event_flags&#32;operator~(event_flags&#32;a)&#32;<emphasis role="keyword">noexcept</emphasis>
2577 {
2578 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;detail::enum_bit_not(a);
2579 }
2580 
2581 <emphasis role="keyword">struct&#32;</emphasis>no_user_data;
2582 
2583 <emphasis role="comment">//&#32;layout&#32;compatible&#32;with&#32;zmq_poller_event_t</emphasis>
2584 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">class</emphasis>&#32;T&#32;=&#32;no_user_data&gt;&#32;<emphasis role="keyword">struct&#32;</emphasis>poller_event
2585 {
2586 &#32;&#32;&#32;&#32;socket_ref&#32;socket;
2587 <emphasis role="preprocessor">#ifdef&#32;_WIN32</emphasis>
2588 &#32;&#32;&#32;&#32;SOCKET&#32;fd;
2589 <emphasis role="preprocessor">#else</emphasis>
2590 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;fd;
2591 <emphasis role="preprocessor">#endif</emphasis>
2592 &#32;&#32;&#32;&#32;T&#32;*user_data;
2593 &#32;&#32;&#32;&#32;event_flags&#32;events;
2594 };
2595 
2596 <emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">typename</emphasis>&#32;T&#32;=&#32;no_user_data&gt;&#32;<emphasis role="keyword">class&#32;</emphasis>poller_t
2597 {
2598 &#32;&#32;<emphasis role="keyword">public</emphasis>:
2599 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>event_type&#32;=&#32;poller_event&lt;T&gt;;
2600 
2601 &#32;&#32;&#32;&#32;poller_t()&#32;:&#32;poller_ptr(zmq_poller_new())
2602 &#32;&#32;&#32;&#32;{
2603 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(!poller_ptr)
2604 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;error_t();
2605 &#32;&#32;&#32;&#32;}
2606 
2607 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;
2608 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typename</emphasis>&#32;Dummy&#32;=&#32;void,
2609 &#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typename</emphasis>&#32;=
2610 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typename</emphasis>&#32;std::enable_if&lt;!std::is_same&lt;T,&#32;no_user_data&gt;::value,&#32;Dummy&gt;::type&gt;
2611 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;add(zmq::socket_ref&#32;socket,&#32;event_flags&#32;events,&#32;T&#32;*user_data)
2612 &#32;&#32;&#32;&#32;{
2613 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;add_impl(socket,&#32;events,&#32;user_data);
2614 &#32;&#32;&#32;&#32;}
2615 
2616 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;add(zmq::socket_ref&#32;socket,&#32;event_flags&#32;events)
2617 &#32;&#32;&#32;&#32;{
2618 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;add_impl(socket,&#32;events,&#32;<emphasis role="keyword">nullptr</emphasis>);
2619 &#32;&#32;&#32;&#32;}
2620 
2621 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;remove(zmq::socket_ref&#32;socket)
2622 &#32;&#32;&#32;&#32;{
2623 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(0&#32;!=&#32;zmq_poller_remove(poller_ptr.get(),&#32;socket.handle()))&#32;{
2624 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;error_t();
2625 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
2626 &#32;&#32;&#32;&#32;}
2627 
2628 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;modify(zmq::socket_ref&#32;socket,&#32;event_flags&#32;events)
2629 &#32;&#32;&#32;&#32;{
2630 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(0
2631 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;!=&#32;zmq_poller_modify(poller_ptr.get(),&#32;socket.handle(),
2632 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">short</emphasis><emphasis role="keyword">&gt;</emphasis>(events)))&#32;{
2633 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;error_t();
2634 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
2635 &#32;&#32;&#32;&#32;}
2636 
2637 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">size_t</emphasis>&#32;wait_all(std::vector&lt;event_type&gt;&#32;&amp;poller_events,
2638 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;std::chrono::milliseconds&#32;timeout)
2639 &#32;&#32;&#32;&#32;{
2640 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_poller_wait_all(
2641 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;poller_ptr.get(),
2642 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">reinterpret_cast&lt;</emphasis>zmq_poller_event_t&#32;*<emphasis role="keyword">&gt;</emphasis>(poller_events.data()),
2643 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">int</emphasis><emphasis role="keyword">&gt;</emphasis>(poller_events.size()),
2644 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">long</emphasis><emphasis role="keyword">&gt;</emphasis>(timeout.count()));
2645 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(rc&#32;&gt;&#32;0)
2646 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">size_t</emphasis><emphasis role="keyword">&gt;</emphasis>(rc);
2647 
2648 <emphasis role="preprocessor">#if&#32;ZMQ_VERSION&#32;&gt;=&#32;ZMQ_MAKE_VERSION(4,&#32;2,&#32;3)</emphasis>
2649 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;EAGAIN)
2650 <emphasis role="preprocessor">#else</emphasis>
2651 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(zmq_errno()&#32;==&#32;ETIMEDOUT)
2652 <emphasis role="preprocessor">#endif</emphasis>
2653 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;0;
2654 
2655 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;error_t();
2656 &#32;&#32;&#32;&#32;}
2657 
2658 &#32;&#32;<emphasis role="keyword">private</emphasis>:
2659 &#32;&#32;&#32;&#32;<emphasis role="keyword">struct&#32;</emphasis>destroy_poller_t
2660 &#32;&#32;&#32;&#32;{
2661 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;operator()(<emphasis role="keywordtype">void</emphasis>&#32;*ptr)&#32;<emphasis role="keyword">noexcept</emphasis>
2662 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;{
2663 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">int</emphasis>&#32;rc&#32;=&#32;zmq_poller_destroy(&amp;ptr);
2664 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ZMQ_ASSERT(rc&#32;==&#32;0);
2665 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
2666 &#32;&#32;&#32;&#32;};
2667 
2668 &#32;&#32;&#32;&#32;std::unique_ptr&lt;void,&#32;destroy_poller_t&gt;&#32;poller_ptr;
2669 
2670 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;add_impl(zmq::socket_ref&#32;socket,&#32;event_flags&#32;events,&#32;T&#32;*user_data)
2671 &#32;&#32;&#32;&#32;{
2672 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(0
2673 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;!=&#32;zmq_poller_add(poller_ptr.get(),&#32;socket.handle(),&#32;user_data,
2674 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">static_cast&lt;</emphasis><emphasis role="keywordtype">short</emphasis><emphasis role="keyword">&gt;</emphasis>(events)))&#32;{
2675 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">throw</emphasis>&#32;error_t();
2676 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
2677 &#32;&#32;&#32;&#32;}
2678 };
2679 <emphasis role="preprocessor">#endif&#32;</emphasis><emphasis role="comment">//&#32;&#32;defined(ZMQ_BUILD_DRAFT_API)&#32;&amp;&amp;&#32;defined(ZMQ_CPP11)&#32;&amp;&amp;&#32;defined(ZMQ_HAVE_POLLER)</emphasis>
2680 
2681 <emphasis role="keyword">inline</emphasis>&#32;std::ostream&#32;&amp;operator&lt;&lt;(std::ostream&#32;&amp;os,&#32;<emphasis role="keyword">const</emphasis>&#32;<link linkend="_classzmq_1_1message__t">message_t</link>&#32;&amp;msg)
2682 {
2683 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;os&#32;&lt;&lt;&#32;msg.str();
2684 }
2685 
2686 }&#32;<emphasis role="comment">//&#32;namespace&#32;zmq</emphasis>
2687 
2688 <emphasis role="preprocessor">#endif&#32;</emphasis><emphasis role="comment">//&#32;__ZMQ_HPP_INCLUDED__</emphasis>
</programlisting></section>
