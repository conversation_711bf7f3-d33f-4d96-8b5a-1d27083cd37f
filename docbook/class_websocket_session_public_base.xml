<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_class_websocket_session_public_base" xml:lang="en-US">
<title>WebsocketSessionPublicBase Class Reference</title>
<indexterm><primary>WebsocketSessionPublicBase</primary></indexterm>
<para>Inheritance diagram for WebsocketSessionPublicBase:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="class_websocket_session_public_base.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_websocket_session_public_base_1aa15c26c834db01066f7e3b0c273f4adb"/>virtual void <emphasis role="strong">AsyncSendTextData</emphasis> (std::shared_ptr&lt; std::string &gt; &amp;&amp;stringPtr)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_websocket_session_public_base_1a357badd16072398838e7c33a9c59c680"/>virtual void <emphasis role="strong">AsyncClose</emphasis> ()=0</para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Protected Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_websocket_session_public_base_1ace0211adbf8034f1dd03222294ce7878"/><emphasis role="strong">WebsocketSessionPublicBase</emphasis> (uint32_t uniqueId)</para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Protected Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_websocket_session_public_base_1aab1e18e799978fe5933fd9350f7d176c"/>const uint32_t <emphasis role="strong">m_uniqueId</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
<para>
The documentation for this class was generated from the following file:</para>
module/telem_cmd_interface/include/<link linkend="__beast_websocket_server_8h">BeastWebsocketServer.h</link></section>
