<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_struct_get_hdtn_version_api_command__t" xml:lang="en-US">
<title>GetHdtnVersionApiCommand_t Struct Reference</title>
<indexterm><primary>GetHdtnVersionApiCommand_t</primary></indexterm>
<para>Inheritance diagram for GetHdtnVersionApiCommand_t:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="struct_get_hdtn_version_api_command__t.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Static Public Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_struct_get_hdtn_version_api_command__t_1adb4879863c549992a54423fde6414536"/>static TELEMETRY_DEFINITIONS_EXPORT const std::string <emphasis role="strong">name</emphasis> = &quot;get_hdtn_version&quot;</para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Additional Inherited Members    </title>
Public Member Functions inherited from <link linkend="_struct_api_command__t">ApiCommand_t</link>        <itemizedlist>
            <listitem><para>TELEMETRY_DEFINITIONS_EXPORT <link linkend="_struct_api_command__t_1a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</link> ()</para>
</listitem>
            <listitem><para>TELEMETRY_DEFINITIONS_EXPORT bool <emphasis role="strong">operator==</emphasis> (const <link linkend="_struct_api_command__t">ApiCommand_t</link> &amp;o) const</para>
</listitem>
            <listitem><para>TELEMETRY_DEFINITIONS_EXPORT bool <emphasis role="strong">operator!=</emphasis> (const <link linkend="_struct_api_command__t">ApiCommand_t</link> &amp;o) const</para>
</listitem>
            <listitem><para>virtual TELEMETRY_DEFINITIONS_EXPORT boost::property_tree::ptree <link linkend="_struct_api_command__t_1ab28f4b4980e74c59e770c28c3a3cc83a">GetNewPropertyTree</link> () const override</para>
</listitem>
            <listitem><para>virtual TELEMETRY_DEFINITIONS_EXPORT bool <link linkend="_struct_api_command__t_1ace0b0d45056be361a39ca9eeb968ed55">SetValuesFromPropertyTree</link> (const boost::property_tree::ptree &amp;pt) override</para>
</listitem>
        </itemizedlist>
</simplesect>
Public Member Functions inherited from <link linkend="_class_json_serializable">JsonSerializable</link>        <itemizedlist>
            <listitem><para>std::string <emphasis role="strong">ToJson</emphasis> (bool pretty=true) const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">ToJsonFile</emphasis> (const boost::filesystem::path &amp;filePath, bool pretty=true) const</para>
</listitem>
            <listitem><para>std::string <emphasis role="strong">ToXml</emphasis> () const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">ToXmlFile</emphasis> (const std::string &amp;fileName, char indentCharacter=&apos; &apos;, int indentCount=2) const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">SetValuesFromJson</emphasis> (const std::string &amp;jsonString)</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">SetValuesFromJsonCharArray</emphasis> (const char *data, const std::size_t size)</para>
</listitem>
        </itemizedlist>
Static Public Member Functions inherited from <link linkend="_struct_api_command__t">ApiCommand_t</link>        <itemizedlist>
            <listitem><para>static TELEMETRY_DEFINITIONS_EXPORT std::shared_ptr&lt; <link linkend="_struct_api_command__t">ApiCommand_t</link> &gt; <emphasis role="strong">CreateFromJson</emphasis> (const std::string &amp;jsonStr)</para>
</listitem>
        </itemizedlist>
Static Public Member Functions inherited from <link linkend="_class_json_serializable">JsonSerializable</link>        <itemizedlist>
            <listitem><para>static bool <emphasis role="strong">LoadTextFileIntoString</emphasis> (const boost::filesystem::path &amp;filePath, std::string &amp;fileContentsAsString)</para>
</listitem>
            <listitem><para>static void <emphasis role="strong">GetAllJsonKeys</emphasis> (const std::string &amp;jsonText, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</para>
</listitem>
            <listitem><para>static void <emphasis role="strong">GetAllJsonKeysLineByLine</emphasis> (std::istream &amp;stream, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInFilePath</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, const boost::filesystem::path &amp;originalUserJsonFilePath, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInString</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, const std::string &amp;originalUserJsonString, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInStream</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, std::istream &amp;originalUserJsonStream, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static std::string <emphasis role="strong">PtToJsonString</emphasis> (const boost::property_tree::ptree &amp;pt, bool pretty=true)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonCharArray</emphasis> (char *data, const std::size_t size, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonStream</emphasis> (std::istream &amp;jsonStream, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonString</emphasis> (const std::string &amp;jsonStr, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonFilePath</emphasis> (const boost::filesystem::path &amp;jsonFilePath, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static std::string <emphasis role="strong">PtToXmlString</emphasis> (const boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static boost::property_tree::ptree <emphasis role="strong">GetPropertyTreeFromXmlString</emphasis> (const std::string &amp;jsonStr)</para>
</listitem>
            <listitem><para>static boost::property_tree::ptree <emphasis role="strong">GetPropertyTreeFromXmlFile</emphasis> (const std::string &amp;xmlFileName)</para>
</listitem>
        </itemizedlist>
Public Attributes inherited from <link linkend="_struct_api_command__t">ApiCommand_t</link>        <itemizedlist>
            <listitem><para>std::string <emphasis role="strong">m_apiCall</emphasis></para>
</listitem>
        </itemizedlist>
<para>
The documentation for this struct was generated from the following files:</para>
common/telemetry_definitions/include/<link linkend="__telemetry_definitions_8h">TelemetryDefinitions.h</link>common/telemetry_definitions/src/TelemetryDefinitions.cpp</section>
