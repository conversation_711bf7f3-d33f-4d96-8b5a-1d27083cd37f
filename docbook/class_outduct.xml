<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_class_outduct" xml:lang="en-US">
<title>Outduct Class Reference</title>
<indexterm><primary>Outduct</primary></indexterm>
<para>Inheritance diagram for Outduct:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="class_outduct.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_outduct_1a3d54043e8be71f9783c7695939c2bbc2"/>OUTDUCT_MANAGER_LIB_EXPORT <emphasis role="strong">Outduct</emphasis> (const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> &amp;outductConfig, const uint64_t outductUuid)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a1131e780e70b9af556eeaacbe8b31d2c"/>virtual void <emphasis role="strong">PopulateOutductTelemetry</emphasis> (std::unique_ptr&lt; <link linkend="_struct_outduct_telemetry__t">OutductTelemetry_t</link> &gt; &amp;outductTelem)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a9050711ddf3b07d2503dadc01b6e05f4"/>virtual std::size_t <emphasis role="strong">GetTotalBundlesUnacked</emphasis> () const noexcept=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a0120999b87ce99c34c9caea80f4ee409"/>virtual bool <emphasis role="strong">Forward</emphasis> (const uint8_t *bundleData, const std::size_t size, std::vector&lt; uint8_t &gt; &amp;&amp;userData)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a41bc1487fab85c396274198ba71c2116"/>virtual bool <emphasis role="strong">Forward</emphasis> (<link linkend="_classzmq_1_1message__t">zmq::message_t</link> &amp;movableDataZmq, std::vector&lt; uint8_t &gt; &amp;&amp;userData)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1ab3f43ac1bbbac6b246f40ced953445ce"/>virtual bool <emphasis role="strong">Forward</emphasis> (padded_vector_uint8_t &amp;movableDataVec, std::vector&lt; uint8_t &gt; &amp;&amp;userData)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1ac5189e24f0336ff975a25997a02ead5b"/>virtual void <emphasis role="strong">SetOnFailedBundleVecSendCallback</emphasis> (const OnFailedBundleVecSendCallback_t &amp;callback)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1ab4de323b8d292696aa06d80d361e2a8d"/>virtual void <emphasis role="strong">SetOnFailedBundleZmqSendCallback</emphasis> (const OnFailedBundleZmqSendCallback_t &amp;callback)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a9cfa5ba7b62a34b257499e51a08f3a11"/>virtual void <emphasis role="strong">SetOnSuccessfulBundleSendCallback</emphasis> (const OnSuccessfulBundleSendCallback_t &amp;callback)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1ac0374d326a1a6f9645273d0b7050022f"/>virtual void <emphasis role="strong">SetOnOutductLinkStatusChangedCallback</emphasis> (const OnOutductLinkStatusChangedCallback_t &amp;callback)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1aa61f9350b2bf904e261c7d412e7e98a1"/>virtual void <emphasis role="strong">SetUserAssignedUuid</emphasis> (uint64_t userAssignedUuid)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a9b03ca25daf4e025e4b7c39986e7ef4a"/>virtual OUTDUCT_MANAGER_LIB_EXPORT void <emphasis role="strong">SetRate</emphasis> (uint64_t maxSendRateBitsPerSecOrZeroToDisable)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1afb77e4e8d7c021633ba11c296e6d676e"/>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">Init</emphasis> ()</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a8f8acd9649ee6041ff0075e1d93a07bb"/>virtual void <emphasis role="strong">Connect</emphasis> ()=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a6b26e4c8d0c5e6f83a1169329b3e34c2"/>virtual bool <emphasis role="strong">ReadyToForward</emphasis> ()=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1ae5e3c6f5f3dc1ddb58ca1df55c39eb67"/>virtual void <emphasis role="strong">Stop</emphasis> ()=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a3a04125580ae4bcbeaef385dd4a8600e"/>virtual void <emphasis role="strong">GetOutductFinalStats</emphasis> (<link linkend="_struct_outduct_final_stats">OutductFinalStats</link> &amp;finalStats)=0</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a1f52844d4d3230713f94c9d2379aa76d"/>OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductUuid</emphasis> () const</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a7bc56120366e26a39d2b8649142dcf06"/>virtual OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductMaxNumberOfBundlesInPipeline</emphasis> () const</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1af6865ea342bc15c0948bc7e08275c22f"/>OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductMaxSumOfBundleBytesInPipeline</emphasis> () const</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a8ef969d09fbe27dbb2e554e25d5a30b4"/>OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductNextHopNodeId</emphasis> () const</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1ab11d4cb1320f373b031f7321dc3de42d"/>OUTDUCT_MANAGER_LIB_EXPORT std::string <emphasis role="strong">GetConvergenceLayerName</emphasis> () const</para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a822ccc9e49c2b46c28523be992e83822"/>OUTDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">GetAssumedInitiallyDown</emphasis> () const</para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Public Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_outduct_1a0d1df24d131d3b49340c21f40de169d4"/>bool <emphasis role="strong">m_linkIsUpPerTimeSchedule</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a21cc7e27c84d9b1a0382c0afa7e4d093"/>bool <emphasis role="strong">m_physicalLinkStatusIsKnown</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1aeb7df8e552d97667d0a8aa64454d996e"/>bool <emphasis role="strong">m_linkIsUpPhysically</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Protected Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_outduct_1aa0e8278a9eea979a89210b51828cd178"/>OUTDUCT_MANAGER_LIB_EXPORT <emphasis role="strong">Outduct</emphasis> (const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> &amp;outductConfig, const uint64_t outductUuid, const bool assumedInitiallyDown)</para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Protected Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_outduct_1a0c9964b51b922b5b4a4aca549e0a256a"/>const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> <emphasis role="strong">m_outductConfig</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1a13b66a5a84bb088da54ae6f1a0bfc55b"/>const uint64_t <emphasis role="strong">m_outductUuid</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_outduct_1aa12cdd8dc71db643a05e62c0d22c0679"/>const bool <emphasis role="strong">m_assumedInitiallyDown</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
<para>
The documentation for this class was generated from the following files:</para>
common/outduct_manager/include/<link linkend="__outduct_8h">Outduct.h</link>common/outduct_manager/src/<link linkend="__outduct_8cpp">Outduct.cpp</link></section>
