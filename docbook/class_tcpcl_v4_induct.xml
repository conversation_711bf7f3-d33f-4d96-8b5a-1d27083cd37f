<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_class_tcpcl_v4_induct" xml:lang="en-US">
<title>TcpclV4Induct Class Reference</title>
<indexterm><primary>TcpclV4Induct</primary></indexterm>
<para>Inheritance diagram for TcpclV4Induct:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="class_tcpcl_v4_induct.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_tcpcl_v4_induct_1a030358d0b4419871cc37c687b2dfa291"/>INDUCT_MANAGER_LIB_EXPORT <emphasis role="strong">TcpclV4Induct</emphasis> (const InductProcessBundleCallback_t &amp;inductProcessBundleCallback, const <link linkend="_structinduct__element__config__t">induct_element_config_t</link> &amp;inductConfig, const uint64_t myNodeId, const uint64_t maxBundleSizeBytes, const OnNewOpportunisticLinkCallback_t &amp;onNewOpportunisticLinkCallback, const OnDeletedOpportunisticLinkCallback_t &amp;onDeletedOpportunisticLinkCallback)</para>
</listitem>
            <listitem><para>virtual INDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_tcpcl_v4_induct_1ae2b399d45c52ada940af4385c2a03e08">PopulateInductTelemetry</link> (<link linkend="_struct_induct_telemetry__t">InductTelemetry_t</link> &amp;inductTelem) override</para>
</listitem>
        </itemizedlist>
</simplesect>
Public Member Functions inherited from <link linkend="_class_induct">Induct</link>        <itemizedlist>
            <listitem><para>INDUCT_MANAGER_LIB_EXPORT <emphasis role="strong">Induct</emphasis> (const InductProcessBundleCallback_t &amp;inductProcessBundleCallback, const <link linkend="_structinduct__element__config__t">induct_element_config_t</link> &amp;inductConfig)</para>
</listitem>
            <listitem><para>virtual INDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">Init</emphasis> ()</para>
</listitem>
            <listitem><para>INDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">ForwardOnOpportunisticLink</emphasis> (const uint64_t remoteNodeId, padded_vector_uint8_t &amp;dataVec, const uint32_t timeoutSeconds)</para>
</listitem>
            <listitem><para>INDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">ForwardOnOpportunisticLink</emphasis> (const uint64_t remoteNodeId, <link linkend="_classzmq_1_1message__t">zmq::message_t</link> &amp;dataZmq, const uint32_t timeoutSeconds)</para>
</listitem>
            <listitem><para>INDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">ForwardOnOpportunisticLink</emphasis> (const uint64_t remoteNodeId, const uint8_t *bundleData, const std::size_t size, const uint32_t timeoutSeconds)</para>
</listitem>
        </itemizedlist>
<simplesect>
    <title>Additional Inherited Members    </title>
Protected Member Functions inherited from <link linkend="_class_induct">Induct</link>        <itemizedlist>
            <listitem><para>INDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">BundleSinkTryGetData_FromIoServiceThread</emphasis> (<link linkend="_struct_induct_1_1_opportunistic_bundle_queue">OpportunisticBundleQueue</link> &amp;opportunisticBundleQueue, std::pair&lt; std::unique_ptr&lt; <link linkend="_classzmq_1_1message__t">zmq::message_t</link> &gt;, padded_vector_uint8_t &gt; &amp;bundleDataPair)</para>
</listitem>
            <listitem><para>INDUCT_MANAGER_LIB_EXPORT void <emphasis role="strong">BundleSinkNotifyOpportunisticDataAcked_FromIoServiceThread</emphasis> (<link linkend="_struct_induct_1_1_opportunistic_bundle_queue">OpportunisticBundleQueue</link> &amp;opportunisticBundleQueue)</para>
</listitem>
            <listitem><para>INDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">ForwardOnOpportunisticLink</emphasis> (const uint64_t remoteNodeId, <link linkend="_classzmq_1_1message__t">zmq::message_t</link> *zmqMsgPtr, padded_vector_uint8_t *vec8Ptr, const uint32_t timeoutSeconds)</para>
</listitem>
        </itemizedlist>
</simplesect>
Protected Attributes inherited from <link linkend="_class_induct">Induct</link>        <itemizedlist>
            <listitem><para>const InductProcessBundleCallback_t <emphasis role="strong">m_inductProcessBundleCallback</emphasis></para>
</listitem>
            <listitem><para>const <link linkend="_structinduct__element__config__t">induct_element_config_t</link> <emphasis role="strong">m_inductConfig</emphasis></para>
</listitem>
            <listitem><para>std::map&lt; uint64_t, <link linkend="_struct_induct_1_1_opportunistic_bundle_queue">OpportunisticBundleQueue</link> &gt; <emphasis role="strong">m_mapNodeIdToOpportunisticBundleQueue</emphasis></para>
</listitem>
            <listitem><para>boost::mutex <emphasis role="strong">m_mapNodeIdToOpportunisticBundleQueueMutex</emphasis></para>
</listitem>
            <listitem><para>OnNewOpportunisticLinkCallback_t <emphasis role="strong">m_onNewOpportunisticLinkCallback</emphasis></para>
</listitem>
            <listitem><para>OnDeletedOpportunisticLinkCallback_t <emphasis role="strong">m_onDeletedOpportunisticLinkCallback</emphasis></para>
</listitem>
        </itemizedlist>
<section>
<title>Member Function Documentation</title>
<anchor xml:id="_class_tcpcl_v4_induct_1ae2b399d45c52ada940af4385c2a03e08"/><section>
    <title>PopulateInductTelemetry()</title>
<indexterm><primary>PopulateInductTelemetry</primary><secondary>TcpclV4Induct</secondary></indexterm>
<indexterm><primary>TcpclV4Induct</primary><secondary>PopulateInductTelemetry</secondary></indexterm>
<para><computeroutput>void TcpclV4Induct::PopulateInductTelemetry (<link linkend="_struct_induct_telemetry__t">InductTelemetry_t</link> &amp; inductTelem)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_induct">Induct</link>.</para>
</section>
<para>
The documentation for this class was generated from the following files:</para>
common/induct_manager/include/<link linkend="__tcpcl_v4_induct_8h">TcpclV4Induct.h</link>common/induct_manager/src/<link linkend="__tcpcl_v4_induct_8cpp">TcpclV4Induct.cpp</link></section>
</section>
