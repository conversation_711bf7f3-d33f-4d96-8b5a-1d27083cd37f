<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_bpv6_8h_source" xml:lang="en-US">
<title>bpv6.h</title>
<indexterm><primary>common/bpcodec/include/codec/bpv6.h</primary></indexterm>
Go to the documentation of this file.<programlisting linenumbering="unnumbered">1 
19 
20 <emphasis role="preprocessor">#ifndef&#32;BPV6_H</emphasis>
21 <emphasis role="preprocessor">#define&#32;BPV6_H</emphasis>
22 
23 <emphasis role="preprocessor">#include&#32;&lt;cstdint&gt;</emphasis>
24 <emphasis role="preprocessor">#include&#32;&lt;cstddef&gt;</emphasis>
25 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__cbhe_8h">Cbhe.h</link>&quot;</emphasis>
26 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__timestamp_util_8h">TimestampUtil.h</link>&quot;</emphasis>
27 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__primary_block_8h">codec/PrimaryBlock.h</link>&quot;</emphasis>
28 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__enum_as_flags_macro_8h">EnumAsFlagsMacro.h</link>&quot;</emphasis>
29 <emphasis role="preprocessor">#include&#32;&lt;array&gt;</emphasis>
30 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__fragment_set_8h">FragmentSet.h</link>&quot;</emphasis>
31 <emphasis role="preprocessor">#include&#32;&quot;bpcodec_export.h&quot;</emphasis>
32 <emphasis role="preprocessor">#ifndef&#32;CLASS_VISIBILITY_BPCODEC</emphasis>
33 <emphasis role="preprocessor">#&#32;&#32;ifdef&#32;_WIN32</emphasis>
34 <emphasis role="preprocessor">#&#32;&#32;&#32;&#32;define&#32;CLASS_VISIBILITY_BPCODEC</emphasis>
35 <emphasis role="preprocessor">#&#32;&#32;else</emphasis>
36 <emphasis role="preprocessor">#&#32;&#32;&#32;&#32;define&#32;CLASS_VISIBILITY_BPCODEC&#32;BPCODEC_EXPORT</emphasis>
37 <emphasis role="preprocessor">#&#32;&#32;endif</emphasis>
38 <emphasis role="preprocessor">#endif</emphasis>
39 
40 
41 <emphasis role="comment">//&#32;(1-byte&#32;version)&#32;+&#32;(1-byte&#32;sdnv&#32;block&#32;length)&#32;+&#32;(1-byte&#32;sdnv&#32;zero&#32;dictionary&#32;length)&#32;+&#32;(up&#32;to&#32;14&#32;10-byte&#32;sdnvs)&#32;+&#32;(32&#32;bytes&#32;hardware&#32;accelerated&#32;SDNV&#32;overflow&#32;instructions)&#32;</emphasis>
42 <emphasis role="preprocessor">#define&#32;CBHE_BPV6_MINIMUM_SAFE_PRIMARY_HEADER_ENCODE_SIZE&#32;(1&#32;+&#32;1&#32;+&#32;1&#32;+&#32;(14*10)&#32;+&#32;32)</emphasis>
43 
44 <emphasis role="comment">//&#32;(1-byte&#32;block&#32;type)&#32;+&#32;(2&#32;10-byte&#32;sdnvs)&#32;+&#32;(32&#32;bytes&#32;hardware&#32;accelerated&#32;SDNV&#32;overflow&#32;instructions)&#32;</emphasis>
45 <emphasis role="preprocessor">#define&#32;BPV6_MINIMUM_SAFE_CANONICAL_HEADER_ENCODE_SIZE&#32;(1&#32;+&#32;(2*10)&#32;+&#32;32)</emphasis>
46 
47 <emphasis role="comment">//&#32;(1-byte&#32;block&#32;type)&#32;+&#32;(2&#32;10-byte&#32;sdnvs)&#32;+&#32;primary</emphasis>
48 <emphasis role="preprocessor">#define&#32;CBHE_BPV6_MINIMUM_SAFE_PRIMARY_PLUS_CANONICAL_HEADER_ENCODE_SIZE&#32;(1&#32;+&#32;(2*10)&#32;+&#32;CBHE_BPV6_MINIMUM_SAFE_PRIMARY_HEADER_ENCODE_SIZE)</emphasis>
49 
50 <emphasis role="preprocessor">#define&#32;BPV6_CCSDS_VERSION&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(6)</emphasis>
51 <emphasis role="preprocessor">#define&#32;BPV6_5050_TIME_OFFSET&#32;&#32;&#32;&#32;&#32;(946684800)</emphasis>
52 
53 <emphasis role="preprocessor">#define&#32;bpv6_unix_to_5050(time)&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(time&#32;-&#32;BPV6_5050_TIME_OFFSET)</emphasis>
54 <emphasis role="preprocessor">#define&#32;bpv6_5050_to_unix(time)&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;(time&#32;+&#32;BPV6_5050_TIME_OFFSET)</emphasis>
55 
56 <emphasis role="keyword">enum&#32;class</emphasis>&#32;BPV6_PRIORITY&#32;:&#32;uint64_t&#32;{
57 &#32;&#32;&#32;&#32;BULK&#32;=&#32;0,
58 &#32;&#32;&#32;&#32;NORMAL&#32;=&#32;1,
59 &#32;&#32;&#32;&#32;EXPEDITED&#32;=&#32;2
60 };
61 MAKE_ENUM_SUPPORT_OSTREAM_OPERATOR(BPV6_PRIORITY);
62 
63 <emphasis role="keyword">enum&#32;class</emphasis>&#32;BPV6_BUNDLEFLAG&#32;:&#32;uint64_t&#32;{
64 &#32;&#32;&#32;&#32;NO_FLAGS_SET&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;0,
65 &#32;&#32;&#32;&#32;ISFRAGMENT&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;0,
66 &#32;&#32;&#32;&#32;ADMINRECORD&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;1,
67 &#32;&#32;&#32;&#32;NOFRAGMENT&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;2,
68 &#32;&#32;&#32;&#32;CUSTODY_REQUESTED&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;3,
69 &#32;&#32;&#32;&#32;SINGLETON&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;4,
70 &#32;&#32;&#32;&#32;USER_APP_ACK_REQUESTED&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;5,
71 &#32;&#32;&#32;&#32;PRIORITY_BULK&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;(<emphasis role="keyword">static_cast&lt;</emphasis>uint64_t<emphasis role="keyword">&gt;</emphasis>(BPV6_PRIORITY::BULK))&#32;&lt;&lt;&#32;7,
72 &#32;&#32;&#32;&#32;PRIORITY_NORMAL&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;(<emphasis role="keyword">static_cast&lt;</emphasis>uint64_t<emphasis role="keyword">&gt;</emphasis>(BPV6_PRIORITY::NORMAL))&#32;&lt;&lt;&#32;7,
73 &#32;&#32;&#32;&#32;PRIORITY_EXPEDITED&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;(<emphasis role="keyword">static_cast&lt;</emphasis>uint64_t<emphasis role="keyword">&gt;</emphasis>(BPV6_PRIORITY::EXPEDITED))&#32;&lt;&lt;&#32;7,
74 &#32;&#32;&#32;&#32;PRIORITY_BIT_MASK&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;3&#32;&lt;&lt;&#32;7,
75 &#32;&#32;&#32;&#32;RECEPTION_STATUS_REPORTS_REQUESTED&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;14,
76 &#32;&#32;&#32;&#32;CUSTODY_STATUS_REPORTS_REQUESTED&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;15,
77 &#32;&#32;&#32;&#32;FORWARDING_STATUS_REPORTS_REQUESTED&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;16,
78 &#32;&#32;&#32;&#32;DELIVERY_STATUS_REPORTS_REQUESTED&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;17,
79 &#32;&#32;&#32;&#32;DELETION_STATUS_REPORTS_REQUESTED&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;18
80 };
81 MAKE_ENUM_SUPPORT_FLAG_OPERATORS(BPV6_BUNDLEFLAG);
82 MAKE_ENUM_SUPPORT_OSTREAM_OPERATOR(BPV6_BUNDLEFLAG);
83 
84 <emphasis role="comment">//#define&#32;bpv6_bundle_set_priority(flags)&#32;&#32;((uint32_t)((flags&#32;&amp;&#32;0x000003)&#32;&lt;&lt;&#32;7))</emphasis>
85 <emphasis role="comment">//#define&#32;bpv6_bundle_get_priority(flags)&#32;&#32;((BPV6_PRIORITY)((flags&#32;&amp;&#32;0x000180)&#32;&gt;&gt;&#32;7))</emphasis>
86 BOOST_FORCEINLINE&#32;BPV6_PRIORITY&#32;GetPriorityFromFlags(BPV6_BUNDLEFLAG&#32;flags)&#32;{
87 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis>BPV6_PRIORITY<emphasis role="keyword">&gt;</emphasis>(((<emphasis role="keyword">static_cast&lt;</emphasis>std::underlying_type&lt;BPV6_BUNDLEFLAG&gt;::type<emphasis role="keyword">&gt;</emphasis>(flags))&#32;&gt;&gt;&#32;7)&#32;&amp;&#32;3);
88 }
89 
90 
94 <emphasis role="keyword">struct&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6CbhePrimaryBlock&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;<link linkend="_struct_primary_block">PrimaryBlock</link>&#32;{
95 &#32;&#32;&#32;&#32;BPV6_BUNDLEFLAG&#32;m_bundleProcessingControlFlags;
96 &#32;&#32;&#32;&#32;uint64_t&#32;m_blockLength;
97 &#32;&#32;&#32;&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;m_destinationEid;
98 &#32;&#32;&#32;&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;m_sourceNodeId;
99 &#32;&#32;&#32;&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;m_reportToEid;
100 &#32;&#32;&#32;&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;m_custodianEid;
101 &#32;&#32;&#32;&#32;<link linkend="_struct_timestamp_util_1_1bpv6__creation__timestamp__t">TimestampUtil::bpv6_creation_timestamp_t</link>&#32;m_creationTimestamp;
102 &#32;&#32;&#32;&#32;uint64_t&#32;m_lifetimeSeconds;
103 &#32;&#32;&#32;&#32;uint64_t&#32;m_tmpDictionaryLengthIgnoredAndAssumedZero;&#32;<emphasis role="comment">//Used&#32;only&#32;by&#32;sdnv&#32;decode&#32;operations&#32;as&#32;temporary&#32;variable&#32;to&#32;preserve&#32;sdnv&#32;encoded&#32;order.&#32;Class&#32;members&#32;ignore&#32;(treat&#32;as&#32;padding&#32;bytes).</emphasis>
104 &#32;&#32;&#32;&#32;uint64_t&#32;m_fragmentOffset;
105 &#32;&#32;&#32;&#32;uint64_t&#32;m_totalApplicationDataUnitLength;
106 
107 &#32;&#32;&#32;&#32;
108 
109 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CbhePrimaryBlock();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
110 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;~Bpv6CbhePrimaryBlock();&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
111 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CbhePrimaryBlock(<emphasis role="keyword">const</emphasis>&#32;Bpv6CbhePrimaryBlock&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
112 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CbhePrimaryBlock(Bpv6CbhePrimaryBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
113 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CbhePrimaryBlock&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6CbhePrimaryBlock&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
114 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CbhePrimaryBlock&amp;&#32;operator=(Bpv6CbhePrimaryBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
115 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6CbhePrimaryBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;==</emphasis>
116 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6CbhePrimaryBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;!=</emphasis>
117 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetZero();
118 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization);&#32;<emphasis role="comment">//not&#32;const&#32;as&#32;it&#32;needs&#32;to&#32;modify&#32;m_blockLength</emphasis>
119 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;uint64_t&#32;GetSerializationSize()&#32;<emphasis role="keyword">const</emphasis>;
120 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DeserializeBpv6(<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;&amp;&#32;numBytesTakenToDecode,&#32;uint64_t&#32;bufferSize);
121 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;uint64_t&#32;GetSecondsSinceCreate()&#32;<emphasis role="keyword">const</emphasis>;
122 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;HasFlagSet(BPV6_BUNDLEFLAG&#32;flag)&#32;<emphasis role="keyword">const</emphasis>;
123 &#32;&#32;&#32;&#32;
129 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;<link linkend="_struct_bpv6_cbhe_primary_block_1a697968b82e02cfe9a9b7ef6cf51e63c8">bpv6_primary_block_print</link>()&#32;<emphasis role="keyword">const</emphasis>;
130 
131 &#32;&#32;&#32;&#32;
132 &#32;&#32;&#32;&#32;
133 
134 
135 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;HasCustodyFlagSet()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
136 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;HasFragmentationFlagSet()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
137 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<link linkend="_structcbhe__bundle__uuid__t">cbhe_bundle_uuid_t</link>&#32;GetCbheBundleUuidFragmentFromPrimary(uint64_t&#32;payloadSizeBytes)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
138 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<link linkend="_structcbhe__bundle__uuid__nofragment__t">cbhe_bundle_uuid_nofragment_t</link>&#32;GetCbheBundleUuidNoFragmentFromPrimary()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
139 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;GetFinalDestinationEid()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
140 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;GetSourceEid()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
141 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint8_t&#32;GetPriority()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
142 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetExpirationSeconds()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
143 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetSequenceForSecondsScale()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
144 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetExpirationMilliseconds()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
145 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetSequenceForMillisecondsScale()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
146 };
147 
148 <emphasis role="comment">//&#32;https://www.iana.org/assignments/bundle/bundle.xhtml#block-types</emphasis>
149 <emphasis role="keyword">enum&#32;class</emphasis>&#32;BPV6_BLOCK_TYPE_CODE&#32;:&#32;uint8_t&#32;{
150 &#32;&#32;&#32;&#32;PRIMARY_IMPLICIT_ZERO&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;0,
151 &#32;&#32;&#32;&#32;PAYLOAD&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1,
152 &#32;&#32;&#32;&#32;BUNDLE_AUTHENTICATION&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;2,
153 &#32;&#32;&#32;&#32;PAYLOAD_INTEGRITY&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;3,
154 &#32;&#32;&#32;&#32;PAYLOAD_CONFIDENTIALITY&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;4,
155 &#32;&#32;&#32;&#32;PREVIOUS_HOP_INSERTION&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;5,
156 &#32;&#32;&#32;&#32;UNUSED_6&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;6,
157 &#32;&#32;&#32;&#32;UNUSED_7&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;7,
158 &#32;&#32;&#32;&#32;METADATA_EXTENSION&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;8,
159 &#32;&#32;&#32;&#32;EXTENSION_SECURITY&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;9,
160 &#32;&#32;&#32;&#32;CUSTODY_TRANSFER_ENHANCEMENT&#32;&#32;&#32;&#32;&#32;&#32;=&#32;10,
161 &#32;&#32;&#32;&#32;UNUSED_11&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;11,
162 &#32;&#32;&#32;&#32;UNUSED_12&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;12,
163 &#32;&#32;&#32;&#32;BPLIB_BIB&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;13,
164 &#32;&#32;&#32;&#32;BUNDLE_AGE&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;20,
165 &#32;&#32;&#32;&#32;RESERVED_MAX_BLOCK_TYPES&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;21&#32;<emphasis role="comment">//for&#32;sizing&#32;lookup&#32;tables</emphasis>
166 };
167 MAKE_ENUM_SUPPORT_OSTREAM_OPERATOR(BPV6_BLOCK_TYPE_CODE);
168 
169 <emphasis role="keyword">enum&#32;class</emphasis>&#32;BPV6_BLOCKFLAG&#32;:&#32;uint64_t&#32;{
170 &#32;&#32;&#32;&#32;NO_FLAGS_SET&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;0,
171 &#32;&#32;&#32;&#32;MUST_BE_REPLICATED_IN_EVERY_FRAGMENT&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;0,
172 &#32;&#32;&#32;&#32;STATUS_REPORT_REQUESTED_IF_BLOCK_CANT_BE_PROCESSED&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;1,
173 &#32;&#32;&#32;&#32;DELETE_BUNDLE_IF_BLOCK_CANT_BE_PROCESSED&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;2,
174 &#32;&#32;&#32;&#32;IS_LAST_BLOCK&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;3,
175 &#32;&#32;&#32;&#32;DISCARD_BLOCK_IF_IT_CANT_BE_PROCESSED&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;4,
176 &#32;&#32;&#32;&#32;BLOCK_WAS_FORWARDED_WITHOUT_BEING_PROCESSED&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;5,
177 &#32;&#32;&#32;&#32;BLOCK_CONTAINS_AN_EID_REFERENCE_FIELD&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1&#32;&lt;&lt;&#32;6,
178 };
179 MAKE_ENUM_SUPPORT_FLAG_OPERATORS(BPV6_BLOCKFLAG);
180 MAKE_ENUM_SUPPORT_OSTREAM_OPERATOR(BPV6_BLOCKFLAG);
181 
185 <emphasis role="keyword">struct&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6CanonicalBlock&#32;{
186 &#32;&#32;&#32;&#32;BPV6_BLOCKFLAG&#32;m_blockProcessingControlFlags;
187 &#32;&#32;&#32;&#32;uint64_t&#32;m_blockTypeSpecificDataLength;
188 &#32;&#32;&#32;&#32;uint8_t&#32;*&#32;m_blockTypeSpecificDataPtr;&#32;<emphasis role="comment">//if&#32;NULL,&#32;data&#32;won&apos;t&#32;be&#32;copied&#32;(just&#32;allocated)</emphasis>
189 &#32;&#32;&#32;&#32;BPV6_BLOCK_TYPE_CODE&#32;m_blockTypeCode;&#32;<emphasis role="comment">//should&#32;be&#32;at&#32;beginning&#32;but&#32;here&#32;do&#32;to&#32;better&#32;packing</emphasis>
190 
191 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CanonicalBlock();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
192 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6CanonicalBlock();&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
193 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CanonicalBlock(<emphasis role="keyword">const</emphasis>&#32;Bpv6CanonicalBlock&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
194 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CanonicalBlock(Bpv6CanonicalBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
195 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CanonicalBlock&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6CanonicalBlock&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
196 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CanonicalBlock&amp;&#32;operator=(Bpv6CanonicalBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
197 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6CanonicalBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;==</emphasis>
198 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6CanonicalBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;!=</emphasis>
199 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetZero();
200 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization);&#32;<emphasis role="comment">//modifies&#32;m_blockTypeSpecificDataPtr&#32;to&#32;serialized&#32;location</emphasis>
201 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;uint64_t&#32;GetSerializationSize()&#32;<emphasis role="keyword">const</emphasis>;
202 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetCanonicalBlockTypeSpecificDataSerializationSize()&#32;<emphasis role="keyword">const</emphasis>;
203 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">static</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DeserializeBpv6(std::unique_ptr&lt;Bpv6CanonicalBlock&gt;&#32;&amp;&#32;canonicalPtr,&#32;<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;serialization,
204 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;uint64_t&#32;&amp;&#32;numBytesTakenToDecode,&#32;uint64_t&#32;bufferSize,&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;isAdminRecord,
205 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::unique_ptr&lt;Bpv6CanonicalBlock&gt;*&#32;blockNumberToRecycledCanonicalBlockArray,
206 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::unique_ptr&lt;Bpv6CanonicalBlock&gt;*&#32;recycledAdminRecord);
207 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Virtual_DeserializeExtensionBlockDataBpv6();
208 &#32;&#32;&#32;&#32;<emphasis role="comment">//virtual&#32;bool&#32;Virtual_DeserializeExtensionBlockDataBpv7();</emphasis>
214 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;<link linkend="_struct_bpv6_canonical_block_1a4d378932d1f160a78fa916cf52a604e9">bpv6_canonical_block_print</link>()&#32;<emphasis role="keyword">const</emphasis>;
215 
221 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;<link linkend="_struct_bpv6_canonical_block_1a40d08f135b83cde189c2ea40731cade7">bpv6_block_flags_print</link>()&#32;<emphasis role="keyword">const</emphasis>;
222 
223 };
224 
225 
226 <emphasis role="keyword">struct&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6CustodyTransferEnhancementBlock&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;Bpv6CanonicalBlock&#32;{
227 &#32;&#32;&#32;&#32;
228 <emphasis role="keyword">public</emphasis>:
229 &#32;&#32;&#32;&#32;<emphasis role="keyword">static</emphasis>&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keywordtype">unsigned</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;CBHE_MAX_SERIALIZATION_SIZE&#32;=
230 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;1&#32;+&#32;<emphasis role="comment">//block&#32;type</emphasis>
231 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//block&#32;flags&#32;sdnv&#32;+</emphasis>
232 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;1&#32;+&#32;<emphasis role="comment">//block&#32;length&#32;(1-byte-min-sized-sdnv)&#32;+</emphasis>
233 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//custody&#32;id&#32;sdnv&#32;+</emphasis>
234 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;45;&#32;<emphasis role="comment">//length&#32;of&#32;&quot;ipn:18446744073709551615.18446744073709551615&quot;&#32;(note&#32;45&#32;&gt;&#32;32&#32;so&#32;sdnv&#32;hardware&#32;acceleration&#32;overwrite&#32;is&#32;satisfied)</emphasis>
235 
236 &#32;&#32;&#32;&#32;uint64_t&#32;m_custodyId;
237 &#32;&#32;&#32;&#32;std::string&#32;m_ctebCreatorCustodianEidString;
238 
239 <emphasis role="keyword">public</emphasis>:
240 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CustodyTransferEnhancementBlock();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
241 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6CustodyTransferEnhancementBlock()&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
242 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CustodyTransferEnhancementBlock(<emphasis role="keyword">const</emphasis>&#32;Bpv6CustodyTransferEnhancementBlock&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
243 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CustodyTransferEnhancementBlock(Bpv6CustodyTransferEnhancementBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
244 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CustodyTransferEnhancementBlock&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6CustodyTransferEnhancementBlock&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
245 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6CustodyTransferEnhancementBlock&amp;&#32;operator=(Bpv6CustodyTransferEnhancementBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
246 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6CustodyTransferEnhancementBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;==</emphasis>
247 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6CustodyTransferEnhancementBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;!=</emphasis>
248 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetZero()&#32;<emphasis role="keyword">override</emphasis>;
249 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization)&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//modifies&#32;m_dataPtr&#32;to&#32;serialized&#32;location</emphasis>
250 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetCanonicalBlockTypeSpecificDataSerializationSize()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
251 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Virtual_DeserializeExtensionBlockDataBpv6()&#32;<emphasis role="keyword">override</emphasis>;
252 };
253 
254 <emphasis role="comment">//https://datatracker.ietf.org/doc/html/rfc6259</emphasis>
255 <emphasis role="keyword">struct&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6PreviousHopInsertionCanonicalBlock&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;Bpv6CanonicalBlock&#32;{
256 &#32;&#32;&#32;&#32;<emphasis role="keyword">static</emphasis>&#32;<emphasis role="keyword">constexpr</emphasis>&#32;uint64_t&#32;largestSerializedDataOnlySize&#32;=
257 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;4&#32;+&#32;<emphasis role="comment">//ipn\0</emphasis>
258 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;20&#32;+&#32;<emphasis role="comment">//&#32;18446744073709551615</emphasis>
259 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;1&#32;+&#32;<emphasis role="comment">//&#32;:</emphasis>
260 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;20&#32;+&#32;<emphasis role="comment">//&#32;18446744073709551615</emphasis>
261 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;1;&#32;<emphasis role="comment">//&#32;\0</emphasis>
262 
263 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6PreviousHopInsertionCanonicalBlock();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
264 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6PreviousHopInsertionCanonicalBlock()&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
265 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6PreviousHopInsertionCanonicalBlock(<emphasis role="keyword">const</emphasis>&#32;Bpv6PreviousHopInsertionCanonicalBlock&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
266 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6PreviousHopInsertionCanonicalBlock(Bpv6PreviousHopInsertionCanonicalBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
267 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6PreviousHopInsertionCanonicalBlock&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6PreviousHopInsertionCanonicalBlock&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
268 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6PreviousHopInsertionCanonicalBlock&amp;&#32;operator=(Bpv6PreviousHopInsertionCanonicalBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
269 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6PreviousHopInsertionCanonicalBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;==</emphasis>
270 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6PreviousHopInsertionCanonicalBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;!=</emphasis>
271 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetZero()&#32;<emphasis role="keyword">override</emphasis>;
272 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization)&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//modifies&#32;m_dataPtr&#32;to&#32;serialized&#32;location</emphasis>
273 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetCanonicalBlockTypeSpecificDataSerializationSize()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
274 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Virtual_DeserializeExtensionBlockDataBpv6()&#32;<emphasis role="keyword">override</emphasis>;
275 
276 &#32;&#32;&#32;&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;m_previousNode;
277 };
278 
279 <emphasis role="comment">//https://datatracker.ietf.org/doc/html/draft-irtf-dtnrg-bundle-age-block-01</emphasis>
280 <emphasis role="keyword">struct&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6BundleAgeCanonicalBlock&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;Bpv6CanonicalBlock&#32;{
281 &#32;&#32;&#32;&#32;<emphasis role="keyword">static</emphasis>&#32;<emphasis role="keyword">constexpr</emphasis>&#32;uint64_t&#32;largestSerializedDataOnlySize&#32;=&#32;10;&#32;<emphasis role="comment">//sdnv&#32;bundle&#32;age</emphasis>
282 
283 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6BundleAgeCanonicalBlock();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
284 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6BundleAgeCanonicalBlock()&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
285 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6BundleAgeCanonicalBlock(<emphasis role="keyword">const</emphasis>&#32;Bpv6BundleAgeCanonicalBlock&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
286 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6BundleAgeCanonicalBlock(Bpv6BundleAgeCanonicalBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
287 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6BundleAgeCanonicalBlock&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6BundleAgeCanonicalBlock&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
288 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6BundleAgeCanonicalBlock&amp;&#32;operator=(Bpv6BundleAgeCanonicalBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
289 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6BundleAgeCanonicalBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;==</emphasis>
290 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6BundleAgeCanonicalBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;!=</emphasis>
291 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetZero()&#32;<emphasis role="keyword">override</emphasis>;
292 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization)&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//modifies&#32;m_dataPtr&#32;to&#32;serialized&#32;location</emphasis>
293 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetCanonicalBlockTypeSpecificDataSerializationSize()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
294 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Virtual_DeserializeExtensionBlockDataBpv6()&#32;<emphasis role="keyword">override</emphasis>;
295 
296 &#32;&#32;&#32;&#32;uint64_t&#32;m_bundleAgeMicroseconds;
297 };
298 
299 <emphasis role="keyword">enum&#32;class</emphasis>&#32;BPV6_METADATA_TYPE_CODE&#32;:&#32;uint64_t&#32;{
300 &#32;&#32;&#32;&#32;UNDEFINED_ZERO&#32;=&#32;0,
301 &#32;&#32;&#32;&#32;URI&#32;=&#32;1
302 };
303 MAKE_ENUM_SUPPORT_OSTREAM_OPERATOR(BPV6_METADATA_TYPE_CODE);
304 
305 <emphasis role="keyword">struct&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;<link linkend="_struct_bpv6_metadata_content_base">Bpv6MetadataContentBase</link>&#32;{
306 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<link linkend="_struct_bpv6_metadata_content_base">~Bpv6MetadataContentBase</link>()&#32;=&#32;0;&#32;<emphasis role="comment">//&#32;Pure&#32;virtual&#32;destructor</emphasis>
307 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">const</emphasis>&#32;=&#32;0;
308 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetSerializationSize()&#32;<emphasis role="keyword">const</emphasis>&#32;=&#32;0;
309 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DeserializeBpv6(<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;&amp;&#32;numBytesTakenToDecode,&#32;uint64_t&#32;bufferSize)&#32;=&#32;0;
310 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;IsEqual(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_bpv6_metadata_content_base">Bpv6MetadataContentBase</link>&#32;*&#32;otherPtr)&#32;<emphasis role="keyword">const</emphasis>&#32;=&#32;0;
311 };
312 
313 <emphasis role="keyword">class&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6MetadataContentUriList&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;<link linkend="_struct_bpv6_metadata_content_base">Bpv6MetadataContentBase</link>&#32;{
314 <emphasis role="keyword">public</emphasis>:
315 &#32;&#32;&#32;&#32;std::vector&lt;cbhe_eid_t&gt;&#32;m_uriArray;
316 
317 <emphasis role="keyword">public</emphasis>:
318 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataContentUriList();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
319 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6MetadataContentUriList()&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
320 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataContentUriList(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataContentUriList&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
321 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataContentUriList(Bpv6MetadataContentUriList&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
322 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataContentUriList&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataContentUriList&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
323 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataContentUriList&amp;&#32;operator=(Bpv6MetadataContentUriList&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
324 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataContentUriList&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;
325 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataContentUriList&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;
326 
327 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
328 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetSerializationSize()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
329 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DeserializeBpv6(<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;&amp;&#32;numBytesTakenToDecode,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">override</emphasis>;
330 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;IsEqual(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_bpv6_metadata_content_base">Bpv6MetadataContentBase</link>&#32;*&#32;otherPtr)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
331 
332 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;Reset();
333 };
334 
335 <emphasis role="keyword">class&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6MetadataContentGeneric&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;<link linkend="_struct_bpv6_metadata_content_base">Bpv6MetadataContentBase</link>&#32;{
336 <emphasis role="keyword">public</emphasis>:
337 &#32;&#32;&#32;&#32;std::vector&lt;uint8_t&gt;&#32;m_genericRawMetadata;
338 
339 <emphasis role="keyword">public</emphasis>:
340 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataContentGeneric();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
341 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6MetadataContentGeneric()&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
342 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataContentGeneric(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataContentGeneric&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
343 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataContentGeneric(Bpv6MetadataContentGeneric&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
344 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataContentGeneric&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataContentGeneric&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
345 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataContentGeneric&amp;&#32;operator=(Bpv6MetadataContentGeneric&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
346 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataContentGeneric&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;
347 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataContentGeneric&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;
348 
349 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
350 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetSerializationSize()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
351 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DeserializeBpv6(<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;&amp;&#32;numBytesTakenToDecode,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">override</emphasis>;
352 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;IsEqual(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_bpv6_metadata_content_base">Bpv6MetadataContentBase</link>&#32;*&#32;otherPtr)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
353 
354 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;Reset();
355 };
356 
357 <emphasis role="comment">//https://datatracker.ietf.org/doc/html/rfc6258</emphasis>
358 <emphasis role="keyword">struct&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6MetadataCanonicalBlock&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;Bpv6CanonicalBlock&#32;{
359 
360 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataCanonicalBlock();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
361 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6MetadataCanonicalBlock()&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
362 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataCanonicalBlock(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataCanonicalBlock&amp;&#32;o)&#32;=&#32;<emphasis role="keyword">delete</emphasis>;&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
363 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataCanonicalBlock(Bpv6MetadataCanonicalBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
364 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataCanonicalBlock&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataCanonicalBlock&amp;&#32;o)&#32;=&#32;<emphasis role="keyword">delete</emphasis>;&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
365 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6MetadataCanonicalBlock&amp;&#32;operator=(Bpv6MetadataCanonicalBlock&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
366 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataCanonicalBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;==</emphasis>
367 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6MetadataCanonicalBlock&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;!=</emphasis>
368 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetZero()&#32;<emphasis role="keyword">override</emphasis>;
369 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization)&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//modifies&#32;m_dataPtr&#32;to&#32;serialized&#32;location</emphasis>
370 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetCanonicalBlockTypeSpecificDataSerializationSize()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
371 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Virtual_DeserializeExtensionBlockDataBpv6()&#32;<emphasis role="keyword">override</emphasis>;
372 
373 &#32;&#32;&#32;&#32;BPV6_METADATA_TYPE_CODE&#32;m_metadataTypeCode;
374 &#32;&#32;&#32;&#32;std::unique_ptr&lt;Bpv6MetadataContentBase&gt;&#32;m_metadataContentPtr;
375 };
376 
377 <emphasis role="comment">//Administrative&#32;record&#32;types</emphasis>
378 <emphasis role="keyword">enum&#32;class</emphasis>&#32;BPV6_ADMINISTRATIVE_RECORD_TYPE_CODE&#32;:&#32;uint8_t&#32;{
379 &#32;&#32;&#32;&#32;UNUSED_ZERO&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;0,
380 &#32;&#32;&#32;&#32;BUNDLE_STATUS_REPORT&#32;&#32;&#32;&#32;&#32;=&#32;1,
381 &#32;&#32;&#32;&#32;CUSTODY_SIGNAL&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;2,
382 &#32;&#32;&#32;&#32;AGGREGATE_CUSTODY_SIGNAL&#32;=&#32;4,
383 &#32;&#32;&#32;&#32;ENCAPSULATED_BUNDLE&#32;&#32;&#32;&#32;&#32;&#32;=&#32;7,
384 &#32;&#32;&#32;&#32;SAGA_MESSAGE&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;42
385 };
386 MAKE_ENUM_SUPPORT_OSTREAM_OPERATOR(BPV6_ADMINISTRATIVE_RECORD_TYPE_CODE);
387 
388 <emphasis role="comment">//Administrative&#32;record&#32;flags</emphasis>
389 <emphasis role="keyword">enum&#32;class</emphasis>&#32;BPV6_ADMINISTRATIVE_RECORD_FLAGS&#32;:&#32;uint8_t&#32;{
390 &#32;&#32;&#32;&#32;BUNDLE_IS_A_FRAGMENT&#32;=&#32;1&#32;<emphasis role="comment">//00000001</emphasis>
391 };
392 MAKE_ENUM_SUPPORT_FLAG_OPERATORS(BPV6_ADMINISTRATIVE_RECORD_FLAGS);
393 MAKE_ENUM_SUPPORT_OSTREAM_OPERATOR(BPV6_ADMINISTRATIVE_RECORD_FLAGS);
394 
395 
396 <emphasis role="keyword">enum&#32;class</emphasis>&#32;BPV6_BUNDLE_STATUS_REPORT_STATUS_FLAGS&#32;:&#32;uint8_t&#32;{
397 &#32;&#32;&#32;&#32;NO_FLAGS_SET&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;0,
398 &#32;&#32;&#32;&#32;REPORTING_NODE_RECEIVED_BUNDLE&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;(1&#32;&lt;&lt;&#32;0),
399 &#32;&#32;&#32;&#32;REPORTING_NODE_ACCEPTED_CUSTODY_OF_BUNDLE&#32;&#32;&#32;&#32;=&#32;(1&#32;&lt;&lt;&#32;1),
400 &#32;&#32;&#32;&#32;REPORTING_NODE_FORWARDED_BUNDLE&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;(1&#32;&lt;&lt;&#32;2),
401 &#32;&#32;&#32;&#32;REPORTING_NODE_DELIVERED_BUNDLE&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;(1&#32;&lt;&lt;&#32;3),
402 &#32;&#32;&#32;&#32;REPORTING_NODE_DELETED_BUNDLE&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;(1&#32;&lt;&lt;&#32;4),
403 };
404 MAKE_ENUM_SUPPORT_FLAG_OPERATORS(BPV6_BUNDLE_STATUS_REPORT_STATUS_FLAGS);
405 MAKE_ENUM_SUPPORT_OSTREAM_OPERATOR(BPV6_BUNDLE_STATUS_REPORT_STATUS_FLAGS);
406 
407 <emphasis role="keyword">enum&#32;class</emphasis>&#32;BPV6_BUNDLE_STATUS_REPORT_REASON_CODES&#32;:&#32;uint8_t&#32;{
408 &#32;&#32;&#32;&#32;NO_ADDITIONAL_INFORMATION&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;0,
409 &#32;&#32;&#32;&#32;LIFETIME_EXPIRED&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;1,
410 &#32;&#32;&#32;&#32;FORWARDED_OVER_UNIDIRECTIONAL_LINK&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;2,
411 &#32;&#32;&#32;&#32;TRANSMISSION_CANCELLED&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;3,
412 &#32;&#32;&#32;&#32;DEPLETED_STORAGE&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;4,
413 &#32;&#32;&#32;&#32;DESTINATION_ENDPOINT_ID_UNINTELLIGIBLE&#32;&#32;&#32;&#32;&#32;&#32;=&#32;5,
414 &#32;&#32;&#32;&#32;NO_KNOWN_ROUTE_TO_DESTINATION_FROM_HERE&#32;&#32;&#32;&#32;&#32;=&#32;6,
415 &#32;&#32;&#32;&#32;NO_TIMELY_CONTACT_WITH_NEXT_NODE_ON_ROUTE&#32;&#32;&#32;=&#32;7,
416 &#32;&#32;&#32;&#32;BLOCK_UNINTELLIGIBLE&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;8
417 };
418 MAKE_ENUM_SUPPORT_OSTREAM_OPERATOR(BPV6_BUNDLE_STATUS_REPORT_REASON_CODES);
419 
420 <emphasis role="keyword">enum&#32;class</emphasis>&#32;BPV6_CUSTODY_SIGNAL_REASON_CODES_7BIT&#32;:&#32;uint8_t&#32;{
421 &#32;&#32;&#32;&#32;NO_ADDITIONAL_INFORMATION&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;0,
422 &#32;&#32;&#32;&#32;REDUNDANT_RECEPTION&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;3,
423 &#32;&#32;&#32;&#32;DEPLETED_STORAGE&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;4,
424 &#32;&#32;&#32;&#32;DESTINATION_ENDPOINT_ID_UNINTELLIGIBLE&#32;&#32;&#32;&#32;&#32;&#32;=&#32;5,
425 &#32;&#32;&#32;&#32;NO_KNOWN_ROUTE_TO_DESTINATION_FROM_HERE&#32;&#32;&#32;&#32;&#32;=&#32;6,
426 &#32;&#32;&#32;&#32;NO_TIMELY_CONTACT_WITH_NEXT_NODE_ON_ROUTE&#32;&#32;&#32;=&#32;7,
427 &#32;&#32;&#32;&#32;BLOCK_UNINTELLIGIBLE&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;=&#32;8
428 };
429 MAKE_ENUM_SUPPORT_OSTREAM_OPERATOR(BPV6_CUSTODY_SIGNAL_REASON_CODES_7BIT);
430 
431 
432 <emphasis role="keyword">struct&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;<link linkend="_struct_bpv6_administrative_record_content_base">Bpv6AdministrativeRecordContentBase</link>&#32;{
433 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<link linkend="_struct_bpv6_administrative_record_content_base">~Bpv6AdministrativeRecordContentBase</link>()&#32;=&#32;0;&#32;<emphasis role="comment">//&#32;Pure&#32;virtual&#32;destructor</emphasis>
434 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">const</emphasis>&#32;=&#32;0;
435 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetSerializationSize()&#32;<emphasis role="keyword">const</emphasis>&#32;=&#32;0;
436 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DeserializeBpv6(<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;&amp;&#32;numBytesTakenToDecode,&#32;uint64_t&#32;bufferSize)&#32;=&#32;0;
437 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;IsEqual(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_bpv6_administrative_record_content_base">Bpv6AdministrativeRecordContentBase</link>&#32;*&#32;otherPtr)&#32;<emphasis role="keyword">const</emphasis>&#32;=&#32;0;
438 };
439 
440 <emphasis role="keyword">class&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6AdministrativeRecordContentBundleStatusReport&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;<link linkend="_struct_bpv6_administrative_record_content_base">Bpv6AdministrativeRecordContentBase</link>&#32;{
441 
442 
443 <emphasis role="keyword">public</emphasis>:
444 &#32;&#32;&#32;&#32;<emphasis role="keyword">static</emphasis>&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keywordtype">unsigned</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;CBHE_MAX_SERIALIZATION_SIZE&#32;=
445 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;3&#32;+&#32;<emphasis role="comment">//admin&#32;flags&#32;+&#32;status&#32;flags&#32;+&#32;reason&#32;code</emphasis>
446 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//fragmentOffsetSdnv.length&#32;+</emphasis>
447 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//fragmentLengthSdnv.length&#32;+</emphasis>
448 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//receiptTimeSecondsSdnv.length&#32;+</emphasis>
449 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;5&#32;+&#32;<emphasis role="comment">//receiptTimeNanosecSdnv.length&#32;+</emphasis>
450 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//custodyTimeSecondsSdnv.length&#32;+</emphasis>
451 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;5&#32;+&#32;<emphasis role="comment">//custodyTimeNanosecSdnv.length&#32;+</emphasis>
452 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//forwardTimeSecondsSdnv.length&#32;+</emphasis>
453 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;5&#32;+&#32;<emphasis role="comment">//forwardTimeNanosecSdnv.length&#32;+</emphasis>
454 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//deliveryTimeSecondsSdnv.length&#32;+</emphasis>
455 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;5&#32;+&#32;<emphasis role="comment">//deliveryTimeNanosecSdnv.length&#32;+</emphasis>
456 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//deletionTimeSecondsSdnv.length&#32;+</emphasis>
457 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;5&#32;+&#32;<emphasis role="comment">//deletionTimeNanosecSdnv.length&#32;+</emphasis>
458 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//creationTimeSecondsSdnv.length&#32;+</emphasis>
459 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//creationTimeCountSdnv.length&#32;+</emphasis>
460 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;1&#32;+&#32;<emphasis role="comment">//eidLengthSdnv.length&#32;+</emphasis>
461 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;45;&#32;<emphasis role="comment">//length&#32;of&#32;&quot;ipn:18446744073709551615.18446744073709551615&quot;&#32;(note&#32;45&#32;&gt;&#32;32&#32;so&#32;sdnv&#32;hardware&#32;acceleration&#32;overwrite&#32;is&#32;satisfied)</emphasis>
462 &#32;&#32;&#32;&#32;BPV6_BUNDLE_STATUS_REPORT_STATUS_FLAGS&#32;m_statusFlags;
463 &#32;&#32;&#32;&#32;BPV6_BUNDLE_STATUS_REPORT_REASON_CODES&#32;m_reasonCode;
464 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;m_isFragment;
465 &#32;&#32;&#32;&#32;uint64_t&#32;m_fragmentOffsetIfPresent;
466 &#32;&#32;&#32;&#32;uint64_t&#32;m_fragmentLengthIfPresent;
467 
468 &#32;&#32;&#32;&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;m_timeOfReceiptOfBundle;
469 &#32;&#32;&#32;&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;m_timeOfCustodyAcceptanceOfBundle;
470 &#32;&#32;&#32;&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;m_timeOfForwardingOfBundle;
471 &#32;&#32;&#32;&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;m_timeOfDeliveryOfBundle;
472 &#32;&#32;&#32;&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;m_timeOfDeletionOfBundle;
473 
474 &#32;&#32;&#32;&#32;<emphasis role="comment">//from&#32;primary&#32;block&#32;of&#32;subject&#32;bundle</emphasis>
475 &#32;&#32;&#32;&#32;<link linkend="_struct_timestamp_util_1_1bpv6__creation__timestamp__t">TimestampUtil::bpv6_creation_timestamp_t</link>&#32;m_copyOfBundleCreationTimestamp;
476 
477 &#32;&#32;&#32;&#32;std::string&#32;m_bundleSourceEid;
478 
479 <emphasis role="keyword">public</emphasis>:
480 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentBundleStatusReport();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
481 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6AdministrativeRecordContentBundleStatusReport()&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
482 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentBundleStatusReport(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentBundleStatusReport&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
483 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentBundleStatusReport(Bpv6AdministrativeRecordContentBundleStatusReport&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
484 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentBundleStatusReport&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentBundleStatusReport&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
485 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentBundleStatusReport&amp;&#32;operator=(Bpv6AdministrativeRecordContentBundleStatusReport&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
486 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentBundleStatusReport&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;
487 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentBundleStatusReport&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;
488 
489 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
490 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetSerializationSize()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
491 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DeserializeBpv6(<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;&amp;&#32;numBytesTakenToDecode,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">override</emphasis>;
492 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;IsEqual(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_bpv6_administrative_record_content_base">Bpv6AdministrativeRecordContentBase</link>&#32;*&#32;otherPtr)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
493 
494 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;Reset();
495 
496 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetTimeOfReceiptOfBundleAndStatusFlag(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;&amp;&#32;dtnTime);
497 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetTimeOfCustodyAcceptanceOfBundleAndStatusFlag(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;&amp;&#32;dtnTime);
498 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetTimeOfForwardingOfBundleAndStatusFlag(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;&amp;&#32;dtnTime);
499 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetTimeOfDeliveryOfBundleAndStatusFlag(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;&amp;&#32;dtnTime);
500 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetTimeOfDeletionOfBundleAndStatusFlag(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;&amp;&#32;dtnTime);
501 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;HasBundleStatusReportStatusFlagSet(<emphasis role="keyword">const</emphasis>&#32;BPV6_BUNDLE_STATUS_REPORT_STATUS_FLAGS&#32;&amp;&#32;flag)&#32;<emphasis role="keyword">const</emphasis>;
502 };
503 
504 <emphasis role="keyword">class&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6AdministrativeRecordContentCustodySignal&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;<link linkend="_struct_bpv6_administrative_record_content_base">Bpv6AdministrativeRecordContentBase</link>&#32;{
505 &#32;&#32;&#32;&#32;
506 
507 <emphasis role="keyword">public</emphasis>:
508 &#32;&#32;&#32;&#32;<emphasis role="keyword">static</emphasis>&#32;<emphasis role="keyword">constexpr</emphasis>&#32;<emphasis role="keywordtype">unsigned</emphasis>&#32;<emphasis role="keywordtype">int</emphasis>&#32;CBHE_MAX_SERIALIZATION_SIZE&#32;=
509 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;2&#32;+&#32;<emphasis role="comment">//admin&#32;flags&#32;+&#32;(bit7&#32;status&#32;flags&#32;|&#32;&#32;bit&#32;6..0&#32;reason&#32;code)</emphasis>
510 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//fragmentOffsetSdnv.length&#32;+</emphasis>
511 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//fragmentLengthSdnv.length&#32;+</emphasis>
512 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//signalTimeSecondsSdnv.length&#32;+</emphasis>
513 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;5&#32;+&#32;<emphasis role="comment">//signalTimeNanosecSdnv.length&#32;+</emphasis>
514 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//creationTimeSecondsSdnv.length&#32;+</emphasis>
515 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;10&#32;+&#32;<emphasis role="comment">//creationTimeCountSdnv.length&#32;+</emphasis>
516 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;1&#32;+&#32;<emphasis role="comment">//eidLengthSdnv.length&#32;+</emphasis>
517 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;45;&#32;<emphasis role="comment">//length&#32;of&#32;&quot;ipn:18446744073709551615.18446744073709551615&quot;&#32;(note&#32;45&#32;&gt;&#32;32&#32;so&#32;sdnv&#32;hardware&#32;acceleration&#32;overwrite&#32;is&#32;satisfied)</emphasis>
518 <emphasis role="keyword">private</emphasis>:
519 &#32;&#32;&#32;&#32;uint8_t&#32;m_statusFlagsPlus7bitReasonCode;
520 <emphasis role="keyword">public</emphasis>:
521 &#32;&#32;&#32;&#32;
522 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;m_isFragment;
523 &#32;&#32;&#32;&#32;uint64_t&#32;m_fragmentOffsetIfPresent;
524 &#32;&#32;&#32;&#32;uint64_t&#32;m_fragmentLengthIfPresent;
525 
526 &#32;&#32;&#32;&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;m_timeOfSignalGeneration;
527 
528 &#32;&#32;&#32;&#32;<emphasis role="comment">//from&#32;primary&#32;block&#32;of&#32;subject&#32;bundle</emphasis>
529 &#32;&#32;&#32;&#32;<link linkend="_struct_timestamp_util_1_1bpv6__creation__timestamp__t">TimestampUtil::bpv6_creation_timestamp_t</link>&#32;m_copyOfBundleCreationTimestamp;
530 
531 &#32;&#32;&#32;&#32;std::string&#32;m_bundleSourceEid;
532 
533 <emphasis role="keyword">public</emphasis>:
534 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentCustodySignal();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
535 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6AdministrativeRecordContentCustodySignal()&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
536 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentCustodySignal(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentCustodySignal&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
537 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentCustodySignal(Bpv6AdministrativeRecordContentCustodySignal&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
538 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentCustodySignal&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentCustodySignal&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
539 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentCustodySignal&amp;&#32;operator=(Bpv6AdministrativeRecordContentCustodySignal&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
540 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentCustodySignal&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;
541 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentCustodySignal&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;
542 
543 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
544 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetSerializationSize()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
545 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DeserializeBpv6(<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;&amp;&#32;numBytesTakenToDecode,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">override</emphasis>;
546 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;IsEqual(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_bpv6_administrative_record_content_base">Bpv6AdministrativeRecordContentBase</link>&#32;*&#32;otherPtr)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
547 
548 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;Reset();
549 
550 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetTimeOfSignalGeneration(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_timestamp_util_1_1dtn__time__t">TimestampUtil::dtn_time_t</link>&#32;&amp;&#32;dtnTime);
551 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetCustodyTransferStatusAndReason(<emphasis role="keywordtype">bool</emphasis>&#32;custodyTransferSucceeded,&#32;BPV6_CUSTODY_SIGNAL_REASON_CODES_7BIT&#32;reasonCode7bit);
552 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DidCustodyTransferSucceed()&#32;<emphasis role="keyword">const</emphasis>;
553 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;BPV6_CUSTODY_SIGNAL_REASON_CODES_7BIT&#32;GetReasonCode()&#32;<emphasis role="keyword">const</emphasis>;
554 };
555 
556 <emphasis role="keyword">class&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6AdministrativeRecordContentAggregateCustodySignal&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;<link linkend="_struct_bpv6_administrative_record_content_base">Bpv6AdministrativeRecordContentBase</link>&#32;{
557 &#32;&#32;&#32;&#32;
558 <emphasis role="keyword">private</emphasis>:
559 &#32;&#32;&#32;&#32;<emphasis role="comment">//The&#32;second&#32;field&#32;shall&#32;be&#32;a&#32;�Status�&#32;byte&#32;encoded&#32;in&#32;the&#32;same&#32;way&#32;as&#32;the&#32;status&#32;byte</emphasis>
560 &#32;&#32;&#32;&#32;<emphasis role="comment">//for&#32;administrative&#32;records&#32;in&#32;RFC&#32;5050,&#32;using&#32;the&#32;same&#32;reason&#32;codes</emphasis>
561 &#32;&#32;&#32;&#32;uint8_t&#32;m_statusFlagsPlus7bitReasonCode;
562 <emphasis role="keyword">public</emphasis>:
563 &#32;&#32;&#32;&#32;FragmentSet::data_fragment_set_t&#32;m_custodyIdFills;
564 
565 <emphasis role="keyword">public</emphasis>:
566 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentAggregateCustodySignal();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
567 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6AdministrativeRecordContentAggregateCustodySignal()&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
568 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentAggregateCustodySignal(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentAggregateCustodySignal&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
569 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentAggregateCustodySignal(Bpv6AdministrativeRecordContentAggregateCustodySignal&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
570 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentAggregateCustodySignal&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentAggregateCustodySignal&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
571 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecordContentAggregateCustodySignal&amp;&#32;operator=(Bpv6AdministrativeRecordContentAggregateCustodySignal&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
572 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentAggregateCustodySignal&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;
573 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecordContentAggregateCustodySignal&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;
574 
575 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
576 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetSerializationSize()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
577 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DeserializeBpv6(<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;&amp;&#32;numBytesTakenToDecode,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">override</emphasis>;
578 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;IsEqual(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_bpv6_administrative_record_content_base">Bpv6AdministrativeRecordContentBase</link>&#32;*&#32;otherPtr)&#32;<emphasis role="keyword">const&#32;override</emphasis>;
579 
580 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;Reset();
581 
582 &#32;&#32;&#32;&#32;
583 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetCustodyTransferStatusAndReason(<emphasis role="keywordtype">bool</emphasis>&#32;custodyTransferSucceeded,&#32;BPV6_CUSTODY_SIGNAL_REASON_CODES_7BIT&#32;reasonCode7bit);
584 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DidCustodyTransferSucceed()&#32;<emphasis role="keyword">const</emphasis>;
585 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;BPV6_CUSTODY_SIGNAL_REASON_CODES_7BIT&#32;GetReasonCode()&#32;<emphasis role="keyword">const</emphasis>;
586 &#32;&#32;&#32;&#32;<emphasis role="comment">//return&#32;number&#32;of&#32;fills</emphasis>
587 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;uint64_t&#32;AddCustodyIdToFill(<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;custodyId);
588 &#32;&#32;&#32;&#32;<emphasis role="comment">//return&#32;number&#32;of&#32;fills</emphasis>
589 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;uint64_t&#32;AddContiguousCustodyIdsToFill(<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;firstCustodyId,&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;lastCustodyId);
590 <emphasis role="keyword">public</emphasis>:&#32;<emphasis role="comment">//only&#32;public&#32;for&#32;unit&#32;testing</emphasis>
591 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;uint64_t&#32;SerializeFills(uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;bufferSize)&#32;<emphasis role="keyword">const</emphasis>;
592 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;uint64_t&#32;GetFillSerializedSize()&#32;<emphasis role="keyword">const</emphasis>;
593 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;DeserializeFills(<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;serialization,&#32;uint64_t&#32;&amp;&#32;numBytesTakenToDecode,&#32;uint64_t&#32;bufferSize);
594 };
595 
596 <emphasis role="keyword">struct&#32;</emphasis>CLASS_VISIBILITY_BPCODEC&#32;Bpv6AdministrativeRecord&#32;:&#32;<emphasis role="keyword">public</emphasis>&#32;Bpv6CanonicalBlock&#32;{
597 
598 &#32;&#32;&#32;&#32;BPV6_ADMINISTRATIVE_RECORD_TYPE_CODE&#32;m_adminRecordTypeCode;
599 &#32;&#32;&#32;&#32;std::unique_ptr&lt;Bpv6AdministrativeRecordContentBase&gt;&#32;m_adminRecordContentPtr;
600 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;m_isFragment;
601 
602 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecord();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
603 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~Bpv6AdministrativeRecord()&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
604 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecord(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecord&amp;&#32;o)&#32;=&#32;<emphasis role="keyword">delete</emphasis>;&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
605 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecord(Bpv6AdministrativeRecord&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
606 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecord&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecord&amp;&#32;o)&#32;=&#32;<emphasis role="keyword">delete</emphasis>;&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
607 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;Bpv6AdministrativeRecord&amp;&#32;operator=(Bpv6AdministrativeRecord&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
608 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecord&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;==</emphasis>
609 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;Bpv6AdministrativeRecord&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;!=</emphasis>
610 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetZero()&#32;<emphasis role="keyword">override</emphasis>;
611 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;SerializeBpv6(uint8_t&#32;*&#32;serialization)&#32;<emphasis role="keyword">override</emphasis>;&#32;<emphasis role="comment">//modifies&#32;m_dataPtr&#32;to&#32;serialized&#32;location</emphasis>
612 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;uint64_t&#32;GetCanonicalBlockTypeSpecificDataSerializationSize()&#32;<emphasis role="keyword">const&#32;override</emphasis>;
613 &#32;&#32;&#32;&#32;BPCODEC_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Virtual_DeserializeExtensionBlockDataBpv6()&#32;<emphasis role="keyword">override</emphasis>;
614 };
615 
616 
617 
618 <emphasis role="preprocessor">#endif&#32;</emphasis><emphasis role="comment">//BPV6_H</emphasis>
</programlisting></section>
