<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="__free_list_allocator_8h_source" xml:lang="en-US">
<title>FreeListAllocator.h</title>
<indexterm><primary>common/util/include/FreeListAllocator.h</primary></indexterm>
Go to the documentation of this file.<programlisting linenumbering="unnumbered">1 
19 
20 <emphasis role="preprocessor">#ifndef&#32;FREE_LIST_ALLOCATOR_H</emphasis>
21 <emphasis role="preprocessor">#define&#32;FREE_LIST_ALLOCATOR_H&#32;1</emphasis>
22 
23 &#32;<emphasis role="comment">/*</emphasis>
24 <emphasis role="comment">&#32;//motivation&#32;behind&#32;FreeListAllocator&#32;is&#32;as&#32;follows,&#32;the&#32;first&#32;is&#32;fastest&#32;and&#32;doesn&apos;t&#32;spend&#32;time&#32;in&#32;operator&#32;new</emphasis>
25 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;{&#32;//fastest&#32;by&#32;far</emphasis>
26 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::set&lt;int,&#32;std::less&lt;int&gt;,&#32;FreeListAllocator&lt;int&gt;&#32;&gt;&#32;s;</emphasis>
27 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;boost::timer::auto_cpu_timer&#32;timer;</emphasis>
28 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;for&#32;(unsigned&#32;int&#32;i&#32;=&#32;0;&#32;i&#32;&lt;&#32;10000000;&#32;++i)&#32;{</emphasis>
29 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;s.insert(5);&#32;s.insert(10);&#32;s.insert(2);&#32;s.clear();</emphasis>
30 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}</emphasis>
31 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;},</emphasis>
32 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;{&#32;//slowest</emphasis>
33 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::set&lt;int&gt;&#32;s;</emphasis>
34 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;boost::timer::auto_cpu_timer&#32;timer;</emphasis>
35 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;for&#32;(unsigned&#32;int&#32;i&#32;=&#32;0;&#32;i&#32;&lt;&#32;10000000;&#32;++i)&#32;{</emphasis>
36 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;s.insert(5);&#32;s.insert(10);&#32;s.insert(2);&#32;s.clear();</emphasis>
37 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}</emphasis>
38 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;}</emphasis>
39 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;{&#32;//second&#32;fastest</emphasis>
40 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;boost::container::set&lt;int,&#32;std::less&lt;int&gt;,&#32;boost::container::adaptive_pool&lt;int&gt;&#32;&gt;&#32;s;</emphasis>
41 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;boost::timer::auto_cpu_timer&#32;timer;</emphasis>
42 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;for&#32;(unsigned&#32;int&#32;i&#32;=&#32;0;&#32;i&#32;&lt;&#32;10000000;&#32;++i)&#32;{</emphasis>
43 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;s.insert(5);&#32;s.insert(10);&#32;s.insert(2);&#32;s.clear();</emphasis>
44 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}</emphasis>
45 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;}</emphasis>
46 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;{&#32;//slow</emphasis>
47 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::set&lt;int,&#32;std::less&lt;int&gt;,&#32;boost::container::adaptive_pool&lt;int&gt;&#32;&gt;&#32;s;</emphasis>
48 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;boost::timer::auto_cpu_timer&#32;timer;</emphasis>
49 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;for&#32;(unsigned&#32;int&#32;i&#32;=&#32;0;&#32;i&#32;&lt;&#32;10000000;&#32;++i)&#32;{</emphasis>
50 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;s.insert(5);&#32;s.insert(10);&#32;s.insert(2);&#32;s.clear();</emphasis>
51 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}</emphasis>
52 <emphasis role="comment">&#32;&#32;&#32;&#32;&#32;}</emphasis>
53 <emphasis role="comment">&#32;*/</emphasis>
54 
55 <emphasis role="preprocessor">#include&#32;&lt;algorithm&gt;</emphasis>
56 <emphasis role="preprocessor">#include&#32;&lt;iostream&gt;</emphasis>
57 <emphasis role="preprocessor">#include&#32;&lt;memory&gt;</emphasis>
58 <emphasis role="preprocessor">#include&#32;&lt;type_traits&gt;</emphasis>
59 <emphasis role="preprocessor">#include&#32;&lt;utility&gt;</emphasis>
60 
61 <emphasis role="keyword">template</emphasis>&#32;&lt;<emphasis role="keyword">typename</emphasis>&#32;T,&#32;std::<emphasis role="keywordtype">size_t</emphasis>&#32;MaxListSize&#32;=&#32;100&gt;
62 <emphasis role="keyword">class&#32;</emphasis>FreeListAllocator&#32;{
63 &#32;&#32;&#32;&#32;<emphasis role="keyword">union&#32;</emphasis>node&#32;{
64 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node*&#32;next;
65 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typename</emphasis>&#32;std::aligned_storage&lt;<emphasis role="keyword">sizeof</emphasis>(T),&#32;<emphasis role="keyword">alignof</emphasis>(T)&gt;::type&#32;storage;
66 &#32;&#32;&#32;&#32;};
67 &#32;&#32;&#32;&#32;
68 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;clear()&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{
69 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node*&#32;p&#32;=&#32;list;
70 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">while</emphasis>&#32;(p)&#32;{
71 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node*&#32;tmp&#32;=&#32;p;
72 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;p&#32;=&#32;p-&gt;next;
73 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">delete</emphasis>&#32;tmp;
74 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
75 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;list&#32;=&#32;<emphasis role="keyword">nullptr</emphasis>;
76 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSize&#32;=&#32;0;
77 &#32;&#32;&#32;&#32;}
78 
79 &#32;&#32;&#32;&#32;<emphasis role="comment">//This&#32;technique&#32;is&#32;useful&#32;in&#32;classes&#32;that&#32;have&#32;multiple&#32;constructors</emphasis>
80 &#32;&#32;&#32;&#32;node*&#32;list&#32;=&#32;<emphasis role="keyword">nullptr</emphasis>;
81 <emphasis role="keyword">public</emphasis>:
82 &#32;&#32;&#32;&#32;std::size_t&#32;listSize&#32;=&#32;0;
83 &#32;&#32;&#32;&#32;std::size_t&#32;listSizeCopy&#32;=&#32;0;&#32;<emphasis role="comment">//for&#32;unit&#32;testing</emphasis>
84 
85 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>value_type&#32;=&#32;T;
86 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>size_type&#32;=&#32;std::size_t;
87 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>propagate_on_container_move_assignment&#32;=&#32;std::true_type;
88 
89 &#32;&#32;&#32;&#32;<emphasis role="comment">//the&#32;three&#32;constructor&#32;versions&#32;must&#32;be&#32;defined&#32;(even&#32;if&#32;they&#32;do&#32;nothing)&#32;to&#32;allow&#32;for&#32;copy-constructions&#32;from&#32;allocator&#32;objects&#32;of&#32;other&#32;types.</emphasis>
90 &#32;&#32;&#32;&#32;FreeListAllocator()&#32;<emphasis role="keyword">noexcept</emphasis>&#32;=&#32;<emphasis role="keywordflow">default</emphasis>;
91 &#32;&#32;&#32;&#32;FreeListAllocator(<emphasis role="keyword">const</emphasis>&#32;FreeListAllocator&amp;&#32;other)&#32;noexcept&#32;:
92 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSizeCopy(other.listSize)&#32;{}&#32;<emphasis role="comment">//do&#32;not&#32;copy&#32;the&#32;list&#32;to&#32;avoid&#32;double&#32;delete</emphasis>
93 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&#32;&lt;<emphasis role="keyword">typename</emphasis>&#32;U&gt;
94 &#32;&#32;&#32;&#32;FreeListAllocator(<emphasis role="keyword">const</emphasis>&#32;FreeListAllocator&lt;U,&#32;MaxListSize&gt;&amp;&#32;other)&#32;noexcept&#32;:&#32;<emphasis role="comment">//used&#32;by&#32;get_allocator()</emphasis>
95 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSizeCopy(other.listSize)&#32;{}&#32;<emphasis role="comment">//do&#32;not&#32;copy&#32;the&#32;list&#32;to&#32;avoid&#32;double&#32;delete</emphasis>
96 
97 &#32;&#32;&#32;&#32;FreeListAllocator(FreeListAllocator&amp;&amp;&#32;other)&#32;noexcept&#32;:&#32;list(other.list),&#32;listSize(other.listSize)&#32;{
98 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;other.list&#32;=&#32;<emphasis role="keyword">nullptr</emphasis>;
99 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;other.listSize&#32;=&#32;0;
100 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;other.listSizeCopy&#32;=&#32;0;
101 &#32;&#32;&#32;&#32;}
102 
103 &#32;&#32;&#32;&#32;std::size_t&#32;GetCurrentListSizeFromGetAllocatorCopy()&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{
104 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;listSizeCopy;
105 &#32;&#32;&#32;&#32;}
106 
107 &#32;&#32;&#32;&#32;std::size_t&#32;GetMaxListSize()<emphasis role="keyword">&#32;const&#32;</emphasis>{
108 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;MaxListSize;
109 &#32;&#32;&#32;&#32;}
110 
111 &#32;&#32;&#32;&#32;FreeListAllocator&amp;&#32;operator&#32;=&#32;(<emphasis role="keyword">const</emphasis>&#32;FreeListAllocator&amp;&#32;other)&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{
112 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;noop</emphasis>
113 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSizeCopy&#32;=&#32;other.listSize;&#32;<emphasis role="comment">//do&#32;not&#32;copy&#32;the&#32;list&#32;to&#32;avoid&#32;double&#32;delete</emphasis>
114 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>;
115 }
116 
117 &#32;&#32;&#32;&#32;FreeListAllocator&amp;&#32;operator&#32;=&#32;(FreeListAllocator&amp;&amp;&#32;other)&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{
118 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;clear();
119 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;list&#32;=&#32;other.list;
120 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSize&#32;=&#32;other.listSize;
121 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;other.list&#32;=&#32;<emphasis role="keyword">nullptr</emphasis>;
122 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;other.listSize&#32;=&#32;0;
123 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>;
124 &#32;&#32;&#32;&#32;}
125 
126 &#32;&#32;&#32;&#32;~FreeListAllocator()&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{&#32;clear();&#32;}
127 
128 &#32;&#32;&#32;&#32;T*&#32;allocate(size_type&#32;n)&#32;{
129 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;Allocate(&quot;&#32;&lt;&lt;&#32;n&#32;&lt;&lt;&#32;&quot;)&#32;from&#32;&quot;;</emphasis>
130 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(n&#32;==&#32;1)&#32;{
131 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node*&#32;ptr&#32;=&#32;list;
132 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(ptr)&#32;{
133 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;freelist\n&quot;;</emphasis>
134 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;list&#32;=&#32;list-&gt;next;
135 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;--listSize;
136 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
137 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">else</emphasis>&#32;{
138 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;new&#32;node\n&quot;;</emphasis>
139 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ptr&#32;=&#32;<emphasis role="keyword">new</emphasis>&#32;node;
140 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
141 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">reinterpret_cast&lt;</emphasis>T*<emphasis role="keyword">&gt;</emphasis>(ptr);
142 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
143 
144 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;::operator&#32;new\n&quot;;</emphasis>
145 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis>T*<emphasis role="keyword">&gt;</emphasis>(::operator&#32;<emphasis role="keyword">new</emphasis>(n&#32;*&#32;<emphasis role="keyword">sizeof</emphasis>(T)));
146 &#32;&#32;&#32;&#32;}
147 
148 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;deallocate(T*&#32;ptr,&#32;size_type&#32;n)&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{
149 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;Deallocate(&quot;&#32;&lt;&lt;&#32;static_cast&lt;void*&gt;(ptr)&#32;&lt;&lt;&#32;&quot;,&#32;&quot;&#32;&lt;&lt;&#32;n&#32;&lt;&lt;&#32;&quot;)&#32;to&#32;&quot;;</emphasis>
150 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;((n&#32;==&#32;1)&#32;&amp;&amp;&#32;(listSize&#32;&lt;&#32;MaxListSize))&#32;{
151 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;freelist\n&quot;;</emphasis>
152 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node*&#32;node_ptr&#32;=&#32;<emphasis role="keyword">reinterpret_cast&lt;</emphasis>node*<emphasis role="keyword">&gt;</emphasis>(ptr);
153 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node_ptr-&gt;next&#32;=&#32;list;
154 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;list&#32;=&#32;node_ptr;
155 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;++listSize;
156 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
157 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">else</emphasis>&#32;{
158 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;::operator&#32;delete\n&quot;;</emphasis>
159 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;::operator&#32;<emphasis role="keyword">delete</emphasis>(ptr);
160 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
161 &#32;&#32;&#32;&#32;}
162 
163 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">typename</emphasis>&#32;U&gt;
164 &#32;&#32;&#32;&#32;<emphasis role="keyword">struct&#32;</emphasis><link linkend="_struct_free_list_allocator_1_1rebind">rebind</link>&#32;{
165 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;FreeListAllocator&lt;U,&#32;MaxListSize&gt;&#32;other;
166 &#32;&#32;&#32;&#32;};
167 };
168 
169 
170 <emphasis role="keyword">template</emphasis>&#32;&lt;<emphasis role="keyword">typename</emphasis>&#32;T,&#32;<emphasis role="keyword">typename</emphasis>&#32;U&gt;
171 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator&#32;==&#32;(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_class_free_list_allocator">FreeListAllocator&lt;T&gt;</link>&amp;,&#32;<emphasis role="keyword">const</emphasis>&#32;<link linkend="_class_free_list_allocator">FreeListAllocator&lt;U&gt;</link>&amp;)&#32;{
172 &#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;ret&#32;true\n&quot;;</emphasis>
173 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">true</emphasis>;
174 }
175 
176 <emphasis role="keyword">template</emphasis>&#32;&lt;<emphasis role="keyword">typename</emphasis>&#32;T,&#32;<emphasis role="keyword">typename</emphasis>&#32;U&gt;
177 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator&#32;!=&#32;(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_class_free_list_allocator">FreeListAllocator&lt;T&gt;</link>&amp;,&#32;<emphasis role="keyword">const</emphasis>&#32;<link linkend="_class_free_list_allocator">FreeListAllocator&lt;U&gt;</link>&amp;)&#32;{
178 &#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;ret&#32;false\n&quot;;</emphasis>
179 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">false</emphasis>;
180 }
181 
182 
183 
184 
185 
186 
187 
188 
189 
190 
191 
192 
193 <emphasis role="keyword">template</emphasis>&#32;&lt;<emphasis role="keyword">typename</emphasis>&#32;T,&#32;std::<emphasis role="keywordtype">size_t</emphasis>&#32;MaxInitialListSize&#32;=&#32;100&gt;
194 <emphasis role="keyword">class&#32;</emphasis>FreeListAllocatorDynamic&#32;{
195 &#32;&#32;&#32;&#32;<emphasis role="keyword">union&#32;</emphasis>node&#32;{
196 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node*&#32;next;
197 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typename</emphasis>&#32;std::aligned_storage&lt;<emphasis role="keyword">sizeof</emphasis>(T),&#32;<emphasis role="keyword">alignof</emphasis>(T)&gt;::type&#32;storage;
198 &#32;&#32;&#32;&#32;};
199 
200 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;clear()&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{
201 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node*&#32;p&#32;=&#32;list;
202 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">while</emphasis>&#32;(p)&#32;{
203 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node*&#32;tmp&#32;=&#32;p;
204 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;p&#32;=&#32;p-&gt;next;
205 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">delete</emphasis>&#32;tmp;
206 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
207 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;list&#32;=&#32;<emphasis role="keyword">nullptr</emphasis>;
208 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSize&#32;=&#32;0;
209 &#32;&#32;&#32;&#32;}
210 
211 
212 &#32;&#32;&#32;&#32;<emphasis role="comment">//This&#32;technique&#32;is&#32;useful&#32;in&#32;classes&#32;that&#32;have&#32;multiple&#32;constructors</emphasis>
213 &#32;&#32;&#32;&#32;node*&#32;list&#32;=&#32;<emphasis role="keyword">nullptr</emphasis>;
214 <emphasis role="keyword">public</emphasis>:
215 &#32;&#32;&#32;&#32;std::size_t&#32;listSize&#32;=&#32;0;
216 &#32;&#32;&#32;&#32;std::size_t&#32;listSizeCopy&#32;=&#32;0;&#32;<emphasis role="comment">//for&#32;unit&#32;testing</emphasis>
217 &#32;&#32;&#32;&#32;std::shared_ptr&lt;std::size_t&gt;&#32;maxListSizePtr&#32;=&#32;std::make_shared&lt;std::size_t&gt;(MaxInitialListSize);
218 
219 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>value_type&#32;=&#32;T;
220 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>size_type&#32;=&#32;std::size_t;
221 &#32;&#32;&#32;&#32;<emphasis role="keyword">using&#32;</emphasis>propagate_on_container_move_assignment&#32;=&#32;std::true_type;
222 
223 &#32;&#32;&#32;&#32;
224 &#32;&#32;&#32;&#32;<emphasis role="comment">//the&#32;three&#32;constructor&#32;versions&#32;must&#32;be&#32;defined&#32;(even&#32;if&#32;they&#32;do&#32;nothing)&#32;to&#32;allow&#32;for&#32;copy-constructions&#32;from&#32;allocator&#32;objects&#32;of&#32;other&#32;types.</emphasis>
225 &#32;&#32;&#32;&#32;FreeListAllocatorDynamic()&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{}&#32;<emphasis role="comment">//&#32;=&#32;default;&#32;will&#32;fail&#32;compile&#32;because&#32;of&#32;shared_ptr</emphasis>
226 &#32;&#32;&#32;&#32;FreeListAllocatorDynamic(<emphasis role="keyword">const</emphasis>&#32;FreeListAllocatorDynamic&amp;&#32;other)&#32;noexcept&#32;:
227 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSizeCopy(other.listSize),&#32;<emphasis role="comment">//do&#32;not&#32;copy&#32;the&#32;list&#32;to&#32;avoid&#32;double&#32;delete</emphasis>
228 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;maxListSizePtr(other.maxListSizePtr)&#32;{}&#32;<emphasis role="comment">//increase&#32;use_count&#32;of&#32;shared_ptr&#32;so&#32;SetMaxListSize&#32;will&#32;work&#32;after&#32;get_allocator()&#32;copies</emphasis>
229 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&#32;&lt;<emphasis role="keyword">typename</emphasis>&#32;U&gt;
230 &#32;&#32;&#32;&#32;FreeListAllocatorDynamic(<emphasis role="keyword">const</emphasis>&#32;FreeListAllocatorDynamic&lt;U,&#32;MaxInitialListSize&gt;&amp;&#32;other)&#32;noexcept&#32;:&#32;<emphasis role="comment">//used&#32;by&#32;get_allocator()</emphasis>
231 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSizeCopy(other.listSize),&#32;<emphasis role="comment">//do&#32;not&#32;copy&#32;the&#32;list&#32;to&#32;avoid&#32;double&#32;delete</emphasis>
232 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;maxListSizePtr(other.maxListSizePtr)&#32;{}&#32;<emphasis role="comment">//increase&#32;use_count&#32;of&#32;shared_ptr&#32;so&#32;SetMaxListSize&#32;will&#32;work&#32;after&#32;get_allocator()&#32;copies</emphasis>
233 
234 &#32;&#32;&#32;&#32;FreeListAllocatorDynamic(FreeListAllocatorDynamic&amp;&amp;&#32;other)&#32;noexcept&#32;:
235 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;list(other.list),
236 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSize(other.listSize),
237 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;maxListSizePtr(std::make_shared&lt;std::size_t&gt;(*(other.maxListSizePtr)))&#32;<emphasis role="comment">//copy&#32;to&#32;new&#32;shared_ptr</emphasis>
238 &#32;&#32;&#32;&#32;{
239 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;other.list&#32;=&#32;<emphasis role="keyword">nullptr</emphasis>;
240 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;other.listSize&#32;=&#32;0;
241 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;other.listSizeCopy&#32;=&#32;0;
242 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//other.maxListSizePtr.reset();&#32;//keep&#32;other&#32;in&#32;a&#32;valid&#32;state</emphasis>
243 &#32;&#32;&#32;&#32;}
244 
245 &#32;&#32;&#32;&#32;std::size_t&#32;GetCurrentListSizeFromGetAllocatorCopy()&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{
246 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;listSizeCopy;
247 &#32;&#32;&#32;&#32;}
248 
249 &#32;&#32;&#32;&#32;std::size_t&#32;GetMaxListSize()<emphasis role="keyword">&#32;const&#32;</emphasis>{
250 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*maxListSizePtr;
251 &#32;&#32;&#32;&#32;}
252 
253 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetMaxListSizeFromGetAllocatorCopy(std::size_t&#32;newMaxListSize)<emphasis role="keyword">&#32;const&#32;</emphasis>{
254 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;*maxListSizePtr&#32;=&#32;newMaxListSize;
255 &#32;&#32;&#32;&#32;}
256 
257 &#32;&#32;&#32;&#32;FreeListAllocatorDynamic&amp;&#32;operator&#32;=&#32;(<emphasis role="keyword">const</emphasis>&#32;FreeListAllocatorDynamic&amp;&#32;other)&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{
258 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;noop</emphasis>
259 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSizeCopy&#32;=&#32;other.listSize;&#32;<emphasis role="comment">//do&#32;not&#32;copy&#32;the&#32;list&#32;to&#32;avoid&#32;double&#32;delete</emphasis>
260 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>;
261 &#32;&#32;&#32;&#32;}
262 
263 &#32;&#32;&#32;&#32;FreeListAllocatorDynamic&amp;&#32;operator&#32;=&#32;(FreeListAllocatorDynamic&amp;&amp;&#32;other)&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{
264 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;clear();
265 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;list&#32;=&#32;other.list;
266 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;listSize&#32;=&#32;other.listSize;
267 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;other.list&#32;=&#32;<emphasis role="keyword">nullptr</emphasis>;
268 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;other.listSize&#32;=&#32;0;
269 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;*<emphasis role="keyword">this</emphasis>;
270 &#32;&#32;&#32;&#32;}
271 
272 &#32;&#32;&#32;&#32;~FreeListAllocatorDynamic()&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{&#32;clear();&#32;}
273 
274 &#32;&#32;&#32;&#32;T*&#32;allocate(size_type&#32;n)&#32;{
275 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;Allocate(&quot;&#32;&lt;&lt;&#32;n&#32;&lt;&lt;&#32;&quot;)&#32;from&#32;&quot;;</emphasis>
276 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(n&#32;==&#32;1)&#32;{
277 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node*&#32;ptr&#32;=&#32;list;
278 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;(ptr)&#32;{
279 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;freelist\n&quot;;</emphasis>
280 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;list&#32;=&#32;list-&gt;next;
281 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;--listSize;
282 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
283 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">else</emphasis>&#32;{
284 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;new&#32;node\n&quot;;</emphasis>
285 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ptr&#32;=&#32;<emphasis role="keyword">new</emphasis>&#32;node;
286 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
287 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">reinterpret_cast&lt;</emphasis>T*<emphasis role="keyword">&gt;</emphasis>(ptr);
288 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
289 
290 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;::operator&#32;new\n&quot;;</emphasis>
291 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">static_cast&lt;</emphasis>T*<emphasis role="keyword">&gt;</emphasis>(::operator&#32;<emphasis role="keyword">new</emphasis>(n&#32;*&#32;<emphasis role="keyword">sizeof</emphasis>(T)));
292 &#32;&#32;&#32;&#32;}
293 
294 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">void</emphasis>&#32;deallocate(T*&#32;ptr,&#32;size_type&#32;n)&#32;<emphasis role="keyword">noexcept</emphasis>&#32;{
295 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;Deallocate(&quot;&#32;&lt;&lt;&#32;static_cast&lt;void*&gt;(ptr)&#32;&lt;&lt;&#32;&quot;,&#32;&quot;&#32;&lt;&lt;&#32;n&#32;&lt;&lt;&#32;&quot;)&#32;to&#32;&quot;;</emphasis>
296 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">if</emphasis>&#32;((n&#32;==&#32;1)&#32;&amp;&amp;&#32;(listSize&#32;&lt;&#32;(*maxListSizePtr)))&#32;{
297 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;freelist\n&quot;;</emphasis>
298 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node*&#32;node_ptr&#32;=&#32;<emphasis role="keyword">reinterpret_cast&lt;</emphasis>node*<emphasis role="keyword">&gt;</emphasis>(ptr);
299 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;node_ptr-&gt;next&#32;=&#32;list;
300 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;list&#32;=&#32;node_ptr;
301 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;++listSize;
302 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
303 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordflow">else</emphasis>&#32;{
304 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="comment">//std::cout&#32;&lt;&lt;&#32;&quot;::operator&#32;delete\n&quot;;</emphasis>
305 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;::operator&#32;<emphasis role="keyword">delete</emphasis>(ptr);
306 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;}
307 &#32;&#32;&#32;&#32;}
308 
309 &#32;&#32;&#32;&#32;<emphasis role="keyword">template</emphasis>&lt;<emphasis role="keyword">typename</emphasis>&#32;U&gt;
310 &#32;&#32;&#32;&#32;<emphasis role="keyword">struct&#32;</emphasis><link linkend="_struct_free_list_allocator_dynamic_1_1rebind">rebind</link>&#32;{
311 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;FreeListAllocatorDynamic&lt;U,&#32;MaxInitialListSize&gt;&#32;other;
312 &#32;&#32;&#32;&#32;};
313 };
314 
315 
316 <emphasis role="keyword">template</emphasis>&#32;&lt;<emphasis role="keyword">typename</emphasis>&#32;T,&#32;<emphasis role="keyword">typename</emphasis>&#32;U&gt;
317 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator&#32;==&#32;(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_class_free_list_allocator_dynamic">FreeListAllocatorDynamic&lt;T&gt;</link>&amp;,&#32;<emphasis role="keyword">const</emphasis>&#32;<link linkend="_class_free_list_allocator_dynamic">FreeListAllocatorDynamic&lt;U&gt;</link>&amp;)&#32;{
318 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">true</emphasis>;
319 }
320 
321 <emphasis role="keyword">template</emphasis>&#32;&lt;<emphasis role="keyword">typename</emphasis>&#32;T,&#32;<emphasis role="keyword">typename</emphasis>&#32;U&gt;
322 <emphasis role="keyword">inline</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator&#32;!=&#32;(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_class_free_list_allocator_dynamic">FreeListAllocatorDynamic&lt;T&gt;</link>&amp;,&#32;<emphasis role="keyword">const</emphasis>&#32;<link linkend="_class_free_list_allocator_dynamic">FreeListAllocatorDynamic&lt;U&gt;</link>&amp;)&#32;{
323 &#32;&#32;&#32;&#32;<emphasis role="keywordflow">return</emphasis>&#32;<emphasis role="keyword">false</emphasis>;
324 }
325 
326 <emphasis role="preprocessor">#endif&#32;</emphasis><emphasis role="comment">//FREE_LIST_ALLOCATOR_H</emphasis>
327 
</programlisting></section>
