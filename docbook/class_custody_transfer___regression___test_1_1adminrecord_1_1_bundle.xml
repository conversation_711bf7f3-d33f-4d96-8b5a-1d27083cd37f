<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_class_custody_transfer___regression___test_1_1adminrecord_1_1_bundle" xml:lang="en-US">
<title>CustodyTransfer_Regression_Test.adminrecord.Bundle Class Reference</title>
<indexterm><primary>CustodyTransfer_Regression_Test.adminrecord.Bundle</primary></indexterm>
<para>Inheritance diagram for CustodyTransfer_Regression_Test.adminrecord.Bundle:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="class_custody_transfer___regression___test_1_1adminrecord_1_1_bundle.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Static Public Attributes    </title>
        <itemizedlist>
            <listitem><para>list <link linkend="_class_custody_transfer___regression___test_1_1adminrecord_1_1_bundle_1a08105bad5d29885d0e1d1fa9cf9f5fb8">fields_desc</link></para>
</listitem>
        </itemizedlist>
</simplesect>
<section>
<title>Member Data Documentation</title>
<anchor xml:id="_class_custody_transfer___regression___test_1_1adminrecord_1_1_bundle_1a08105bad5d29885d0e1d1fa9cf9f5fb8"/><section>
    <title>fields_desc</title>
<indexterm><primary>fields_desc</primary><secondary>CustodyTransfer_Regression_Test.adminrecord.Bundle</secondary></indexterm>
<indexterm><primary>CustodyTransfer_Regression_Test.adminrecord.Bundle</primary><secondary>fields_desc</secondary></indexterm>
<para><computeroutput>list CustodyTransfer_Regression_Test.adminrecord.Bundle.fields_desc<computeroutput>[static]</computeroutput></computeroutput></para><emphasis role="strong">Initial value:</emphasis><programlisting linenumbering="unnumbered">=&#32;&#32;[
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ByteEnumField(<emphasis role="stringliteral">&apos;version&apos;</emphasis>,&#32;default=6,&#32;enum={6:<emphasis role="stringliteral">&quot;BPv6&quot;</emphasis>}),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&apos;processing_control_flags&apos;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2LenField(<emphasis role="stringliteral">&apos;block_length&apos;</emphasis>,&#32;<emphasis role="keywordtype">None</emphasis>),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;dest_scheme_offset&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;dest_ssp_offset&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;src_scheme_offset&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;src_ssp_offset&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;report_scheme_offset&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;report_ssp_offset&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;custodian_scheme_offset&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;custodian_ssp_offset&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;creation_timestamp_time&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;creation_timestamp_num&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2(<emphasis role="stringliteral">&quot;lifetime&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;SDNV2FieldLenField(<emphasis role="stringliteral">&quot;dictionary_length&quot;</emphasis>,&#32;0,&#32;length_of=<emphasis role="stringliteral">&quot;dictionary&quot;</emphasis>),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;FieldListField(<emphasis role="stringliteral">&quot;dictionary&quot;</emphasis>,&#32;<emphasis role="keywordtype">None</emphasis>,&#32;
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;StrNullField(<emphasis role="stringliteral">&quot;dictionary&quot;</emphasis>,&#32;default=b<emphasis role="stringliteral">&quot;&quot;</emphasis>,&#32;),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;length_from=(<emphasis role="keyword">lambda</emphasis>&#32;pkt:&#32;pkt.dictionary_length)),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ConditionalField(SDNV2(<emphasis role="stringliteral">&quot;fragment_offset&quot;</emphasis>,&#32;0),&#32;
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">lambda</emphasis>&#32;p:&#32;(p.processing_control_flags&#32;&amp;&#32;1)),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;ConditionalField(SDNV2(<emphasis role="stringliteral">&quot;total_adu_length&quot;</emphasis>,&#32;0),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">lambda</emphasis>&#32;p:&#32;(p.processing_control_flags&#32;&amp;&#32;1)),
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;PacketListField(<emphasis role="stringliteral">&quot;blocks&quot;</emphasis>,&#32;[],&#32;next_cls_cb=is_end_of_blocks_list)
&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;]
</programlisting></section>
<para>
The documentation for this class was generated from the following file:</para>
tests/test_scripts_linux/CustodyTransfer_Regression_Test/adminrecord.py</section>
</section>
