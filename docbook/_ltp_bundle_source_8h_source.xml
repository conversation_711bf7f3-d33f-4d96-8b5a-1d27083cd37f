<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="__ltp_bundle_source_8h_source" xml:lang="en-US">
<title>LtpBundleSource.h</title>
<indexterm><primary>common/ltp/include/LtpBundleSource.h</primary></indexterm>
Go to the documentation of this file.<programlisting linenumbering="unnumbered">1 
21 
22 <emphasis role="preprocessor">#ifndef&#32;_LTP_BUNDLE_SOURCE_H</emphasis>
23 <emphasis role="preprocessor">#define&#32;_LTP_BUNDLE_SOURCE_H&#32;1</emphasis>
24 
25 <emphasis role="preprocessor">#include&#32;&lt;string&gt;</emphasis>
26 <emphasis role="preprocessor">#include&#32;&lt;boost/thread.hpp&gt;</emphasis>
27 <emphasis role="preprocessor">#include&#32;&lt;boost/asio.hpp&gt;</emphasis>
28 <emphasis role="preprocessor">#include&#32;&lt;boost/function.hpp&gt;</emphasis>
29 <emphasis role="preprocessor">#include&#32;&lt;unordered_set&gt;</emphasis>
30 <emphasis role="preprocessor">#include&#32;&lt;vector&gt;</emphasis>
31 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__telemetry_definitions_8h">TelemetryDefinitions.h</link>&quot;</emphasis>
32 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__ltp_engine_8h">LtpEngine.h</link>&quot;</emphasis>
33 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__ltp_engine_config_8h">LtpEngineConfig.h</link>&quot;</emphasis>
34 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__free_list_allocator_8h">FreeListAllocator.h</link>&quot;</emphasis>
35 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__bundle_callback_function_defines_8h">BundleCallbackFunctionDefines.h</link>&quot;</emphasis>
36 <emphasis role="preprocessor">#include&#32;&lt;zmq.hpp&gt;</emphasis>
37 <emphasis role="preprocessor">#include&#32;&lt;atomic&gt;</emphasis>
38 <emphasis role="preprocessor">#include&#32;&lt;boost/core/noncopyable.hpp&gt;</emphasis>
39 
40 <emphasis role="keyword">class&#32;</emphasis>LtpBundleSource&#32;:&#32;<emphasis role="keyword">private</emphasis>&#32;boost::noncopyable&#32;{
41 <emphasis role="keyword">private</emphasis>:
42 &#32;&#32;&#32;&#32;LtpBundleSource()&#32;=&#32;<emphasis role="keyword">delete</emphasis>;
43 <emphasis role="keyword">public</emphasis>:
44 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;LtpBundleSource(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_ltp_engine_config">LtpEngineConfig</link>&amp;&#32;ltpTxCfg);
45 
46 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~LtpBundleSource();
47 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Init();
48 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;Stop();
49 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Forward(<emphasis role="keyword">const</emphasis>&#32;uint8_t*&#32;bundleData,&#32;<emphasis role="keyword">const</emphasis>&#32;std::size_t&#32;size,&#32;std::vector&lt;uint8_t&gt;&amp;&amp;&#32;userData);
50 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Forward(<link linkend="_classzmq_1_1message__t">zmq::message_t</link>&#32;&amp;&#32;dataZmq,&#32;std::vector&lt;uint8_t&gt;&amp;&amp;&#32;userData);
51 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Forward(padded_vector_uint8_t&amp;&#32;dataVec,&#32;std::vector&lt;uint8_t&gt;&amp;&amp;&#32;userData);
52 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;std::size_t&#32;GetTotalBundlesAcked()&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">noexcept</emphasis>;
53 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;std::size_t&#32;GetTotalBundlesSent()&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">noexcept</emphasis>;
54 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;std::size_t&#32;GetTotalBundlesUnacked()&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">noexcept</emphasis>;
55 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;std::size_t&#32;GetTotalBundleBytesAcked()&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">noexcept</emphasis>;
56 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;std::size_t&#32;GetTotalBundleBytesSent()&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">noexcept</emphasis>;
57 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;std::size_t&#32;GetTotalBundleBytesUnacked()&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keyword">noexcept</emphasis>;
58 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetOnFailedBundleVecSendCallback(<emphasis role="keyword">const</emphasis>&#32;OnFailedBundleVecSendCallback_t&amp;&#32;callback);
59 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetOnFailedBundleZmqSendCallback(<emphasis role="keyword">const</emphasis>&#32;OnFailedBundleZmqSendCallback_t&amp;&#32;callback);
60 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetOnSuccessfulBundleSendCallback(<emphasis role="keyword">const</emphasis>&#32;OnSuccessfulBundleSendCallback_t&amp;&#32;callback);
61 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetOnOutductLinkStatusChangedCallback(<emphasis role="keyword">const</emphasis>&#32;OnOutductLinkStatusChangedCallback_t&amp;&#32;callback);
62 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetUserAssignedUuid(uint64_t&#32;userAssignedUuid);
63 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetRate(uint64_t&#32;maxSendRateBitsPerSecOrZeroToDisable);
64 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetPing(uint64_t&#32;senderPingSecondsOrZeroToDisable);
65 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetPingToDefaultConfig();
66 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;GetTelemetry(<link linkend="_struct_ltp_outduct_telemetry__t">LtpOutductTelemetry_t</link>&amp;&#32;telem)&#32;<emphasis role="keyword">const</emphasis>;
67 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;uint64_t&#32;GetOutductMaxNumberOfBundlesInPipeline()&#32;<emphasis role="keyword">const</emphasis>;
68 &#32;&#32;&#32;&#32;
69 <emphasis role="keyword">protected</emphasis>:
70 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;ReadyToForward()&#32;=&#32;0;
71 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;SetLtpEnginePtr()&#32;=&#32;0;
72 &#32;&#32;&#32;&#32;LTP_LIB_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;GetTransportLayerSpecificTelem(<link linkend="_struct_ltp_outduct_telemetry__t">LtpOutductTelemetry_t</link>&amp;&#32;telem)&#32;<emphasis role="keyword">const</emphasis>&#32;=&#32;0;
73 <emphasis role="keyword">private</emphasis>:
74 &#32;&#32;&#32;&#32;
75 
76 &#32;&#32;&#32;&#32;<emphasis role="comment">//ltp&#32;callback&#32;functions&#32;for&#32;a&#32;sender</emphasis>
77 &#32;&#32;&#32;&#32;LTP_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SessionStartCallback(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_ltp_1_1session__id__t">Ltp::session_id_t</link>&#32;&amp;&#32;sessionId);
78 &#32;&#32;&#32;&#32;LTP_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;TransmissionSessionCompletedCallback(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_ltp_1_1session__id__t">Ltp::session_id_t</link>&#32;&amp;&#32;sessionId);
79 &#32;&#32;&#32;&#32;LTP_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;InitialTransmissionCompletedCallback(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_ltp_1_1session__id__t">Ltp::session_id_t</link>&#32;&amp;&#32;sessionId);
80 &#32;&#32;&#32;&#32;LTP_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;TransmissionSessionCancelledCallback(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_ltp_1_1session__id__t">Ltp::session_id_t</link>&#32;&amp;&#32;sessionId,&#32;CANCEL_SEGMENT_REASON_CODES&#32;reasonCode);
81 
82 &#32;&#32;&#32;&#32;std::atomic&lt;bool&gt;&#32;m_useLocalConditionVariableAckReceived;
83 &#32;&#32;&#32;&#32;boost::condition_variable&#32;m_localConditionVariableAckReceived;
84 
85 &#32;&#32;&#32;&#32;<emphasis role="comment">//ltp&#32;vars</emphasis>
86 <emphasis role="keyword">protected</emphasis>:
87 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_ltp_engine_config">LtpEngineConfig</link>&#32;m_ltpTxCfg;
88 &#32;&#32;&#32;&#32;<link linkend="_class_ltp_engine">LtpEngine</link>&#32;*&#32;m_ltpEnginePtr;
89 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;M_CLIENT_SERVICE_ID;
90 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;M_THIS_ENGINE_ID;
91 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;M_REMOTE_LTP_ENGINE_ID;
92 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;M_BUNDLE_PIPELINE_LIMIT;
93 <emphasis role="keyword">private</emphasis>:
94 &#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;std::unordered_set&lt;uint64_t,
95 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::hash&lt;uint64_t&gt;,
96 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;std::equal_to&lt;uint64_t&gt;,
97 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<link linkend="_class_free_list_allocator_dynamic">FreeListAllocatorDynamic&lt;uint64_t&gt;</link>&#32;&gt;&#32;active_session_number_set_t;
98 &#32;&#32;&#32;&#32;active_session_number_set_t&#32;m_activeSessionNumbersSet;
99 &#32;&#32;&#32;&#32;std::atomic&lt;unsigned&#32;int&gt;&#32;m_startingCount;
100 
101 &#32;&#32;&#32;&#32;<emphasis role="comment">//telemetry</emphasis>
102 &#32;&#32;&#32;&#32;std::atomic&lt;uint64_t&gt;&#32;m_totalBundlesSent;
103 &#32;&#32;&#32;&#32;std::atomic&lt;uint64_t&gt;&#32;m_totalBundlesAcked;
104 &#32;&#32;&#32;&#32;std::atomic&lt;uint64_t&gt;&#32;m_totalBundlesFailedToSend;
105 &#32;&#32;&#32;&#32;std::atomic&lt;uint64_t&gt;&#32;m_totalBundleBytesSent;
106 };
107 
108 
109 
110 <emphasis role="preprocessor">#endif&#32;</emphasis><emphasis role="comment">//_LTP_BUNDLE_SOURCE_H</emphasis>
</programlisting></section>
