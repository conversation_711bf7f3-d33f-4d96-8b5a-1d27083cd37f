<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="__bp_sink_pattern_8h_source" xml:lang="en-US">
<title>BpSinkPattern.h</title>
<indexterm><primary>common/bpcodec/include/app_patterns/BpSinkPattern.h</primary></indexterm>
Go to the documentation of this file.<programlisting linenumbering="unnumbered">1 
26 
27 <emphasis role="preprocessor">#ifndef&#32;_BP_SINK_PATTERN_H</emphasis>
28 <emphasis role="preprocessor">#define&#32;_BP_SINK_PATTERN_H&#32;1</emphasis>
29 <emphasis role="preprocessor">#include&#32;&quot;bp_app_patterns_lib_export.h&quot;</emphasis>
30 <emphasis role="preprocessor">#ifndef&#32;CLASS_VISIBILITY_BP_APP_PATTERNS_LIB</emphasis>
31 <emphasis role="preprocessor">#&#32;&#32;ifdef&#32;_WIN32</emphasis>
32 <emphasis role="preprocessor">#&#32;&#32;&#32;&#32;define&#32;CLASS_VISIBILITY_BP_APP_PATTERNS_LIB</emphasis>
33 <emphasis role="preprocessor">#&#32;&#32;else</emphasis>
34 <emphasis role="preprocessor">#&#32;&#32;&#32;&#32;define&#32;CLASS_VISIBILITY_BP_APP_PATTERNS_LIB&#32;BP_APP_PATTERNS_LIB_EXPORT</emphasis>
35 <emphasis role="preprocessor">#&#32;&#32;endif</emphasis>
36 <emphasis role="preprocessor">#endif</emphasis>
37 <emphasis role="preprocessor">#include&#32;&lt;stdint.h&gt;</emphasis>
38 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__induct_manager_8h">InductManager.h</link>&quot;</emphasis>
39 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__outduct_manager_8h">OutductManager.h</link>&quot;</emphasis>
40 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="_bpv6_8h">codec/bpv6.h</link>&quot;</emphasis>
41 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__custody_transfer_manager_8h">codec/CustodyTransferManager.h</link>&quot;</emphasis>
42 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__bpv6_fragment_manager_8h">codec/Bpv6FragmentManager.h</link>&quot;</emphasis>
43 <emphasis role="preprocessor">#include&#32;&lt;boost/asio.hpp&gt;</emphasis>
44 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__tcpcl_induct_8h">TcpclInduct.h</link>&quot;</emphasis>
45 <emphasis role="preprocessor">#include&#32;&lt;queue&gt;</emphasis>
46 <emphasis role="preprocessor">#include&#32;&lt;unordered_set&gt;</emphasis>
47 <emphasis role="preprocessor">#include&#32;&lt;atomic&gt;</emphasis>
48 
49 <emphasis role="keyword">class&#32;</emphasis>CLASS_VISIBILITY_BP_APP_PATTERNS_LIB&#32;BpSinkPattern&#32;{
50 <emphasis role="keyword">public</emphasis>:
51 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_EXPORT&#32;BpSinkPattern();
52 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;Stop();
53 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_EXPORT&#32;<emphasis role="keyword">virtual</emphasis>&#32;~BpSinkPattern();
54 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Init(InductsConfig_ptr&#32;&amp;&#32;inductsConfigPtr,&#32;OutductsConfig_ptr&#32;&amp;&#32;outductsConfigPtr,
55 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;boost::filesystem::path&amp;&#32;bpSecConfigFilePath,
56 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;isAcsAware,&#32;<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;&amp;&#32;myEid,&#32;uint32_t&#32;processingLagMs,&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;maxBundleSizeBytes,&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;myBpEchoServiceId&#32;=&#32;2047);
57 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;LogStats(<link linkend="_struct_primary_block">PrimaryBlock</link>&amp;&#32;primaryBlock,
58 &#32;&#32;&#32;&#32;&#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;isBpVersion6);
59 <emphasis role="keyword">protected</emphasis>:
60 &#32;&#32;&#32;&#32;<emphasis role="keyword">virtual</emphasis>&#32;<emphasis role="keywordtype">bool</emphasis>&#32;ProcessPayload(<emphasis role="keyword">const</emphasis>&#32;uint8_t&#32;*&#32;data,&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;size)&#32;=&#32;0;
61 <emphasis role="keyword">private</emphasis>:
62 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;WholeBundleReadyCallback(padded_vector_uint8_t&#32;&amp;&#32;wholeBundleVec);
63 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Process(padded_vector_uint8_t&#32;&amp;&#32;rxBuf,&#32;<emphasis role="keyword">const</emphasis>&#32;std::size_t&#32;messageSize);
64 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;AcsNeedToSend_TimerExpired(<emphasis role="keyword">const</emphasis>&#32;boost::system::error_code&amp;&#32;e);
65 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;TransferRate_TimerExpired(<emphasis role="keyword">const</emphasis>&#32;boost::system::error_code&amp;&#32;e);
66 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SendAcsFromTimerThread();
67 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;OnNewOpportunisticLinkCallback(<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;remoteNodeId,&#32;<link linkend="_class_induct">Induct</link>*&#32;thisInductPtr,&#32;<emphasis role="keywordtype">void</emphasis>*&#32;sinkPtr);
68 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;OnDeletedOpportunisticLinkCallback(<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;remoteNodeId,&#32;<link linkend="_class_induct">Induct</link>*&#32;thisInductPtr,&#32;<emphasis role="keywordtype">void</emphasis>*&#32;sinkPtrAboutToBeDeleted);
69 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;Forward_ThreadSafe(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;&amp;&#32;destEid,&#32;padded_vector_uint8_t&amp;&#32;bundleToMoveAndSend);
70 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;SenderReaderThreadFunc();
71 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;OnFailedBundleVecSendCallback(padded_vector_uint8_t&amp;&#32;movableBundle,&#32;std::vector&lt;uint8_t&gt;&amp;&#32;userData,&#32;uint64_t&#32;outductUuid);
72 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;OnSuccessfulBundleSendCallback(std::vector&lt;uint8_t&gt;&amp;&#32;userData,&#32;uint64_t&#32;outductUuid);
73 &#32;&#32;&#32;&#32;BP_APP_PATTERNS_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;OnOutductLinkStatusChangedCallback(<emphasis role="keywordtype">bool</emphasis>&#32;isLinkDownEvent,&#32;uint64_t&#32;outductUuid);
74 <emphasis role="keyword">public</emphasis>:
75 
76 &#32;&#32;&#32;&#32;uint64_t&#32;m_totalPayloadBytesRx;
77 &#32;&#32;&#32;&#32;uint64_t&#32;m_totalBundleBytesRx;
78 &#32;&#32;&#32;&#32;uint64_t&#32;m_totalBundlesVersion6Rx;
79 &#32;&#32;&#32;&#32;uint64_t&#32;m_totalBundlesVersion7Rx;
80 
81 &#32;&#32;&#32;&#32;uint64_t&#32;m_lastPayloadBytesRx;
82 &#32;&#32;&#32;&#32;uint64_t&#32;m_lastBundleBytesRx;
83 &#32;&#32;&#32;&#32;uint64_t&#32;m_lastBundlesRx;
84 &#32;&#32;&#32;&#32;boost::posix_time::ptime&#32;m_lastPtime;
85 &#32;&#32;&#32;&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;m_lastPreviousNode;
86 &#32;&#32;&#32;&#32;std::vector&lt;uint64_t&gt;&#32;m_hopCounts;
87 &#32;&#32;&#32;&#32;uint64_t&#32;m_bpv7Priority;
88 
89 <emphasis role="keyword">private</emphasis>:
90 &#32;&#32;&#32;&#32;uint32_t&#32;M_EXTRA_PROCESSING_TIME_MS;
91 
92 &#32;&#32;&#32;&#32;<link linkend="_class_induct_manager">InductManager</link>&#32;m_inductManager;
93 &#32;&#32;&#32;&#32;<link linkend="_class_outduct_manager">OutductManager</link>&#32;m_outductManager;
94 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;m_hasSendCapability;
95 &#32;&#32;&#32;&#32;<emphasis role="keywordtype">bool</emphasis>&#32;m_hasSendCapabilityOverTcpclBidirectionalInduct;
96 &#32;&#32;&#32;&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;m_myEid;
97 &#32;&#32;&#32;&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;m_myEidEcho;
98 &#32;&#32;&#32;&#32;std::string&#32;m_myEidUriString;
99 &#32;&#32;&#32;&#32;std::unique_ptr&lt;CustodyTransferManager&gt;&#32;m_custodyTransferManagerPtr;
100 &#32;&#32;&#32;&#32;uint64_t&#32;m_nextCtebCustodyId;
101 &#32;&#32;&#32;&#32;<link linkend="_class_bundle_view_v6">BundleViewV6</link>&#32;m_custodySignalRfc5050RenderedBundleView;
102 &#32;&#32;&#32;&#32;boost::asio::io_service&#32;m_ioService;
103 &#32;&#32;&#32;&#32;boost::asio::deadline_timer&#32;m_timerAcs;
104 &#32;&#32;&#32;&#32;boost::asio::deadline_timer&#32;m_timerTransferRateStats;
105 &#32;&#32;&#32;&#32;std::unique_ptr&lt;boost::thread&gt;&#32;m_ioServiceThreadPtr;
106 &#32;&#32;&#32;&#32;std::unique_ptr&lt;boost::thread&gt;&#32;m_threadSenderReaderPtr;
107 &#32;&#32;&#32;&#32;boost::condition_variable&#32;m_conditionVariableSenderReader;
108 &#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;std::pair&lt;cbhe_eid_t,&#32;padded_vector_uint8_t&gt;&#32;desteid_bundle_pair_t;
109 &#32;&#32;&#32;&#32;std::queue&lt;desteid_bundle_pair_t&gt;&#32;m_bundleToSendQueue;
110 &#32;&#32;&#32;&#32;boost::mutex&#32;m_mutexCurrentlySendingBundleIdSet;
111 &#32;&#32;&#32;&#32;std::unordered_set&lt;uint64_t&gt;&#32;m_currentlySendingBundleIdSet;
112 &#32;&#32;&#32;&#32;boost::condition_variable&#32;m_cvCurrentlySendingBundleIdSet;
113 &#32;&#32;&#32;&#32;boost::mutex&#32;m_mutexQueueBundlesThatFailedToSend;
114 &#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;std::pair&lt;uint64_t,&#32;cbhe_eid_t&gt;&#32;bundleid_finaldesteid_pair_t;
115 &#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;std::pair&lt;padded_vector_uint8_t,&#32;bundleid_finaldesteid_pair_t&gt;&#32;bundle_userdata_pair_t;
116 &#32;&#32;&#32;&#32;std::queue&lt;bundle_userdata_pair_t&gt;&#32;m_queueBundlesThatFailedToSend;
117 &#32;&#32;&#32;&#32;std::atomic&lt;bool&gt;&#32;m_linkIsDown;
118 &#32;&#32;&#32;&#32;std::atomic&lt;bool&gt;&#32;m_runningSenderThread;
119 &#32;&#32;&#32;&#32;boost::mutex&#32;m_mutexCtm;
120 &#32;&#32;&#32;&#32;boost::mutex&#32;m_mutexSendBundleQueue;
121 &#32;&#32;&#32;&#32;uint64_t&#32;m_tcpclOpportunisticRemoteNodeId;
122 &#32;&#32;&#32;&#32;std::string&#32;m_opportunisticConvergenceLayerName;
123 &#32;&#32;&#32;&#32;<link linkend="_class_induct">Induct</link>&#32;*&#32;m_tcpclInductPtr;
124 
125 &#32;&#32;&#32;&#32;<emphasis role="keyword">struct&#32;</emphasis><link linkend="_struct_bp_sink_pattern_1_1_bp_sec_impl">BpSecImpl</link>;
126 &#32;&#32;&#32;&#32;std::unique_ptr&lt;BpSecImpl&gt;&#32;m_bpsecPimpl;&#32;<emphasis role="comment">//&#32;Pointer&#32;to&#32;the&#32;internal&#32;implementation</emphasis>
127 
128 &#32;&#32;&#32;&#32;<link linkend="_class_bpv6_fragment_manager">Bpv6FragmentManager</link>&#32;m_fragmentManager;
129 };
130 
131 
132 <emphasis role="preprocessor">#endif&#32;&#32;</emphasis><emphasis role="comment">//_BP_SINK_PATTERN_H</emphasis>
</programlisting></section>
