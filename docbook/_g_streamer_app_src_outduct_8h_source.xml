<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="__g_streamer_app_src_outduct_8h_source" xml:lang="en-US">
<title>GStreamerAppSrcOutduct.h</title>
<indexterm><primary>common/streaming/BpOutduct/include/GStreamerAppSrcOutduct.h</primary></indexterm>
Go to the documentation of this file.<programlisting linenumbering="unnumbered">1 
13 
14 <emphasis role="preprocessor">#include&#32;&lt;gst/gst.h&gt;</emphasis>
15 <emphasis role="preprocessor">#include&#32;&lt;gst/app/gstappsrc.h&gt;</emphasis>
16 <emphasis role="preprocessor">#include&#32;&lt;gst/app/gstappsink.h&gt;</emphasis>
17 
18 <emphasis role="preprocessor">#include&#32;&lt;boost/asio.hpp&gt;</emphasis>
19 <emphasis role="preprocessor">#include&#32;&lt;boost/process.hpp&gt;</emphasis>
20 <emphasis role="preprocessor">#include&#32;&lt;boost/smart_ptr/make_unique.hpp&gt;</emphasis>
21 <emphasis role="preprocessor">#include&#32;&lt;boost/thread/thread.hpp&gt;</emphasis>
22 
23 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__dtn_util_8h">DtnUtil.h</link>&quot;</emphasis>
24 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__dtn_rtp_frame_8h">DtnRtpFrame.h</link>&quot;</emphasis>
25 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__padded_vector_uint8_8h">PaddedVectorUint8.h</link>&quot;</emphasis>
26 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__async_listener_8h">AsyncListener.h</link>&quot;</emphasis>
27 <emphasis role="preprocessor">#include&#32;&quot;streaming_lib_export.h&quot;</emphasis>
28 
29 <emphasis role="preprocessor">#define&#32;SAMPLE_RATE&#32;90000</emphasis>
30 <emphasis role="preprocessor">#define&#32;DEFAULT_NUM_CIRC_BUFFERS&#32;1000000</emphasis>
31 
32 <emphasis role="preprocessor">#define&#32;GST_HDTN_OUTDUCT_SOCKET_PATH&#32;&quot;/tmp/hdtn_gst_shm_outduct&quot;</emphasis>
33 <emphasis role="preprocessor">#define&#32;GST_APPSRC_MAX_BYTES_IN_BUFFER&#32;20000000</emphasis>
34 <emphasis role="preprocessor">#define&#32;MAX_NUM_BUFFERS_QUEUE&#32;(UINT16_MAX)&#32;</emphasis><emphasis role="comment">//&#32;once&#32;around&#32;an&#32;rtp&#32;sequence&#32;overflow</emphasis>
35 <emphasis role="preprocessor">#define&#32;MAX_SIZE_BYTES_QUEUE&#32;(0)&#32;</emphasis><emphasis role="comment">//&#32;0&#32;=&#32;disable</emphasis>
36 <emphasis role="preprocessor">#define&#32;MAX_SIZE_TIME_QUEUE&#32;(0)&#32;</emphasis><emphasis role="comment">//&#32;0&#32;=&#32;disable</emphasis>
37 <emphasis role="preprocessor">#define&#32;MIN_THRESHHOLD_TIME_QUEUE_NS&#32;(500000)&#32;</emphasis><emphasis role="comment">//(3e9)&#32;//&#32;Min.&#32;amount&#32;of&#32;data&#32;in&#32;the&#32;queue&#32;to&#32;allow&#32;reading&#32;(in&#32;ns,&#32;0=disable)</emphasis>
38 
39 <emphasis role="preprocessor">#define&#32;RTP_LATENCY_MILLISEC&#32;(500)&#32;</emphasis>
40 <emphasis role="preprocessor">#define&#32;RTP_MAX_DROPOUT_TIME_MILLISEC&#32;(200)&#32;</emphasis><emphasis role="comment">//&#32;The&#32;maximum&#32;time&#32;(milliseconds)&#32;of&#32;missing&#32;packets&#32;tolerated.</emphasis>
41 <emphasis role="preprocessor">#define&#32;RTP_MAX_MISORDER_TIME_MIILISEC&#32;(60000)&#32;</emphasis><emphasis role="comment">//The&#32;maximum&#32;time&#32;(milliseconds)&#32;of&#32;misordered&#32;packets&#32;tolerated.</emphasis>
42 <emphasis role="preprocessor">#define&#32;RTP_MODE&#32;(1)&#32;</emphasis><emphasis role="comment">//&#32;gst&#32;default</emphasis>
43 &#32;
44 
45 <emphasis role="keyword">typedef</emphasis>&#32;boost::function&lt;void(padded_vector_uint8_t&#32;&amp;&#32;wholeBundleVec)&gt;&#32;WholeBundleReadyCallback_t;
46 <emphasis role="keyword">typedef</emphasis>&#32;&#32;boost::circular_buffer&lt;padded_vector_uint8_t&gt;&#32;CbQueue_t;
47 
48 
49 <emphasis role="keyword">class&#32;</emphasis>GStreamerAppSrcOutduct
50 {
51 <emphasis role="keyword">public</emphasis>:
52 &#32;&#32;&#32;&#32;
53 &#32;&#32;&#32;&#32;STREAMING_LIB_EXPORT&#32;GStreamerAppSrcOutduct(std::string&#32;shmSocketPath,&#32;std::string&#32;gstCaps);
54 &#32;&#32;&#32;&#32;STREAMING_LIB_EXPORT&#32;~GStreamerAppSrcOutduct();
55 
56 &#32;&#32;&#32;&#32;STREAMING_LIB_EXPORT&#32;<emphasis role="keywordtype">int</emphasis>&#32;PushRtpPacketToGStreamerOutduct(padded_vector_uint8_t&amp;&#32;rtpPacketToTake);
57 &#32;&#32;&#32;&#32;
58 &#32;&#32;&#32;&#32;STREAMING_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;TryWaitForIncomingDataAvailable(<emphasis role="keyword">const</emphasis>&#32;boost::posix_time::time_duration&amp;&#32;timeout);
59 
60 &#32;&#32;&#32;&#32;STREAMING_LIB_EXPORT&#32;<emphasis role="keyword">static</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;SetGStreamerAppSrcOutductInstance(GStreamerAppSrcOutduct*&#32;gStreamerAppSrcOutduct);
61 &#32;&#32;&#32;&#32;
62 &#32;&#32;&#32;&#32;CbQueue_t&#32;m_incomingRtpPacketQueue;
63 &#32;&#32;&#32;&#32;CbQueue_t&#32;m_incomingRtpPacketQueueForDisplay;&#32;
64 &#32;&#32;&#32;&#32;CbQueue_t&#32;m_incomingRtpPacketQueueForFilesink;
65 
66 &#32;&#32;&#32;&#32;uint64_t&#32;m_numFilesinkSamples&#32;=&#32;0;
67 &#32;&#32;&#32;&#32;uint64_t&#32;m_numDisplaySamples&#32;=&#32;0;
68 &#32;&#32;&#32;
69 <emphasis role="keyword">private</emphasis>:
70 &#32;&#32;&#32;&#32;
71 &#32;&#32;&#32;&#32;std::unique_ptr&lt;AsyncListener&lt;CbQueue_t&gt;&gt;&#32;m_bundleCallbackAsyncListenerPtr;
72 &#32;&#32;&#32;&#32;std::unique_ptr&lt;AsyncListener&lt;CbQueue_t&gt;&gt;&#32;m_rtpPacketToDisplayAsyncListenerPtr;
73 &#32;&#32;&#32;&#32;std::unique_ptr&lt;AsyncListener&lt;CbQueue_t&gt;&gt;&#32;m_rtpPacketToFilesinkAsyncListenerPtr;
74 
75 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;thread&#32;members</emphasis>
76 &#32;&#32;&#32;&#32;std::unique_ptr&lt;boost::thread&gt;&#32;m_packetTeeThread;
77 &#32;&#32;&#32;&#32;std::unique_ptr&lt;boost::thread&gt;&#32;m_displayThread;
78 &#32;&#32;&#32;&#32;std::unique_ptr&lt;boost::thread&gt;&#32;m_filesinkThread;
79 &#32;&#32;&#32;&#32;std::unique_ptr&lt;boost::thread&gt;&#32;m_busMonitoringThread;
80 
81 &#32;&#32;&#32;&#32;std::string&#32;m_shmSocketPath;
82 &#32;&#32;&#32;&#32;std::string&#32;m_gstCaps;
83 &#32;&#32;&#32;&#32;std::atomic&lt;bool&gt;&#32;m_running;
84 &#32;&#32;&#32;&#32;std::atomic&lt;bool&gt;&#32;m_runDisplayThread;
85 &#32;&#32;&#32;&#32;std::atomic&lt;bool&gt;&#32;m_runFilesinkThread;
86 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;gst&#32;members</emphasis>
87 &#32;&#32;&#32;&#32;GstBus&#32;*m_bus;
88 
89 &#32;&#32;&#32;&#32;<emphasis role="comment">/*&#32;setup&#32;functions&#32;*/</emphasis>
90 &#32;&#32;&#32;&#32;STREAMING_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">int</emphasis>&#32;CreateElements();
91 &#32;&#32;&#32;&#32;STREAMING_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">int</emphasis>&#32;BuildPipeline();
92 &#32;&#32;&#32;&#32;STREAMING_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">int</emphasis>&#32;StartPlaying();
93 &#32;&#32;&#32;&#32;STREAMING_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">int</emphasis>&#32;CheckInitializationSuccess();
94 
95 &#32;&#32;&#32;&#32;<emphasis role="comment">/*&#32;Operating&#32;functions&#32;*/</emphasis>
96 &#32;&#32;&#32;&#32;STREAMING_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;OnBusMessages();
97 &#32;&#32;&#32;&#32;STREAMING_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;TeeDataToQueuesThread();
98 &#32;&#32;&#32;&#32;STREAMING_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;PushDataToFilesinkThread();
99 &#32;&#32;&#32;&#32;STREAMING_LIB_NO_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;PushDataToDisplayThread();
100 
101 
102 &#32;&#32;&#32;&#32;<emphasis role="comment">/*&#32;pipeline&#32;members&#32;*/</emphasis>
103 
104 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;To&#32;display</emphasis>
105 &#32;&#32;&#32;&#32;GstElement&#32;*m_pipeline;
106 &#32;&#32;&#32;&#32;GstElement&#32;*m_displayAppsrc;
107 &#32;&#32;&#32;&#32;<emphasis role="comment">/*&#32;cap&#32;goes&#32;here*/</emphasis>
108 &#32;&#32;&#32;&#32;GstElement&#32;*m_displayQueue;
109 &#32;&#32;&#32;&#32;GstElement&#32;*m_rtpjitterbuffer;
110 &#32;&#32;&#32;&#32;GstElement&#32;*m_rtph264depay;
111 &#32;&#32;&#32;&#32;GstElement&#32;*m_h264parse;
112 &#32;&#32;&#32;&#32;GstElement&#32;*m_h264timestamper;
113 &#32;&#32;&#32;&#32;GstElement&#32;*m_decodeQueue;
114 &#32;&#32;&#32;&#32;GstElement&#32;*m_avdec_h264;
115 &#32;&#32;&#32;&#32;GstElement&#32;*m_postDecodeQueue;
116 &#32;&#32;&#32;&#32;GstElement&#32;*m_displayShmsink;&#32;
117 
118 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;To&#32;filesink&#32;</emphasis>
119 &#32;&#32;&#32;&#32;GstElement&#32;*m_filesinkAppsrc;
120 &#32;&#32;&#32;&#32;GstElement&#32;*m_filesinkQueue;
121 &#32;&#32;&#32;&#32;GstElement&#32;*m_filesinkShmsink;
122 
123 &#32;&#32;&#32;&#32;<emphasis role="comment">//&#32;stat&#32;keeping&#32;</emphasis>
124 &#32;&#32;&#32;&#32;uint64_t&#32;m_totalIncomingCbOverruns;
125 &#32;&#32;&#32;&#32;uint64_t&#32;m_totalFilesinkCbOverruns;
126 &#32;&#32;&#32;&#32;uint64_t&#32;m_totalDisplayCbOverruns;
127 };
128 
129 
130 
131 <emphasis role="keyword">struct&#32;</emphasis><link linkend="_struct_hdtn_gst_handoff_utils__t">HdtnGstHandoffUtils_t</link>&#32;{
132 &#32;&#32;&#32;&#32;GstBuffer&#32;*buffer;
133 &#32;&#32;&#32;&#32;GstMapInfo&#32;map;
134 &#32;&#32;&#32;&#32;GstFlowReturn&#32;ret;
135 };
136 
137 
138 
139 
140 <emphasis role="comment">//&#32;Use&#32;sync=true&#32;if:</emphasis>
141 <emphasis role="comment">//&#32;There&#32;is&#32;a&#32;human&#32;watching&#32;the&#32;output,&#32;e.g.&#32;movie&#32;playback</emphasis>
142 <emphasis role="comment">//&#32;Use&#32;sync=false&#32;if:</emphasis>
143 <emphasis role="comment">//&#32;You&#32;are&#32;using&#32;a&#32;live&#32;source</emphasis>
144 <emphasis role="comment">//&#32;The&#32;pipeline&#32;is&#32;being&#32;post-processed,&#32;e.g.&#32;neural&#32;net</emphasis>
145 
146 <emphasis role="comment">//&#32;&#32;&#32;&#32;&#32;alignment=au&#32;means&#32;that&#32;each&#32;output&#32;buffer&#32;contains&#32;the&#32;NALs&#32;for&#32;a&#32;whole</emphasis>
147 <emphasis role="comment">//&#32;picture.&#32;alignment=nal&#32;just&#32;means&#32;that&#32;each&#32;output&#32;buffer&#32;contains</emphasis>
148 <emphasis role="comment">//&#32;complete&#32;NALs,&#32;but&#32;those&#32;do&#32;not&#32;need&#32;to&#32;represent&#32;a&#32;whole&#32;frame.</emphasis>
</programlisting></section>
