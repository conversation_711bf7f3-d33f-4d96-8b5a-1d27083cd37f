<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_structboost_1_1asio_1_1basic__dir__monitor__service_1_1completion__key" xml:lang="en-US">
<title>boost::asio::basic_dir_monitor_service&lt; DirMonitorImplementation &gt;::completion_key Struct Reference</title>
<indexterm><primary>boost::asio::basic_dir_monitor_service&lt; DirMonitorImplementation &gt;::completion_key</primary></indexterm>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_structboost_1_1asio_1_1basic__dir__monitor__service_1_1completion__key_1ac3fb9b1d72f8267c5ed9254d1da36738"/><emphasis role="strong">completion_key</emphasis> (HANDLE h, const std::wstring &amp;d, std::shared_ptr&lt; DirMonitorImplementation &gt; &amp;i)</para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Public Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_structboost_1_1asio_1_1basic__dir__monitor__service_1_1completion__key_1a67a60fa9425ad16fb60d73fe7a0cf455"/>HANDLE <emphasis role="strong">handle</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structboost_1_1asio_1_1basic__dir__monitor__service_1_1completion__key_1a6eaa2da34ccdeceb118e0ed8a4fdcf58"/>std::wstring <emphasis role="strong">dirname</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structboost_1_1asio_1_1basic__dir__monitor__service_1_1completion__key_1ab5685c9dbda21b5687221cab4d8575a5"/>std::weak_ptr&lt; DirMonitorImplementation &gt; <emphasis role="strong">impl</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structboost_1_1asio_1_1basic__dir__monitor__service_1_1completion__key_1ac8dd7b48bb55dfa69de68d702dd60aed"/>char <emphasis role="strong">buffer</emphasis> [1024]</para>
</listitem>
            <listitem><para><anchor xml:id="_structboost_1_1asio_1_1basic__dir__monitor__service_1_1completion__key_1aa84116b928aceb83819aa35a9337ffe8"/>OVERLAPPED <emphasis role="strong">overlapped</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
<para>
The documentation for this struct was generated from the following file:</para>
common/util/include/dir_monitor/windows/basic_dir_monitor_service.hpp</section>
