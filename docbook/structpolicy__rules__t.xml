<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_structpolicy__rules__t" xml:lang="en-US">
<title>policy_rules_t Struct Reference</title>
<indexterm><primary>policy_rules_t</primary></indexterm>
<para>Inheritance diagram for policy_rules_t:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="structpolicy__rules__t.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1ad52d64fed15cb3c99c86215407657404"/>CONFIG_LIB_EXPORT bool <emphasis role="strong">operator==</emphasis> (const <link linkend="_structpolicy__rules__t">policy_rules_t</link> &amp;other) const</para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1ab8a08fd688ca96a3e80c188b0ce90f5c"/>CONFIG_LIB_EXPORT <emphasis role="strong">policy_rules_t</emphasis> (const <link linkend="_structpolicy__rules__t">policy_rules_t</link> &amp;o)</para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1a85ee8a4ee4dda557e3dc1cc937f437e3"/>CONFIG_LIB_EXPORT <emphasis role="strong">policy_rules_t</emphasis> (<link linkend="_structpolicy__rules__t">policy_rules_t</link> &amp;&amp;o) noexcept</para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1a541f3a06db27b3efe3aa5b9e767502f8"/>CONFIG_LIB_EXPORT <link linkend="_structpolicy__rules__t">policy_rules_t</link> &amp; <emphasis role="strong">operator=</emphasis> (const <link linkend="_structpolicy__rules__t">policy_rules_t</link> &amp;o)</para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1a5f2e63b2d8c85e033a0a274da237742c"/>CONFIG_LIB_EXPORT <link linkend="_structpolicy__rules__t">policy_rules_t</link> &amp; <emphasis role="strong">operator=</emphasis> (<link linkend="_structpolicy__rules__t">policy_rules_t</link> &amp;&amp;o) noexcept</para>
</listitem>
            <listitem><para>virtual CONFIG_LIB_EXPORT boost::property_tree::ptree <link linkend="_structpolicy__rules__t_1aa8027d6d7a7b4ef760d463cbd321d41d">GetNewPropertyTree</link> () const override</para>
</listitem>
            <listitem><para>virtual CONFIG_LIB_EXPORT bool <link linkend="_structpolicy__rules__t_1a564168f897de24d28e86fe44a75e59ac">SetValuesFromPropertyTree</link> (const boost::property_tree::ptree &amp;pt) override</para>
</listitem>
        </itemizedlist>
</simplesect>
Public Member Functions inherited from <link linkend="_class_json_serializable">JsonSerializable</link>        <itemizedlist>
            <listitem><para>std::string <emphasis role="strong">ToJson</emphasis> (bool pretty=true) const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">ToJsonFile</emphasis> (const boost::filesystem::path &amp;filePath, bool pretty=true) const</para>
</listitem>
            <listitem><para>std::string <emphasis role="strong">ToXml</emphasis> () const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">ToXmlFile</emphasis> (const std::string &amp;fileName, char indentCharacter=&apos; &apos;, int indentCount=2) const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">SetValuesFromJson</emphasis> (const std::string &amp;jsonString)</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">SetValuesFromJsonCharArray</emphasis> (const char *data, const std::size_t size)</para>
</listitem>
        </itemizedlist>
<simplesect>
    <title>Public Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1a0ab8f9eb18df4b50ec4b0152049fbd73"/>std::string <emphasis role="strong">m_description</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1ad1daee09967254b888fb1aae5679fdf7"/>uint64_t <emphasis role="strong">m_securityPolicyRuleId</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1a3d01f11c6dab0359e77b924e36a933be"/>std::string <emphasis role="strong">m_securityRole</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1a6e7ca61d188067e4544f8db05ad58c69"/>std::string <emphasis role="strong">m_securitySource</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1ac00c85b3bce91f849e10be6b25945dad"/>std::set&lt; std::string &gt; <emphasis role="strong">m_bundleSource</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1aeb1732526d09c9daf35981636aa5cc30"/>std::set&lt; std::string &gt; <emphasis role="strong">m_bundleFinalDestination</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1a7ae71c477c60afe496b3b940c1eaaf03"/>std::set&lt; uint64_t &gt; <emphasis role="strong">m_securityTargetBlockTypes</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1a4a62723273e2cdb4127d028d54481add"/>std::string <emphasis role="strong">m_securityService</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1ad0367d81b2983ed1000367fbe3a012af"/>std::string <emphasis role="strong">m_securityContext</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1a113c527ac5056378c545f5b01a63e7e2"/>std::string <emphasis role="strong">m_securityFailureEventSetReferenceName</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1a5e3965d39509d3265add56ecf4ed19bb"/>const <link linkend="_structsecurity__failure__event__sets__t">security_failure_event_sets_t</link> * <emphasis role="strong">m_securityFailureEventSetReferencePtr</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_structpolicy__rules__t_1ab52fc440f3740f8884220b77be0aef3c"/>security_context_params_vector_t <emphasis role="strong">m_securityContextParamsVec</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Additional Inherited Members    </title>
Static Public Member Functions inherited from <link linkend="_class_json_serializable">JsonSerializable</link>        <itemizedlist>
            <listitem><para>static bool <emphasis role="strong">LoadTextFileIntoString</emphasis> (const boost::filesystem::path &amp;filePath, std::string &amp;fileContentsAsString)</para>
</listitem>
            <listitem><para>static void <emphasis role="strong">GetAllJsonKeys</emphasis> (const std::string &amp;jsonText, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</para>
</listitem>
            <listitem><para>static void <emphasis role="strong">GetAllJsonKeysLineByLine</emphasis> (std::istream &amp;stream, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInFilePath</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, const boost::filesystem::path &amp;originalUserJsonFilePath, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInString</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, const std::string &amp;originalUserJsonString, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInStream</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, std::istream &amp;originalUserJsonStream, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static std::string <emphasis role="strong">PtToJsonString</emphasis> (const boost::property_tree::ptree &amp;pt, bool pretty=true)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonCharArray</emphasis> (char *data, const std::size_t size, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonStream</emphasis> (std::istream &amp;jsonStream, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonString</emphasis> (const std::string &amp;jsonStr, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonFilePath</emphasis> (const boost::filesystem::path &amp;jsonFilePath, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static std::string <emphasis role="strong">PtToXmlString</emphasis> (const boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static boost::property_tree::ptree <emphasis role="strong">GetPropertyTreeFromXmlString</emphasis> (const std::string &amp;jsonStr)</para>
</listitem>
            <listitem><para>static boost::property_tree::ptree <emphasis role="strong">GetPropertyTreeFromXmlFile</emphasis> (const std::string &amp;xmlFileName)</para>
</listitem>
        </itemizedlist>
</simplesect>
<section>
<title>Member Function Documentation</title>
<anchor xml:id="_structpolicy__rules__t_1aa8027d6d7a7b4ef760d463cbd321d41d"/><section>
    <title>GetNewPropertyTree()</title>
<indexterm><primary>GetNewPropertyTree</primary><secondary>policy_rules_t</secondary></indexterm>
<indexterm><primary>policy_rules_t</primary><secondary>GetNewPropertyTree</secondary></indexterm>
<para><computeroutput>boost::property_tree::ptree policy_rules_t::GetNewPropertyTree ( ) const<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_json_serializable">JsonSerializable</link>.</para>
</section>
<anchor xml:id="_structpolicy__rules__t_1a564168f897de24d28e86fe44a75e59ac"/><section>
    <title>SetValuesFromPropertyTree()</title>
<indexterm><primary>SetValuesFromPropertyTree</primary><secondary>policy_rules_t</secondary></indexterm>
<indexterm><primary>policy_rules_t</primary><secondary>SetValuesFromPropertyTree</secondary></indexterm>
<para><computeroutput>bool policy_rules_t::SetValuesFromPropertyTree (const boost::property_tree::ptree &amp; pt)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_json_serializable">JsonSerializable</link>.</para>
</section>
<para>
The documentation for this struct was generated from the following files:</para>
common/config/include/<link linkend="__bp_sec_config_8h">BpSecConfig.h</link>common/config/src/<link linkend="__bp_sec_config_8cpp">BpSecConfig.cpp</link></section>
</section>
