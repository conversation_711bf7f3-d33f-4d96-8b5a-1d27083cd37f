<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="__bpv6_cbhe_primary_block_8cpp" xml:lang="en-US">
<title>common/bpcodec/src/codec/Bpv6CbhePrimaryBlock.cpp File Reference</title>
<indexterm><primary>common/bpcodec/src/codec/Bpv6CbhePrimaryBlock.cpp</primary></indexterm>
<programlisting linenumbering="unnumbered">#include &lt;stdio.h&gt;<?linebreak?>#include &lt;assert.h&gt;<?linebreak?>#include &lt;string.h&gt;<?linebreak?>#include &quot;codec/bpv6.h&quot;<?linebreak?>#include &lt;inttypes.h&gt;<?linebreak?>#include &quot;Sdnv.h&quot;<?linebreak?>#include &lt;utility&gt;<?linebreak?>#include &quot;Logger.h&quot;<?linebreak?></programlisting><section>
<title>Detailed Description</title>

<para><formalpara><title>Author</title>

<para>Brian Tomko <link xlink:href="mailto:<EMAIL>"><EMAIL></link> </para>

<para>Gilbert Clark</para>
</formalpara>
<formalpara><title>Copyright</title>

<para>Copyright (c) 2021 United States Government as represented by the National Aeronautics and Space Administration. No copyright is claimed in the United States under Title 17, U.S.Code. All Other Rights Reserved.</para>
</formalpara>
</para>
<section xml:id="_import__installation_2src_2test__main_8cpp_1LICENSE">
<title>LICENSE</title>
<para>Released under the NASA Open Source Agreement (NOSA) See LICENSE.md in the source root directory for more information. </para>
</section>
</section>
</section>
