<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="__catalog_entry_8h_source" xml:lang="en-US">
<title>CatalogEntry.h</title>
<indexterm><primary>module/storage/include/CatalogEntry.h</primary></indexterm>
Go to the documentation of this file.<programlisting linenumbering="unnumbered">1 
19 
20 <emphasis role="preprocessor">#ifndef&#32;_CATALOG_ENTRY_H</emphasis>
21 <emphasis role="preprocessor">#define&#32;_CATALOG_ENTRY_H&#32;1</emphasis>
22 
23 <emphasis role="preprocessor">#include&#32;&lt;cstdint&gt;</emphasis>
24 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__memory_manager_tree_array_8h">MemoryManagerTreeArray.h</link>&quot;</emphasis>
25 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="__primary_block_8h">codec/PrimaryBlock.h</link>&quot;</emphasis>
26 <emphasis role="preprocessor">#include&#32;&quot;storage_lib_export.h&quot;</emphasis>
27 
28 <emphasis role="keyword">struct&#32;</emphasis>catalog_entry_t&#32;{
29 &#32;&#32;&#32;&#32;uint64_t&#32;bundleSizeBytes;
30 &#32;&#32;&#32;&#32;uint64_t&#32;payloadSizeBytes;
31 &#32;&#32;&#32;&#32;segment_id_chain_vec_t&#32;segmentIdChainVec;
32 &#32;&#32;&#32;&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;destEid;
33 &#32;&#32;&#32;&#32;uint64_t&#32;encodedAbsExpirationAndCustodyAndPriority;
34 &#32;&#32;&#32;&#32;uint64_t&#32;sequence;
35 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;<emphasis role="keywordtype">void</emphasis>&#32;*&#32;ptrUuidKeyInMap;
36 
37 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;catalog_entry_t();&#32;<emphasis role="comment">//a&#32;default&#32;constructor:&#32;X()</emphasis>
38 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;~catalog_entry_t();&#32;<emphasis role="comment">//a&#32;destructor:&#32;~X()</emphasis>
39 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;catalog_entry_t(<emphasis role="keyword">const</emphasis>&#32;catalog_entry_t&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;constructor:&#32;X(const&#32;X&amp;)</emphasis>
40 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;catalog_entry_t(catalog_entry_t&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;constructor:&#32;X(X&amp;&amp;)</emphasis>
41 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;catalog_entry_t&amp;&#32;operator=(<emphasis role="keyword">const</emphasis>&#32;catalog_entry_t&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;copy&#32;assignment:&#32;operator=(const&#32;X&amp;)</emphasis>
42 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;catalog_entry_t&amp;&#32;operator=(catalog_entry_t&amp;&amp;&#32;o);&#32;<emphasis role="comment">//a&#32;move&#32;assignment:&#32;operator=(X&amp;&amp;)</emphasis>
43 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator==(<emphasis role="keyword">const</emphasis>&#32;catalog_entry_t&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;==</emphasis>
44 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator!=(<emphasis role="keyword">const</emphasis>&#32;catalog_entry_t&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;!=</emphasis>
45 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;operator&lt;(<emphasis role="keyword">const</emphasis>&#32;catalog_entry_t&#32;&amp;&#32;o)&#32;<emphasis role="keyword">const</emphasis>;&#32;<emphasis role="comment">//operator&#32;&lt;&#32;so&#32;it&#32;can&#32;be&#32;used&#32;as&#32;a&#32;map&#32;key</emphasis>
46 
47 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;uint8_t&#32;GetPriorityIndex()&#32;<emphasis role="keyword">const</emphasis>&#32;;
48 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;uint64_t&#32;GetAbsExpiration()&#32;<emphasis role="keyword">const</emphasis>;
49 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;HasCustodyAndFragmentation()&#32;<emphasis role="keyword">const</emphasis>;
50 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;HasCustodyAndNonFragmentation()&#32;<emphasis role="keyword">const</emphasis>;
51 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;HasCustody()&#32;<emphasis role="keyword">const</emphasis>;
52 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">void</emphasis>&#32;Init(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_struct_primary_block">PrimaryBlock</link>&#32;&amp;&#32;primary,&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;paramBundleSizeBytes,&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;paramPayloadSizeBytes,&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;paramNumSegmentsRequired,&#32;<emphasis role="keywordtype">void</emphasis>&#32;*&#32;paramPtrUuidKeyInMap,&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;*bundleEidMaskPtr&#32;=&#32;NULL);
53 };
54 
55 <emphasis role="preprocessor">#endif&#32;</emphasis><emphasis role="comment">//_CATALOG_ENTRY_H</emphasis>
</programlisting></section>
