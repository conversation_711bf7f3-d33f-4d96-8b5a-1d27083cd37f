<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_structhdtn_1_1_stats_logger_1_1metric__t" xml:lang="en-US">
<title>hdtn::StatsLogger::metric_t Struct Reference</title>
<indexterm><primary>hdtn::StatsLogger::metric_t</primary></indexterm>
<para>
<computeroutput>#include &lt;StatsLogger.h&gt;</computeroutput>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_structhdtn_1_1_stats_logger_1_1metric__t_1aba11fd77db31015e02c522b52d5f12c2"/>STATS_LIB_EXPORT <emphasis role="strong">metric_t</emphasis> (std::string name, uint64_t val)</para>
</listitem>
            <listitem><para><anchor xml:id="_structhdtn_1_1_stats_logger_1_1metric__t_1a2729f7717f10f659e440fdbf36101698"/>STATS_LIB_EXPORT <emphasis role="strong">metric_t</emphasis> (std::string name, double val)</para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Public Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_structhdtn_1_1_stats_logger_1_1metric__t_1a020173d46ac20b77641a16d9e3d204fc"/>std::string <emphasis role="strong">name</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
<simplesect>
    <title>Friends    </title>
        <itemizedlist>
            <listitem><para>STATS_LIB_EXPORT friend std::ostream &amp; <link linkend="_structhdtn_1_1_stats_logger_1_1metric__t_1a67e2bf6dbc42391471b26ef7e80c96c0">operator&lt;&lt;</link> (std::ostream &amp;strm, const <link linkend="_structhdtn_1_1_stats_logger_1_1metric__t">StatsLogger::metric_t</link> m)</para>
</listitem>
        </itemizedlist>
</simplesect>
<section>
<title>Detailed Description</title>

<para>Represents a metric name/value pair. Handles storing and logging either an int or float value </para>
</section>
<section>
<title>Friends And Related Symbol Documentation</title>
<anchor xml:id="_structhdtn_1_1_stats_logger_1_1metric__t_1a67e2bf6dbc42391471b26ef7e80c96c0"/><section>
    <title>operator&lt;&lt;</title>
<indexterm><primary>operator&lt;&lt;</primary><secondary>hdtn::StatsLogger::metric_t</secondary></indexterm>
<indexterm><primary>hdtn::StatsLogger::metric_t</primary><secondary>operator&lt;&lt;</secondary></indexterm>
<para><computeroutput>STATS_LIB_EXPORT friend std::ostream &amp; operator&lt;&lt; (std::ostream &amp; strm, const <link linkend="_structhdtn_1_1_stats_logger_1_1metric__t">StatsLogger::metric_t</link> m)<computeroutput>[friend]</computeroutput></computeroutput></para>
<para>Overloads the stream operator to support <link linkend="_structhdtn_1_1_stats_logger_1_1metric__t">metric_t</link> </para>
</section>
<para>
The documentation for this struct was generated from the following files:</para>
common/stats_logger/include/StatsLogger.hcommon/stats_logger/src/StatsLogger.cpp</section>
</section>
