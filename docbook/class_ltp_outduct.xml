<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_class_ltp_outduct" xml:lang="en-US">
<title>LtpOutduct Class Reference</title>
<indexterm><primary>LtpOutduct</primary></indexterm>
<para>Inheritance diagram for LtpOutduct:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="class_ltp_outduct.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_ltp_outduct_1aab5cead3c768a69c794e00dc22615b93"/>OUTDUCT_MANAGER_LIB_EXPORT <emphasis role="strong">LtpOutduct</emphasis> (const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> &amp;outductConfig, const uint64_t outductUuid)</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <link linkend="_class_ltp_outduct_1a81d48464f610522ec573c7495e78d6b8">Init</link> () override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_ltp_outduct_1ae7648fda25c02531e20eddf83394c31b">PopulateOutductTelemetry</link> (std::unique_ptr&lt; <link linkend="_struct_outduct_telemetry__t">OutductTelemetry_t</link> &gt; &amp;outductTelem) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT std::size_t <link linkend="_class_ltp_outduct_1a4d7cf76784a643eda9ebd8a0fe986191">GetTotalBundlesUnacked</link> () const noexcept override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <link linkend="_class_ltp_outduct_1a9a654fee5ed9ca4ee3f3138b928bbacf">Forward</link> (const uint8_t *bundleData, const std::size_t size, std::vector&lt; uint8_t &gt; &amp;&amp;userData) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <link linkend="_class_ltp_outduct_1abe5177bc67fe3e4d53fef360dffd535d">Forward</link> (<link linkend="_classzmq_1_1message__t">zmq::message_t</link> &amp;movableDataZmq, std::vector&lt; uint8_t &gt; &amp;&amp;userData) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <link linkend="_class_ltp_outduct_1a3479a8c6c387e1664b6159a12545ccb5">Forward</link> (padded_vector_uint8_t &amp;movableDataVec, std::vector&lt; uint8_t &gt; &amp;&amp;userData) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_ltp_outduct_1a354c10ce80ee24ff27701922926a3b00">SetOnFailedBundleVecSendCallback</link> (const OnFailedBundleVecSendCallback_t &amp;callback) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_ltp_outduct_1a632663d0a50a01d7a3d679d76e30eaeb">SetOnFailedBundleZmqSendCallback</link> (const OnFailedBundleZmqSendCallback_t &amp;callback) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_ltp_outduct_1acdcadfc8cb8f3c83e65459d1b22048dc">SetOnSuccessfulBundleSendCallback</link> (const OnSuccessfulBundleSendCallback_t &amp;callback) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_ltp_outduct_1a3626135598ecc7ba7cdfec036db3b948">SetOnOutductLinkStatusChangedCallback</link> (const OnOutductLinkStatusChangedCallback_t &amp;callback) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_ltp_outduct_1aeb715cc55e0805751471f3f40c96752d">SetUserAssignedUuid</link> (uint64_t userAssignedUuid) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_ltp_outduct_1a87754765fe696315fbf59e3ecca1b86a">SetRate</link> (uint64_t maxSendRateBitsPerSecOrZeroToDisable) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT uint64_t <link linkend="_class_ltp_outduct_1add88d5b84420f11cd80d96d7023e7213">GetOutductMaxNumberOfBundlesInPipeline</link> () const override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_ltp_outduct_1a5973b53e51122140fbd1004cbc2c3387">Connect</link> () override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <link linkend="_class_ltp_outduct_1a8f591e1118d714124d581a036ab21093">ReadyToForward</link> () override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_ltp_outduct_1a51ae30f116c85e425c7352d253296203">Stop</link> () override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_ltp_outduct_1adce77e5d9243bfc5317925af8742f9f6">GetOutductFinalStats</link> (<link linkend="_struct_outduct_final_stats">OutductFinalStats</link> &amp;finalStats) override</para>
</listitem>
            <listitem><para><anchor xml:id="_class_ltp_outduct_1afd293e7f4da75e4118fc343bcda7cbb2"/>OUTDUCT_MANAGER_LIB_EXPORT void <emphasis role="strong">SetPing</emphasis> (uint64_t senderPingSecondsOrZeroToDisable)</para>
</listitem>
            <listitem><para><anchor xml:id="_class_ltp_outduct_1a5da2609ca141c3b7fc25a4c1bef6919f"/>OUTDUCT_MANAGER_LIB_EXPORT void <emphasis role="strong">SetPingToDefaultConfig</emphasis> ()</para>
</listitem>
        </itemizedlist>
</simplesect>
Public Member Functions inherited from <link linkend="_class_outduct">Outduct</link>        <itemizedlist>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT <emphasis role="strong">Outduct</emphasis> (const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> &amp;outductConfig, const uint64_t outductUuid)</para>
</listitem>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductUuid</emphasis> () const</para>
</listitem>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductMaxSumOfBundleBytesInPipeline</emphasis> () const</para>
</listitem>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductNextHopNodeId</emphasis> () const</para>
</listitem>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT std::string <emphasis role="strong">GetConvergenceLayerName</emphasis> () const</para>
</listitem>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">GetAssumedInitiallyDown</emphasis> () const</para>
</listitem>
        </itemizedlist>
<simplesect>
    <title>Protected Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_ltp_outduct_1a907cdd1972aff8dbb7b8b3f67d303f81"/>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">SetLtpBundleSourcePtr</emphasis> ()=0</para>
</listitem>
        </itemizedlist>
</simplesect>
Protected Member Functions inherited from <link linkend="_class_outduct">Outduct</link>        <itemizedlist>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT <emphasis role="strong">Outduct</emphasis> (const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> &amp;outductConfig, const uint64_t outductUuid, const bool assumedInitiallyDown)</para>
</listitem>
        </itemizedlist>
<simplesect>
    <title>Protected Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_ltp_outduct_1a8514e7a25db9dd4bc872aaccd501dfea"/><link linkend="_class_ltp_bundle_source">LtpBundleSource</link> * <emphasis role="strong">m_ltpBundleSourcePtr</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_class_ltp_outduct_1a5302d7b408be0634e06b166b2cb8a8c6"/><link linkend="_struct_ltp_engine_config">LtpEngineConfig</link> <emphasis role="strong">m_ltpTxCfg</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
Protected Attributes inherited from <link linkend="_class_outduct">Outduct</link>        <itemizedlist>
            <listitem><para>const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> <emphasis role="strong">m_outductConfig</emphasis></para>
</listitem>
            <listitem><para>const uint64_t <emphasis role="strong">m_outductUuid</emphasis></para>
</listitem>
            <listitem><para>const bool <emphasis role="strong">m_assumedInitiallyDown</emphasis></para>
</listitem>
        </itemizedlist>
<simplesect>
    <title>Additional Inherited Members    </title>
Public Attributes inherited from <link linkend="_class_outduct">Outduct</link>        <itemizedlist>
            <listitem><para>bool <emphasis role="strong">m_linkIsUpPerTimeSchedule</emphasis></para>
</listitem>
            <listitem><para>bool <emphasis role="strong">m_physicalLinkStatusIsKnown</emphasis></para>
</listitem>
            <listitem><para>bool <emphasis role="strong">m_linkIsUpPhysically</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
<section>
<title>Member Function Documentation</title>
<anchor xml:id="_class_ltp_outduct_1a5973b53e51122140fbd1004cbc2c3387"/><section>
    <title>Connect()</title>
<indexterm><primary>Connect</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>Connect</secondary></indexterm>
<para><computeroutput>void LtpOutduct::Connect ( )<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1a9a654fee5ed9ca4ee3f3138b928bbacf"/><section>
    <title>Forward()<computeroutput>[1/3]</computeroutput></title>
<indexterm><primary>Forward</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>Forward</secondary></indexterm>
<para><computeroutput>bool LtpOutduct::Forward (const uint8_t * bundleData, const std::size_t size, std::vector&lt; uint8_t &gt; &amp;&amp; userData)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1a3479a8c6c387e1664b6159a12545ccb5"/><section>
    <title>Forward()<computeroutput>[2/3]</computeroutput></title>
<indexterm><primary>Forward</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>Forward</secondary></indexterm>
<para><computeroutput>bool LtpOutduct::Forward (padded_vector_uint8_t &amp; movableDataVec, std::vector&lt; uint8_t &gt; &amp;&amp; userData)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1abe5177bc67fe3e4d53fef360dffd535d"/><section>
    <title>Forward()<computeroutput>[3/3]</computeroutput></title>
<indexterm><primary>Forward</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>Forward</secondary></indexterm>
<para><computeroutput>bool LtpOutduct::Forward (<link linkend="_classzmq_1_1message__t">zmq::message_t</link> &amp; movableDataZmq, std::vector&lt; uint8_t &gt; &amp;&amp; userData)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1adce77e5d9243bfc5317925af8742f9f6"/><section>
    <title>GetOutductFinalStats()</title>
<indexterm><primary>GetOutductFinalStats</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>GetOutductFinalStats</secondary></indexterm>
<para><computeroutput>void LtpOutduct::GetOutductFinalStats (<link linkend="_struct_outduct_final_stats">OutductFinalStats</link> &amp; finalStats)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1add88d5b84420f11cd80d96d7023e7213"/><section>
    <title>GetOutductMaxNumberOfBundlesInPipeline()</title>
<indexterm><primary>GetOutductMaxNumberOfBundlesInPipeline</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>GetOutductMaxNumberOfBundlesInPipeline</secondary></indexterm>
<para><computeroutput>uint64_t LtpOutduct::GetOutductMaxNumberOfBundlesInPipeline ( ) const<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Reimplemented from <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1a4d7cf76784a643eda9ebd8a0fe986191"/><section>
    <title>GetTotalBundlesUnacked()</title>
<indexterm><primary>GetTotalBundlesUnacked</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>GetTotalBundlesUnacked</secondary></indexterm>
<para><computeroutput>std::size_t LtpOutduct::GetTotalBundlesUnacked ( ) const<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput>, <computeroutput>[noexcept]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1a81d48464f610522ec573c7495e78d6b8"/><section>
    <title>Init()</title>
<indexterm><primary>Init</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>Init</secondary></indexterm>
<para><computeroutput>bool LtpOutduct::Init ( )<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Reimplemented from <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1ae7648fda25c02531e20eddf83394c31b"/><section>
    <title>PopulateOutductTelemetry()</title>
<indexterm><primary>PopulateOutductTelemetry</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>PopulateOutductTelemetry</secondary></indexterm>
<para><computeroutput>void LtpOutduct::PopulateOutductTelemetry (std::unique_ptr&lt; <link linkend="_struct_outduct_telemetry__t">OutductTelemetry_t</link> &gt; &amp; outductTelem)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1a8f591e1118d714124d581a036ab21093"/><section>
    <title>ReadyToForward()</title>
<indexterm><primary>ReadyToForward</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>ReadyToForward</secondary></indexterm>
<para><computeroutput>bool LtpOutduct::ReadyToForward ( )<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1a354c10ce80ee24ff27701922926a3b00"/><section>
    <title>SetOnFailedBundleVecSendCallback()</title>
<indexterm><primary>SetOnFailedBundleVecSendCallback</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>SetOnFailedBundleVecSendCallback</secondary></indexterm>
<para><computeroutput>void LtpOutduct::SetOnFailedBundleVecSendCallback (const OnFailedBundleVecSendCallback_t &amp; callback)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1a632663d0a50a01d7a3d679d76e30eaeb"/><section>
    <title>SetOnFailedBundleZmqSendCallback()</title>
<indexterm><primary>SetOnFailedBundleZmqSendCallback</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>SetOnFailedBundleZmqSendCallback</secondary></indexterm>
<para><computeroutput>void LtpOutduct::SetOnFailedBundleZmqSendCallback (const OnFailedBundleZmqSendCallback_t &amp; callback)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1a3626135598ecc7ba7cdfec036db3b948"/><section>
    <title>SetOnOutductLinkStatusChangedCallback()</title>
<indexterm><primary>SetOnOutductLinkStatusChangedCallback</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>SetOnOutductLinkStatusChangedCallback</secondary></indexterm>
<para><computeroutput>void LtpOutduct::SetOnOutductLinkStatusChangedCallback (const OnOutductLinkStatusChangedCallback_t &amp; callback)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1acdcadfc8cb8f3c83e65459d1b22048dc"/><section>
    <title>SetOnSuccessfulBundleSendCallback()</title>
<indexterm><primary>SetOnSuccessfulBundleSendCallback</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>SetOnSuccessfulBundleSendCallback</secondary></indexterm>
<para><computeroutput>void LtpOutduct::SetOnSuccessfulBundleSendCallback (const OnSuccessfulBundleSendCallback_t &amp; callback)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1a87754765fe696315fbf59e3ecca1b86a"/><section>
    <title>SetRate()</title>
<indexterm><primary>SetRate</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>SetRate</secondary></indexterm>
<para><computeroutput>void LtpOutduct::SetRate (uint64_t maxSendRateBitsPerSecOrZeroToDisable)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Reimplemented from <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1aeb715cc55e0805751471f3f40c96752d"/><section>
    <title>SetUserAssignedUuid()</title>
<indexterm><primary>SetUserAssignedUuid</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>SetUserAssignedUuid</secondary></indexterm>
<para><computeroutput>void LtpOutduct::SetUserAssignedUuid (uint64_t userAssignedUuid)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_ltp_outduct_1a51ae30f116c85e425c7352d253296203"/><section>
    <title>Stop()</title>
<indexterm><primary>Stop</primary><secondary>LtpOutduct</secondary></indexterm>
<indexterm><primary>LtpOutduct</primary><secondary>Stop</secondary></indexterm>
<para><computeroutput>void LtpOutduct::Stop ( )<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<para>
The documentation for this class was generated from the following files:</para>
common/outduct_manager/include/<link linkend="__ltp_outduct_8h">LtpOutduct.h</link>common/outduct_manager/src/<link linkend="__ltp_outduct_8cpp">LtpOutduct.cpp</link></section>
</section>
