<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_struct_slip_over_uart_induct_connection_telemetry__t" xml:lang="en-US">
<title>SlipOverUartInductConnectionTelemetry_t Struct Reference</title>
<indexterm><primary>SlipOverUartInductConnectionTelemetry_t</primary></indexterm>
<para>Inheritance diagram for SlipOverUartInductConnectionTelemetry_t:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="struct_slip_over_uart_induct_connection_telemetry__t.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para>virtual TELEMETRY_DEFINITIONS_EXPORT bool <link linkend="_struct_slip_over_uart_induct_connection_telemetry__t_1a24b9c31a190d196038dea4ac59017867">operator==</link> (const <link linkend="_struct_induct_connection_telemetry__t">InductConnectionTelemetry_t</link> &amp;o) const override</para>
</listitem>
            <listitem><para>virtual TELEMETRY_DEFINITIONS_EXPORT bool <link linkend="_struct_slip_over_uart_induct_connection_telemetry__t_1acaffbe3674a117d092a5d5ba83b9a6a7">operator!=</link> (const <link linkend="_struct_induct_connection_telemetry__t">InductConnectionTelemetry_t</link> &amp;o) const override</para>
</listitem>
            <listitem><para>virtual TELEMETRY_DEFINITIONS_EXPORT boost::property_tree::ptree <link linkend="_struct_slip_over_uart_induct_connection_telemetry__t_1a5cb97b799fa7510bd626cd41e73fc9a1">GetNewPropertyTree</link> () const override</para>
</listitem>
            <listitem><para>virtual TELEMETRY_DEFINITIONS_EXPORT bool <link linkend="_struct_slip_over_uart_induct_connection_telemetry__t_1a64d283d164adab106d2b27ca612f3466">SetValuesFromPropertyTree</link> (const boost::property_tree::ptree &amp;pt) override</para>
</listitem>
        </itemizedlist>
</simplesect>
Public Member Functions inherited from <link linkend="_class_json_serializable">JsonSerializable</link>        <itemizedlist>
            <listitem><para>std::string <emphasis role="strong">ToJson</emphasis> (bool pretty=true) const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">ToJsonFile</emphasis> (const boost::filesystem::path &amp;filePath, bool pretty=true) const</para>
</listitem>
            <listitem><para>std::string <emphasis role="strong">ToXml</emphasis> () const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">ToXmlFile</emphasis> (const std::string &amp;fileName, char indentCharacter=&apos; &apos;, int indentCount=2) const</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">SetValuesFromJson</emphasis> (const std::string &amp;jsonString)</para>
</listitem>
            <listitem><para>bool <emphasis role="strong">SetValuesFromJsonCharArray</emphasis> (const char *data, const std::size_t size)</para>
</listitem>
        </itemizedlist>
<simplesect>
    <title>Public Attributes    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1ac256665b3a40a887ac5b2104f9e64d69"/>uint64_t <emphasis role="strong">m_totalSlipBytesSent</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a7ffff34b5b867b621bbc5e0b95bb6bd0"/>uint64_t <emphasis role="strong">m_totalSlipBytesReceived</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a53117d1d134fd92118a650e571519efb"/>uint64_t <emphasis role="strong">m_totalReceivedChunks</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a030833daf06ab893e89a1191da902a1e"/>uint64_t <emphasis role="strong">m_largestReceivedBytesPerChunk</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a25a8d590d10b0f5922d49db1622ff228"/>uint64_t <emphasis role="strong">m_averageReceivedBytesPerChunk</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a749677c90081bdf3988349bd8edf5d2a"/>uint64_t <emphasis role="strong">m_totalBundlesSentAndAcked</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a79739b2fea0a0ca0e6a91fce9db26f6b"/>uint64_t <emphasis role="strong">m_totalBundleBytesSentAndAcked</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a7a058034f8d1d1248e53577f70524709"/>uint64_t <emphasis role="strong">m_totalBundlesSent</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a8acad0659fe75f098e5a78c3d2392bc3"/>uint64_t <emphasis role="strong">m_totalBundleBytesSent</emphasis></para>
</listitem>
            <listitem><para><anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a0d86630ab498612ac3ba8cff5ba5fdd1"/>uint64_t <emphasis role="strong">m_totalBundlesFailedToSend</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
Public Attributes inherited from <link linkend="_struct_induct_connection_telemetry__t">InductConnectionTelemetry_t</link>        <itemizedlist>
            <listitem><para>std::string <emphasis role="strong">m_connectionName</emphasis></para>
</listitem>
            <listitem><para>std::string <emphasis role="strong">m_inputName</emphasis></para>
</listitem>
            <listitem><para>uint64_t <emphasis role="strong">m_totalBundlesReceived</emphasis></para>
</listitem>
            <listitem><para>uint64_t <emphasis role="strong">m_totalBundleBytesReceived</emphasis></para>
</listitem>
        </itemizedlist>
<simplesect>
    <title>Additional Inherited Members    </title>
Static Public Member Functions inherited from <link linkend="_class_json_serializable">JsonSerializable</link>        <itemizedlist>
            <listitem><para>static bool <emphasis role="strong">LoadTextFileIntoString</emphasis> (const boost::filesystem::path &amp;filePath, std::string &amp;fileContentsAsString)</para>
</listitem>
            <listitem><para>static void <emphasis role="strong">GetAllJsonKeys</emphasis> (const std::string &amp;jsonText, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</para>
</listitem>
            <listitem><para>static void <emphasis role="strong">GetAllJsonKeysLineByLine</emphasis> (std::istream &amp;stream, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInFilePath</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, const boost::filesystem::path &amp;originalUserJsonFilePath, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInString</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, const std::string &amp;originalUserJsonString, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">HasUnusedJsonVariablesInStream</emphasis> (const <link linkend="_class_json_serializable">JsonSerializable</link> &amp;config, std::istream &amp;originalUserJsonStream, std::string &amp;returnedErrorMessage)</para>
</listitem>
            <listitem><para>static std::string <emphasis role="strong">PtToJsonString</emphasis> (const boost::property_tree::ptree &amp;pt, bool pretty=true)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonCharArray</emphasis> (char *data, const std::size_t size, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonStream</emphasis> (std::istream &amp;jsonStream, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonString</emphasis> (const std::string &amp;jsonStr, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static bool <emphasis role="strong">GetPropertyTreeFromJsonFilePath</emphasis> (const boost::filesystem::path &amp;jsonFilePath, boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static std::string <emphasis role="strong">PtToXmlString</emphasis> (const boost::property_tree::ptree &amp;pt)</para>
</listitem>
            <listitem><para>static boost::property_tree::ptree <emphasis role="strong">GetPropertyTreeFromXmlString</emphasis> (const std::string &amp;jsonStr)</para>
</listitem>
            <listitem><para>static boost::property_tree::ptree <emphasis role="strong">GetPropertyTreeFromXmlFile</emphasis> (const std::string &amp;xmlFileName)</para>
</listitem>
        </itemizedlist>
</simplesect>
<section>
<title>Member Function Documentation</title>
<anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a5cb97b799fa7510bd626cd41e73fc9a1"/><section>
    <title>GetNewPropertyTree()</title>
<indexterm><primary>GetNewPropertyTree</primary><secondary>SlipOverUartInductConnectionTelemetry_t</secondary></indexterm>
<indexterm><primary>SlipOverUartInductConnectionTelemetry_t</primary><secondary>GetNewPropertyTree</secondary></indexterm>
<para><computeroutput>boost::property_tree::ptree SlipOverUartInductConnectionTelemetry_t::GetNewPropertyTree ( ) const<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Reimplemented from <link linkend="_struct_induct_connection_telemetry__t">InductConnectionTelemetry_t</link>.</para>
</section>
<anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1acaffbe3674a117d092a5d5ba83b9a6a7"/><section>
    <title>operator!=()</title>
<indexterm><primary>operator!=</primary><secondary>SlipOverUartInductConnectionTelemetry_t</secondary></indexterm>
<indexterm><primary>SlipOverUartInductConnectionTelemetry_t</primary><secondary>operator!=</secondary></indexterm>
<para><computeroutput>bool SlipOverUartInductConnectionTelemetry_t::operator!= (const <link linkend="_struct_induct_connection_telemetry__t">InductConnectionTelemetry_t</link> &amp; o) const<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Reimplemented from <link linkend="_struct_induct_connection_telemetry__t">InductConnectionTelemetry_t</link>.</para>
</section>
<anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a24b9c31a190d196038dea4ac59017867"/><section>
    <title>operator==()</title>
<indexterm><primary>operator==</primary><secondary>SlipOverUartInductConnectionTelemetry_t</secondary></indexterm>
<indexterm><primary>SlipOverUartInductConnectionTelemetry_t</primary><secondary>operator==</secondary></indexterm>
<para><computeroutput>bool SlipOverUartInductConnectionTelemetry_t::operator== (const <link linkend="_struct_induct_connection_telemetry__t">InductConnectionTelemetry_t</link> &amp; o) const<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Reimplemented from <link linkend="_struct_induct_connection_telemetry__t">InductConnectionTelemetry_t</link>.</para>
</section>
<anchor xml:id="_struct_slip_over_uart_induct_connection_telemetry__t_1a64d283d164adab106d2b27ca612f3466"/><section>
    <title>SetValuesFromPropertyTree()</title>
<indexterm><primary>SetValuesFromPropertyTree</primary><secondary>SlipOverUartInductConnectionTelemetry_t</secondary></indexterm>
<indexterm><primary>SlipOverUartInductConnectionTelemetry_t</primary><secondary>SetValuesFromPropertyTree</secondary></indexterm>
<para><computeroutput>bool SlipOverUartInductConnectionTelemetry_t::SetValuesFromPropertyTree (const boost::property_tree::ptree &amp; pt)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Reimplemented from <link linkend="_struct_induct_connection_telemetry__t">InductConnectionTelemetry_t</link>.</para>
</section>
<para>
The documentation for this struct was generated from the following files:</para>
common/telemetry_definitions/include/<link linkend="__telemetry_definitions_8h">TelemetryDefinitions.h</link>common/telemetry_definitions/src/TelemetryDefinitions.cpp</section>
</section>
