<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="__custody_timers_8h_source" xml:lang="en-US">
<title>CustodyTimers.h</title>
<indexterm><primary>module/storage/include/CustodyTimers.h</primary></indexterm>
Go to the documentation of this file.<programlisting linenumbering="unnumbered">1 
18 
19 <emphasis role="preprocessor">#ifndef&#32;_CUSTODY_TIMERS_H</emphasis>
20 <emphasis role="preprocessor">#define&#32;_CUSTODY_TIMERS_H&#32;1</emphasis>
21 
22 <emphasis role="preprocessor">#include&#32;&lt;cstdint&gt;</emphasis>
23 <emphasis role="preprocessor">#include&#32;&lt;map&gt;</emphasis>
24 <emphasis role="preprocessor">#include&#32;&lt;list&gt;</emphasis>
25 <emphasis role="preprocessor">#include&#32;&lt;utility&gt;</emphasis>
26 <emphasis role="preprocessor">#include&#32;&lt;string&gt;</emphasis>
27 <emphasis role="preprocessor">#include&#32;&quot;<link linkend="_bpv6_8h">codec/bpv6.h</link>&quot;</emphasis>
28 <emphasis role="preprocessor">#include&#32;&lt;boost/date_time.hpp&gt;</emphasis>
29 <emphasis role="preprocessor">#include&#32;&quot;storage_lib_export.h&quot;</emphasis>
30 
31 <emphasis role="keyword">class&#32;</emphasis>CustodyTimers&#32;{
32 <emphasis role="keyword">private</emphasis>:
33 &#32;&#32;&#32;&#32;CustodyTimers();
34 <emphasis role="keyword">public</emphasis>:
35 &#32;&#32;&#32;&#32;
36 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;CustodyTimers(<emphasis role="keyword">const</emphasis>&#32;boost::posix_time::time_duration&#32;&amp;&#32;timeout);
37 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;~CustodyTimers();
38 
39 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;PollOneAndPopExpiredCustodyTimer(uint64_t&#32;&amp;&#32;custodyId,&#32;<emphasis role="keyword">const</emphasis>&#32;std::vector&lt;cbhe_eid_t&gt;&#32;&amp;&#32;availableDestEids,&#32;<emphasis role="keyword">const</emphasis>&#32;boost::posix_time::ptime&#32;&amp;&#32;nowPtime);
40 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;PollOneAndPopAnyExpiredCustodyTimer(uint64_t&#32;&amp;&#32;custodyId,&#32;<emphasis role="keyword">const</emphasis>&#32;boost::posix_time::ptime&#32;&amp;&#32;nowPtime);
41 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;StartCustodyTransferTimer(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;&amp;&#32;finalDestEid,&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;custodyId);
42 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;<emphasis role="keywordtype">bool</emphasis>&#32;CancelCustodyTransferTimer(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;&amp;&#32;finalDestEid,&#32;<emphasis role="keyword">const</emphasis>&#32;uint64_t&#32;custodyId);
43 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;std::size_t&#32;GetNumCustodyTransferTimers();
44 &#32;&#32;&#32;&#32;STORAGE_LIB_EXPORT&#32;std::size_t&#32;GetNumCustodyTransferTimers(<emphasis role="keyword">const</emphasis>&#32;<link linkend="_structcbhe__eid__t">cbhe_eid_t</link>&#32;&amp;&#32;finalDestEid);
45 
46 <emphasis role="keyword">protected</emphasis>:
47 &#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;std::pair&lt;uint64_t,&#32;boost::posix_time::ptime&gt;&#32;custid_ptime_pair_t;
48 &#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;std::list&lt;custid_ptime_pair_t&gt;&#32;custid_ptime_list_t;
49 &#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;std::map&lt;uint64_t,&#32;custid_ptime_list_t::iterator&gt;&#32;custid_to_listiterator_map_t;
50 &#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;std::pair&lt;uint64_t,&#32;custid_ptime_list_t::iterator&gt;&#32;custid_to_listiterator_map_insertion_element_t;
51 &#32;&#32;&#32;&#32;<emphasis role="keyword">typedef</emphasis>&#32;std::map&lt;cbhe_eid_t,&#32;custid_ptime_list_t&gt;&#32;desteid_to_custidexpirylist_map_t;
52 
53 &#32;&#32;&#32;&#32;desteid_to_custidexpirylist_map_t&#32;m_mapDestEidToCustodyIdExpiryList;
54 &#32;&#32;&#32;&#32;custid_to_listiterator_map_t&#32;m_mapCustodyIdToListIterator;
55 
56 &#32;&#32;&#32;&#32;<emphasis role="keyword">const</emphasis>&#32;boost::posix_time::time_duration&#32;M_CUSTODY_TIMEOUT_DURATION;
57 };
58 
59 
60 <emphasis role="preprocessor">#endif&#32;</emphasis><emphasis role="comment">//_CUSTODY_TIMERS_H</emphasis>
</programlisting></section>
