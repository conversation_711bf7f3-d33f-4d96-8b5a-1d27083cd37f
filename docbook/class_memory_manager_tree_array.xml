<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_class_memory_manager_tree_array" xml:lang="en-US">
<title>MemoryManagerTreeArray Class Reference</title>
<indexterm><primary>MemoryManagerTreeArray</primary></indexterm>
<para>Inheritance diagram for MemoryManagerTreeArray:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="class_memory_manager_tree_array.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para>STORAGE_LIB_EXPORT <link linkend="_class_memory_manager_tree_array_1a19393a79d4a93ae89ebfa86d6cb12194">MemoryManagerTreeArray</link> (const uint64_t maxSegments)</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT bool <link linkend="_class_memory_manager_tree_array_1a142e32e906555aebe67f1c231ecf4680">AllocateSegments_ThreadSafe</link> (segment_id_chain_vec_t &amp;segmentVec)</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT bool <link linkend="_class_memory_manager_tree_array_1a39946e2ab459e03aef0309b79cc44f5d">FreeSegments_ThreadSafe</link> (const segment_id_chain_vec_t &amp;segmentVec)</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT bool <link linkend="_class_memory_manager_tree_array_1ad30ec86217a17414ea4a096d4956e4c8">IsSegmentFree</link> (const segment_id_t segmentId) const</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT void <link linkend="_class_memory_manager_tree_array_1a4076196cdbf3a371a58cb4f919e74fbc">BackupDataToVector</link> (memmanager_t &amp;backup) const</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT const memmanager_t &amp; <link linkend="_class_memory_manager_tree_array_1a4d6670e6ab6547a32eee0a9eb6f62cee">GetVectorsConstRef</link> () const</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT bool <link linkend="_class_memory_manager_tree_array_1a20efc84ba0eb5300bd6943dd62e5c88c">IsBackupEqual</link> (const memmanager_t &amp;backup) const</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT bool <link linkend="_class_memory_manager_tree_array_1a27141ad52dd26d31bc6fe30a83e22c5e">FreeSegmentId_NotThreadSafe</link> (const segment_id_t segmentId)</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT segment_id_t <link linkend="_class_memory_manager_tree_array_1aeea93460b519117ea4d3e39aec28a7af">GetAndSetFirstFreeSegmentId_NotThreadSafe</link> ()</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT bool <link linkend="_class_memory_manager_tree_array_1a6f28fc208e8aa40fce358093f771ca6f">AllocateSegmentId_NotThreadSafe</link> (const segment_id_t segmentId)</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT uint64_t <link linkend="_class_memory_manager_tree_array_1a85421ecfa3c17906e49606075c7a2017">GetNumAllocatedSegments_NotThreadSafe</link> () const noexcept</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT uint64_t <link linkend="_class_memory_manager_tree_array_1addec86d391e8e4ed229938f8454f2591">GetNumAllocatedSegments_ThreadSafe</link> () const</para>
</listitem>
            <listitem><para>STORAGE_LIB_EXPORT uint64_t <link linkend="_class_memory_manager_tree_array_1af2fc922b92336ea17d4adcbe3d570f2a">GetMaxSegments</link> () const noexcept</para>
</listitem>
        </itemizedlist>
</simplesect>
<section>
<title>Constructor &amp; Destructor Documentation</title>
<anchor xml:id="_class_memory_manager_tree_array_1a19393a79d4a93ae89ebfa86d6cb12194"/><section>
    <title>MemoryManagerTreeArray()</title>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<para><computeroutput>MemoryManagerTreeArray::MemoryManagerTreeArray (const uint64_t maxSegments)</computeroutput></para>
<para>Constructor that sets the max number of segments that can be allocated and sets up internal data structures.</para>

<para>
                <formalpara>
                    <title>
Parameters</title>
                    <para>
                    <table frame="all">
                        <tgroup cols="2" align="left" colsep="1" rowsep="1">
                        <colspec colwidth="1*"/>
                        <colspec colwidth="4*"/>
                        <tbody>
                            <row>
<entry>maxSegments</entry><entry>
<para>The max number of segments that can be allocated. Memory requirements for this class is approximately 1 bit per segment. </para>
</entry>
                            </row>
                        </tbody>
                        </tgroup>
                    </table>
                    </para>
                </formalpara>
                </para>
</section>
</section>
<section>
<title>Member Function Documentation</title>
<anchor xml:id="_class_memory_manager_tree_array_1a6f28fc208e8aa40fce358093f771ca6f"/><section>
    <title>AllocateSegmentId_NotThreadSafe()</title>
<indexterm><primary>AllocateSegmentId_NotThreadSafe</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>AllocateSegmentId_NotThreadSafe</secondary></indexterm>
<para><computeroutput>bool MemoryManagerTreeArray::AllocateSegmentId_NotThreadSafe (const segment_id_t segmentId)</computeroutput></para>
<para>Manually allocate the specified segment (if free), useful for restore from disk (rebuilding the <link linkend="_class_memory_manager_tree_array">MemoryManagerTreeArray</link> after power loss) operations.</para>

<para>
                <formalpara>
                    <title>
Parameters</title>
                    <para>
                    <table frame="all">
                        <tgroup cols="2" align="left" colsep="1" rowsep="1">
                        <colspec colwidth="1*"/>
                        <colspec colwidth="4*"/>
                        <tbody>
                            <row>
<entry>segmentId</entry><entry>
<para>The segment to be set as allocated. </para>
</entry>
                            </row>
                        </tbody>
                        </tgroup>
                    </table>
                    </para>
                </formalpara>
                <formalpara><title>Returns</title>

<para>True if and only if the segmentId is available and marked as allocated, or False otherwise. </para>
</formalpara>
<formalpara><title>Postcondition</title>

<para>The internal data structures are updated if and only if the segmentId is available. </para>
</formalpara>
</para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1a142e32e906555aebe67f1c231ecf4680"/><section>
    <title>AllocateSegments_ThreadSafe()</title>
<indexterm><primary>AllocateSegments_ThreadSafe</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>AllocateSegments_ThreadSafe</secondary></indexterm>
<para><computeroutput>bool MemoryManagerTreeArray::AllocateSegments_ThreadSafe (segment_id_chain_vec_t &amp; segmentVec)</computeroutput></para>
<para>Thread safe method to allocate a vector of the first available free segment numbers in numerical order. The desired number of segments must be set prior to the call by calling segmentVec.resize() (i.e. number of desired segments should be the vector size).</para>

<para>
                <formalpara>
                    <title>
Parameters</title>
                    <para>
                    <table frame="all">
                        <tgroup cols="2" align="left" colsep="1" rowsep="1">
                        <colspec colwidth="1*"/>
                        <colspec colwidth="4*"/>
                        <tbody>
                            <row>
<entry>segmentVec</entry><entry>
<para>The preallocated vector of segments to be filled. Will be resized to zero on failure. </para>
</entry>
                            </row>
                        </tbody>
                        </tgroup>
                    </table>
                    </para>
                </formalpara>
                <formalpara><title>Returns</title>

<para>True if the segmentVec was fully populated (the <link linkend="_class_memory_manager_tree_array">MemoryManagerTreeArray</link> was not full prior to the last segment being allocated), or False otherwise. The segmentVec is also resized to zero on a return of False. </para>
</formalpara>
<formalpara><title>Postcondition</title>

<para>The internal data structures are updated if and only if the <link linkend="_class_memory_manager_tree_array">MemoryManagerTreeArray</link> was able to allocate the entire vector of segment IDs. </para>
</formalpara>
</para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1a4076196cdbf3a371a58cb4f919e74fbc"/><section>
    <title>BackupDataToVector()</title>
<indexterm><primary>BackupDataToVector</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>BackupDataToVector</secondary></indexterm>
<para><computeroutput>void MemoryManagerTreeArray::BackupDataToVector (memmanager_t &amp; backup) const</computeroutput></para>
<para>Backup the internal data structure to the given reference parameter, useful for equality comparision in unit testing.</para>

<para>
                <formalpara>
                    <title>
Parameters</title>
                    <para>
                    <table frame="all">
                        <tgroup cols="2" align="left" colsep="1" rowsep="1">
                        <colspec colwidth="1*"/>
                        <colspec colwidth="4*"/>
                        <tbody>
                            <row>
<entry>backup</entry><entry>
<para>The data to copy the internal data structure to. </para>
</entry>
                            </row>
                        </tbody>
                        </tgroup>
                    </table>
                    </para>
                </formalpara>
                </para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1a27141ad52dd26d31bc6fe30a83e22c5e"/><section>
    <title>FreeSegmentId_NotThreadSafe()</title>
<indexterm><primary>FreeSegmentId_NotThreadSafe</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>FreeSegmentId_NotThreadSafe</secondary></indexterm>
<para><computeroutput>bool MemoryManagerTreeArray::FreeSegmentId_NotThreadSafe (const segment_id_t segmentId)</computeroutput></para>
<para>Free the given segment Id.</para>

<para>
                <formalpara>
                    <title>
Parameters</title>
                    <para>
                    <table frame="all">
                        <tgroup cols="2" align="left" colsep="1" rowsep="1">
                        <colspec colwidth="1*"/>
                        <colspec colwidth="4*"/>
                        <tbody>
                            <row>
<entry>segmentId</entry><entry>
<para>The segment to be freed from the internal data structure. </para>
</entry>
                            </row>
                        </tbody>
                        </tgroup>
                    </table>
                    </para>
                </formalpara>
                <formalpara><title>Returns</title>

<para>True if the given segment number was freed, or False otherwise. </para>
</formalpara>
<formalpara><title>Postcondition</title>

<para>The internal data structures are updated if and only if the given segment Id was allocated prior to the call. </para>
</formalpara>
</para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1a39946e2ab459e03aef0309b79cc44f5d"/><section>
    <title>FreeSegments_ThreadSafe()</title>
<indexterm><primary>FreeSegments_ThreadSafe</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>FreeSegments_ThreadSafe</secondary></indexterm>
<para><computeroutput>bool MemoryManagerTreeArray::FreeSegments_ThreadSafe (const segment_id_chain_vec_t &amp; segmentVec)</computeroutput></para>
<para>Thread safe method to free a vector of segment numbers.</para>

<para>
                <formalpara>
                    <title>
Parameters</title>
                    <para>
                    <table frame="all">
                        <tgroup cols="2" align="left" colsep="1" rowsep="1">
                        <colspec colwidth="1*"/>
                        <colspec colwidth="4*"/>
                        <tbody>
                            <row>
<entry>segmentVec</entry><entry>
<para>The vector of segments to mark as free in the internal data structure. </para>
</entry>
                            </row>
                        </tbody>
                        </tgroup>
                    </table>
                    </para>
                </formalpara>
                <formalpara><title>Returns</title>

<para>True if all the segment numbers in the segmentVec were freed, or False otherwise. </para>
</formalpara>
<formalpara><title>Postcondition</title>

<para>The internal data structures are updated for only the segment IDs that were allocated (now they are marked free). Segments that were already free remain unchanged (with False returned). </para>
</formalpara>
</para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1aeea93460b519117ea4d3e39aec28a7af"/><section>
    <title>GetAndSetFirstFreeSegmentId_NotThreadSafe()</title>
<indexterm><primary>GetAndSetFirstFreeSegmentId_NotThreadSafe</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>GetAndSetFirstFreeSegmentId_NotThreadSafe</secondary></indexterm>
<para><computeroutput>segment_id_t MemoryManagerTreeArray::GetAndSetFirstFreeSegmentId_NotThreadSafe ( )</computeroutput></para>
<para>Allocate and get the first available free segment number in numerical order. Requires 6 recursive calls.</para>

<para><formalpara><title>Returns</title>

<para>The first available free segment Id if the <link linkend="_class_memory_manager_tree_array">MemoryManagerTreeArray</link> is not full, or SEGMENT_ID_FULL otherwise. </para>
</formalpara>
<formalpara><title>Postcondition</title>

<para>The internal data structures are updated if and only if the <link linkend="_class_memory_manager_tree_array">MemoryManagerTreeArray</link> is not full. </para>
</formalpara>
</para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1af2fc922b92336ea17d4adcbe3d570f2a"/><section>
    <title>GetMaxSegments()</title>
<indexterm><primary>GetMaxSegments</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>GetMaxSegments</secondary></indexterm>
<para><computeroutput>uint64_t MemoryManagerTreeArray::GetMaxSegments ( ) const<computeroutput>[noexcept]</computeroutput></computeroutput></para>
<para>Get the maximum number of segments (i.e. free + allocated).</para>

<para><formalpara><title>Returns</title>

<para>Maximum number of segments. </para>
</formalpara>
</para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1a85421ecfa3c17906e49606075c7a2017"/><section>
    <title>GetNumAllocatedSegments_NotThreadSafe()</title>
<indexterm><primary>GetNumAllocatedSegments_NotThreadSafe</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>GetNumAllocatedSegments_NotThreadSafe</secondary></indexterm>
<para><computeroutput>uint64_t MemoryManagerTreeArray::GetNumAllocatedSegments_NotThreadSafe ( ) const<computeroutput>[noexcept]</computeroutput></computeroutput></para>
<para>Get the number of allocated segments.</para>

<para><formalpara><title>Returns</title>

<para>Number of allocated segments. </para>
</formalpara>
</para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1addec86d391e8e4ed229938f8454f2591"/><section>
    <title>GetNumAllocatedSegments_ThreadSafe()</title>
<indexterm><primary>GetNumAllocatedSegments_ThreadSafe</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>GetNumAllocatedSegments_ThreadSafe</secondary></indexterm>
<para><computeroutput>uint64_t MemoryManagerTreeArray::GetNumAllocatedSegments_ThreadSafe ( ) const</computeroutput></para>
<para>Thread safe method to get the number of allocated segments.</para>

<para><formalpara><title>Returns</title>

<para>Number of allocated segments. </para>
</formalpara>
</para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1a4d6670e6ab6547a32eee0a9eb6f62cee"/><section>
    <title>GetVectorsConstRef()</title>
<indexterm><primary>GetVectorsConstRef</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>GetVectorsConstRef</secondary></indexterm>
<para><computeroutput>const memmanager_t &amp; MemoryManagerTreeArray::GetVectorsConstRef ( ) const</computeroutput></para>
<para>Get a const reference to the internal data structure, useful for equality comparision in unit testing.</para>

<para><formalpara><title>Returns</title>

<para>A const reference to the internal data structure. </para>
</formalpara>
</para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1a20efc84ba0eb5300bd6943dd62e5c88c"/><section>
    <title>IsBackupEqual()</title>
<indexterm><primary>IsBackupEqual</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>IsBackupEqual</secondary></indexterm>
<para><computeroutput>bool MemoryManagerTreeArray::IsBackupEqual (const memmanager_t &amp; backup) const</computeroutput></para>
<para>Compare the internal data structure to the given const reference parameter, useful for equality comparision in unit testing.</para>

<para>
                <formalpara>
                    <title>
Parameters</title>
                    <para>
                    <table frame="all">
                        <tgroup cols="2" align="left" colsep="1" rowsep="1">
                        <colspec colwidth="1*"/>
                        <colspec colwidth="4*"/>
                        <tbody>
                            <row>
<entry>backup</entry><entry>
<para>The data to compare the internal data structure to. </para>
</entry>
                            </row>
                        </tbody>
                        </tgroup>
                    </table>
                    </para>
                </formalpara>
                <formalpara><title>Returns</title>

<para>True if the data structures are equal, or False otherwise. </para>
</formalpara>
</para>
</section>
<anchor xml:id="_class_memory_manager_tree_array_1ad30ec86217a17414ea4a096d4956e4c8"/><section>
    <title>IsSegmentFree()</title>
<indexterm><primary>IsSegmentFree</primary><secondary>MemoryManagerTreeArray</secondary></indexterm>
<indexterm><primary>MemoryManagerTreeArray</primary><secondary>IsSegmentFree</secondary></indexterm>
<para><computeroutput>bool MemoryManagerTreeArray::IsSegmentFree (const segment_id_t segmentId) const</computeroutput></para>
<para><link linkend="_struct_test">Test</link> if the specified segment is free.</para>

<para>
                <formalpara>
                    <title>
Parameters</title>
                    <para>
                    <table frame="all">
                        <tgroup cols="2" align="left" colsep="1" rowsep="1">
                        <colspec colwidth="1*"/>
                        <colspec colwidth="4*"/>
                        <tbody>
                            <row>
<entry>segmentId</entry><entry>
<para>The segment to be tested. </para>
</entry>
                            </row>
                        </tbody>
                        </tgroup>
                    </table>
                    </para>
                </formalpara>
                <formalpara><title>Returns</title>

<para>True if and only if the segmentId is available/free, or False otherwise. </para>
</formalpara>
</para>
</section>
<para>
The documentation for this class was generated from the following files:</para>
module/storage/include/<link linkend="__memory_manager_tree_array_8h">MemoryManagerTreeArray.h</link>module/storage/src/<link linkend="__memory_manager_tree_array_8cpp">MemoryManagerTreeArray.cpp</link></section>
</section>
