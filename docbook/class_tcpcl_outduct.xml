<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<section xmlns="http://docbook.org/ns/docbook" version="5.0" xmlns:xlink="http://www.w3.org/1999/xlink" xml:id="_class_tcpcl_outduct" xml:lang="en-US">
<title>TcpclOutduct Class Reference</title>
<indexterm><primary>TcpclOutduct</primary></indexterm>
<para>Inheritance diagram for TcpclOutduct:    <informalfigure>
        <mediaobject>
            <imageobject>
                <imagedata width="50%" align="center" valign="middle" scalefit="0" fileref="class_tcpcl_outduct.png"></imagedata>
            </imageobject>
        </mediaobject>
    </informalfigure>
</para>
<simplesect>
    <title>Public Member Functions    </title>
        <itemizedlist>
            <listitem><para><anchor xml:id="_class_tcpcl_outduct_1a675a9f87447f87cbb5617aedb1c470f2"/>OUTDUCT_MANAGER_LIB_EXPORT <emphasis role="strong">TcpclOutduct</emphasis> (const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> &amp;outductConfig, const uint64_t myNodeId, const uint64_t outductUuid, const OutductOpportunisticProcessReceivedBundleCallback_t &amp;outductOpportunisticProcessReceivedBundleCallback=OutductOpportunisticProcessReceivedBundleCallback_t())</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_tcpcl_outduct_1a4fafdbe7305454f0b9c70ee99d3a9a39">PopulateOutductTelemetry</link> (std::unique_ptr&lt; <link linkend="_struct_outduct_telemetry__t">OutductTelemetry_t</link> &gt; &amp;outductTelem) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT std::size_t <link linkend="_class_tcpcl_outduct_1a0c49186b3f885dfd74b20f1861f6d88a">GetTotalBundlesUnacked</link> () const noexcept override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <link linkend="_class_tcpcl_outduct_1a8a0bf21996f4f3790018537ff5ff671b">Forward</link> (const uint8_t *bundleData, const std::size_t size, std::vector&lt; uint8_t &gt; &amp;&amp;userData) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <link linkend="_class_tcpcl_outduct_1a88a9c737f354e31f23fbec57e9dfde70">Forward</link> (<link linkend="_classzmq_1_1message__t">zmq::message_t</link> &amp;movableDataZmq, std::vector&lt; uint8_t &gt; &amp;&amp;userData) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <link linkend="_class_tcpcl_outduct_1a0a0dd8c09094fae24c3e38e7b12c1671">Forward</link> (padded_vector_uint8_t &amp;movableDataVec, std::vector&lt; uint8_t &gt; &amp;&amp;userData) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_tcpcl_outduct_1a0fe72f6937059f29585537d7b77c4978">SetOnFailedBundleVecSendCallback</link> (const OnFailedBundleVecSendCallback_t &amp;callback) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_tcpcl_outduct_1abb959858e6b9b2906c893de94de51404">SetOnFailedBundleZmqSendCallback</link> (const OnFailedBundleZmqSendCallback_t &amp;callback) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_tcpcl_outduct_1a8980eecd20f46ac9022f9adf1bf2fac9">SetOnSuccessfulBundleSendCallback</link> (const OnSuccessfulBundleSendCallback_t &amp;callback) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_tcpcl_outduct_1af1c95899cf22b7911ce5f705fff80b82">SetOnOutductLinkStatusChangedCallback</link> (const OnOutductLinkStatusChangedCallback_t &amp;callback) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_tcpcl_outduct_1ab9f83a78ce22a64be6068368c6bb1531">SetUserAssignedUuid</link> (uint64_t userAssignedUuid) override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_tcpcl_outduct_1ac74aada0fd688898ce9ddef3edfda79f">Connect</link> () override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <link linkend="_class_tcpcl_outduct_1a8d8e86b8fa675ae8180ebe0bac329fd3">ReadyToForward</link> () override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_tcpcl_outduct_1ac18f77b27b66545b9afd8fceb4df32c6">Stop</link> () override</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <link linkend="_class_tcpcl_outduct_1a2c32c3342b1414d6a4c7b1516c573e5e">GetOutductFinalStats</link> (<link linkend="_struct_outduct_final_stats">OutductFinalStats</link> &amp;finalStats) override</para>
</listitem>
        </itemizedlist>
</simplesect>
Public Member Functions inherited from <link linkend="_class_outduct">Outduct</link>        <itemizedlist>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT <emphasis role="strong">Outduct</emphasis> (const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> &amp;outductConfig, const uint64_t outductUuid)</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT void <emphasis role="strong">SetRate</emphasis> (uint64_t maxSendRateBitsPerSecOrZeroToDisable)</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">Init</emphasis> ()</para>
</listitem>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductUuid</emphasis> () const</para>
</listitem>
            <listitem><para>virtual OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductMaxNumberOfBundlesInPipeline</emphasis> () const</para>
</listitem>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductMaxSumOfBundleBytesInPipeline</emphasis> () const</para>
</listitem>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT uint64_t <emphasis role="strong">GetOutductNextHopNodeId</emphasis> () const</para>
</listitem>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT std::string <emphasis role="strong">GetConvergenceLayerName</emphasis> () const</para>
</listitem>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT bool <emphasis role="strong">GetAssumedInitiallyDown</emphasis> () const</para>
</listitem>
        </itemizedlist>
<simplesect>
    <title>Additional Inherited Members    </title>
Public Attributes inherited from <link linkend="_class_outduct">Outduct</link>        <itemizedlist>
            <listitem><para>bool <emphasis role="strong">m_linkIsUpPerTimeSchedule</emphasis></para>
</listitem>
            <listitem><para>bool <emphasis role="strong">m_physicalLinkStatusIsKnown</emphasis></para>
</listitem>
            <listitem><para>bool <emphasis role="strong">m_linkIsUpPhysically</emphasis></para>
</listitem>
        </itemizedlist>
</simplesect>
Protected Member Functions inherited from <link linkend="_class_outduct">Outduct</link>        <itemizedlist>
            <listitem><para>OUTDUCT_MANAGER_LIB_EXPORT <emphasis role="strong">Outduct</emphasis> (const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> &amp;outductConfig, const uint64_t outductUuid, const bool assumedInitiallyDown)</para>
</listitem>
        </itemizedlist>
Protected Attributes inherited from <link linkend="_class_outduct">Outduct</link>        <itemizedlist>
            <listitem><para>const <link linkend="_structoutduct__element__config__t">outduct_element_config_t</link> <emphasis role="strong">m_outductConfig</emphasis></para>
</listitem>
            <listitem><para>const uint64_t <emphasis role="strong">m_outductUuid</emphasis></para>
</listitem>
            <listitem><para>const bool <emphasis role="strong">m_assumedInitiallyDown</emphasis></para>
</listitem>
        </itemizedlist>
<section>
<title>Member Function Documentation</title>
<anchor xml:id="_class_tcpcl_outduct_1ac74aada0fd688898ce9ddef3edfda79f"/><section>
    <title>Connect()</title>
<indexterm><primary>Connect</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>Connect</secondary></indexterm>
<para><computeroutput>void TcpclOutduct::Connect ( )<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1a8a0bf21996f4f3790018537ff5ff671b"/><section>
    <title>Forward()<computeroutput>[1/3]</computeroutput></title>
<indexterm><primary>Forward</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>Forward</secondary></indexterm>
<para><computeroutput>bool TcpclOutduct::Forward (const uint8_t * bundleData, const std::size_t size, std::vector&lt; uint8_t &gt; &amp;&amp; userData)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1a0a0dd8c09094fae24c3e38e7b12c1671"/><section>
    <title>Forward()<computeroutput>[2/3]</computeroutput></title>
<indexterm><primary>Forward</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>Forward</secondary></indexterm>
<para><computeroutput>bool TcpclOutduct::Forward (padded_vector_uint8_t &amp; movableDataVec, std::vector&lt; uint8_t &gt; &amp;&amp; userData)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1a88a9c737f354e31f23fbec57e9dfde70"/><section>
    <title>Forward()<computeroutput>[3/3]</computeroutput></title>
<indexterm><primary>Forward</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>Forward</secondary></indexterm>
<para><computeroutput>bool TcpclOutduct::Forward (<link linkend="_classzmq_1_1message__t">zmq::message_t</link> &amp; movableDataZmq, std::vector&lt; uint8_t &gt; &amp;&amp; userData)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1a2c32c3342b1414d6a4c7b1516c573e5e"/><section>
    <title>GetOutductFinalStats()</title>
<indexterm><primary>GetOutductFinalStats</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>GetOutductFinalStats</secondary></indexterm>
<para><computeroutput>void TcpclOutduct::GetOutductFinalStats (<link linkend="_struct_outduct_final_stats">OutductFinalStats</link> &amp; finalStats)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1a0c49186b3f885dfd74b20f1861f6d88a"/><section>
    <title>GetTotalBundlesUnacked()</title>
<indexterm><primary>GetTotalBundlesUnacked</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>GetTotalBundlesUnacked</secondary></indexterm>
<para><computeroutput>std::size_t TcpclOutduct::GetTotalBundlesUnacked ( ) const<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput>, <computeroutput>[noexcept]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1a4fafdbe7305454f0b9c70ee99d3a9a39"/><section>
    <title>PopulateOutductTelemetry()</title>
<indexterm><primary>PopulateOutductTelemetry</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>PopulateOutductTelemetry</secondary></indexterm>
<para><computeroutput>void TcpclOutduct::PopulateOutductTelemetry (std::unique_ptr&lt; <link linkend="_struct_outduct_telemetry__t">OutductTelemetry_t</link> &gt; &amp; outductTelem)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1a8d8e86b8fa675ae8180ebe0bac329fd3"/><section>
    <title>ReadyToForward()</title>
<indexterm><primary>ReadyToForward</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>ReadyToForward</secondary></indexterm>
<para><computeroutput>bool TcpclOutduct::ReadyToForward ( )<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1a0fe72f6937059f29585537d7b77c4978"/><section>
    <title>SetOnFailedBundleVecSendCallback()</title>
<indexterm><primary>SetOnFailedBundleVecSendCallback</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>SetOnFailedBundleVecSendCallback</secondary></indexterm>
<para><computeroutput>void TcpclOutduct::SetOnFailedBundleVecSendCallback (const OnFailedBundleVecSendCallback_t &amp; callback)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1abb959858e6b9b2906c893de94de51404"/><section>
    <title>SetOnFailedBundleZmqSendCallback()</title>
<indexterm><primary>SetOnFailedBundleZmqSendCallback</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>SetOnFailedBundleZmqSendCallback</secondary></indexterm>
<para><computeroutput>void TcpclOutduct::SetOnFailedBundleZmqSendCallback (const OnFailedBundleZmqSendCallback_t &amp; callback)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1af1c95899cf22b7911ce5f705fff80b82"/><section>
    <title>SetOnOutductLinkStatusChangedCallback()</title>
<indexterm><primary>SetOnOutductLinkStatusChangedCallback</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>SetOnOutductLinkStatusChangedCallback</secondary></indexterm>
<para><computeroutput>void TcpclOutduct::SetOnOutductLinkStatusChangedCallback (const OnOutductLinkStatusChangedCallback_t &amp; callback)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1a8980eecd20f46ac9022f9adf1bf2fac9"/><section>
    <title>SetOnSuccessfulBundleSendCallback()</title>
<indexterm><primary>SetOnSuccessfulBundleSendCallback</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>SetOnSuccessfulBundleSendCallback</secondary></indexterm>
<para><computeroutput>void TcpclOutduct::SetOnSuccessfulBundleSendCallback (const OnSuccessfulBundleSendCallback_t &amp; callback)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1ab9f83a78ce22a64be6068368c6bb1531"/><section>
    <title>SetUserAssignedUuid()</title>
<indexterm><primary>SetUserAssignedUuid</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>SetUserAssignedUuid</secondary></indexterm>
<para><computeroutput>void TcpclOutduct::SetUserAssignedUuid (uint64_t userAssignedUuid)<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<anchor xml:id="_class_tcpcl_outduct_1ac18f77b27b66545b9afd8fceb4df32c6"/><section>
    <title>Stop()</title>
<indexterm><primary>Stop</primary><secondary>TcpclOutduct</secondary></indexterm>
<indexterm><primary>TcpclOutduct</primary><secondary>Stop</secondary></indexterm>
<para><computeroutput>void TcpclOutduct::Stop ( )<computeroutput>[override]</computeroutput>, <computeroutput>[virtual]</computeroutput></computeroutput></para><para>
Implements <link linkend="_class_outduct">Outduct</link>.</para>
</section>
<para>
The documentation for this class was generated from the following files:</para>
common/outduct_manager/include/<link linkend="__tcpcl_outduct_8h">TcpclOutduct.h</link>common/outduct_manager/src/<link linkend="__tcpcl_outduct_8cpp">TcpclOutduct.cpp</link></section>
</section>
