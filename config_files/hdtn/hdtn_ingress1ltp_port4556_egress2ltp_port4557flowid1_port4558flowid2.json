{"hdtnConfigName": "my hdtn config", "userInterfaceOn": true, "mySchemeName": "unused_scheme_name", "myNodeId": 10, "myBpEchoServiceId": 2047, "myCustodialSsp": "unused_custodial_ssp", "myCustodialServiceId": 0, "myRouterServiceId": 100, "isAcsAware": true, "acsMaxFillsPerAcsPacket": 100, "acsSendPeriodMilliseconds": 1000, "retransmitBundleAfterNoCustodySignalMilliseconds": 10000, "maxBundleSizeBytes": 10000000, "maxIngressBundleWaitOnEgressMilliseconds": 2000, "bufferRxToStorageOnLinkUpSaturation": false, "maxLtpReceiveUdpPacketSizeBytes": 65536, "neighborDepletedStorageDelaySeconds": 0, "fragmentBundlesLargerThanBytes": 0, "enforceBundlePriority": false, "zmqBoundRouterPubSubPortPath": 10200, "zmqBoundTelemApiPortPath": 10305, "inductsConfig": {"inductConfigName": "myconfig", "inductVector": [{"name": "i1", "convergenceLayer": "ltp_over_udp", "boundPort": 4556, "numRxCircularBufferElements": 100, "thisLtpEngineId": 100, "remoteLtpEngineId": 200, "ltpReportSegmentMtu": 1000, "oneWayLightTimeMs": 1000, "oneWayMarginTimeMs": 200, "clientServiceId": 1, "preallocatedRedDataBytes": 200000, "ltpMaxRetriesPerSerialNumber": 5, "ltpRandomNumberSizeBits": 64, "ltpRemoteUdpHostname": "localhost", "ltpRemoteUdpPort": 1113, "ltpRxDataSegmentSessionNumberRecreationPreventerHistorySize": 1000, "ltpMaxExpectedSimultaneousSessions": 500, "ltpMaxUdpPacketsToSendPerSystemCall": 1, "delaySendingOfReportSegmentsTimeMsOrZeroToDisable": 20, "keepActiveSessionDataOnDisk": false, "activeSessionDataOnDiskNewFileDurationMs": 2000, "activeSessionDataOnDiskDirectory": "./"}, {"name": "i2", "convergenceLayer": "ltp_over_udp", "boundPort": 4556, "numRxCircularBufferElements": 100, "thisLtpEngineId": 100, "remoteLtpEngineId": 201, "ltpReportSegmentMtu": 1000, "oneWayLightTimeMs": 1000, "oneWayMarginTimeMs": 200, "clientServiceId": 1, "preallocatedRedDataBytes": 200000, "ltpMaxRetriesPerSerialNumber": 5, "ltpRandomNumberSizeBits": 64, "ltpRemoteUdpHostname": "localhost", "ltpRemoteUdpPort": 2113, "ltpRxDataSegmentSessionNumberRecreationPreventerHistorySize": 1000, "ltpMaxExpectedSimultaneousSessions": 500, "ltpMaxUdpPacketsToSendPerSystemCall": 1, "delaySendingOfReportSegmentsTimeMsOrZeroToDisable": 20, "keepActiveSessionDataOnDisk": false, "activeSessionDataOnDiskNewFileDurationMs": 2000, "activeSessionDataOnDiskDirectory": "./"}]}, "outductsConfig": {"outductConfigName": "myconfig", "outductVector": [{"name": "for egress", "convergenceLayer": "ltp_over_udp", "nextHopNodeId": 1, "remoteHostname": "localhost", "remotePort": 4557, "maxNumberOfBundlesInPipeline": 50, "maxSumOfBundleBytesInPipeline": 50000000, "thisLtpEngineId": 20004557, "remoteLtpEngineId": 10004557, "ltpDataSegmentMtu": 60000, "oneWayLightTimeMs": 1000, "oneWayMarginTimeMs": 200, "clientServiceId": 1, "numRxCircularBufferElements": 100, "ltpMaxRetriesPerSerialNumber": 5, "ltpCheckpointEveryNthDataSegment": 0, "ltpRandomNumberSizeBits": 64, "ltpSenderBoundPort": 4556, "ltpMaxUdpPacketsToSendPerSystemCall": 1, "ltpSenderPingSecondsOrZeroToDisable": 15, "delaySendingOfDataSegmentsTimeMsOrZeroToDisable": 20, "keepActiveSessionDataOnDisk": false, "activeSessionDataOnDiskNewFileDurationMs": 2000, "activeSessionDataOnDiskDirectory": "./"}, {"name": "for egress", "convergenceLayer": "ltp_over_udp", "nextHopNodeId": 2, "remoteHostname": "localhost", "remotePort": 4558, "maxNumberOfBundlesInPipeline": 50, "maxSumOfBundleBytesInPipeline": 50000000, "thisLtpEngineId": 20004558, "remoteLtpEngineId": 10004558, "ltpDataSegmentMtu": 60000, "oneWayLightTimeMs": 1000, "oneWayMarginTimeMs": 200, "clientServiceId": 1, "numRxCircularBufferElements": 100, "ltpMaxRetriesPerSerialNumber": 5, "ltpCheckpointEveryNthDataSegment": 0, "ltpRandomNumberSizeBits": 64, "ltpSenderBoundPort": 4556, "ltpMaxUdpPacketsToSendPerSystemCall": 1, "ltpSenderPingSecondsOrZeroToDisable": 15, "delaySendingOfDataSegmentsTimeMsOrZeroToDisable": 20, "keepActiveSessionDataOnDisk": false, "activeSessionDataOnDiskNewFileDurationMs": 2000, "activeSessionDataOnDiskDirectory": "./"}]}, "storageConfig": {"storageImplementation": "asio_single_threaded", "tryToRestoreFromDisk": false, "autoDeleteFilesOnExit": true, "totalStorageCapacityBytes": **********, "storageDeletionPolicy": "never", "storageDiskConfigVector": [{"name": "d1", "storeFilePath": "./store1.bin"}, {"name": "d2", "storeFilePath": "./store2.bin"}]}}