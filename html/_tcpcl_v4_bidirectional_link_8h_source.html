<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: common/tcpcl/include/TcpclV4BidirectionalLink.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('_tcpcl_v4_bidirectional_link_8h_source.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">TcpclV4BidirectionalLink.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_tcpcl_v4_bidirectional_link_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span> </div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="preprocessor">#ifndef _TCPCLV4_BIDIRECTIONAL_LINK_H</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="preprocessor">#define _TCPCLV4_BIDIRECTIONAL_LINK_H 1</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span> </div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span><span class="preprocessor">#include &lt;boost/thread.hpp&gt;</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span><span class="preprocessor">#include &lt;boost/asio.hpp&gt;</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="preprocessor">#include &lt;map&gt;</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span><span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="preprocessor">#include &lt;memory&gt;</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="preprocessor">#include &quot;<a class="code" href="_tcpcl_v4_8h.html">TcpclV4.h</a>&quot;</span></div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="preprocessor">#include &quot;<a class="code" href="_tcp_async_sender_8h.html">TcpAsyncSender.h</a>&quot;</span></div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span><span class="preprocessor">#include &quot;<a class="code" href="_telemetry_definitions_8h.html">TelemetryDefinitions.h</a>&quot;</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span><span class="preprocessor">#include &quot;<a class="code" href="_circular_index_buffer_single_producer_single_consumer_configurable_8h.html">CircularIndexBufferSingleProducerSingleConsumerConfigurable.h</a>&quot;</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span><span class="preprocessor">#include &quot;<a class="code" href="_bidirectional_link_8h.html">BidirectionalLink.h</a>&quot;</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span><span class="preprocessor">#include &quot;<a class="code" href="_bundle_callback_function_defines_8h.html">BundleCallbackFunctionDefines.h</a>&quot;</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="preprocessor">#include &lt;atomic&gt;</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span><span class="preprocessor">#ifdef OPENSSL_SUPPORT_ENABLED</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span><span class="preprocessor">#include &lt;boost/asio/ssl.hpp&gt;</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span> </div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span><span class="comment">//DO NOT USE THE FOLLOWING from earlier TCPCLv4:</span></div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span><span class="comment">//generate an x509 version 3 key with an IPN URI subjectAltName:</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span><span class="comment">//C:\openssl-1.1.1e_msvc2017\bin\openssl.exe req -x509 -newkey rsa:4096 -nodes -keyout privatekey.pem -out cert.pem -sha256 -days 365 -extensions v3_req -extensions v3_ca -subj &quot;/C=US/ST=Ohio/L=Cleveland/O=NASA/OU=HDTN/CN=localhost&quot; -addext &quot;subjectAltName = URI:ipn:10.0&quot; -config C:\Users\<USER>\Downloads\openssl-1.1.1e\apps\openssl.cnf</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span> </div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span><span class="comment">//INSTEAD, USE THE RFC 9174 certificate profile:</span></div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span><span class="comment">//generate an x509 version 3 key with an otherName subjectAltName:</span></div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span><span class="comment">//C:\openssl-1.1.1e_msvc2017\bin\openssl.exe req -x509 -newkey rsa:4096 -nodes -keyout privatekey.pem -out cert.pem -sha256 -days 365 -extensions v3_req -extensions v3_ca -subj &quot;/C=US/ST=Ohio/L=Cleveland/O=NASA/OU=HDTN/CN=localhost&quot; -addext &quot;subjectAltName = otherName:*******.*******.11;IA5:ipn:10.0&quot; -config C:\Users\<USER>\Downloads\openssl-1.1.1e\apps\openssl.cnf</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span> </div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span> </div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span><span class="comment">//generate the dh file:</span></div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span><span class="comment">//C:\openssl-1.1.1e_msvc2017\bin\openssl.exe dhparam -outform PEM -out dh4096.pem 4096</span></div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span> </div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span> </div>
<div class="foldopen" id="foldopen00057" data-start="{" data-end="};">
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno"><a class="line" href="class_tcpcl_v4_bidirectional_link.html">   57</a></span><span class="keyword">class </span>CLASS_VISIBILITY_TCPCL_LIB TcpclV4BidirectionalLink : <span class="keyword">public</span> BidirectionalLink {</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span><span class="keyword">public</span>:</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span>    <span class="comment">/*</span></div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span><span class="comment">    //sink</span></div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span><span class="comment">    typedef boost::function&lt;void(std::vector&lt;uint8_t&gt; &amp; wholeBundleVec)&gt; WholeBundleReadyCallback_t;</span></div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span><span class="comment">    typedef boost::function&lt;void()&gt; NotifyReadyToDeleteCallback_t;</span></div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span><span class="comment">    typedef boost::function&lt;bool(std::pair&lt;std::unique_ptr&lt;zmq::message_t&gt;, std::vector&lt;uint8_t&gt; &gt; &amp; bundleDataPair)&gt; TryGetOpportunisticDataFunction_t;</span></div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span><span class="comment">    typedef boost::function&lt;void()&gt; NotifyOpportunisticDataAckedCallback_t;</span></div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span><span class="comment">    typedef boost::function&lt;void(TcpclV4BidirectionalLink * thisTcpclBundleSinkPtr)&gt; OnContactHeaderCallback_t;</span></div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span><span class="comment">    //source</span></div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span><span class="comment">    typedef boost::function&lt;void(std::vector&lt;uint8_t&gt; &amp; movableBundle)&gt; OutductOpportunisticProcessReceivedBundleCallback_t;</span></div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span><span class="comment">    typedef boost::function&lt;void()&gt; OnSuccessfulAckCallback_t;*/</span></div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span>    TcpclV4BidirectionalLink();</div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span><span class="keyword">public</span>:</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span> </div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span> </div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span>    TCPCL_LIB_EXPORT TcpclV4BidirectionalLink(</div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span>        <span class="keyword">const</span> std::string &amp; implementationStringForCout,</div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span>        <span class="keyword">const</span> uint64_t reconnectionDelaySecondsIfNotZero, <span class="comment">//source</span></div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span>        <span class="keyword">const</span> <span class="keywordtype">bool</span> deleteSocketAfterShutdown,</div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span>        <span class="keyword">const</span> <span class="keywordtype">bool</span> isActiveEntity,</div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span>        <span class="keyword">const</span> uint16_t desiredKeepAliveIntervalSeconds,</div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span>        boost::asio::io_service * externalIoServicePtr,</div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span>        <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> myMaxTxUnackedBundles,</div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span>        <span class="keyword">const</span> uint64_t myMaxRxSegmentSizeBytes,</div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span>        <span class="keyword">const</span> uint64_t myMaxRxBundleSizeBytes,</div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span>        <span class="keyword">const</span> uint64_t myNodeId,</div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span>        <span class="keyword">const</span> std::string &amp; expectedRemoteEidUriStringIfNotEmpty,</div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span>        <span class="keyword">const</span> <span class="keywordtype">bool</span> tryUseTls,</div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span>        <span class="keyword">const</span> <span class="keywordtype">bool</span> tlsIsRequired</div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>    );</div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span> </div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>    TCPCL_LIB_EXPORT <span class="keyword">virtual</span> ~TcpclV4BidirectionalLink();</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span>    TCPCL_LIB_EXPORT <span class="keywordtype">bool</span> BaseClass_Forward(<span class="keyword">const</span> uint8_t* bundleData, <span class="keyword">const</span> std::size_t size, std::vector&lt;uint8_t&gt;&amp;&amp; userData);</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span>    TCPCL_LIB_EXPORT <span class="keywordtype">bool</span> BaseClass_Forward(padded_vector_uint8_t&amp; dataVec, std::vector&lt;uint8_t&gt;&amp;&amp; userData);</div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span>    TCPCL_LIB_EXPORT <span class="keywordtype">bool</span> BaseClass_Forward(<a class="code hl_class" href="classzmq_1_1message__t.html">zmq::message_t</a> &amp; dataZmq, std::vector&lt;uint8_t&gt;&amp;&amp; userData);</div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span>    TCPCL_LIB_EXPORT <span class="keywordtype">bool</span> BaseClass_Forward(std::unique_ptr&lt;zmq::message_t&gt; &amp; zmqMessageUniquePtr, padded_vector_uint8_t&amp; vecMessage, <span class="keyword">const</span> <span class="keywordtype">bool</span> usingZmqData, std::vector&lt;uint8_t&gt;&amp;&amp; userData);</div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span> </div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span>    TCPCL_LIB_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> Virtual_GetMaxTxBundlesInPipeline() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span> </div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_SetOnFailedBundleVecSendCallback(<span class="keyword">const</span> OnFailedBundleVecSendCallback_t&amp; callback);</div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_SetOnFailedBundleZmqSendCallback(<span class="keyword">const</span> OnFailedBundleZmqSendCallback_t&amp; callback);</div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_SetOnSuccessfulBundleSendCallback(<span class="keyword">const</span> OnSuccessfulBundleSendCallback_t&amp; callback);</div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_SetOnOutductLinkStatusChangedCallback(<span class="keyword">const</span> OnOutductLinkStatusChangedCallback_t&amp; callback);</div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_SetUserAssignedUuid(uint64_t userAssignedUuid);</div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_GetTelemetry(<a class="code hl_struct" href="struct_tcpcl_v4_induct_connection_telemetry__t.html">TcpclV4InductConnectionTelemetry_t</a>&amp; telem) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno">  104</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_GetTelemetry(<a class="code hl_struct" href="struct_tcpcl_v4_outduct_telemetry__t.html">TcpclV4OutductTelemetry_t</a>&amp; telem) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span> </div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span><span class="keyword">protected</span>:</div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_SendContactHeader();</div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_SendSessionInit();</div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_TryToWaitForAllBundlesToFinishSending();</div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span>    TCPCL_LIB_EXPORT <span class="keywordtype">void</span> BaseClass_DoTcpclShutdown(<span class="keywordtype">bool</span> doCleanShutdown, TCPCLV4_SESSION_TERMINATION_REASON_CODES sessionTerminationReasonCode, <span class="keywordtype">bool</span> isAckOfAnEarlierSessionTerminationMessage);</div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> Virtual_OnTcpclShutdownComplete_CalledFromIoServiceThread() = 0;</div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> Virtual_OnSuccessfulWholeBundleAcknowledged() = 0;</div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> Virtual_WholeBundleReady(padded_vector_uint8_t &amp; wholeBundleVec) = 0;</div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span>    TCPCL_LIB_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">void</span> Virtual_OnTcpSendSuccessful_CalledFromIoServiceThread();</div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span>    TCPCL_LIB_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">void</span> Virtual_OnTcpSendContactHeaderSuccessful_CalledFromIoServiceThread();</div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span>    TCPCL_LIB_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">void</span> Virtual_OnSessionInitReceivedAndProcessedSuccessfully();</div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span> </div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno">  118</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno">  119</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_DataSegmentCallback(padded_vector_uint8_t &amp; dataSegmentDataVec, <span class="keywordtype">bool</span> isStartFlag, <span class="keywordtype">bool</span> isEndFlag,</div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno">  120</span>        uint64_t transferId, <span class="keyword">const</span> <a class="code hl_struct" href="struct_tcpcl_v4_1_1tcpclv4__extensions__t.html">TcpclV4::tcpclv4_extensions_t</a> &amp; transferExtensions);</div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_AckCallback(<span class="keyword">const</span> <a class="code hl_struct" href="struct_tcpcl_v4_1_1tcpclv4__ack__t.html">TcpclV4::tcpclv4_ack_t</a> &amp; ack);</div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_KeepAliveCallback();</div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno">  123</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_ContactHeaderCallback(<span class="keywordtype">bool</span> remoteHasEnabledTlsSecurity);</div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_SessionInitCallback(uint16_t keepAliveIntervalSeconds, uint64_t segmentMru, uint64_t transferMru,</div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span>        <span class="keyword">const</span> std::string &amp; remoteNodeEidUri, <span class="keyword">const</span> <a class="code hl_struct" href="struct_tcpcl_v4_1_1tcpclv4__extensions__t.html">TcpclV4::tcpclv4_extensions_t</a> &amp; sessionExtensions);</div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_SessionTerminationMessageCallback(TCPCLV4_SESSION_TERMINATION_REASON_CODES terminationReasonCode, <span class="keywordtype">bool</span> isAckOfAnEarlierSessionTerminationMessage);</div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno">  127</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_MessageRejectCallback(TCPCLV4_MESSAGE_REJECT_REASON_CODES refusalCode, uint8_t rejectedMessageHeader);</div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_BundleRefusalCallback(TCPCLV4_TRANSFER_REFUSE_REASON_CODES refusalCode, uint64_t transferId);</div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno">  129</span> </div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno">  130</span>    </div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_DoHandleSocketShutdown(<span class="keywordtype">bool</span> doCleanShutdown, TCPCLV4_SESSION_TERMINATION_REASON_CODES sessionTerminationReasonCode, <span class="keywordtype">bool</span> isAckOfAnEarlierSessionTerminationMessage);</div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno">  132</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_OnSendShutdownMessageTimeout_TimerExpired(<span class="keyword">const</span> boost::system::error_code&amp; e, <span class="keywordtype">bool</span> isAckOfAnEarlierSessionTerminationMessage);</div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno">  133</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_OnWaitForSessionTerminationAckTimeout_TimerExpired(<span class="keyword">const</span> boost::system::error_code&amp; e);</div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno">  134</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_RemainInEndingState_TimerExpired(<span class="keyword">const</span> boost::system::error_code&amp; e);</div>
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno">  135</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_RestartNoKeepaliveReceivedTimer();</div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_RestartNeedToSendKeepAliveMessageTimer();</div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_OnNoKeepAlivePacketReceived_TimerExpired(<span class="keyword">const</span> boost::system::error_code&amp; e);</div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_OnNeedToSendKeepAliveMessage_TimerExpired(<span class="keyword">const</span> boost::system::error_code&amp; e);</div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_HandleTcpSend(<span class="keyword">const</span> boost::system::error_code&amp; error, std::size_t bytes_transferred, <a class="code hl_struct" href="struct_tcp_async_sender_element.html">TcpAsyncSenderElement</a>* elPtr);</div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_HandleTcpSendContactHeader(<span class="keyword">const</span> boost::system::error_code&amp; error, std::size_t bytes_transferred, <a class="code hl_struct" href="struct_tcp_async_sender_element.html">TcpAsyncSenderElement</a>* elPtr);</div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_HandleTcpSendShutdown(<span class="keyword">const</span> boost::system::error_code&amp; error, std::size_t bytes_transferred, <a class="code hl_struct" href="struct_tcp_async_sender_element.html">TcpAsyncSenderElement</a>* elPtr);</div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span>    </div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span>    TCPCL_LIB_NO_EXPORT <span class="keywordtype">void</span> BaseClass_CloseAndDeleteSockets();</div>
<div class="line"><a id="l00144" name="l00144"></a><span class="lineno">  144</span> </div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span><span class="keyword">protected</span>:</div>
<div class="line"><a id="l00146" name="l00146"></a><span class="lineno">  146</span>    <span class="keyword">const</span> std::string M_BASE_IMPLEMENTATION_STRING_FOR_COUT;</div>
<div class="line"><a id="l00147" name="l00147"></a><span class="lineno">  147</span>    <span class="keyword">const</span> uint64_t M_BASE_SHUTDOWN_MESSAGE_RECONNECTION_DELAY_SECONDS_TO_SEND;</div>
<div class="line"><a id="l00148" name="l00148"></a><span class="lineno">  148</span>    <span class="keyword">const</span> uint16_t M_BASE_DESIRED_KEEPALIVE_INTERVAL_SECONDS;</div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno">  149</span>    <span class="keyword">const</span> <span class="keywordtype">bool</span> M_BASE_DELETE_SOCKET_AFTER_SHUTDOWN;</div>
<div class="line"><a id="l00150" name="l00150"></a><span class="lineno">  150</span>    <span class="keyword">const</span> <span class="keywordtype">bool</span> M_BASE_IS_ACTIVE_ENTITY;</div>
<div class="line"><a id="l00151" name="l00151"></a><span class="lineno">  151</span>    <span class="keyword">const</span> std::string M_BASE_THIS_TCPCL_EID_STRING;</div>
<div class="line"><a id="l00152" name="l00152"></a><span class="lineno">  152</span>    <span class="keyword">const</span> <span class="keywordtype">bool</span> M_BASE_TRY_USE_TLS;</div>
<div class="line"><a id="l00153" name="l00153"></a><span class="lineno">  153</span>    <span class="keyword">const</span> <span class="keywordtype">bool</span> M_BASE_TLS_IS_REQUIRED;</div>
<div class="line"><a id="l00154" name="l00154"></a><span class="lineno">  154</span>    <span class="keywordtype">bool</span> m_base_usingTls;</div>
<div class="line"><a id="l00155" name="l00155"></a><span class="lineno">  155</span>    std::string M_BASE_EXPECTED_REMOTE_CONTACT_HEADER_EID_STRING_IF_NOT_EMPTY;</div>
<div class="line"><a id="l00156" name="l00156"></a><span class="lineno">  156</span>    uint16_t m_base_keepAliveIntervalSeconds;</div>
<div class="line"><a id="l00157" name="l00157"></a><span class="lineno">  157</span>    std::unique_ptr&lt;boost::asio::io_service&gt; m_base_localIoServiceUniquePtr; <span class="comment">//if an external one is not provided, create it here and set the ioServiceRef below to it</span></div>
<div class="line"><a id="l00158" name="l00158"></a><span class="lineno">  158</span>    boost::asio::io_service &amp; m_base_ioServiceRef;</div>
<div class="line"><a id="l00159" name="l00159"></a><span class="lineno">  159</span>    boost::asio::deadline_timer m_base_noKeepAlivePacketReceivedTimer;</div>
<div class="line"><a id="l00160" name="l00160"></a><span class="lineno">  160</span>    boost::asio::deadline_timer m_base_needToSendKeepAliveMessageTimer;</div>
<div class="line"><a id="l00161" name="l00161"></a><span class="lineno">  161</span>    boost::asio::deadline_timer m_base_sendSessionTerminationMessageTimeoutTimer;</div>
<div class="line"><a id="l00162" name="l00162"></a><span class="lineno">  162</span>    boost::asio::deadline_timer m_base_waitForSessionTerminationAckTimeoutTimer;</div>
<div class="line"><a id="l00163" name="l00163"></a><span class="lineno">  163</span>    boost::asio::deadline_timer m_base_remainInEndingStateTimer;</div>
<div class="line"><a id="l00164" name="l00164"></a><span class="lineno">  164</span>    <span class="keywordtype">bool</span> m_base_shutdownCalled;</div>
<div class="line"><a id="l00165" name="l00165"></a><span class="lineno">  165</span>    std::atomic&lt;bool&gt; m_base_readyToForward; <span class="comment">//bundleSource</span></div>
<div class="line"><a id="l00166" name="l00166"></a><span class="lineno">  166</span>    std::atomic&lt;bool&gt; m_base_sinkIsSafeToDelete; <span class="comment">//bundleSink</span></div>
<div class="line"><a id="l00167" name="l00167"></a><span class="lineno">  167</span>    std::atomic&lt;bool&gt; m_base_tcpclShutdownComplete; <span class="comment">//bundleSource</span></div>
<div class="line"><a id="l00168" name="l00168"></a><span class="lineno">  168</span>    std::atomic&lt;bool&gt; m_base_useLocalConditionVariableAckReceived; <span class="comment">//bundleSource</span></div>
<div class="line"><a id="l00169" name="l00169"></a><span class="lineno">  169</span>    std::atomic&lt;bool&gt; m_base_dataReceivedServedAsKeepaliveReceived;</div>
<div class="line"><a id="l00170" name="l00170"></a><span class="lineno">  170</span>    std::atomic&lt;bool&gt; m_base_dataSentServedAsKeepaliveSent;</div>
<div class="line"><a id="l00171" name="l00171"></a><span class="lineno">  171</span>    <span class="keywordtype">bool</span> m_base_doUpgradeSocketToSsl;</div>
<div class="line"><a id="l00172" name="l00172"></a><span class="lineno">  172</span>    <span class="keywordtype">bool</span> m_base_didSuccessfulSslHandshake;</div>
<div class="line"><a id="l00173" name="l00173"></a><span class="lineno">  173</span>    boost::condition_variable m_base_localConditionVariableAckReceived;</div>
<div class="line"><a id="l00174" name="l00174"></a><span class="lineno">  174</span>    uint64_t m_base_reconnectionDelaySecondsIfNotZero; <span class="comment">//bundle source only, increases with exponential back-off mechanism</span></div>
<div class="line"><a id="l00175" name="l00175"></a><span class="lineno">  175</span> </div>
<div class="line"><a id="l00176" name="l00176"></a><span class="lineno">  176</span>    <a class="code hl_class" href="class_tcpcl_v4.html">TcpclV4</a> m_base_tcpclV4RxStateMachine;</div>
<div class="line"><a id="l00177" name="l00177"></a><span class="lineno">  177</span>    uint64_t m_base_myNextTransferId;</div>
<div class="line"><a id="l00178" name="l00178"></a><span class="lineno">  178</span>    std::string m_base_tcpclRemoteEidString;</div>
<div class="line"><a id="l00179" name="l00179"></a><span class="lineno">  179</span>    uint64_t m_base_tcpclRemoteNodeId;</div>
<div class="line"><a id="l00180" name="l00180"></a><span class="lineno">  180</span><span class="preprocessor">#ifdef OPENSSL_SUPPORT_ENABLED</span></div>
<div class="line"><a id="l00181" name="l00181"></a><span class="lineno">  181</span>    std::shared_ptr&lt; boost::asio::ssl::stream&lt;boost::asio::ip::tcp::socket&gt; &gt; m_base_sslStreamSharedPtr;</div>
<div class="line"><a id="l00182" name="l00182"></a><span class="lineno">  182</span>    std::unique_ptr&lt;TcpAsyncSenderSsl&gt; m_base_tcpAsyncSenderSslPtr;</div>
<div class="line"><a id="l00183" name="l00183"></a><span class="lineno">  183</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00184" name="l00184"></a><span class="lineno">  184</span>    std::shared_ptr&lt;boost::asio::ip::tcp::socket&gt; m_base_tcpSocketPtr;</div>
<div class="line"><a id="l00185" name="l00185"></a><span class="lineno">  185</span>    std::unique_ptr&lt;TcpAsyncSender&gt; m_base_tcpAsyncSenderPtr;</div>
<div class="line"><a id="l00186" name="l00186"></a><span class="lineno">  186</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00187" name="l00187"></a><span class="lineno">  187</span>    TcpAsyncSenderElement::OnSuccessfulSendCallbackByIoServiceThread_t m_base_handleTcpSendCallback;</div>
<div class="line"><a id="l00188" name="l00188"></a><span class="lineno">  188</span>    TcpAsyncSenderElement::OnSuccessfulSendCallbackByIoServiceThread_t m_base_handleTcpSendContactHeaderCallback;</div>
<div class="line"><a id="l00189" name="l00189"></a><span class="lineno">  189</span>    TcpAsyncSenderElement::OnSuccessfulSendCallbackByIoServiceThread_t m_base_handleTcpSendShutdownCallback;</div>
<div class="line"><a id="l00190" name="l00190"></a><span class="lineno">  190</span>    padded_vector_uint8_t m_base_fragmentedBundleRxConcat;</div>
<div class="line"><a id="l00191" name="l00191"></a><span class="lineno">  191</span> </div>
<div class="line"><a id="l00192" name="l00192"></a><span class="lineno">  192</span>    <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> M_BASE_MY_MAX_TX_UNACKED_BUNDLES;</div>
<div class="line"><a id="l00193" name="l00193"></a><span class="lineno">  193</span>    std::unique_ptr&lt;CircularIndexBufferSingleProducerSingleConsumerConfigurable&gt; m_base_segmentsToAckCbPtr; <span class="comment">//CircularIndexBufferSingleProducerSingleConsumerConfigurable m_base_bytesToAckCb;</span></div>
<div class="line"><a id="l00194" name="l00194"></a><span class="lineno">  194</span>    std::vector&lt;TcpclV4::tcpclv4_ack_t&gt; m_base_segmentsToAckCbVec; <span class="comment">//std::vector&lt;uint64_t&gt; m_base_bytesToAckCbVec;</span></div>
<div class="line"><a id="l00195" name="l00195"></a><span class="lineno">  195</span>    std::vector&lt;std::vector&lt;uint8_t&gt; &gt; m_base_userDataCbVec;</div>
<div class="line"><a id="l00196" name="l00196"></a><span class="lineno">  196</span>    <span class="keyword">const</span> uint64_t M_BASE_MY_MAX_RX_SEGMENT_SIZE_BYTES;</div>
<div class="line"><a id="l00197" name="l00197"></a><span class="lineno">  197</span>    <span class="keyword">const</span> uint64_t M_BASE_MY_MAX_RX_BUNDLE_SIZE_BYTES;</div>
<div class="line"><a id="l00198" name="l00198"></a><span class="lineno">  198</span>    uint64_t m_base_remoteMaxRxSegmentSizeBytes;</div>
<div class="line"><a id="l00199" name="l00199"></a><span class="lineno">  199</span>    uint64_t m_base_remoteMaxRxBundleSizeBytes;</div>
<div class="line"><a id="l00200" name="l00200"></a><span class="lineno">  200</span>    uint64_t m_base_remoteMaxRxSegmentsPerBundle;</div>
<div class="line"><a id="l00201" name="l00201"></a><span class="lineno">  201</span>    uint64_t m_base_maxUnackedSegments;</div>
<div class="line"><a id="l00202" name="l00202"></a><span class="lineno">  202</span>    uint64_t m_base_ackCbSize;</div>
<div class="line"><a id="l00203" name="l00203"></a><span class="lineno">  203</span> </div>
<div class="line"><a id="l00204" name="l00204"></a><span class="lineno">  204</span>    OnFailedBundleVecSendCallback_t m_base_onFailedBundleVecSendCallback;</div>
<div class="line"><a id="l00205" name="l00205"></a><span class="lineno">  205</span>    OnFailedBundleZmqSendCallback_t m_base_onFailedBundleZmqSendCallback;</div>
<div class="line"><a id="l00206" name="l00206"></a><span class="lineno">  206</span>    OnSuccessfulBundleSendCallback_t m_base_onSuccessfulBundleSendCallback;</div>
<div class="line"><a id="l00207" name="l00207"></a><span class="lineno">  207</span>    OnOutductLinkStatusChangedCallback_t m_base_onOutductLinkStatusChangedCallback;</div>
<div class="line"><a id="l00208" name="l00208"></a><span class="lineno">  208</span>    uint64_t m_base_userAssignedUuid;</div>
<div class="line"><a id="l00209" name="l00209"></a><span class="lineno">  209</span> </div>
<div class="line"><a id="l00210" name="l00210"></a><span class="lineno">  210</span>    std::string m_base_inductConnectionName;</div>
<div class="line"><a id="l00211" name="l00211"></a><span class="lineno">  211</span>    std::string m_base_inductInputName;</div>
<div class="line"><a id="l00212" name="l00212"></a><span class="lineno">  212</span>};</div>
</div>
<div class="line"><a id="l00213" name="l00213"></a><span class="lineno">  213</span> </div>
<div class="line"><a id="l00214" name="l00214"></a><span class="lineno">  214</span> </div>
<div class="line"><a id="l00215" name="l00215"></a><span class="lineno">  215</span> </div>
<div class="line"><a id="l00216" name="l00216"></a><span class="lineno">  216</span><span class="preprocessor">#endif  </span><span class="comment">//_TCPCLV4_BIDIRECTIONAL_LINK_H</span></div>
<div class="ttc" id="a_bidirectional_link_8h_html"><div class="ttname"><a href="_bidirectional_link_8h.html">BidirectionalLink.h</a></div></div>
<div class="ttc" id="a_bundle_callback_function_defines_8h_html"><div class="ttname"><a href="_bundle_callback_function_defines_8h.html">BundleCallbackFunctionDefines.h</a></div></div>
<div class="ttc" id="a_circular_index_buffer_single_producer_single_consumer_configurable_8h_html"><div class="ttname"><a href="_circular_index_buffer_single_producer_single_consumer_configurable_8h.html">CircularIndexBufferSingleProducerSingleConsumerConfigurable.h</a></div></div>
<div class="ttc" id="a_tcp_async_sender_8h_html"><div class="ttname"><a href="_tcp_async_sender_8h.html">TcpAsyncSender.h</a></div></div>
<div class="ttc" id="a_tcpcl_v4_8h_html"><div class="ttname"><a href="_tcpcl_v4_8h.html">TcpclV4.h</a></div></div>
<div class="ttc" id="a_telemetry_definitions_8h_html"><div class="ttname"><a href="_telemetry_definitions_8h.html">TelemetryDefinitions.h</a></div></div>
<div class="ttc" id="aclass_tcpcl_v4_html"><div class="ttname"><a href="class_tcpcl_v4.html">TcpclV4</a></div><div class="ttdef"><b>Definition</b> TcpclV4.h:155</div></div>
<div class="ttc" id="aclasszmq_1_1message__t_html"><div class="ttname"><a href="classzmq_1_1message__t.html">zmq::message_t</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:382</div></div>
<div class="ttc" id="astruct_tcp_async_sender_element_html"><div class="ttname"><a href="struct_tcp_async_sender_element.html">TcpAsyncSenderElement</a></div><div class="ttdef"><b>Definition</b> TcpAsyncSender.h:50</div></div>
<div class="ttc" id="astruct_tcpcl_v4_1_1tcpclv4__ack__t_html"><div class="ttname"><a href="struct_tcpcl_v4_1_1tcpclv4__ack__t.html">TcpclV4::tcpclv4_ack_t</a></div><div class="ttdef"><b>Definition</b> TcpclV4.h:196</div></div>
<div class="ttc" id="astruct_tcpcl_v4_1_1tcpclv4__extensions__t_html"><div class="ttname"><a href="struct_tcpcl_v4_1_1tcpclv4__extensions__t.html">TcpclV4::tcpclv4_extensions_t</a></div><div class="ttdef"><b>Definition</b> TcpclV4.h:181</div></div>
<div class="ttc" id="astruct_tcpcl_v4_induct_connection_telemetry__t_html"><div class="ttname"><a href="struct_tcpcl_v4_induct_connection_telemetry__t.html">TcpclV4InductConnectionTelemetry_t</a></div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.h:193</div></div>
<div class="ttc" id="astruct_tcpcl_v4_outduct_telemetry__t_html"><div class="ttname"><a href="struct_tcpcl_v4_outduct_telemetry__t.html">TcpclV4OutductTelemetry_t</a></div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.h:403</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_bdd9a5d540de89e9fe90efdfc6973a4f.html">common</a></li><li class="navelem"><a class="el" href="dir_a302f79f1b6fd0c9b750b57cc449be24.html">tcpcl</a></li><li class="navelem"><a class="el" href="dir_4035804a0c654d72d2897795532217b0.html">include</a></li><li class="navelem"><a class="el" href="_tcpcl_v4_bidirectional_link_8h.html">TcpclV4BidirectionalLink.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
