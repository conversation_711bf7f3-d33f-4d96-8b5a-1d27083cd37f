<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_ltp_encap_local_stream_engine.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">LtpEncapLocalStreamEngine Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#ad7394c450f2e6bdd6a97742e81f4143d">CancellationRequest</a>(const Ltp::session_id_t &amp;sessionId)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a28480f01282f829dfbeb364524dca2d1">CancellationRequest_ThreadSafe</a>(const Ltp::session_id_t &amp;sessionId)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>Connect</b>(const std::string &amp;socketOrPipePath, bool isStreamCreator) (defined in <a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a>)</td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a91161ddb66cf94e6803426cedf8947fa">GetEngineIndex</a>()</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a4d92e87ddbec73364195aa96ff26943d">GetMaxNumberOfSessionsInPipeline</a>() const</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#ac85ce5836f8fc5186fc5eb9c8eab7b60">GetNextPacketToSend</a>(UdpSendPacketInfo &amp;udpSendPacketInfo)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>LtpEncapLocalStreamEngine</b>(const uint64_t maxEncapRxPacketSizeBytes, const LtpEngineConfig &amp;ltpRxOrTxCfg) (defined in <a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a>)</td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a757b55af0b037caa9a74c16095eeb5a9">LtpEngine</a>(const LtpEngineConfig &amp;ltpRxOrTxCfg, const uint8_t engineIndexForEncodingIntoRandomSessionNumber, bool startIoServiceThread)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html#afad8ec440f3f4816d553b3d6743a483a">m_countAsyncSendCallbackCalls</a></td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html#afe3e4ae23aea7734d28aa4b073ca836c">m_countAsyncSendCalls</a></td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#afaec1d6368c078f8ab48b70a866163be">m_countAsyncSendsLimitedByRate</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html#a7d30ee4dcf6e042c2ce6037d9329191e">m_countBatchSendCallbackCalls</a></td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html#a245c7bdf1ddf78e8ec7d8e3941b6d684">m_countBatchSendCalls</a></td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html#adc128f626253b5ce98260b8f437706f1">m_countBatchUdpPacketsSent</a></td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html#a9eae1bda9f910bbae2fc860bc375c001">m_countCircularBufferOverruns</a></td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#ae8b9956d00992ec381a3dde36052d9e0">m_countPacketsThatCompletedOngoingOperations</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a1e1b3add6403a97ec1cc088f5bdade75">m_countPacketsWithOngoingOperations</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html#a70cdef458b8622fd4d1f01fc58339607">m_countUdpPacketsReceived</a></td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a40ecc6a317c1939ac4a610185f2325a3">m_ioServiceLtpEngine</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#ab7af72361a64ed96f399f09da03aa32f">M_MAX_UDP_PACKETS_TO_SEND_PER_SYSTEM_CALL</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a1763e65d50a7c8bf398f26f583802343">m_numCheckpointTimerExpiredCallbacksRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a118775a99920325d3610df81635fcfce">m_numDelayedFullyClaimedPrimaryReportSegmentsSentRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a07cc9142b412ee2276a6b3ce13c46339">m_numDelayedFullyClaimedSecondaryReportSegmentsSentRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a7865d752880e0c9ccca8698e46e42336">m_numDelayedPartiallyClaimedPrimaryReportSegmentsSentRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#ac44e7895529da02c5e7945efc12b7f45">m_numDelayedPartiallyClaimedSecondaryReportSegmentsSentRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#aef60d5b1702f7056ec0792dd5b4b861f">m_numDeletedFullyClaimedPendingReportsRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a76849a49ef076249f14230a85434ec76">m_numDiscretionaryCheckpointsNotResentRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#afc4e2efef914afc93e64c82914be0021">m_numEventsTransmissionRequestDiskWritesTooSlow</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#aa96c52cebe678593e90904420bad57d1">m_numGapsFilledByOutOfOrderDataSegmentsRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a2c366ddcce89f58bddd5117b789abc40">m_numReportSegmentsCreatedViaSplitRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a9a5dd020869dae6b25ad0a71bc65c28e">m_numReportSegmentsTooLargeAndNeedingSplitRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a3198a5091e8c461058b7e810b5789e32">m_numReportSegmentsUnableToBeIssuedRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a95ecb54427055af145d6aa11aecc5fb4">m_numReportSegmentTimerExpiredCallbacksRef</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#af8e0b9091c58b682193f8d82f3a32306">m_numRxSessionsCancelledBySender</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a89e22b17ea67c8745c91a0db340e85c3">m_numStagnantRxSessionsDeleted</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a5fdcaa43c182aa129db5ff43a7a0cf1b">m_numTxSessionsCancelledByReceiver</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#aaacad4876ebaf2a0ca609bdb2486b8b9">m_numTxSessionsReturnedToStorage</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#acfaed5ddbfb81f4a8231ea549ee576f0">m_totalCancelSegmentsAcknowledged</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#ae079576b5fbc92cf4305560456ecd7eb">m_totalCancelSegmentSendRetries</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#acfd032abbb14553c1286a52db854e29e">m_totalCancelSegmentsFailedToSend</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#acc439a7edd94073c12bfe384ad10d32e">m_totalCancelSegmentsStarted</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a69d7c7df39e9c3a70c05018380b64dcd">m_totalPingRetries</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#aaccc32aa5044fcccdfebfce9138978d8">m_totalPingsAcknowledged</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#aba7405ff7bcce1fb92b81723bef37a42">m_totalPingsFailedToSend</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a9f796012072e7a5cf5eb6ed9c7827932">m_totalPingsStarted</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#ad2623f77cb51cc0fc6ec4e4a6c0ca5d0">m_totalRedDataBytesFailedToSend</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a636aab451f26fe2953648351dde9f8c6">m_totalRedDataBytesSuccessfullySent</a></td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a527be4fce2ec201f40f2c3199ff803e5">NumActiveReceivers</a>() const</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a0d9a84917c557db610ef934fb05c5cb9">NumActiveSenders</a>() const</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#af468073c4352e673fc27eb14c8736323">OnSendPacketsSystemCallCompleted_ThreadSafe</a>()</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a64bb0494968816f34b5306f2a45c74e5">PacketIn</a>(const bool isLastChunkOfPacket, const uint8_t *data, const std::size_t size, Ltp::SessionOriginatorEngineIdDecodedCallback_t *sessionOriginatorEngineIdDecodedCallbackPtr=NULL)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a0f1d4863b86f2d6c6885e25f77d5bc69">PacketIn</a>(const std::vector&lt; boost::asio::const_buffer &gt; &amp;constBufferVec)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#ab32abf93dbdfbf50911440699941d721">PacketIn_ThreadSafe</a>(const uint8_t *data, const std::size_t size, Ltp::SessionOriginatorEngineIdDecodedCallback_t *sessionOriginatorEngineIdDecodedCallbackPtr=NULL)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a2a1755c01b72234fff34d175103073e9">PacketIn_ThreadSafe</a>(const std::vector&lt; boost::asio::const_buffer &gt; &amp;constBufferVec)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#aa8b227e7e66f397cc811d23e1d323ce1">PostExternalLinkDownEvent_ThreadSafe</a>()</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="odd"><td class="entry"><b>ReadyToSend</b>() const noexcept (defined in <a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a>)</td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html#a199073bfd78f249f4d923979e76e6e23">Reset</a>() override</td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html#a6e4771b9b89b8be763f9dd38221c1ed0">Reset_ThreadSafe_Blocking</a>()</td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a971d1bd8b790709bd4bf23b0dc796046">SetCheckpointEveryNthDataPacketForSenders</a>(uint64_t checkpointEveryNthDataPacketSender)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a19a1b2b299a2f596b2a7f93f6595283c">SetDeferDelays</a>(const uint64_t delaySendingOfReportSegmentsTimeMsOrZeroToDisable, const uint64_t delaySendingOfDataSegmentsTimeMsOrZeroToDisable)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#af8583ec3de26586c59f69bf1bf539528">SetDeferDelays_ThreadSafe</a>(const uint64_t delaySendingOfReportSegmentsTimeMsOrZeroToDisable, const uint64_t delaySendingOfDataSegmentsTimeMsOrZeroToDisable)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a78364e8c88d84aafa75a6b6ff8f51d5e">SetDelays</a>(const boost::posix_time::time_duration &amp;oneWayLightTime, const boost::posix_time::time_duration &amp;oneWayMarginTime, bool updateRunningTimers)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a3071c64c76d53530278ca8e4ed2d2020">SetDelays_ThreadSafe</a>(const boost::posix_time::time_duration &amp;oneWayLightTime, const boost::posix_time::time_duration &amp;oneWayMarginTime, bool updateRunningTimers)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#ab61ea92dfeba438b2e4f3c4f455c98d3">SetGreenPartSegmentArrivalCallback</a>(const GreenPartSegmentArrivalCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#ad8b1d868d0906fb5994a40a9081a5473">SetInitialTransmissionCompletedCallback</a>(const InitialTransmissionCompletedCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#afae9b922d32fe829c6ada4c983457799">SetMtuDataSegment</a>(uint64_t mtuDataSegment)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a0ac0e4173a8c54014cb3a0a0ae159f15">SetMtuDataSegment_ThreadSafe</a>(uint64_t mtuDataSegment)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a009c7ca31d567ec910e5effe4eadf98a">SetMtuReportSegment</a>(uint64_t mtuReportSegment)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a6df3abcfdb9483420a8c741a722d9fd4">SetMtuReportSegment_ThreadSafe</a>(uint64_t mtuReportSegment)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a821b8b38c306d58fbb95d0502805ab7a">SetOnFailedBundleVecSendCallback</a>(const OnFailedBundleVecSendCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a59076af529936255e27971570f105400">SetOnFailedBundleZmqSendCallback</a>(const OnFailedBundleZmqSendCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#affa6969c0c3a53ea679816f3ae78ead6">SetOnOutductLinkStatusChangedCallback</a>(const OnOutductLinkStatusChangedCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a6e5d18cc85f6d3922e7e6886c9e1286a">SetOnSuccessfulBundleSendCallback</a>(const OnSuccessfulBundleSendCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a60c38194c8ba48ffaf553bcaa78a33a6">SetPing</a>(const uint64_t senderPingSecondsOrZeroToDisable)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a1359f7d61a41b77bed11301de8f3abcc">SetPing_ThreadSafe</a>(const uint64_t senderPingSecondsOrZeroToDisable)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a275c7fe16d2bdf692d1447f371278224">SetPingToDefaultConfig</a>()</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#ad6d546634c7f6b4161d384c623c0aec4">SetPingToDefaultConfig_ThreadSafe</a>()</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#ac4ba9ce09bccb9141c1df926a8d50d9c">SetRate</a>(const uint64_t maxSendRateBitsPerSecOrZeroToDisable)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#aeef2662c62f7d21d6fec951aa6984d53">SetRate_ThreadSafe</a>(const uint64_t maxSendRateBitsPerSecOrZeroToDisable)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a773bd49c8ec82f41967225938184eac4">SetReceptionSessionCancelledCallback</a>(const ReceptionSessionCancelledCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a31312b9da9693a7a76438219626f38b9">SetRedPartReceptionCallback</a>(const RedPartReceptionCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a5603105c2cfda6253b35c1506cb3bd8d">SetSessionStartCallback</a>(const SessionStartCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#ada18dcd3df9b464a01d8194b1ebc49fe">SetTransmissionSessionCancelledCallback</a>(const TransmissionSessionCancelledCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#ae88cb57be73e00ba89646165f5f520ab">SetTransmissionSessionCompletedCallback</a>(const TransmissionSessionCompletedCallback_t &amp;callback)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a8ad4bbecb6c499f4f5c17a39956a2060">SetUserAssignedUuid</a>(uint64_t userAssignedUuid)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="odd"><td class="entry"><b>Stop</b>() (defined in <a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a>)</td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a246dc8848d370f2e0a84c566df062ef4">TransmissionRequest</a>(std::shared_ptr&lt; transmission_request_t &gt; &amp;transmissionRequest)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a073f8a94fa4eb6173982658695c192b3">TransmissionRequest</a>(uint64_t destinationClientServiceId, uint64_t destinationLtpEngineId, LtpClientServiceDataToSend &amp;&amp;clientServiceDataToSend, std::shared_ptr&lt; LtpTransmissionRequestUserData &gt; &amp;&amp;userDataPtrToTake, uint64_t lengthOfRedPart)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a556935f33de12e11e3e5062c21d78cdf">TransmissionRequest</a>(uint64_t destinationClientServiceId, uint64_t destinationLtpEngineId, const uint8_t *clientServiceDataToCopyAndSend, uint64_t length, std::shared_ptr&lt; LtpTransmissionRequestUserData &gt; &amp;&amp;userDataPtrToTake, uint64_t lengthOfRedPart)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_ltp_engine.html#a1d05ac120aa7df8af2e0a0a3be268c84">TransmissionRequest</a>(uint64_t destinationClientServiceId, uint64_t destinationLtpEngineId, const uint8_t *clientServiceDataToCopyAndSend, uint64_t length, uint64_t lengthOfRedPart)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a95e0dd55d54d86bea09c180b5f8d4888">TransmissionRequest_ThreadSafe</a>(std::shared_ptr&lt; transmission_request_t &gt; &amp;&amp;transmissionRequest)</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="odd"><td class="entry"><b>~LtpEncapLocalStreamEngine</b>() override (defined in <a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a>)</td><td class="entry"><a class="el" href="class_ltp_encap_local_stream_engine.html">LtpEncapLocalStreamEngine</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_ltp_engine.html#a9b1589f36e14f9d1c9ea721779d401e3">~LtpEngine</a>()</td><td class="entry"><a class="el" href="class_ltp_engine.html">LtpEngine</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
