<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: BpGenAsync Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_bp_gen_async.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="class_bp_gen_async-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">BpGenAsync Class Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for BpGenAsync:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bp_gen_async.png" usemap="#BpGenAsync_map" alt=""/>
  <map id="BpGenAsync_map" name="BpGenAsync_map">
<area href="class_bp_source_pattern.html" alt="BpSourcePattern" shape="rect" coords="0,0,108,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ad5694bdccd66eaa0ec99b77aed740b7a" id="r_ad5694bdccd66eaa0ec99b77aed740b7a"><td class="memItemLeft" align="right" valign="top"><a id="ad5694bdccd66eaa0ec99b77aed740b7a" name="ad5694bdccd66eaa0ec99b77aed740b7a"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>BpGenAsync</b> (uint64_t bundleSizeBytes)</td></tr>
<tr class="separator:ad5694bdccd66eaa0ec99b77aed740b7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_class_bp_source_pattern"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_class_bp_source_pattern')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="class_bp_source_pattern.html">BpSourcePattern</a></td></tr>
<tr class="memitem:a273ccdd5a290a55e1136bca8de7d9314 inherit pub_methods_class_bp_source_pattern" id="r_a273ccdd5a290a55e1136bca8de7d9314"><td class="memItemLeft" align="right" valign="top">
BP_APP_PATTERNS_LIB_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><b>Stop</b> ()</td></tr>
<tr class="separator:a273ccdd5a290a55e1136bca8de7d9314 inherit pub_methods_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae28affe7d1dac701de4f5f434a7c1bc5 inherit pub_methods_class_bp_source_pattern" id="r_ae28affe7d1dac701de4f5f434a7c1bc5"><td class="memItemLeft" align="right" valign="top">
BP_APP_PATTERNS_LIB_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><b>Start</b> (OutductsConfig_ptr &amp;outductsConfigPtr, InductsConfig_ptr &amp;inductsConfigPtr, const boost::filesystem::path &amp;bpSecConfigFilePath, bool custodyTransferUseAcs, const <a class="el" href="structcbhe__eid__t.html">cbhe_eid_t</a> &amp;myEid, double bundleRate, const <a class="el" href="structcbhe__eid__t.html">cbhe_eid_t</a> &amp;finalDestEid, const uint64_t myCustodianServiceId, const unsigned int bundleSendTimeoutSeconds, const uint64_t bundleLifetimeMilliseconds, const uint64_t bundlePriority, const bool requireRxBundleBeforeNextTx=false, const bool forceDisableCustody=false, const bool useBpVersion7=false, const uint64_t claRate=0)</td></tr>
<tr class="separator:ae28affe7d1dac701de4f5f434a7c1bc5 inherit pub_methods_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pro-methods" name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a8ed354ddf15ee6c7169c498865f0bd3a" id="r_a8ed354ddf15ee6c7169c498865f0bd3a"><td class="memItemLeft" align="right" valign="top">virtual uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a8ed354ddf15ee6c7169c498865f0bd3a">GetNextPayloadLength_Step1</a> () override</td></tr>
<tr class="separator:a8ed354ddf15ee6c7169c498865f0bd3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abfe17d4e096c5c154f6e5ab142caa1cc" id="r_abfe17d4e096c5c154f6e5ab142caa1cc"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abfe17d4e096c5c154f6e5ab142caa1cc">CopyPayload_Step2</a> (uint8_t *destinationBuffer) override</td></tr>
<tr class="separator:abfe17d4e096c5c154f6e5ab142caa1cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_class_bp_source_pattern"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pro_methods_class_bp_source_pattern')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="class_bp_source_pattern.html">BpSourcePattern</a></td></tr>
<tr class="memitem:a4192a857f6af7cb64a60618c6ea44f50 inherit pro_methods_class_bp_source_pattern" id="r_a4192a857f6af7cb64a60618c6ea44f50"><td class="memItemLeft" align="right" valign="top">virtual BP_APP_PATTERNS_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bp_source_pattern.html#a4192a857f6af7cb64a60618c6ea44f50">TryWaitForDataAvailable</a> (const boost::posix_time::time_duration &amp;timeout)</td></tr>
<tr class="separator:a4192a857f6af7cb64a60618c6ea44f50 inherit pro_methods_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af81631bb710c10b4e36da974ae176e6b inherit pro_methods_class_bp_source_pattern" id="r_af81631bb710c10b4e36da974ae176e6b"><td class="memItemLeft" align="right" valign="top">
virtual BP_APP_PATTERNS_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>ProcessNonAdminRecordBundlePayload</b> (const uint8_t *data, const uint64_t size)</td></tr>
<tr class="separator:af81631bb710c10b4e36da974ae176e6b inherit pro_methods_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_attribs_class_bp_source_pattern"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_attribs_class_bp_source_pattern')"><img src="closed.png" alt="-"/>&#160;Public Attributes inherited from <a class="el" href="class_bp_source_pattern.html">BpSourcePattern</a></td></tr>
<tr class="memitem:ae7d9ffa9cf7ba1621b5daa1991f7c16e inherit pub_attribs_class_bp_source_pattern" id="r_ae7d9ffa9cf7ba1621b5daa1991f7c16e"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_bundleCount</b></td></tr>
<tr class="separator:ae7d9ffa9cf7ba1621b5daa1991f7c16e inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63167706f8ccdaeb6c128fcd93b6c906 inherit pub_attribs_class_bp_source_pattern" id="r_a63167706f8ccdaeb6c128fcd93b6c906"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_numRfc5050CustodyTransfers</b></td></tr>
<tr class="separator:a63167706f8ccdaeb6c128fcd93b6c906 inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4564d639f48710e23d17a004b6121aaa inherit pub_attribs_class_bp_source_pattern" id="r_a4564d639f48710e23d17a004b6121aaa"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_numAcsCustodyTransfers</b></td></tr>
<tr class="separator:a4564d639f48710e23d17a004b6121aaa inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3cf30b0c7d20c8e9ce119153c4df80e8 inherit pub_attribs_class_bp_source_pattern" id="r_a3cf30b0c7d20c8e9ce119153c4df80e8"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_numAcsPacketsReceived</b></td></tr>
<tr class="separator:a3cf30b0c7d20c8e9ce119153c4df80e8 inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c8a5eed06dd39a3395a7c981fd802de inherit pub_attribs_class_bp_source_pattern" id="r_a2c8a5eed06dd39a3395a7c981fd802de"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalNonAdminRecordBpv6PayloadBytesRx</b></td></tr>
<tr class="separator:a2c8a5eed06dd39a3395a7c981fd802de inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4eb2e676f4e943258e868854f130f142 inherit pub_attribs_class_bp_source_pattern" id="r_a4eb2e676f4e943258e868854f130f142"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalNonAdminRecordBpv6BundleBytesRx</b></td></tr>
<tr class="separator:a4eb2e676f4e943258e868854f130f142 inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f5aa8c960749cf1b70cc66a8c79d724 inherit pub_attribs_class_bp_source_pattern" id="r_a6f5aa8c960749cf1b70cc66a8c79d724"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalNonAdminRecordBpv6BundlesRx</b></td></tr>
<tr class="separator:a6f5aa8c960749cf1b70cc66a8c79d724 inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0986443735cca1cce14e8bc0b1dc4870 inherit pub_attribs_class_bp_source_pattern" id="r_a0986443735cca1cce14e8bc0b1dc4870"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalNonAdminRecordBpv7PayloadBytesRx</b></td></tr>
<tr class="separator:a0986443735cca1cce14e8bc0b1dc4870 inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d180303279225fd0c2446106c8e8d09 inherit pub_attribs_class_bp_source_pattern" id="r_a6d180303279225fd0c2446106c8e8d09"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalNonAdminRecordBpv7BundleBytesRx</b></td></tr>
<tr class="separator:a6d180303279225fd0c2446106c8e8d09 inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a608331545467a4700c85395363e8172f inherit pub_attribs_class_bp_source_pattern" id="r_a608331545467a4700c85395363e8172f"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalNonAdminRecordBpv7BundlesRx</b></td></tr>
<tr class="separator:a608331545467a4700c85395363e8172f inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5918ea451e24872e50305840bbf66940 inherit pub_attribs_class_bp_source_pattern" id="r_a5918ea451e24872e50305840bbf66940"><td class="memItemLeft" align="right" valign="top">
<a class="el" href="struct_outduct_final_stats.html">OutductFinalStats</a>&#160;</td><td class="memItemRight" valign="bottom"><b>m_outductFinalStats</b></td></tr>
<tr class="separator:a5918ea451e24872e50305840bbf66940 inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad24d6a1122cb3b0932ef31bed17d093a inherit pub_attribs_class_bp_source_pattern" id="r_ad24d6a1122cb3b0932ef31bed17d093a"><td class="memItemLeft" align="right" valign="top">
std::atomic&lt; bool &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>m_allOutductsReady</b></td></tr>
<tr class="separator:ad24d6a1122cb3b0932ef31bed17d093a inherit pub_attribs_class_bp_source_pattern"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="abfe17d4e096c5c154f6e5ab142caa1cc" name="abfe17d4e096c5c154f6e5ab142caa1cc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abfe17d4e096c5c154f6e5ab142caa1cc">&#9670;&#160;</a></span>CopyPayload_Step2()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool BpGenAsync::CopyPayload_Step2 </td>
          <td>(</td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>destinationBuffer</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel protected">protected</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Implements <a class="el" href="class_bp_source_pattern.html">BpSourcePattern</a>.</p>

</div>
</div>
<a id="a8ed354ddf15ee6c7169c498865f0bd3a" name="a8ed354ddf15ee6c7169c498865f0bd3a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8ed354ddf15ee6c7169c498865f0bd3a">&#9670;&#160;</a></span>GetNextPayloadLength_Step1()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t BpGenAsync::GetNextPayloadLength_Step1 </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel protected">protected</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Implements <a class="el" href="class_bp_source_pattern.html">BpSourcePattern</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>common/bpcodec/apps/bpgen/include/<a class="el" href="_bp_gen_async_8h_source.html">BpGenAsync.h</a></li>
<li>common/bpcodec/apps/bpgen/src/<a class="el" href="_bp_gen_async_8cpp.html">BpGenAsync.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="class_bp_gen_async.html">BpGenAsync</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
