<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: common/bpcodec/apps/BpSendStream/include/BpSendStreamRunner.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('_bp_send_stream_runner_8h_source.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">BpSendStreamRunner.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_bp_send_stream_runner_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span> </div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="preprocessor">#pragma once</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span> </div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="preprocessor">#include &quot;<a class="code" href="_bp_send_stream_8h.html">BpSendStream.h</a>&quot;</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="preprocessor">#include &lt;stdint.h&gt;</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span> </div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span> </div>
<div class="foldopen" id="foldopen00020" data-start="{" data-end="};">
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno"><a class="line" href="class_bp_send_stream_runner.html">   20</a></span><span class="keyword">class </span>BpSendStreamRunner {</div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="keyword">public</span>:</div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span>    BpSendStreamRunner();</div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span>    ~BpSendStreamRunner();</div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span> </div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span>    std::string ReadSdpFile(<span class="keyword">const</span> boost::filesystem::path sdpFilePath);</div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span>    std::string TranslateSdpToBp(std::string sdp, std::string uriCbheNumber, std::string bpEID);</div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span>    </div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span>    <span class="keywordtype">bool</span> Run(<span class="keywordtype">int</span> argc, <span class="keyword">const</span> <span class="keywordtype">char</span>* <span class="keyword">const</span> argv[], std::atomic&lt;bool&gt;&amp; running, <span class="keywordtype">bool</span> useSignalHandler);</div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span>    uint64_t m_bundleCount;</div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span> </div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span>    <a class="code hl_struct" href="struct_outduct_final_stats.html">OutductFinalStats</a> m_outductFinalStats;</div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span> </div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span> </div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span>    <span class="keywordtype">void</span> MonitorExitKeypressThreadFunction();</div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span> </div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span>    std::atomic&lt;bool&gt; m_runningFromSigHandler;</div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span>};</div>
</div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span> </div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span> </div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span> </div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span><span class="comment">// std::string BpSendStreamRunner::TranslateSdpToBp(std::string sdp, std::string uriCbheNumber, std::string bpEID)</span></div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span><span class="comment">// {</span></div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span><span class="comment">//     std::string newSdp;</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span> </div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span><span class="comment">//     // we do not care about the &quot;o&quot; and &quot;v&quot; fields</span></div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span> </div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span><span class="comment">//     // replace the &quot;c&quot; field with DTN BP</span></div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span><span class="comment">//     newSdp.append(&quot;c=DTN BP IPN:&quot;);</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span><span class="comment">//     newSdp.append(uriCbheNumber);</span></div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span><span class="comment">//     newSdp.append(&quot;\n&quot;);</span></div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span> </div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span><span class="comment">//     // replace the &quot;m&quot; field with DTN endpoint</span></div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span><span class="comment">//     newSdp.append(&quot;m=&quot;);</span></div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span><span class="comment">//     if (sdp.find(&quot;m=video&quot;) != std::string::npos)</span></div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span><span class="comment">//         newSdp.append(&quot;video &quot;);</span></div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span><span class="comment">//     if (sdp.find(&quot;m=audio&quot;) != std::string::npos)</span></div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span><span class="comment">//         newSdp.append(&quot;audio &quot;);</span></div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span><span class="comment">//     newSdp.append(bpEID);</span></div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span><span class="comment">//     newSdp.append(&quot; &quot;);</span></div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span> </div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span><span class="comment">//     // append the rest of the original SDP message</span></div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span><span class="comment">//     size_t rtpLocation = sdp.find(&quot;RTP/AVP&quot;); // sdp protocol for RTP</span></div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span><span class="comment">//     if (rtpLocation == std::string::npos) </span></div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span><span class="comment">//     {</span></div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span><span class="comment">//         LOG_ERROR(subprocess) &lt;&lt; &quot;Invalid SDP file&quot;;</span></div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span><span class="comment">//         return &quot;ERROR&quot;;</span></div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span><span class="comment">//     }</span></div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span> </div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span><span class="comment">//     std::string sdpSubString = sdp.substr(rtpLocation, UINT64_MAX);</span></div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span><span class="comment">//     newSdp.append(sdpSubString);</span></div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span>    </div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span><span class="comment">//     LOG_INFO(subprocess) &lt;&lt; &quot;Translated SDP:\n&quot; &lt;&lt; newSdp;</span></div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span><span class="comment">//     return newSdp;</span></div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span><span class="comment">// }</span></div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span> </div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span><span class="comment">// std::string BpSendStreamRunner::ReadSdpFile(const boost::filesystem::path sdpFilePath)</span></div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span><span class="comment">// {</span></div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span><span class="comment">//     std::string sdpfile;</span></div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span> </div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span><span class="comment">//     while (!boost::filesystem::exists(sdpFilePath))</span></div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span><span class="comment">//     {</span></div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span> </div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span><span class="comment">//     }</span></div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span><span class="comment">//     // wait for file to be written to</span></div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span><span class="comment">//     boost::this_thread::sleep_for(boost::chrono::milliseconds(10));</span></div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span><span class="comment">//     boost::filesystem::load_string_file(sdpFilePath, sdpfile);</span></div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span><span class="comment">//     return sdpfile;</span></div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span><span class="comment">// }</span></div>
<div class="ttc" id="a_bp_send_stream_8h_html"><div class="ttname"><a href="_bp_send_stream_8h.html">BpSendStream.h</a></div></div>
<div class="ttc" id="astruct_outduct_final_stats_html"><div class="ttname"><a href="struct_outduct_final_stats.html">OutductFinalStats</a></div><div class="ttdef"><b>Definition</b> Outduct.h:40</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_bdd9a5d540de89e9fe90efdfc6973a4f.html">common</a></li><li class="navelem"><a class="el" href="dir_16abf196c0e88f5ebd440542ff6d0bc9.html">bpcodec</a></li><li class="navelem"><a class="el" href="dir_b601340823b9299eb70cf5220807ec18.html">apps</a></li><li class="navelem"><a class="el" href="dir_e6465f0b4b1d8e2cf3bd82bd1e40b074.html">BpSendStream</a></li><li class="navelem"><a class="el" href="dir_6b9f913547ded71840b9470149142673.html">include</a></li><li class="navelem"><a class="el" href="_bp_send_stream_runner_8h.html">BpSendStreamRunner.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
