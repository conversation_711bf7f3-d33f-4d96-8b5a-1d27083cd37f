<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: cbhe_bundle_uuid_nofragment_t Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('structcbhe__bundle__uuid__nofragment__t.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcbhe__bundle__uuid__nofragment__t-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">cbhe_bundle_uuid_nofragment_t Struct Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ac1acaba0f4e795d8177469976e8e6927" id="r_ac1acaba0f4e795d8177469976e8e6927"><td class="memItemLeft" align="right" valign="top"><a id="ac1acaba0f4e795d8177469976e8e6927" name="ac1acaba0f4e795d8177469976e8e6927"></a>
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>cbhe_bundle_uuid_nofragment_t</b> (uint64_t paramCreationSeconds, uint64_t paramSequence, uint64_t paramSrcNodeId, uint64_t paramSrcServiceId)</td></tr>
<tr class="separator:ac1acaba0f4e795d8177469976e8e6927"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaac392720ee10f56b8bb978f02e3a69f" id="r_aaac392720ee10f56b8bb978f02e3a69f"><td class="memItemLeft" align="right" valign="top"><a id="aaac392720ee10f56b8bb978f02e3a69f" name="aaac392720ee10f56b8bb978f02e3a69f"></a>
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>cbhe_bundle_uuid_nofragment_t</b> (const <a class="el" href="structcbhe__bundle__uuid__t.html">cbhe_bundle_uuid_t</a> &amp;bundleUuidWithFragment)</td></tr>
<tr class="separator:aaac392720ee10f56b8bb978f02e3a69f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9995b5c6e2cc7a246da549947a821d9c" id="r_a9995b5c6e2cc7a246da549947a821d9c"><td class="memItemLeft" align="right" valign="top"><a id="a9995b5c6e2cc7a246da549947a821d9c" name="a9995b5c6e2cc7a246da549947a821d9c"></a>
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>cbhe_bundle_uuid_nofragment_t</b> (const <a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a> &amp;o)</td></tr>
<tr class="separator:a9995b5c6e2cc7a246da549947a821d9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb4c3708cb298735971484721fcd165e" id="r_acb4c3708cb298735971484721fcd165e"><td class="memItemLeft" align="right" valign="top"><a id="acb4c3708cb298735971484721fcd165e" name="acb4c3708cb298735971484721fcd165e"></a>
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>cbhe_bundle_uuid_nofragment_t</b> (<a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a> &amp;&amp;o)</td></tr>
<tr class="separator:acb4c3708cb298735971484721fcd165e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a30395c9389d59ee85dcb2566886db628" id="r_a30395c9389d59ee85dcb2566886db628"><td class="memItemLeft" align="right" valign="top"><a id="a30395c9389d59ee85dcb2566886db628" name="a30395c9389d59ee85dcb2566886db628"></a>
BPCODEC_EXPORT <a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (const <a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a> &amp;o)</td></tr>
<tr class="separator:a30395c9389d59ee85dcb2566886db628"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa15f4ffde7af7320ff538e10f2896d65" id="r_aa15f4ffde7af7320ff538e10f2896d65"><td class="memItemLeft" align="right" valign="top"><a id="aa15f4ffde7af7320ff538e10f2896d65" name="aa15f4ffde7af7320ff538e10f2896d65"></a>
BPCODEC_EXPORT <a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (<a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a> &amp;&amp;o)</td></tr>
<tr class="separator:aa15f4ffde7af7320ff538e10f2896d65"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a29bb9ef83ba272312584cf9495f8f9ff" id="r_a29bb9ef83ba272312584cf9495f8f9ff"><td class="memItemLeft" align="right" valign="top"><a id="a29bb9ef83ba272312584cf9495f8f9ff" name="a29bb9ef83ba272312584cf9495f8f9ff"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator==</b> (const <a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a> &amp;o) const</td></tr>
<tr class="separator:a29bb9ef83ba272312584cf9495f8f9ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5facb417a303ea9d5ff87eac3c817eff" id="r_a5facb417a303ea9d5ff87eac3c817eff"><td class="memItemLeft" align="right" valign="top"><a id="a5facb417a303ea9d5ff87eac3c817eff" name="a5facb417a303ea9d5ff87eac3c817eff"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator!=</b> (const <a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a> &amp;o) const</td></tr>
<tr class="separator:a5facb417a303ea9d5ff87eac3c817eff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7af55a793a0873e01d9c081cf36d2c35" id="r_a7af55a793a0873e01d9c081cf36d2c35"><td class="memItemLeft" align="right" valign="top"><a id="a7af55a793a0873e01d9c081cf36d2c35" name="a7af55a793a0873e01d9c081cf36d2c35"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator&lt;</b> (const <a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a> &amp;o) const</td></tr>
<tr class="separator:a7af55a793a0873e01d9c081cf36d2c35"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a3b3520a5ac508800117dc48c41226bf2" id="r_a3b3520a5ac508800117dc48c41226bf2"><td class="memItemLeft" align="right" valign="top"><a id="a3b3520a5ac508800117dc48c41226bf2" name="a3b3520a5ac508800117dc48c41226bf2"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>creationSeconds</b></td></tr>
<tr class="separator:a3b3520a5ac508800117dc48c41226bf2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a08c8b2bae9653145398a4c9126490e" id="r_a2a08c8b2bae9653145398a4c9126490e"><td class="memItemLeft" align="right" valign="top"><a id="a2a08c8b2bae9653145398a4c9126490e" name="a2a08c8b2bae9653145398a4c9126490e"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>sequence</b></td></tr>
<tr class="separator:a2a08c8b2bae9653145398a4c9126490e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a250422a98175ee1097e94a1c7e5ec85a" id="r_a250422a98175ee1097e94a1c7e5ec85a"><td class="memItemLeft" align="right" valign="top"><a id="a250422a98175ee1097e94a1c7e5ec85a" name="a250422a98175ee1097e94a1c7e5ec85a"></a>
<a class="el" href="structcbhe__eid__t.html">cbhe_eid_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>srcEid</b></td></tr>
<tr class="separator:a250422a98175ee1097e94a1c7e5ec85a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>common/bpcodec/include/codec/<a class="el" href="_cbhe_8h_source.html">Cbhe.h</a></li>
<li>common/bpcodec/src/codec/<a class="el" href="_cbhe_8cpp.html">Cbhe.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
