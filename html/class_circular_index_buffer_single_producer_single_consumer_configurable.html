<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: CircularIndexBufferSingleProducerSingleConsumerConfigurable Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_circular_index_buffer_single_producer_single_consumer_configurable.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_circular_index_buffer_single_producer_single_consumer_configurable-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">CircularIndexBufferSingleProducerSingleConsumerConfigurable Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a2d5c4e7a6531e699c589b242d7110ce3" id="r_a2d5c4e7a6531e699c589b242d7110ce3"><td class="memItemLeft" align="right" valign="top">HDTN_UTIL_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2d5c4e7a6531e699c589b242d7110ce3">CircularIndexBufferSingleProducerSingleConsumerConfigurable</a> (unsigned int size) noexcept</td></tr>
<tr class="separator:a2d5c4e7a6531e699c589b242d7110ce3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46b00ad33e080d021580400055da4733" id="r_a46b00ad33e080d021580400055da4733"><td class="memItemLeft" align="right" valign="top"><a id="a46b00ad33e080d021580400055da4733" name="a46b00ad33e080d021580400055da4733"></a>
HDTN_UTIL_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>~CircularIndexBufferSingleProducerSingleConsumerConfigurable</b> () noexcept</td></tr>
<tr class="memdesc:a46b00ad33e080d021580400055da4733"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default destructor. <br /></td></tr>
<tr class="separator:a46b00ad33e080d021580400055da4733"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a286eff0f342ca6fc5d65e5c9fc96af3e" id="r_a286eff0f342ca6fc5d65e5c9fc96af3e"><td class="memItemLeft" align="right" valign="top">HDTN_UTIL_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a286eff0f342ca6fc5d65e5c9fc96af3e">Init</a> () noexcept</td></tr>
<tr class="separator:a286eff0f342ca6fc5d65e5c9fc96af3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac161b24c6450bc869b95809206ba915e" id="r_ac161b24c6450bc869b95809206ba915e"><td class="memItemLeft" align="right" valign="top">HDTN_UTIL_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac161b24c6450bc869b95809206ba915e">IsFull</a> () const noexcept</td></tr>
<tr class="separator:ac161b24c6450bc869b95809206ba915e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc0da1f20b669a0407ce9fa48f9e5e94" id="r_abc0da1f20b669a0407ce9fa48f9e5e94"><td class="memItemLeft" align="right" valign="top">HDTN_UTIL_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#abc0da1f20b669a0407ce9fa48f9e5e94">IsEmpty</a> () const noexcept</td></tr>
<tr class="separator:abc0da1f20b669a0407ce9fa48f9e5e94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af49c27b17db5f9b85b676c7e2d24b257" id="r_af49c27b17db5f9b85b676c7e2d24b257"><td class="memItemLeft" align="right" valign="top">HDTN_UTIL_EXPORT unsigned int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#af49c27b17db5f9b85b676c7e2d24b257">GetIndexForWrite</a> () const noexcept</td></tr>
<tr class="separator:af49c27b17db5f9b85b676c7e2d24b257"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad477740071e1b16f335abd9be4eeb508" id="r_ad477740071e1b16f335abd9be4eeb508"><td class="memItemLeft" align="right" valign="top">HDTN_UTIL_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad477740071e1b16f335abd9be4eeb508">CommitWrite</a> () noexcept</td></tr>
<tr class="separator:ad477740071e1b16f335abd9be4eeb508"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a637cab3bcd65b49b8fa0cd6bcaadad6e" id="r_a637cab3bcd65b49b8fa0cd6bcaadad6e"><td class="memItemLeft" align="right" valign="top">HDTN_UTIL_EXPORT unsigned int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a637cab3bcd65b49b8fa0cd6bcaadad6e">GetIndexForRead</a> () const noexcept</td></tr>
<tr class="separator:a637cab3bcd65b49b8fa0cd6bcaadad6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a6a1d52fc5278aefcee86ec5facc3cb" id="r_a0a6a1d52fc5278aefcee86ec5facc3cb"><td class="memItemLeft" align="right" valign="top">HDTN_UTIL_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0a6a1d52fc5278aefcee86ec5facc3cb">CommitRead</a> () noexcept</td></tr>
<tr class="separator:a0a6a1d52fc5278aefcee86ec5facc3cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3246598730dc306b46045e9fa0a7ae84" id="r_a3246598730dc306b46045e9fa0a7ae84"><td class="memItemLeft" align="right" valign="top">HDTN_UTIL_EXPORT unsigned int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a3246598730dc306b46045e9fa0a7ae84">NumInBuffer</a> () const noexcept</td></tr>
<tr class="separator:a3246598730dc306b46045e9fa0a7ae84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e74f27ca377f177c95628bc3f696b33" id="r_a0e74f27ca377f177c95628bc3f696b33"><td class="memItemLeft" align="right" valign="top">HDTN_UTIL_EXPORT unsigned int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0e74f27ca377f177c95628bc3f696b33">GetCapacity</a> () const noexcept</td></tr>
<tr class="separator:a0e74f27ca377f177c95628bc3f696b33"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a2d5c4e7a6531e699c589b242d7110ce3" name="a2d5c4e7a6531e699c589b242d7110ce3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d5c4e7a6531e699c589b242d7110ce3">&#9670;&#160;</a></span>CircularIndexBufferSingleProducerSingleConsumerConfigurable()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CircularIndexBufferSingleProducerSingleConsumerConfigurable::CircularIndexBufferSingleProducerSingleConsumerConfigurable </td>
          <td>(</td>
          <td class="paramtype">unsigned int</td>          <td class="paramname"><span class="paramname"><em>size</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel noexcept">noexcept</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set the working size of the external buffer, then initialize begin and end to zero. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">size</td><td>The working size of the associated external buffer. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a0a6a1d52fc5278aefcee86ec5facc3cb" name="a0a6a1d52fc5278aefcee86ec5facc3cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0a6a1d52fc5278aefcee86ec5facc3cb">&#9670;&#160;</a></span>CommitRead()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void CircularIndexBufferSingleProducerSingleConsumerConfigurable::CommitRead </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel noexcept">noexcept</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Advance read index.</p>
<p>Indicates the completion of the current active read operation, advances begin one element forward (wrap on overflow). </p>

</div>
</div>
<a id="ad477740071e1b16f335abd9be4eeb508" name="ad477740071e1b16f335abd9be4eeb508"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad477740071e1b16f335abd9be4eeb508">&#9670;&#160;</a></span>CommitWrite()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void CircularIndexBufferSingleProducerSingleConsumerConfigurable::CommitWrite </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel noexcept">noexcept</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Advance write index.</p>
<p>Indicates the completion of the current active write operation, advances end one element forward (wrap on overflow). </p>

</div>
</div>
<a id="a0e74f27ca377f177c95628bc3f696b33" name="a0e74f27ca377f177c95628bc3f696b33"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0e74f27ca377f177c95628bc3f696b33">&#9670;&#160;</a></span>GetCapacity()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">unsigned int CircularIndexBufferSingleProducerSingleConsumerConfigurable::GetCapacity </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel noexcept">noexcept</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get the capacity of the circular index buffer.</p>
<dl class="section return"><dt>Returns</dt><dd>The capacity of the circular index buffer. </dd></dl>

</div>
</div>
<a id="a637cab3bcd65b49b8fa0cd6bcaadad6e" name="a637cab3bcd65b49b8fa0cd6bcaadad6e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a637cab3bcd65b49b8fa0cd6bcaadad6e">&#9670;&#160;</a></span>GetIndexForRead()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">unsigned int CircularIndexBufferSingleProducerSingleConsumerConfigurable::GetIndexForRead </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel noexcept">noexcept</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get read index.</p>
<p>Indicates the start of a read operation. </p><dl class="section return"><dt>Returns</dt><dd>CIRCULAR_INDEX_BUFFER_EMPTY if buffer is empty, else the read index. </dd></dl>

</div>
</div>
<a id="af49c27b17db5f9b85b676c7e2d24b257" name="af49c27b17db5f9b85b676c7e2d24b257"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af49c27b17db5f9b85b676c7e2d24b257">&#9670;&#160;</a></span>GetIndexForWrite()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">unsigned int CircularIndexBufferSingleProducerSingleConsumerConfigurable::GetIndexForWrite </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel noexcept">noexcept</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get write index.</p>
<p>Indicates the start of a write operation. </p><dl class="section return"><dt>Returns</dt><dd>CIRCULAR_INDEX_BUFFER_FULL if buffer is full, else the write index. </dd></dl>

</div>
</div>
<a id="a286eff0f342ca6fc5d65e5c9fc96af3e" name="a286eff0f342ca6fc5d65e5c9fc96af3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a286eff0f342ca6fc5d65e5c9fc96af3e">&#9670;&#160;</a></span>Init()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void CircularIndexBufferSingleProducerSingleConsumerConfigurable::Init </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel noexcept">noexcept</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Reset bounds.</p>
<p>Set begin and end back to zero. </p>

</div>
</div>
<a id="abc0da1f20b669a0407ce9fa48f9e5e94" name="abc0da1f20b669a0407ce9fa48f9e5e94"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc0da1f20b669a0407ce9fa48f9e5e94">&#9670;&#160;</a></span>IsEmpty()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool CircularIndexBufferSingleProducerSingleConsumerConfigurable::IsEmpty </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel noexcept">noexcept</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Query whether external buffer is empty.</p>
<p>Checks if begin is equal to end. </p><dl class="section return"><dt>Returns</dt><dd>True if the external buffer is empty, or False otherwise. </dd></dl>

</div>
</div>
<a id="ac161b24c6450bc869b95809206ba915e" name="ac161b24c6450bc869b95809206ba915e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac161b24c6450bc869b95809206ba915e">&#9670;&#160;</a></span>IsFull()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool CircularIndexBufferSingleProducerSingleConsumerConfigurable::IsFull </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel noexcept">noexcept</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Query whether external buffer is full.</p>
<p>Checks if the next element after end (wrap on overflow) is equal to begin. </p><dl class="section return"><dt>Returns</dt><dd>True if the external buffer is full, or False otherwise. </dd></dl>

</div>
</div>
<a id="a3246598730dc306b46045e9fa0a7ae84" name="a3246598730dc306b46045e9fa0a7ae84"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3246598730dc306b46045e9fa0a7ae84">&#9670;&#160;</a></span>NumInBuffer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">unsigned int CircularIndexBufferSingleProducerSingleConsumerConfigurable::NumInBuffer </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel noexcept">noexcept</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get the number of active elements in the external buffer.</p>
<p>Calculates how many elements exist between begin and end. </p><dl class="section return"><dt>Returns</dt><dd>The number of elements in the external buffer that are currently being indexed. </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>common/util/include/<a class="el" href="_circular_index_buffer_single_producer_single_consumer_configurable_8h_source.html">CircularIndexBufferSingleProducerSingleConsumerConfigurable.h</a></li>
<li>common/util/src/<a class="el" href="_circular_index_buffer_single_producer_single_consumer_configurable_8cpp.html">CircularIndexBufferSingleProducerSingleConsumerConfigurable.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="class_circular_index_buffer_single_producer_single_consumer_configurable.html">CircularIndexBufferSingleProducerSingleConsumerConfigurable</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
