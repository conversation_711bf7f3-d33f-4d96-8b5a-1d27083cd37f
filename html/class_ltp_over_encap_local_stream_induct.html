<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: LtpOverEncapLocalStreamInduct Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_ltp_over_encap_local_stream_induct.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="class_ltp_over_encap_local_stream_induct-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">LtpOverEncapLocalStreamInduct Class Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for LtpOverEncapLocalStreamInduct:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_ltp_over_encap_local_stream_induct.png" usemap="#LtpOverEncapLocalStreamInduct_map" alt=""/>
  <map id="LtpOverEncapLocalStreamInduct_map" name="LtpOverEncapLocalStreamInduct_map">
<area href="class_ltp_induct.html" alt="LtpInduct" shape="rect" coords="0,56,198,80"/>
<area href="class_induct.html" alt="Induct" shape="rect" coords="0,0,198,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a31d22fb4c64a1a25a586a97c6cfe00ca" id="r_a31d22fb4c64a1a25a586a97c6cfe00ca"><td class="memItemLeft" align="right" valign="top"><a id="a31d22fb4c64a1a25a586a97c6cfe00ca" name="a31d22fb4c64a1a25a586a97c6cfe00ca"></a>
INDUCT_MANAGER_LIB_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>LtpOverEncapLocalStreamInduct</b> (const InductProcessBundleCallback_t &amp;inductProcessBundleCallback, const <a class="el" href="structinduct__element__config__t.html">induct_element_config_t</a> &amp;inductConfig, const uint64_t maxBundleSizeBytes)</td></tr>
<tr class="separator:a31d22fb4c64a1a25a586a97c6cfe00ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_class_ltp_induct"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_class_ltp_induct')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="class_ltp_induct.html">LtpInduct</a></td></tr>
<tr class="memitem:af04f62cb434dc7c02dbf595a87c6331f inherit pub_methods_class_ltp_induct" id="r_af04f62cb434dc7c02dbf595a87c6331f"><td class="memItemLeft" align="right" valign="top">
INDUCT_MANAGER_LIB_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>LtpInduct</b> (const InductProcessBundleCallback_t &amp;inductProcessBundleCallback, const <a class="el" href="structinduct__element__config__t.html">induct_element_config_t</a> &amp;inductConfig, const uint64_t maxBundleSizeBytes)</td></tr>
<tr class="separator:af04f62cb434dc7c02dbf595a87c6331f inherit pub_methods_class_ltp_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab608160a7952603e1c1abdf294a78943 inherit pub_methods_class_ltp_induct" id="r_ab608160a7952603e1c1abdf294a78943"><td class="memItemLeft" align="right" valign="top">virtual INDUCT_MANAGER_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_ltp_induct.html#ab608160a7952603e1c1abdf294a78943">Init</a> () override</td></tr>
<tr class="separator:ab608160a7952603e1c1abdf294a78943 inherit pub_methods_class_ltp_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade7cc381f23b28b21d1e5eeeaef6070f inherit pub_methods_class_ltp_induct" id="r_ade7cc381f23b28b21d1e5eeeaef6070f"><td class="memItemLeft" align="right" valign="top">virtual INDUCT_MANAGER_LIB_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_ltp_induct.html#ade7cc381f23b28b21d1e5eeeaef6070f">PopulateInductTelemetry</a> (<a class="el" href="struct_induct_telemetry__t.html">InductTelemetry_t</a> &amp;inductTelem) override</td></tr>
<tr class="separator:ade7cc381f23b28b21d1e5eeeaef6070f inherit pub_methods_class_ltp_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_class_induct"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_class_induct')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="class_induct.html">Induct</a></td></tr>
<tr class="memitem:a647593fe6c02215c386ebaa63a0d0824 inherit pub_methods_class_induct" id="r_a647593fe6c02215c386ebaa63a0d0824"><td class="memItemLeft" align="right" valign="top">
INDUCT_MANAGER_LIB_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Induct</b> (const InductProcessBundleCallback_t &amp;inductProcessBundleCallback, const <a class="el" href="structinduct__element__config__t.html">induct_element_config_t</a> &amp;inductConfig)</td></tr>
<tr class="separator:a647593fe6c02215c386ebaa63a0d0824 inherit pub_methods_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0cbe038ec6320e7b05110dbc63e847a3 inherit pub_methods_class_induct" id="r_a0cbe038ec6320e7b05110dbc63e847a3"><td class="memItemLeft" align="right" valign="top">
INDUCT_MANAGER_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>ForwardOnOpportunisticLink</b> (const uint64_t remoteNodeId, padded_vector_uint8_t &amp;dataVec, const uint32_t timeoutSeconds)</td></tr>
<tr class="separator:a0cbe038ec6320e7b05110dbc63e847a3 inherit pub_methods_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17724854b4a473b9a57526cd0dc4f071 inherit pub_methods_class_induct" id="r_a17724854b4a473b9a57526cd0dc4f071"><td class="memItemLeft" align="right" valign="top">
INDUCT_MANAGER_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>ForwardOnOpportunisticLink</b> (const uint64_t remoteNodeId, <a class="el" href="classzmq_1_1message__t.html">zmq::message_t</a> &amp;dataZmq, const uint32_t timeoutSeconds)</td></tr>
<tr class="separator:a17724854b4a473b9a57526cd0dc4f071 inherit pub_methods_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74322837f4037aec13e5bb937ca5badf inherit pub_methods_class_induct" id="r_a74322837f4037aec13e5bb937ca5badf"><td class="memItemLeft" align="right" valign="top">
INDUCT_MANAGER_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>ForwardOnOpportunisticLink</b> (const uint64_t remoteNodeId, const uint8_t *bundleData, const std::size_t size, const uint32_t timeoutSeconds)</td></tr>
<tr class="separator:a74322837f4037aec13e5bb937ca5badf inherit pub_methods_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pro-methods" name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:ac31b217445a77ab20b85a66fd3f9ea04" id="r_ac31b217445a77ab20b85a66fd3f9ea04"><td class="memItemLeft" align="right" valign="top">virtual INDUCT_MANAGER_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac31b217445a77ab20b85a66fd3f9ea04">SetLtpBundleSinkPtr</a> () override</td></tr>
<tr class="separator:ac31b217445a77ab20b85a66fd3f9ea04"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_class_induct"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pro_methods_class_induct')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="class_induct.html">Induct</a></td></tr>
<tr class="memitem:ae03c890c8d5a0ac0b5e3d71e928329fa inherit pro_methods_class_induct" id="r_ae03c890c8d5a0ac0b5e3d71e928329fa"><td class="memItemLeft" align="right" valign="top">
INDUCT_MANAGER_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>BundleSinkTryGetData_FromIoServiceThread</b> (<a class="el" href="struct_induct_1_1_opportunistic_bundle_queue.html">OpportunisticBundleQueue</a> &amp;opportunisticBundleQueue, std::pair&lt; std::unique_ptr&lt; <a class="el" href="classzmq_1_1message__t.html">zmq::message_t</a> &gt;, padded_vector_uint8_t &gt; &amp;bundleDataPair)</td></tr>
<tr class="separator:ae03c890c8d5a0ac0b5e3d71e928329fa inherit pro_methods_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad74c9b440a843473696defb025590a9f inherit pro_methods_class_induct" id="r_ad74c9b440a843473696defb025590a9f"><td class="memItemLeft" align="right" valign="top">
INDUCT_MANAGER_LIB_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><b>BundleSinkNotifyOpportunisticDataAcked_FromIoServiceThread</b> (<a class="el" href="struct_induct_1_1_opportunistic_bundle_queue.html">OpportunisticBundleQueue</a> &amp;opportunisticBundleQueue)</td></tr>
<tr class="separator:ad74c9b440a843473696defb025590a9f inherit pro_methods_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a761599f28ca13bb6845eb2f7c69b7dff inherit pro_methods_class_induct" id="r_a761599f28ca13bb6845eb2f7c69b7dff"><td class="memItemLeft" align="right" valign="top">
INDUCT_MANAGER_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>ForwardOnOpportunisticLink</b> (const uint64_t remoteNodeId, <a class="el" href="classzmq_1_1message__t.html">zmq::message_t</a> *zmqMsgPtr, padded_vector_uint8_t *vec8Ptr, const uint32_t timeoutSeconds)</td></tr>
<tr class="separator:a761599f28ca13bb6845eb2f7c69b7dff inherit pro_methods_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad47fefdf9d04f67cef3eb3d5f5ea6db inherit pro_methods_class_induct" id="r_aad47fefdf9d04f67cef3eb3d5f5ea6db"><td class="memItemLeft" align="right" valign="top">
virtual INDUCT_MANAGER_LIB_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><b>Virtual_PostNotifyBundleReadyToSend_FromIoServiceThread</b> (const uint64_t remoteNodeId)</td></tr>
<tr class="separator:aad47fefdf9d04f67cef3eb3d5f5ea6db inherit pro_methods_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pro_attribs_class_ltp_induct"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pro_attribs_class_ltp_induct')"><img src="closed.png" alt="-"/>&#160;Protected Attributes inherited from <a class="el" href="class_ltp_induct.html">LtpInduct</a></td></tr>
<tr class="memitem:ac737d89f2d5556d00bd051a28e01ef37 inherit pro_attribs_class_ltp_induct" id="r_ac737d89f2d5556d00bd051a28e01ef37"><td class="memItemLeft" align="right" valign="top">
<a class="el" href="class_ltp_bundle_sink.html">LtpBundleSink</a> *&#160;</td><td class="memItemRight" valign="bottom"><b>m_ltpBundleSinkPtr</b></td></tr>
<tr class="separator:ac737d89f2d5556d00bd051a28e01ef37 inherit pro_attribs_class_ltp_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa57caefffb64aa9a4d940a931ff0018d inherit pro_attribs_class_ltp_induct" id="r_aa57caefffb64aa9a4d940a931ff0018d"><td class="memItemLeft" align="right" valign="top">
<a class="el" href="struct_ltp_engine_config.html">LtpEngineConfig</a>&#160;</td><td class="memItemRight" valign="bottom"><b>m_ltpRxCfg</b></td></tr>
<tr class="separator:aa57caefffb64aa9a4d940a931ff0018d inherit pro_attribs_class_ltp_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_attribs_class_induct"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pro_attribs_class_induct')"><img src="closed.png" alt="-"/>&#160;Protected Attributes inherited from <a class="el" href="class_induct.html">Induct</a></td></tr>
<tr class="memitem:a88863aafd33849d348fbd6d97ffc1092 inherit pro_attribs_class_induct" id="r_a88863aafd33849d348fbd6d97ffc1092"><td class="memItemLeft" align="right" valign="top">
const InductProcessBundleCallback_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_inductProcessBundleCallback</b></td></tr>
<tr class="separator:a88863aafd33849d348fbd6d97ffc1092 inherit pro_attribs_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a557e332763667d6232acbcebcf4b788b inherit pro_attribs_class_induct" id="r_a557e332763667d6232acbcebcf4b788b"><td class="memItemLeft" align="right" valign="top">
const <a class="el" href="structinduct__element__config__t.html">induct_element_config_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>m_inductConfig</b></td></tr>
<tr class="separator:a557e332763667d6232acbcebcf4b788b inherit pro_attribs_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e17749fc05146922183f4f4ef32ee86 inherit pro_attribs_class_induct" id="r_a1e17749fc05146922183f4f4ef32ee86"><td class="memItemLeft" align="right" valign="top">
std::map&lt; uint64_t, <a class="el" href="struct_induct_1_1_opportunistic_bundle_queue.html">OpportunisticBundleQueue</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>m_mapNodeIdToOpportunisticBundleQueue</b></td></tr>
<tr class="separator:a1e17749fc05146922183f4f4ef32ee86 inherit pro_attribs_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ac721528b8eaa20a6c9dcaf5adee326 inherit pro_attribs_class_induct" id="r_a5ac721528b8eaa20a6c9dcaf5adee326"><td class="memItemLeft" align="right" valign="top">
boost::mutex&#160;</td><td class="memItemRight" valign="bottom"><b>m_mapNodeIdToOpportunisticBundleQueueMutex</b></td></tr>
<tr class="separator:a5ac721528b8eaa20a6c9dcaf5adee326 inherit pro_attribs_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9a1cef61f590b17dda0481251082f99 inherit pro_attribs_class_induct" id="r_ad9a1cef61f590b17dda0481251082f99"><td class="memItemLeft" align="right" valign="top">
OnNewOpportunisticLinkCallback_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_onNewOpportunisticLinkCallback</b></td></tr>
<tr class="separator:ad9a1cef61f590b17dda0481251082f99 inherit pro_attribs_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6365d298419e7be494c21e0a13d4758 inherit pro_attribs_class_induct" id="r_ae6365d298419e7be494c21e0a13d4758"><td class="memItemLeft" align="right" valign="top">
OnDeletedOpportunisticLinkCallback_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_onDeletedOpportunisticLinkCallback</b></td></tr>
<tr class="separator:ae6365d298419e7be494c21e0a13d4758 inherit pro_attribs_class_induct"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ac31b217445a77ab20b85a66fd3f9ea04" name="ac31b217445a77ab20b85a66fd3f9ea04"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac31b217445a77ab20b85a66fd3f9ea04">&#9670;&#160;</a></span>SetLtpBundleSinkPtr()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool LtpOverEncapLocalStreamInduct::SetLtpBundleSinkPtr </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel protected">protected</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Implements <a class="el" href="class_ltp_induct.html">LtpInduct</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>common/induct_manager/include/<a class="el" href="_ltp_over_encap_local_stream_induct_8h_source.html">LtpOverEncapLocalStreamInduct.h</a></li>
<li>common/induct_manager/src/<a class="el" href="_ltp_over_encap_local_stream_induct_8cpp.html">LtpOverEncapLocalStreamInduct.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="class_ltp_over_encap_local_stream_induct.html">LtpOverEncapLocalStreamInduct</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
