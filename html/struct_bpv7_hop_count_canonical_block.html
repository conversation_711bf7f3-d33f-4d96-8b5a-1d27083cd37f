<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: Bpv7HopCountCanonicalBlock Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('struct_bpv7_hop_count_canonical_block.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="struct_bpv7_hop_count_canonical_block-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Bpv7HopCountCanonicalBlock Struct Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for Bpv7HopCountCanonicalBlock:</div>
<div class="dyncontent">
 <div class="center">
  <img src="struct_bpv7_hop_count_canonical_block.png" usemap="#Bpv7HopCountCanonicalBlock_map" alt=""/>
  <map id="Bpv7HopCountCanonicalBlock_map" name="Bpv7HopCountCanonicalBlock_map">
<area href="struct_bpv7_canonical_block.html" alt="Bpv7CanonicalBlock" shape="rect" coords="0,0,185,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a4f08e643eea8b8344ffc9553206182cd" id="r_a4f08e643eea8b8344ffc9553206182cd"><td class="memItemLeft" align="right" valign="top"><a id="a4f08e643eea8b8344ffc9553206182cd" name="a4f08e643eea8b8344ffc9553206182cd"></a>
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv7HopCountCanonicalBlock</b> (const <a class="el" href="struct_bpv7_hop_count_canonical_block.html">Bpv7HopCountCanonicalBlock</a> &amp;o)</td></tr>
<tr class="separator:a4f08e643eea8b8344ffc9553206182cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0dbdc01346e25701fdd03b809e919136" id="r_a0dbdc01346e25701fdd03b809e919136"><td class="memItemLeft" align="right" valign="top"><a id="a0dbdc01346e25701fdd03b809e919136" name="a0dbdc01346e25701fdd03b809e919136"></a>
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv7HopCountCanonicalBlock</b> (<a class="el" href="struct_bpv7_hop_count_canonical_block.html">Bpv7HopCountCanonicalBlock</a> &amp;&amp;o)</td></tr>
<tr class="separator:a0dbdc01346e25701fdd03b809e919136"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24966878f1fa81b68a3a9306de446e7f" id="r_a24966878f1fa81b68a3a9306de446e7f"><td class="memItemLeft" align="right" valign="top"><a id="a24966878f1fa81b68a3a9306de446e7f" name="a24966878f1fa81b68a3a9306de446e7f"></a>
BPCODEC_EXPORT <a class="el" href="struct_bpv7_hop_count_canonical_block.html">Bpv7HopCountCanonicalBlock</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (const <a class="el" href="struct_bpv7_hop_count_canonical_block.html">Bpv7HopCountCanonicalBlock</a> &amp;o)</td></tr>
<tr class="separator:a24966878f1fa81b68a3a9306de446e7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff9e99642ebc983b2e9e730e43a422ee" id="r_aff9e99642ebc983b2e9e730e43a422ee"><td class="memItemLeft" align="right" valign="top"><a id="aff9e99642ebc983b2e9e730e43a422ee" name="aff9e99642ebc983b2e9e730e43a422ee"></a>
BPCODEC_EXPORT <a class="el" href="struct_bpv7_hop_count_canonical_block.html">Bpv7HopCountCanonicalBlock</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (<a class="el" href="struct_bpv7_hop_count_canonical_block.html">Bpv7HopCountCanonicalBlock</a> &amp;&amp;o)</td></tr>
<tr class="separator:aff9e99642ebc983b2e9e730e43a422ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2257494dbaeaa4cfa2ea9ac3cf314e8d" id="r_a2257494dbaeaa4cfa2ea9ac3cf314e8d"><td class="memItemLeft" align="right" valign="top"><a id="a2257494dbaeaa4cfa2ea9ac3cf314e8d" name="a2257494dbaeaa4cfa2ea9ac3cf314e8d"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator==</b> (const <a class="el" href="struct_bpv7_hop_count_canonical_block.html">Bpv7HopCountCanonicalBlock</a> &amp;o) const</td></tr>
<tr class="separator:a2257494dbaeaa4cfa2ea9ac3cf314e8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c024180c7ee60f3b64108182d323007" id="r_a2c024180c7ee60f3b64108182d323007"><td class="memItemLeft" align="right" valign="top"><a id="a2c024180c7ee60f3b64108182d323007" name="a2c024180c7ee60f3b64108182d323007"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator!=</b> (const <a class="el" href="struct_bpv7_hop_count_canonical_block.html">Bpv7HopCountCanonicalBlock</a> &amp;o) const</td></tr>
<tr class="separator:a2c024180c7ee60f3b64108182d323007"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac75ca415ebda4b9a6cd676c4d383363f" id="r_ac75ca415ebda4b9a6cd676c4d383363f"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac75ca415ebda4b9a6cd676c4d383363f">SetZero</a> () override</td></tr>
<tr class="separator:ac75ca415ebda4b9a6cd676c4d383363f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a8ed1614ec05eb4c35aa9be8ec0f28d" id="r_a2a8ed1614ec05eb4c35aa9be8ec0f28d"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a2a8ed1614ec05eb4c35aa9be8ec0f28d">SerializeBpv7</a> (uint8_t *serialization) override</td></tr>
<tr class="separator:a2a8ed1614ec05eb4c35aa9be8ec0f28d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a853b725d926fc0fae85d2f251635a519" id="r_a853b725d926fc0fae85d2f251635a519"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a853b725d926fc0fae85d2f251635a519">GetCanonicalBlockTypeSpecificDataSerializationSize</a> () const override</td></tr>
<tr class="separator:a853b725d926fc0fae85d2f251635a519"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67142bac593073121536e7194cf92be6" id="r_a67142bac593073121536e7194cf92be6"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a67142bac593073121536e7194cf92be6">Virtual_DeserializeExtensionBlockDataBpv7</a> () override</td></tr>
<tr class="separator:a67142bac593073121536e7194cf92be6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2be883784f3b078eb03156741988dcb1" id="r_a2be883784f3b078eb03156741988dcb1"><td class="memItemLeft" align="right" valign="top"><a id="a2be883784f3b078eb03156741988dcb1" name="a2be883784f3b078eb03156741988dcb1"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>TryReserializeExtensionBlockDataWithoutResizeBpv7</b> ()</td></tr>
<tr class="separator:a2be883784f3b078eb03156741988dcb1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_struct_bpv7_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_struct_bpv7_canonical_block')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a></td></tr>
<tr class="memitem:aecf94f77f6088be4097adb9096b05fbb inherit pub_methods_struct_bpv7_canonical_block" id="r_aecf94f77f6088be4097adb9096b05fbb"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv7CanonicalBlock</b> (const <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;o)</td></tr>
<tr class="separator:aecf94f77f6088be4097adb9096b05fbb inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6107c0a7f2a40d9321c1ea63d7a4912 inherit pub_methods_struct_bpv7_canonical_block" id="r_ab6107c0a7f2a40d9321c1ea63d7a4912"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv7CanonicalBlock</b> (<a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;&amp;o)</td></tr>
<tr class="separator:ab6107c0a7f2a40d9321c1ea63d7a4912 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6934022232ecb9345a8dc96a5b279c35 inherit pub_methods_struct_bpv7_canonical_block" id="r_a6934022232ecb9345a8dc96a5b279c35"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (const <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;o)</td></tr>
<tr class="separator:a6934022232ecb9345a8dc96a5b279c35 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed77c98d427fa491ef308ee29a31a755 inherit pub_methods_struct_bpv7_canonical_block" id="r_aed77c98d427fa491ef308ee29a31a755"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (<a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;&amp;o)</td></tr>
<tr class="separator:aed77c98d427fa491ef308ee29a31a755 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a618acb0922f387f51b12abd8fdbf50b6 inherit pub_methods_struct_bpv7_canonical_block" id="r_a618acb0922f387f51b12abd8fdbf50b6"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator==</b> (const <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;o) const</td></tr>
<tr class="separator:a618acb0922f387f51b12abd8fdbf50b6 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4e212819d6c369f15191a0ac09600d3 inherit pub_methods_struct_bpv7_canonical_block" id="r_ae4e212819d6c369f15191a0ac09600d3"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator!=</b> (const <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;o) const</td></tr>
<tr class="separator:ae4e212819d6c369f15191a0ac09600d3 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2f3079662c86335b5bd331358ed9206 inherit pub_methods_struct_bpv7_canonical_block" id="r_ac2f3079662c86335b5bd331358ed9206"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>GetSerializationSize</b> (const bool isEncrypted) const</td></tr>
<tr class="separator:ac2f3079662c86335b5bd331358ed9206 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab683bc568dcbda62a0276dc05cce8814 inherit pub_methods_struct_bpv7_canonical_block" id="r_ab683bc568dcbda62a0276dc05cce8814"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>GetSerializationSizeOfAadPart</b> () const</td></tr>
<tr class="separator:ab683bc568dcbda62a0276dc05cce8814 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e342453d205ba05a5fa41bde0420d32 inherit pub_methods_struct_bpv7_canonical_block" id="r_a1e342453d205ba05a5fa41bde0420d32"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>SerializeAadPart</b> (uint8_t *serialization) const</td></tr>
<tr class="separator:a1e342453d205ba05a5fa41bde0420d32 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad190ba097fa3361b48506042a01f04f7 inherit pub_methods_struct_bpv7_canonical_block" id="r_ad190ba097fa3361b48506042a01f04f7"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><b>RecomputeCrcAfterDataModification</b> (uint8_t *serializationBase, const uint64_t sizeSerialized)</td></tr>
<tr class="separator:ad190ba097fa3361b48506042a01f04f7 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:ac1b69a3d7c7e48fb52213e1bb7be8c89" id="r_ac1b69a3d7c7e48fb52213e1bb7be8c89"><td class="memItemLeft" align="right" valign="top"><a id="ac1b69a3d7c7e48fb52213e1bb7be8c89" name="ac1b69a3d7c7e48fb52213e1bb7be8c89"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_hopLimit</b></td></tr>
<tr class="separator:ac1b69a3d7c7e48fb52213e1bb7be8c89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9707388359322099414d8c58f98517e4" id="r_a9707388359322099414d8c58f98517e4"><td class="memItemLeft" align="right" valign="top"><a id="a9707388359322099414d8c58f98517e4" name="a9707388359322099414d8c58f98517e4"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_hopCount</b></td></tr>
<tr class="separator:a9707388359322099414d8c58f98517e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_attribs_struct_bpv7_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_attribs_struct_bpv7_canonical_block')"><img src="closed.png" alt="-"/>&#160;Public Attributes inherited from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a></td></tr>
<tr class="memitem:a1841b984116094640351af6cf79946ba inherit pub_attribs_struct_bpv7_canonical_block" id="r_a1841b984116094640351af6cf79946ba"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_blockNumber</b></td></tr>
<tr class="separator:a1841b984116094640351af6cf79946ba inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a770a12675f629dd69c7c1d14464ba708 inherit pub_attribs_struct_bpv7_canonical_block" id="r_a770a12675f629dd69c7c1d14464ba708"><td class="memItemLeft" align="right" valign="top">
BPV7_BLOCKFLAG&#160;</td><td class="memItemRight" valign="bottom"><b>m_blockProcessingControlFlags</b></td></tr>
<tr class="separator:a770a12675f629dd69c7c1d14464ba708 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae98d0fd4ae507d87a09f28c5bed10a71 inherit pub_attribs_struct_bpv7_canonical_block" id="r_ae98d0fd4ae507d87a09f28c5bed10a71"><td class="memItemLeft" align="right" valign="top">
uint8_t *&#160;</td><td class="memItemRight" valign="bottom"><b>m_dataPtr</b></td></tr>
<tr class="separator:ae98d0fd4ae507d87a09f28c5bed10a71 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83f08d9f511accaa9f4245085a3790a7 inherit pub_attribs_struct_bpv7_canonical_block" id="r_a83f08d9f511accaa9f4245085a3790a7"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_dataLength</b></td></tr>
<tr class="separator:a83f08d9f511accaa9f4245085a3790a7 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c08ff0ce312237b02ae98af81ed8d54 inherit pub_attribs_struct_bpv7_canonical_block" id="r_a5c08ff0ce312237b02ae98af81ed8d54"><td class="memItemLeft" align="right" valign="top">
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_computedCrc32</b></td></tr>
<tr class="separator:a5c08ff0ce312237b02ae98af81ed8d54 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab971291a81c13519c05f7e71978ffe63 inherit pub_attribs_struct_bpv7_canonical_block" id="r_ab971291a81c13519c05f7e71978ffe63"><td class="memItemLeft" align="right" valign="top">
uint16_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_computedCrc16</b></td></tr>
<tr class="separator:ab971291a81c13519c05f7e71978ffe63 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae440c38f8ace47da7b0b059c1b074fc4 inherit pub_attribs_struct_bpv7_canonical_block" id="r_ae440c38f8ace47da7b0b059c1b074fc4"><td class="memItemLeft" align="right" valign="top">
BPV7_BLOCK_TYPE_CODE&#160;</td><td class="memItemRight" valign="bottom"><b>m_blockTypeCode</b></td></tr>
<tr class="separator:ae440c38f8ace47da7b0b059c1b074fc4 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88523f11d5dae13761efb28a4a430d9f inherit pub_attribs_struct_bpv7_canonical_block" id="r_a88523f11d5dae13761efb28a4a430d9f"><td class="memItemLeft" align="right" valign="top">
BPV7_CRC_TYPE&#160;</td><td class="memItemRight" valign="bottom"><b>m_crcType</b></td></tr>
<tr class="separator:a88523f11d5dae13761efb28a4a430d9f inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-attribs" name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a64787bf494fb54ef85386f40c532b970" id="r_a64787bf494fb54ef85386f40c532b970"><td class="memItemLeft" align="right" valign="top">static constexpr uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a64787bf494fb54ef85386f40c532b970">largestSerializedDataOnlySize</a></td></tr>
<tr class="separator:a64787bf494fb54ef85386f40c532b970"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_static_attribs_struct_bpv7_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_static_attribs_struct_bpv7_canonical_block')"><img src="closed.png" alt="-"/>&#160;Static Public Attributes inherited from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a></td></tr>
<tr class="memitem:a6224499818798fc8f4976a1aa8d64171 inherit pub_static_attribs_struct_bpv7_canonical_block" id="r_a6224499818798fc8f4976a1aa8d64171"><td class="memItemLeft" align="right" valign="top">static constexpr uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_bpv7_canonical_block.html#a6224499818798fc8f4976a1aa8d64171">smallestSerializedCanonicalSize</a></td></tr>
<tr class="separator:a6224499818798fc8f4976a1aa8d64171 inherit pub_static_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15f2d6d077c3e0fbb3de7fb49657b1b8 inherit pub_static_attribs_struct_bpv7_canonical_block" id="r_a15f2d6d077c3e0fbb3de7fb49657b1b8"><td class="memItemLeft" align="right" valign="top">static constexpr uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_bpv7_canonical_block.html#a15f2d6d077c3e0fbb3de7fb49657b1b8">largestZeroDataSerializedCanonicalSize</a></td></tr>
<tr class="separator:a15f2d6d077c3e0fbb3de7fb49657b1b8 inherit pub_static_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_static_methods_struct_bpv7_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_static_methods_struct_bpv7_canonical_block')"><img src="closed.png" alt="-"/>&#160;Static Public Member Functions inherited from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a></td></tr>
<tr class="memitem:a2b0a572fb082540b24eb3a02d3162822 inherit pub_static_methods_struct_bpv7_canonical_block" id="r_a2b0a572fb082540b24eb3a02d3162822"><td class="memItemLeft" align="right" valign="top">
static BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>DeserializeBpv7</b> (std::unique_ptr&lt; <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &gt; &amp;canonicalPtr, uint8_t *serialization, uint64_t &amp;numBytesTakenToDecode, uint64_t bufferSize, const bool skipCrcVerify, const bool isAdminRecord, std::unique_ptr&lt; <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &gt; *blockNumberToRecycledCanonicalBlockArray, std::unique_ptr&lt; <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &gt; *recycledAdminRecord)</td></tr>
<tr class="separator:a2b0a572fb082540b24eb3a02d3162822 inherit pub_static_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a853b725d926fc0fae85d2f251635a519" name="a853b725d926fc0fae85d2f251635a519"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a853b725d926fc0fae85d2f251635a519">&#9670;&#160;</a></span>GetCanonicalBlockTypeSpecificDataSerializationSize()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t Bpv7HopCountCanonicalBlock::GetCanonicalBlockTypeSpecificDataSerializationSize </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a>.</p>

</div>
</div>
<a id="a2a8ed1614ec05eb4c35aa9be8ec0f28d" name="a2a8ed1614ec05eb4c35aa9be8ec0f28d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a8ed1614ec05eb4c35aa9be8ec0f28d">&#9670;&#160;</a></span>SerializeBpv7()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t Bpv7HopCountCanonicalBlock::SerializeBpv7 </td>
          <td>(</td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>serialization</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a>.</p>

</div>
</div>
<a id="ac75ca415ebda4b9a6cd676c4d383363f" name="ac75ca415ebda4b9a6cd676c4d383363f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac75ca415ebda4b9a6cd676c4d383363f">&#9670;&#160;</a></span>SetZero()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void Bpv7HopCountCanonicalBlock::SetZero </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a>.</p>

</div>
</div>
<a id="a67142bac593073121536e7194cf92be6" name="a67142bac593073121536e7194cf92be6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a67142bac593073121536e7194cf92be6">&#9670;&#160;</a></span>Virtual_DeserializeExtensionBlockDataBpv7()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Bpv7HopCountCanonicalBlock::Virtual_DeserializeExtensionBlockDataBpv7 </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a>.</p>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a id="a64787bf494fb54ef85386f40c532b970" name="a64787bf494fb54ef85386f40c532b970"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a64787bf494fb54ef85386f40c532b970">&#9670;&#160;</a></span>largestSerializedDataOnlySize</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t Bpv7HopCountCanonicalBlock::largestSerializedDataOnlySize</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel static">static</span><span class="mlabel constexpr">constexpr</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div>
<div class="line">        1 + </div>
<div class="line">        9 + </div>
<div class="line">        9</div>
</div><!-- fragment -->
</div>
</div>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>common/bpcodec/include/codec/<a class="el" href="bpv7_8h_source.html">bpv7.h</a></li>
<li>common/bpcodec/src/codec/<a class="el" href="_bpv7_extension_blocks_8cpp.html">Bpv7ExtensionBlocks.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_bpv7_hop_count_canonical_block.html">Bpv7HopCountCanonicalBlock</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
