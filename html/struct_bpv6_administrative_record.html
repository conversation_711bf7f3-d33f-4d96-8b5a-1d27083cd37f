<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: Bpv6AdministrativeRecord Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('struct_bpv6_administrative_record.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="struct_bpv6_administrative_record-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Bpv6AdministrativeRecord Struct Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for Bpv6AdministrativeRecord:</div>
<div class="dyncontent">
 <div class="center">
  <img src="struct_bpv6_administrative_record.png" usemap="#Bpv6AdministrativeRecord_map" alt=""/>
  <map id="Bpv6AdministrativeRecord_map" name="Bpv6AdministrativeRecord_map">
<area href="struct_bpv6_canonical_block.html" alt="Bpv6CanonicalBlock" shape="rect" coords="0,0,161,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a5a70a353f2af3c5e4145ca3bc1b74233" id="r_a5a70a353f2af3c5e4145ca3bc1b74233"><td class="memItemLeft" align="right" valign="top"><a id="a5a70a353f2af3c5e4145ca3bc1b74233" name="a5a70a353f2af3c5e4145ca3bc1b74233"></a>
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv6AdministrativeRecord</b> (const <a class="el" href="struct_bpv6_administrative_record.html">Bpv6AdministrativeRecord</a> &amp;o)=delete</td></tr>
<tr class="separator:a5a70a353f2af3c5e4145ca3bc1b74233"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af4c78712f4c8805fbe0b55271b6f4f85" id="r_af4c78712f4c8805fbe0b55271b6f4f85"><td class="memItemLeft" align="right" valign="top"><a id="af4c78712f4c8805fbe0b55271b6f4f85" name="af4c78712f4c8805fbe0b55271b6f4f85"></a>
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv6AdministrativeRecord</b> (<a class="el" href="struct_bpv6_administrative_record.html">Bpv6AdministrativeRecord</a> &amp;&amp;o)</td></tr>
<tr class="separator:af4c78712f4c8805fbe0b55271b6f4f85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a136f843b65a42feac1dfa71498d5df2c" id="r_a136f843b65a42feac1dfa71498d5df2c"><td class="memItemLeft" align="right" valign="top"><a id="a136f843b65a42feac1dfa71498d5df2c" name="a136f843b65a42feac1dfa71498d5df2c"></a>
BPCODEC_EXPORT <a class="el" href="struct_bpv6_administrative_record.html">Bpv6AdministrativeRecord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (const <a class="el" href="struct_bpv6_administrative_record.html">Bpv6AdministrativeRecord</a> &amp;o)=delete</td></tr>
<tr class="separator:a136f843b65a42feac1dfa71498d5df2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a112f88bf3d4732e57da6fbfc9a587c67" id="r_a112f88bf3d4732e57da6fbfc9a587c67"><td class="memItemLeft" align="right" valign="top"><a id="a112f88bf3d4732e57da6fbfc9a587c67" name="a112f88bf3d4732e57da6fbfc9a587c67"></a>
BPCODEC_EXPORT <a class="el" href="struct_bpv6_administrative_record.html">Bpv6AdministrativeRecord</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (<a class="el" href="struct_bpv6_administrative_record.html">Bpv6AdministrativeRecord</a> &amp;&amp;o)</td></tr>
<tr class="separator:a112f88bf3d4732e57da6fbfc9a587c67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52a96fd7fec176561b7d51e86cc7608c" id="r_a52a96fd7fec176561b7d51e86cc7608c"><td class="memItemLeft" align="right" valign="top"><a id="a52a96fd7fec176561b7d51e86cc7608c" name="a52a96fd7fec176561b7d51e86cc7608c"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator==</b> (const <a class="el" href="struct_bpv6_administrative_record.html">Bpv6AdministrativeRecord</a> &amp;o) const</td></tr>
<tr class="separator:a52a96fd7fec176561b7d51e86cc7608c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace1cb0e6f87b20b60dc69e80a4a82ffd" id="r_ace1cb0e6f87b20b60dc69e80a4a82ffd"><td class="memItemLeft" align="right" valign="top"><a id="ace1cb0e6f87b20b60dc69e80a4a82ffd" name="ace1cb0e6f87b20b60dc69e80a4a82ffd"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator!=</b> (const <a class="el" href="struct_bpv6_administrative_record.html">Bpv6AdministrativeRecord</a> &amp;o) const</td></tr>
<tr class="separator:ace1cb0e6f87b20b60dc69e80a4a82ffd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9be0b2e52152af652da699b34d8655e8" id="r_a9be0b2e52152af652da699b34d8655e8"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a9be0b2e52152af652da699b34d8655e8">SetZero</a> () override</td></tr>
<tr class="separator:a9be0b2e52152af652da699b34d8655e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada7bed07c79123c89be8d0711a24159f" id="r_ada7bed07c79123c89be8d0711a24159f"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ada7bed07c79123c89be8d0711a24159f">SerializeBpv6</a> (uint8_t *serialization) override</td></tr>
<tr class="separator:ada7bed07c79123c89be8d0711a24159f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0fc80478be41ad9a418668237f829dd7" id="r_a0fc80478be41ad9a418668237f829dd7"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a0fc80478be41ad9a418668237f829dd7">GetCanonicalBlockTypeSpecificDataSerializationSize</a> () const override</td></tr>
<tr class="separator:a0fc80478be41ad9a418668237f829dd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b30e246b33c140690b748b6734a4595" id="r_a6b30e246b33c140690b748b6734a4595"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6b30e246b33c140690b748b6734a4595">Virtual_DeserializeExtensionBlockDataBpv6</a> () override</td></tr>
<tr class="separator:a6b30e246b33c140690b748b6734a4595"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_struct_bpv6_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_struct_bpv6_canonical_block')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a></td></tr>
<tr class="memitem:ae61fa3ec97b58135d55dfc6a1ab1ffd5 inherit pub_methods_struct_bpv6_canonical_block" id="r_ae61fa3ec97b58135d55dfc6a1ab1ffd5"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv6CanonicalBlock</b> (const <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &amp;o)</td></tr>
<tr class="separator:ae61fa3ec97b58135d55dfc6a1ab1ffd5 inherit pub_methods_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36ca44b41b564cb4ccc9c94cbfeb4b5a inherit pub_methods_struct_bpv6_canonical_block" id="r_a36ca44b41b564cb4ccc9c94cbfeb4b5a"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv6CanonicalBlock</b> (<a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &amp;&amp;o)</td></tr>
<tr class="separator:a36ca44b41b564cb4ccc9c94cbfeb4b5a inherit pub_methods_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8150b479ac8daabd9127b314e1cd1e94 inherit pub_methods_struct_bpv6_canonical_block" id="r_a8150b479ac8daabd9127b314e1cd1e94"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (const <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &amp;o)</td></tr>
<tr class="separator:a8150b479ac8daabd9127b314e1cd1e94 inherit pub_methods_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afae98765e8b3d2f42da8cf830750af66 inherit pub_methods_struct_bpv6_canonical_block" id="r_afae98765e8b3d2f42da8cf830750af66"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (<a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &amp;&amp;o)</td></tr>
<tr class="separator:afae98765e8b3d2f42da8cf830750af66 inherit pub_methods_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a699cb450cf87a9b98841935f78b8c6b5 inherit pub_methods_struct_bpv6_canonical_block" id="r_a699cb450cf87a9b98841935f78b8c6b5"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator==</b> (const <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &amp;o) const</td></tr>
<tr class="separator:a699cb450cf87a9b98841935f78b8c6b5 inherit pub_methods_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd04786016179f319ddb543ffc9ae118 inherit pub_methods_struct_bpv6_canonical_block" id="r_acd04786016179f319ddb543ffc9ae118"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator!=</b> (const <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &amp;o) const</td></tr>
<tr class="separator:acd04786016179f319ddb543ffc9ae118 inherit pub_methods_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa10a385044d52a45f6129081f2df31ac inherit pub_methods_struct_bpv6_canonical_block" id="r_aa10a385044d52a45f6129081f2df31ac"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>GetSerializationSize</b> () const</td></tr>
<tr class="separator:aa10a385044d52a45f6129081f2df31ac inherit pub_methods_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d378932d1f160a78fa916cf52a604e9 inherit pub_methods_struct_bpv6_canonical_block" id="r_a4d378932d1f160a78fa916cf52a604e9"><td class="memItemLeft" align="right" valign="top">BPCODEC_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_bpv6_canonical_block.html#a4d378932d1f160a78fa916cf52a604e9">bpv6_canonical_block_print</a> () const</td></tr>
<tr class="separator:a4d378932d1f160a78fa916cf52a604e9 inherit pub_methods_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40d08f135b83cde189c2ea40731cade7 inherit pub_methods_struct_bpv6_canonical_block" id="r_a40d08f135b83cde189c2ea40731cade7"><td class="memItemLeft" align="right" valign="top">BPCODEC_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_bpv6_canonical_block.html#a40d08f135b83cde189c2ea40731cade7">bpv6_block_flags_print</a> () const</td></tr>
<tr class="separator:a40d08f135b83cde189c2ea40731cade7 inherit pub_methods_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:af92cbf537a385030cbdfbac1ed811b07" id="r_af92cbf537a385030cbdfbac1ed811b07"><td class="memItemLeft" align="right" valign="top"><a id="af92cbf537a385030cbdfbac1ed811b07" name="af92cbf537a385030cbdfbac1ed811b07"></a>
BPV6_ADMINISTRATIVE_RECORD_TYPE_CODE&#160;</td><td class="memItemRight" valign="bottom"><b>m_adminRecordTypeCode</b></td></tr>
<tr class="separator:af92cbf537a385030cbdfbac1ed811b07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36b47bf1ca0329233f9607d582a79c4c" id="r_a36b47bf1ca0329233f9607d582a79c4c"><td class="memItemLeft" align="right" valign="top"><a id="a36b47bf1ca0329233f9607d582a79c4c" name="a36b47bf1ca0329233f9607d582a79c4c"></a>
std::unique_ptr&lt; <a class="el" href="struct_bpv6_administrative_record_content_base.html">Bpv6AdministrativeRecordContentBase</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>m_adminRecordContentPtr</b></td></tr>
<tr class="separator:a36b47bf1ca0329233f9607d582a79c4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada95eb1fa5a879a65a87ef37878ff2e2" id="r_ada95eb1fa5a879a65a87ef37878ff2e2"><td class="memItemLeft" align="right" valign="top"><a id="ada95eb1fa5a879a65a87ef37878ff2e2" name="ada95eb1fa5a879a65a87ef37878ff2e2"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>m_isFragment</b></td></tr>
<tr class="separator:ada95eb1fa5a879a65a87ef37878ff2e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_attribs_struct_bpv6_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_attribs_struct_bpv6_canonical_block')"><img src="closed.png" alt="-"/>&#160;Public Attributes inherited from <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a></td></tr>
<tr class="memitem:a91780d7a09f42cad2dd93e392238d98b inherit pub_attribs_struct_bpv6_canonical_block" id="r_a91780d7a09f42cad2dd93e392238d98b"><td class="memItemLeft" align="right" valign="top">
BPV6_BLOCKFLAG&#160;</td><td class="memItemRight" valign="bottom"><b>m_blockProcessingControlFlags</b></td></tr>
<tr class="separator:a91780d7a09f42cad2dd93e392238d98b inherit pub_attribs_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae30c6e82cb623bd6f77d97fa8cfcb171 inherit pub_attribs_struct_bpv6_canonical_block" id="r_ae30c6e82cb623bd6f77d97fa8cfcb171"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_blockTypeSpecificDataLength</b></td></tr>
<tr class="separator:ae30c6e82cb623bd6f77d97fa8cfcb171 inherit pub_attribs_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addd20445009c8e3c9a5f91516e3a93b3 inherit pub_attribs_struct_bpv6_canonical_block" id="r_addd20445009c8e3c9a5f91516e3a93b3"><td class="memItemLeft" align="right" valign="top">
uint8_t *&#160;</td><td class="memItemRight" valign="bottom"><b>m_blockTypeSpecificDataPtr</b></td></tr>
<tr class="separator:addd20445009c8e3c9a5f91516e3a93b3 inherit pub_attribs_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a25c3cd2fd60c1ac80bfc000d014c63a8 inherit pub_attribs_struct_bpv6_canonical_block" id="r_a25c3cd2fd60c1ac80bfc000d014c63a8"><td class="memItemLeft" align="right" valign="top">
BPV6_BLOCK_TYPE_CODE&#160;</td><td class="memItemRight" valign="bottom"><b>m_blockTypeCode</b></td></tr>
<tr class="separator:a25c3cd2fd60c1ac80bfc000d014c63a8 inherit pub_attribs_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_static_methods_struct_bpv6_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_static_methods_struct_bpv6_canonical_block')"><img src="closed.png" alt="-"/>&#160;Static Public Member Functions inherited from <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a></td></tr>
<tr class="memitem:a4c6646dc9722261b5ac3b5df8eb55307 inherit pub_static_methods_struct_bpv6_canonical_block" id="r_a4c6646dc9722261b5ac3b5df8eb55307"><td class="memItemLeft" align="right" valign="top">
static BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>DeserializeBpv6</b> (std::unique_ptr&lt; <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &gt; &amp;canonicalPtr, const uint8_t *serialization, uint64_t &amp;numBytesTakenToDecode, uint64_t bufferSize, const bool isAdminRecord, std::unique_ptr&lt; <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &gt; *blockNumberToRecycledCanonicalBlockArray, std::unique_ptr&lt; <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a> &gt; *recycledAdminRecord)</td></tr>
<tr class="separator:a4c6646dc9722261b5ac3b5df8eb55307 inherit pub_static_methods_struct_bpv6_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a0fc80478be41ad9a418668237f829dd7" name="a0fc80478be41ad9a418668237f829dd7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0fc80478be41ad9a418668237f829dd7">&#9670;&#160;</a></span>GetCanonicalBlockTypeSpecificDataSerializationSize()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t Bpv6AdministrativeRecord::GetCanonicalBlockTypeSpecificDataSerializationSize </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a>.</p>

</div>
</div>
<a id="ada7bed07c79123c89be8d0711a24159f" name="ada7bed07c79123c89be8d0711a24159f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada7bed07c79123c89be8d0711a24159f">&#9670;&#160;</a></span>SerializeBpv6()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t Bpv6AdministrativeRecord::SerializeBpv6 </td>
          <td>(</td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>serialization</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a>.</p>

</div>
</div>
<a id="a9be0b2e52152af652da699b34d8655e8" name="a9be0b2e52152af652da699b34d8655e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9be0b2e52152af652da699b34d8655e8">&#9670;&#160;</a></span>SetZero()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void Bpv6AdministrativeRecord::SetZero </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a>.</p>

</div>
</div>
<a id="a6b30e246b33c140690b748b6734a4595" name="a6b30e246b33c140690b748b6734a4595"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6b30e246b33c140690b748b6734a4595">&#9670;&#160;</a></span>Virtual_DeserializeExtensionBlockDataBpv6()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Bpv6AdministrativeRecord::Virtual_DeserializeExtensionBlockDataBpv6 </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv6_canonical_block.html">Bpv6CanonicalBlock</a>.</p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>common/bpcodec/include/codec/<a class="el" href="bpv6_8h_source.html">bpv6.h</a></li>
<li>common/bpcodec/src/codec/<a class="el" href="_bpv6_administrative_records_8cpp.html">Bpv6AdministrativeRecords.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_bpv6_administrative_record.html">Bpv6AdministrativeRecord</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
