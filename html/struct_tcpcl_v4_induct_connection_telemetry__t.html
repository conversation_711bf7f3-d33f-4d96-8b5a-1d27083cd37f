<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: TcpclV4InductConnectionTelemetry_t Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('struct_tcpcl_v4_induct_connection_telemetry__t.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="struct_tcpcl_v4_induct_connection_telemetry__t-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">TcpclV4InductConnectionTelemetry_t Struct Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for TcpclV4InductConnectionTelemetry_t:</div>
<div class="dyncontent">
 <div class="center">
  <img src="struct_tcpcl_v4_induct_connection_telemetry__t.png" usemap="#TcpclV4InductConnectionTelemetry_5Ft_map" alt=""/>
  <map id="TcpclV4InductConnectionTelemetry_5Ft_map" name="TcpclV4InductConnectionTelemetry_5Ft_map">
<area href="struct_induct_connection_telemetry__t.html" alt="InductConnectionTelemetry_t" shape="rect" coords="0,56,222,80"/>
<area href="class_json_serializable.html" alt="JsonSerializable" shape="rect" coords="0,0,222,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a57f1ffa5011efe59072d7bdd378016e9" id="r_a57f1ffa5011efe59072d7bdd378016e9"><td class="memItemLeft" align="right" valign="top">virtual TELEMETRY_DEFINITIONS_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a57f1ffa5011efe59072d7bdd378016e9">operator==</a> (const <a class="el" href="struct_induct_connection_telemetry__t.html">InductConnectionTelemetry_t</a> &amp;o) const override</td></tr>
<tr class="separator:a57f1ffa5011efe59072d7bdd378016e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ec60427368a1e711e7baa4889ca4ac4" id="r_a6ec60427368a1e711e7baa4889ca4ac4"><td class="memItemLeft" align="right" valign="top">virtual TELEMETRY_DEFINITIONS_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a6ec60427368a1e711e7baa4889ca4ac4">operator!=</a> (const <a class="el" href="struct_induct_connection_telemetry__t.html">InductConnectionTelemetry_t</a> &amp;o) const override</td></tr>
<tr class="separator:a6ec60427368a1e711e7baa4889ca4ac4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75cc2432e4f35f8b7c15fdf49c53d540" id="r_a75cc2432e4f35f8b7c15fdf49c53d540"><td class="memItemLeft" align="right" valign="top">virtual TELEMETRY_DEFINITIONS_EXPORT boost::property_tree::ptree&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a75cc2432e4f35f8b7c15fdf49c53d540">GetNewPropertyTree</a> () const override</td></tr>
<tr class="separator:a75cc2432e4f35f8b7c15fdf49c53d540"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff91ea03671adf7180a10369c77dbf8d" id="r_aff91ea03671adf7180a10369c77dbf8d"><td class="memItemLeft" align="right" valign="top">virtual TELEMETRY_DEFINITIONS_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#aff91ea03671adf7180a10369c77dbf8d">SetValuesFromPropertyTree</a> (const boost::property_tree::ptree &amp;pt) override</td></tr>
<tr class="separator:aff91ea03671adf7180a10369c77dbf8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_class_json_serializable"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_class_json_serializable')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="class_json_serializable.html">JsonSerializable</a></td></tr>
<tr class="memitem:a762c2815fb637e887df4d9e537f17ba3 inherit pub_methods_class_json_serializable" id="r_a762c2815fb637e887df4d9e537f17ba3"><td class="memItemLeft" align="right" valign="top">
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>ToJson</b> (bool pretty=true) const</td></tr>
<tr class="separator:a762c2815fb637e887df4d9e537f17ba3 inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50eb3c541e9a36332d6759bb22831472 inherit pub_methods_class_json_serializable" id="r_a50eb3c541e9a36332d6759bb22831472"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>ToJsonFile</b> (const boost::filesystem::path &amp;filePath, bool pretty=true) const</td></tr>
<tr class="separator:a50eb3c541e9a36332d6759bb22831472 inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52b3931dd681a8c817592e648977beb3 inherit pub_methods_class_json_serializable" id="r_a52b3931dd681a8c817592e648977beb3"><td class="memItemLeft" align="right" valign="top">
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>ToXml</b> () const</td></tr>
<tr class="separator:a52b3931dd681a8c817592e648977beb3 inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac14772bef92f861bedb43f6673d8541a inherit pub_methods_class_json_serializable" id="r_ac14772bef92f861bedb43f6673d8541a"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>ToXmlFile</b> (const std::string &amp;fileName, char indentCharacter=' ', int indentCount=2) const</td></tr>
<tr class="separator:ac14772bef92f861bedb43f6673d8541a inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3148d4262386d8617f82f4f8b0f1931d inherit pub_methods_class_json_serializable" id="r_a3148d4262386d8617f82f4f8b0f1931d"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>SetValuesFromJson</b> (const std::string &amp;jsonString)</td></tr>
<tr class="separator:a3148d4262386d8617f82f4f8b0f1931d inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1adb23c7bad19b506530ddb773cc64be inherit pub_methods_class_json_serializable" id="r_a1adb23c7bad19b506530ddb773cc64be"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>SetValuesFromJsonCharArray</b> (const char *data, const std::size_t size)</td></tr>
<tr class="separator:a1adb23c7bad19b506530ddb773cc64be inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a24e3a1992b44ade54e0340350c199b1f" id="r_a24e3a1992b44ade54e0340350c199b1f"><td class="memItemLeft" align="right" valign="top"><a id="a24e3a1992b44ade54e0340350c199b1f" name="a24e3a1992b44ade54e0340350c199b1f"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalIncomingFragmentsAcked</b></td></tr>
<tr class="separator:a24e3a1992b44ade54e0340350c199b1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a34febc16a5ce41ecf9eb804cae4b822f" id="r_a34febc16a5ce41ecf9eb804cae4b822f"><td class="memItemLeft" align="right" valign="top"><a id="a34febc16a5ce41ecf9eb804cae4b822f" name="a34febc16a5ce41ecf9eb804cae4b822f"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalOutgoingFragmentsSent</b></td></tr>
<tr class="separator:a34febc16a5ce41ecf9eb804cae4b822f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b62fc708d8c277ae857e9f0bd7ecd9b" id="r_a3b62fc708d8c277ae857e9f0bd7ecd9b"><td class="memItemLeft" align="right" valign="top"><a id="a3b62fc708d8c277ae857e9f0bd7ecd9b" name="a3b62fc708d8c277ae857e9f0bd7ecd9b"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundlesSentAndAcked</b></td></tr>
<tr class="separator:a3b62fc708d8c277ae857e9f0bd7ecd9b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2e7ac100eeade04e35c2eff22228e69" id="r_ad2e7ac100eeade04e35c2eff22228e69"><td class="memItemLeft" align="right" valign="top"><a id="ad2e7ac100eeade04e35c2eff22228e69" name="ad2e7ac100eeade04e35c2eff22228e69"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundleBytesSentAndAcked</b></td></tr>
<tr class="separator:ad2e7ac100eeade04e35c2eff22228e69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0da16f38b68a8da22088ec311b7e52e5" id="r_a0da16f38b68a8da22088ec311b7e52e5"><td class="memItemLeft" align="right" valign="top"><a id="a0da16f38b68a8da22088ec311b7e52e5" name="a0da16f38b68a8da22088ec311b7e52e5"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundlesSent</b></td></tr>
<tr class="separator:a0da16f38b68a8da22088ec311b7e52e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7aeb7d8c4b36a9514a9f2e9b37e977d" id="r_ac7aeb7d8c4b36a9514a9f2e9b37e977d"><td class="memItemLeft" align="right" valign="top"><a id="ac7aeb7d8c4b36a9514a9f2e9b37e977d" name="ac7aeb7d8c4b36a9514a9f2e9b37e977d"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundleBytesSent</b></td></tr>
<tr class="separator:ac7aeb7d8c4b36a9514a9f2e9b37e977d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27b7c369e536553d9f6b97466fde0607" id="r_a27b7c369e536553d9f6b97466fde0607"><td class="memItemLeft" align="right" valign="top"><a id="a27b7c369e536553d9f6b97466fde0607" name="a27b7c369e536553d9f6b97466fde0607"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundlesFailedToSend</b></td></tr>
<tr class="separator:a27b7c369e536553d9f6b97466fde0607"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_attribs_struct_induct_connection_telemetry__t"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_attribs_struct_induct_connection_telemetry__t')"><img src="closed.png" alt="-"/>&#160;Public Attributes inherited from <a class="el" href="struct_induct_connection_telemetry__t.html">InductConnectionTelemetry_t</a></td></tr>
<tr class="memitem:a918c4d384808ca15c0b500ea9e8c4f18 inherit pub_attribs_struct_induct_connection_telemetry__t" id="r_a918c4d384808ca15c0b500ea9e8c4f18"><td class="memItemLeft" align="right" valign="top">
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>m_connectionName</b></td></tr>
<tr class="separator:a918c4d384808ca15c0b500ea9e8c4f18 inherit pub_attribs_struct_induct_connection_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abada9608295c2708ce7eff85d37a6b44 inherit pub_attribs_struct_induct_connection_telemetry__t" id="r_abada9608295c2708ce7eff85d37a6b44"><td class="memItemLeft" align="right" valign="top">
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>m_inputName</b></td></tr>
<tr class="separator:abada9608295c2708ce7eff85d37a6b44 inherit pub_attribs_struct_induct_connection_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3eca817c07ea8e451ecc5a599e1886d2 inherit pub_attribs_struct_induct_connection_telemetry__t" id="r_a3eca817c07ea8e451ecc5a599e1886d2"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundlesReceived</b></td></tr>
<tr class="separator:a3eca817c07ea8e451ecc5a599e1886d2 inherit pub_attribs_struct_induct_connection_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad4c6971bc09c9c2f9fa0bd2a623ff1f6 inherit pub_attribs_struct_induct_connection_telemetry__t" id="r_ad4c6971bc09c9c2f9fa0bd2a623ff1f6"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundleBytesReceived</b></td></tr>
<tr class="separator:ad4c6971bc09c9c2f9fa0bd2a623ff1f6 inherit pub_attribs_struct_induct_connection_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_static_methods_class_json_serializable"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_static_methods_class_json_serializable')"><img src="closed.png" alt="-"/>&#160;Static Public Member Functions inherited from <a class="el" href="class_json_serializable.html">JsonSerializable</a></td></tr>
<tr class="memitem:a8c0d2f4adc3eae8076d7b6e763b5e8c9 inherit pub_static_methods_class_json_serializable" id="r_a8c0d2f4adc3eae8076d7b6e763b5e8c9"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>LoadTextFileIntoString</b> (const boost::filesystem::path &amp;filePath, std::string &amp;fileContentsAsString)</td></tr>
<tr class="separator:a8c0d2f4adc3eae8076d7b6e763b5e8c9 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50d0c6d42669b91106c6012f985d4ee1 inherit pub_static_methods_class_json_serializable" id="r_a50d0c6d42669b91106c6012f985d4ee1"><td class="memItemLeft" align="right" valign="top">
static void&#160;</td><td class="memItemRight" valign="bottom"><b>GetAllJsonKeys</b> (const std::string &amp;jsonText, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</td></tr>
<tr class="separator:a50d0c6d42669b91106c6012f985d4ee1 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad92085846035c93eb245e0cd9fed73c4 inherit pub_static_methods_class_json_serializable" id="r_ad92085846035c93eb245e0cd9fed73c4"><td class="memItemLeft" align="right" valign="top">
static void&#160;</td><td class="memItemRight" valign="bottom"><b>GetAllJsonKeysLineByLine</b> (std::istream &amp;stream, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</td></tr>
<tr class="separator:ad92085846035c93eb245e0cd9fed73c4 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94353f2da8521917c76a827829c9cc5a inherit pub_static_methods_class_json_serializable" id="r_a94353f2da8521917c76a827829c9cc5a"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>HasUnusedJsonVariablesInFilePath</b> (const <a class="el" href="class_json_serializable.html">JsonSerializable</a> &amp;config, const boost::filesystem::path &amp;originalUserJsonFilePath, std::string &amp;returnedErrorMessage)</td></tr>
<tr class="separator:a94353f2da8521917c76a827829c9cc5a inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d6a8897e5038c0938f9bae38dfeeccb inherit pub_static_methods_class_json_serializable" id="r_a1d6a8897e5038c0938f9bae38dfeeccb"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>HasUnusedJsonVariablesInString</b> (const <a class="el" href="class_json_serializable.html">JsonSerializable</a> &amp;config, const std::string &amp;originalUserJsonString, std::string &amp;returnedErrorMessage)</td></tr>
<tr class="separator:a1d6a8897e5038c0938f9bae38dfeeccb inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa5fbacf36fa5675eb162108f50ec928e inherit pub_static_methods_class_json_serializable" id="r_aa5fbacf36fa5675eb162108f50ec928e"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>HasUnusedJsonVariablesInStream</b> (const <a class="el" href="class_json_serializable.html">JsonSerializable</a> &amp;config, std::istream &amp;originalUserJsonStream, std::string &amp;returnedErrorMessage)</td></tr>
<tr class="separator:aa5fbacf36fa5675eb162108f50ec928e inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6ea55a68c508e3b2eb3da2aa5f6feb8 inherit pub_static_methods_class_json_serializable" id="r_af6ea55a68c508e3b2eb3da2aa5f6feb8"><td class="memItemLeft" align="right" valign="top">
static std::string&#160;</td><td class="memItemRight" valign="bottom"><b>PtToJsonString</b> (const boost::property_tree::ptree &amp;pt, bool pretty=true)</td></tr>
<tr class="separator:af6ea55a68c508e3b2eb3da2aa5f6feb8 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a571a9ee2657f4cf0a9f3835ebcac2890 inherit pub_static_methods_class_json_serializable" id="r_a571a9ee2657f4cf0a9f3835ebcac2890"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromJsonCharArray</b> (char *data, const std::size_t size, boost::property_tree::ptree &amp;pt)</td></tr>
<tr class="separator:a571a9ee2657f4cf0a9f3835ebcac2890 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e1f2f0c799de5c252ff1ab8f332b956 inherit pub_static_methods_class_json_serializable" id="r_a1e1f2f0c799de5c252ff1ab8f332b956"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromJsonStream</b> (std::istream &amp;jsonStream, boost::property_tree::ptree &amp;pt)</td></tr>
<tr class="separator:a1e1f2f0c799de5c252ff1ab8f332b956 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10d2706e6272b11b4eb69526457fa4d4 inherit pub_static_methods_class_json_serializable" id="r_a10d2706e6272b11b4eb69526457fa4d4"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromJsonString</b> (const std::string &amp;jsonStr, boost::property_tree::ptree &amp;pt)</td></tr>
<tr class="separator:a10d2706e6272b11b4eb69526457fa4d4 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a316f4084f79841c5ef63d2df58d8a909 inherit pub_static_methods_class_json_serializable" id="r_a316f4084f79841c5ef63d2df58d8a909"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromJsonFilePath</b> (const boost::filesystem::path &amp;jsonFilePath, boost::property_tree::ptree &amp;pt)</td></tr>
<tr class="separator:a316f4084f79841c5ef63d2df58d8a909 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37748556f4db393580d61a391e276c4d inherit pub_static_methods_class_json_serializable" id="r_a37748556f4db393580d61a391e276c4d"><td class="memItemLeft" align="right" valign="top">
static std::string&#160;</td><td class="memItemRight" valign="bottom"><b>PtToXmlString</b> (const boost::property_tree::ptree &amp;pt)</td></tr>
<tr class="separator:a37748556f4db393580d61a391e276c4d inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3155e9455ae6a5e776795ed5a7d0385b inherit pub_static_methods_class_json_serializable" id="r_a3155e9455ae6a5e776795ed5a7d0385b"><td class="memItemLeft" align="right" valign="top">
static boost::property_tree::ptree&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromXmlString</b> (const std::string &amp;jsonStr)</td></tr>
<tr class="separator:a3155e9455ae6a5e776795ed5a7d0385b inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3c9b14f4a0b0193549abc57695b30dd inherit pub_static_methods_class_json_serializable" id="r_ac3c9b14f4a0b0193549abc57695b30dd"><td class="memItemLeft" align="right" valign="top">
static boost::property_tree::ptree&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromXmlFile</b> (const std::string &amp;xmlFileName)</td></tr>
<tr class="separator:ac3c9b14f4a0b0193549abc57695b30dd inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a75cc2432e4f35f8b7c15fdf49c53d540" name="a75cc2432e4f35f8b7c15fdf49c53d540"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a75cc2432e4f35f8b7c15fdf49c53d540">&#9670;&#160;</a></span>GetNewPropertyTree()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">boost::property_tree::ptree TcpclV4InductConnectionTelemetry_t::GetNewPropertyTree </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_induct_connection_telemetry__t.html">InductConnectionTelemetry_t</a>.</p>

</div>
</div>
<a id="a6ec60427368a1e711e7baa4889ca4ac4" name="a6ec60427368a1e711e7baa4889ca4ac4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6ec60427368a1e711e7baa4889ca4ac4">&#9670;&#160;</a></span>operator!=()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool TcpclV4InductConnectionTelemetry_t::operator!= </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="struct_induct_connection_telemetry__t.html">InductConnectionTelemetry_t</a> &amp;</td>          <td class="paramname"><span class="paramname"><em>o</em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_induct_connection_telemetry__t.html">InductConnectionTelemetry_t</a>.</p>

</div>
</div>
<a id="a57f1ffa5011efe59072d7bdd378016e9" name="a57f1ffa5011efe59072d7bdd378016e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a57f1ffa5011efe59072d7bdd378016e9">&#9670;&#160;</a></span>operator==()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool TcpclV4InductConnectionTelemetry_t::operator== </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="struct_induct_connection_telemetry__t.html">InductConnectionTelemetry_t</a> &amp;</td>          <td class="paramname"><span class="paramname"><em>o</em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_induct_connection_telemetry__t.html">InductConnectionTelemetry_t</a>.</p>

</div>
</div>
<a id="aff91ea03671adf7180a10369c77dbf8d" name="aff91ea03671adf7180a10369c77dbf8d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff91ea03671adf7180a10369c77dbf8d">&#9670;&#160;</a></span>SetValuesFromPropertyTree()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool TcpclV4InductConnectionTelemetry_t::SetValuesFromPropertyTree </td>
          <td>(</td>
          <td class="paramtype">const boost::property_tree::ptree &amp;</td>          <td class="paramname"><span class="paramname"><em>pt</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_induct_connection_telemetry__t.html">InductConnectionTelemetry_t</a>.</p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>common/telemetry_definitions/include/<a class="el" href="_telemetry_definitions_8h_source.html">TelemetryDefinitions.h</a></li>
<li>common/telemetry_definitions/src/<b>TelemetryDefinitions.cpp</b></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_tcpcl_v4_induct_connection_telemetry__t.html">TcpclV4InductConnectionTelemetry_t</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
