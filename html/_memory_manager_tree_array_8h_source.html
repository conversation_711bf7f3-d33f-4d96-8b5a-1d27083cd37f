<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: module/storage/include/MemoryManagerTreeArray.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('_memory_manager_tree_array_8h_source.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">MemoryManagerTreeArray.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_memory_manager_tree_array_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span> </div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span><span class="preprocessor">#ifndef _MEMORY_MANAGER_TREE_ARRAY_H</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="preprocessor">#define _MEMORY_MANAGER_TREE_ARRAY_H 1</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span> </div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="preprocessor">#include &lt;boost/integer.hpp&gt;</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="preprocessor">#include &lt;stdint.h&gt;</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="preprocessor">#include &quot;<a class="code" href="_bundle_storage_config_8h.html">BundleStorageConfig.h</a>&quot;</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="preprocessor">#include &lt;boost/thread.hpp&gt;</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span><span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span><span class="preprocessor">#include &lt;boost/core/noncopyable.hpp&gt;</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span><span class="preprocessor">#include &quot;storage_lib_export.h&quot;</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span> </div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span> </div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="keyword">typedef</span> std::vector&lt;segment_id_t&gt; segment_id_chain_vec_t;</div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span> </div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="keyword">typedef</span> std::vector&lt; std::vector&lt;uint64_t&gt; &gt; memmanager_t;</div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span> </div>
<div class="foldopen" id="foldopen00036" data-start="{" data-end="};">
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno"><a class="line" href="class_memory_manager_tree_array.html">   36</a></span><span class="keyword">class </span>MemoryManagerTreeArray : <span class="keyword">private</span> boost::noncopyable {</div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span>    MemoryManagerTreeArray() = <span class="keyword">delete</span>;</div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="keyword">public</span>:</div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span>    STORAGE_LIB_EXPORT MemoryManagerTreeArray(<span class="keyword">const</span> uint64_t maxSegments);</div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span>    STORAGE_LIB_EXPORT ~MemoryManagerTreeArray();</div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span></div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span>    STORAGE_LIB_EXPORT <span class="keywordtype">bool</span> <a class="code hl_function" href="class_memory_manager_tree_array.html#a142e32e906555aebe67f1c231ecf4680">AllocateSegments_ThreadSafe</a>(segment_id_chain_vec_t &amp; segmentVec);</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span></div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span>    STORAGE_LIB_EXPORT <span class="keywordtype">bool</span> <a class="code hl_function" href="class_memory_manager_tree_array.html#a39946e2ab459e03aef0309b79cc44f5d">FreeSegments_ThreadSafe</a>(<span class="keyword">const</span> segment_id_chain_vec_t &amp; segmentVec);</div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span></div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span>    STORAGE_LIB_EXPORT <span class="keywordtype">bool</span> <a class="code hl_function" href="class_memory_manager_tree_array.html#ad30ec86217a17414ea4a096d4956e4c8">IsSegmentFree</a>(<span class="keyword">const</span> segment_id_t segmentId) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span></div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span>    STORAGE_LIB_EXPORT <span class="keywordtype">void</span> <a class="code hl_function" href="class_memory_manager_tree_array.html#a4076196cdbf3a371a58cb4f919e74fbc">BackupDataToVector</a>(memmanager_t &amp; backup) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span></div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span>    STORAGE_LIB_EXPORT <span class="keyword">const</span> memmanager_t &amp; <a class="code hl_function" href="class_memory_manager_tree_array.html#a4d6670e6ab6547a32eee0a9eb6f62cee">GetVectorsConstRef</a>() <span class="keyword">const</span>;</div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span></div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span>    STORAGE_LIB_EXPORT <span class="keywordtype">bool</span> <a class="code hl_function" href="class_memory_manager_tree_array.html#a20efc84ba0eb5300bd6943dd62e5c88c">IsBackupEqual</a>(<span class="keyword">const</span> memmanager_t &amp; backup) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span></div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span>    STORAGE_LIB_EXPORT <span class="keywordtype">bool</span> <a class="code hl_function" href="class_memory_manager_tree_array.html#a27141ad52dd26d31bc6fe30a83e22c5e">FreeSegmentId_NotThreadSafe</a>(<span class="keyword">const</span> segment_id_t segmentId);</div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span></div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span>    STORAGE_LIB_EXPORT segment_id_t <a class="code hl_function" href="class_memory_manager_tree_array.html#aeea93460b519117ea4d3e39aec28a7af">GetAndSetFirstFreeSegmentId_NotThreadSafe</a>();</div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span></div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span>    STORAGE_LIB_EXPORT <span class="keywordtype">bool</span> <a class="code hl_function" href="class_memory_manager_tree_array.html#a6f28fc208e8aa40fce358093f771ca6f">AllocateSegmentId_NotThreadSafe</a>(<span class="keyword">const</span> segment_id_t segmentId);</div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span></div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span>    STORAGE_LIB_EXPORT uint64_t <a class="code hl_function" href="class_memory_manager_tree_array.html#a85421ecfa3c17906e49606075c7a2017">GetNumAllocatedSegments_NotThreadSafe</a>() <span class="keyword">const</span> <span class="keyword">noexcept</span>;</div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span></div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno">  127</span>    STORAGE_LIB_EXPORT uint64_t <a class="code hl_function" href="class_memory_manager_tree_array.html#addec86d391e8e4ed229938f8454f2591">GetNumAllocatedSegments_ThreadSafe</a>() <span class="keyword">const</span>;</div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span></div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno">  133</span>    STORAGE_LIB_EXPORT uint64_t <a class="code hl_function" href="class_memory_manager_tree_array.html#af2fc922b92336ea17d4adcbe3d570f2a">GetMaxSegments</a>() <span class="keyword">const</span> <span class="keyword">noexcept</span>;</div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno">  134</span> </div>
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno">  135</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span> </div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span> </div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span>    STORAGE_LIB_NO_EXPORT <span class="keywordtype">bool</span> GetAndSetFirstFreeSegmentId(<span class="keyword">const</span> segment_id_t depthIndex, segment_id_t &amp; segmentId);</div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span>    STORAGE_LIB_NO_EXPORT <span class="keywordtype">void</span> AllocateRows(<span class="keyword">const</span> segment_id_t largestSegmentId);</div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span>    STORAGE_LIB_NO_EXPORT <span class="keywordtype">void</span> AllocateRowsMaxMemory();</div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span>    <span class="keyword">const</span> uint64_t M_MAX_SEGMENTS;</div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span>    uint64_t m_numSegmentsAllocated;</div>
<div class="line"><a id="l00144" name="l00144"></a><span class="lineno">  144</span>    std::vector&lt;std::vector&lt;uint64_t&gt; &gt; m_bitMasks;</div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span>    <span class="keyword">mutable</span> boost::mutex m_mutex;</div>
<div class="line"><a id="l00146" name="l00146"></a><span class="lineno">  146</span>};</div>
</div>
<div class="line"><a id="l00147" name="l00147"></a><span class="lineno">  147</span> </div>
<div class="line"><a id="l00148" name="l00148"></a><span class="lineno">  148</span> </div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno">  149</span><span class="preprocessor">#endif </span><span class="comment">//_MEMORY_MANAGER_TREE_ARRAY_H</span></div>
<div class="ttc" id="a_bundle_storage_config_8h_html"><div class="ttname"><a href="_bundle_storage_config_8h.html">BundleStorageConfig.h</a></div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_a142e32e906555aebe67f1c231ecf4680"><div class="ttname"><a href="class_memory_manager_tree_array.html#a142e32e906555aebe67f1c231ecf4680">MemoryManagerTreeArray::AllocateSegments_ThreadSafe</a></div><div class="ttdeci">STORAGE_LIB_EXPORT bool AllocateSegments_ThreadSafe(segment_id_chain_vec_t &amp;segmentVec)</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:231</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_a20efc84ba0eb5300bd6943dd62e5c88c"><div class="ttname"><a href="class_memory_manager_tree_array.html#a20efc84ba0eb5300bd6943dd62e5c88c">MemoryManagerTreeArray::IsBackupEqual</a></div><div class="ttdeci">STORAGE_LIB_EXPORT bool IsBackupEqual(const memmanager_t &amp;backup) const</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:58</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_a27141ad52dd26d31bc6fe30a83e22c5e"><div class="ttname"><a href="class_memory_manager_tree_array.html#a27141ad52dd26d31bc6fe30a83e22c5e">MemoryManagerTreeArray::FreeSegmentId_NotThreadSafe</a></div><div class="ttdeci">STORAGE_LIB_EXPORT bool FreeSegmentId_NotThreadSafe(const segment_id_t segmentId)</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:120</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_a39946e2ab459e03aef0309b79cc44f5d"><div class="ttname"><a href="class_memory_manager_tree_array.html#a39946e2ab459e03aef0309b79cc44f5d">MemoryManagerTreeArray::FreeSegments_ThreadSafe</a></div><div class="ttdeci">STORAGE_LIB_EXPORT bool FreeSegments_ThreadSafe(const segment_id_chain_vec_t &amp;segmentVec)</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:250</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_a4076196cdbf3a371a58cb4f919e74fbc"><div class="ttname"><a href="class_memory_manager_tree_array.html#a4076196cdbf3a371a58cb4f919e74fbc">MemoryManagerTreeArray::BackupDataToVector</a></div><div class="ttdeci">STORAGE_LIB_EXPORT void BackupDataToVector(memmanager_t &amp;backup) const</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:50</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_a4d6670e6ab6547a32eee0a9eb6f62cee"><div class="ttname"><a href="class_memory_manager_tree_array.html#a4d6670e6ab6547a32eee0a9eb6f62cee">MemoryManagerTreeArray::GetVectorsConstRef</a></div><div class="ttdeci">STORAGE_LIB_EXPORT const memmanager_t &amp; GetVectorsConstRef() const</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:54</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_a6f28fc208e8aa40fce358093f771ca6f"><div class="ttname"><a href="class_memory_manager_tree_array.html#a6f28fc208e8aa40fce358093f771ca6f">MemoryManagerTreeArray::AllocateSegmentId_NotThreadSafe</a></div><div class="ttdeci">STORAGE_LIB_EXPORT bool AllocateSegmentId_NotThreadSafe(const segment_id_t segmentId)</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:184</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_a85421ecfa3c17906e49606075c7a2017"><div class="ttname"><a href="class_memory_manager_tree_array.html#a85421ecfa3c17906e49606075c7a2017">MemoryManagerTreeArray::GetNumAllocatedSegments_NotThreadSafe</a></div><div class="ttdeci">STORAGE_LIB_EXPORT uint64_t GetNumAllocatedSegments_NotThreadSafe() const noexcept</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:262</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_ad30ec86217a17414ea4a096d4956e4c8"><div class="ttname"><a href="class_memory_manager_tree_array.html#ad30ec86217a17414ea4a096d4956e4c8">MemoryManagerTreeArray::IsSegmentFree</a></div><div class="ttdeci">STORAGE_LIB_EXPORT bool IsSegmentFree(const segment_id_t segmentId) const</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:106</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_addec86d391e8e4ed229938f8454f2591"><div class="ttname"><a href="class_memory_manager_tree_array.html#addec86d391e8e4ed229938f8454f2591">MemoryManagerTreeArray::GetNumAllocatedSegments_ThreadSafe</a></div><div class="ttdeci">STORAGE_LIB_EXPORT uint64_t GetNumAllocatedSegments_ThreadSafe() const</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:266</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_aeea93460b519117ea4d3e39aec28a7af"><div class="ttname"><a href="class_memory_manager_tree_array.html#aeea93460b519117ea4d3e39aec28a7af">MemoryManagerTreeArray::GetAndSetFirstFreeSegmentId_NotThreadSafe</a></div><div class="ttdeci">STORAGE_LIB_EXPORT segment_id_t GetAndSetFirstFreeSegmentId_NotThreadSafe()</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:97</div></div>
<div class="ttc" id="aclass_memory_manager_tree_array_html_af2fc922b92336ea17d4adcbe3d570f2a"><div class="ttname"><a href="class_memory_manager_tree_array.html#af2fc922b92336ea17d4adcbe3d570f2a">MemoryManagerTreeArray::GetMaxSegments</a></div><div class="ttdeci">STORAGE_LIB_EXPORT uint64_t GetMaxSegments() const noexcept</div><div class="ttdef"><b>Definition</b> MemoryManagerTreeArray.cpp:273</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_a7b0466279106ea0b8f86f609f621680.html">module</a></li><li class="navelem"><a class="el" href="dir_78be2278cffd1fc76ff5ed3840c85112.html">storage</a></li><li class="navelem"><a class="el" href="dir_fede5c1962ad31e42597f7c737c8bd83.html">include</a></li><li class="navelem"><a class="el" href="_memory_manager_tree_array_8h.html">MemoryManagerTreeArray.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
