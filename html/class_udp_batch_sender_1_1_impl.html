<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: UdpBatchSender::Impl Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_udp_batch_sender_1_1_impl.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_udp_batch_sender_1_1_impl-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">UdpBatchSender::Impl Class Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a259a9c5181905a75e6856428e6cb418c" id="r_a259a9c5181905a75e6856428e6cb418c"><td class="memItemLeft" align="right" valign="top"><a id="a259a9c5181905a75e6856428e6cb418c" name="a259a9c5181905a75e6856428e6cb418c"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Impl</b> (boost::asio::io_service &amp;ioServiceRef)</td></tr>
<tr class="separator:a259a9c5181905a75e6856428e6cb418c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7447477a07a333cedeb9c97002c240fc" id="r_a7447477a07a333cedeb9c97002c240fc"><td class="memItemLeft" align="right" valign="top"><a id="a7447477a07a333cedeb9c97002c240fc" name="a7447477a07a333cedeb9c97002c240fc"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Stop</b> ()</td></tr>
<tr class="separator:a7447477a07a333cedeb9c97002c240fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad09943719c7b4f320ee34b0cfa34b090" id="r_ad09943719c7b4f320ee34b0cfa34b090"><td class="memItemLeft" align="right" valign="top"><a id="ad09943719c7b4f320ee34b0cfa34b090" name="ad09943719c7b4f320ee34b0cfa34b090"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Stop_CalledFromWithinIoServiceThread</b> ()</td></tr>
<tr class="separator:ad09943719c7b4f320ee34b0cfa34b090"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8697497d3904f60c0efddeb36a57f76e" id="r_a8697497d3904f60c0efddeb36a57f76e"><td class="memItemLeft" align="right" valign="top"><a id="a8697497d3904f60c0efddeb36a57f76e" name="a8697497d3904f60c0efddeb36a57f76e"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>Init</b> (const std::string &amp;remoteHostname, const uint16_t remotePort)</td></tr>
<tr class="separator:a8697497d3904f60c0efddeb36a57f76e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5a983e4aa45ea26bb10c0e27f0dadcc" id="r_ab5a983e4aa45ea26bb10c0e27f0dadcc"><td class="memItemLeft" align="right" valign="top"><a id="ab5a983e4aa45ea26bb10c0e27f0dadcc" name="ab5a983e4aa45ea26bb10c0e27f0dadcc"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>Init</b> (const boost::asio::ip::udp::endpoint &amp;udpDestinationEndpoint)</td></tr>
<tr class="separator:ab5a983e4aa45ea26bb10c0e27f0dadcc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb40077070540c36c025e6696f87fcef" id="r_aeb40077070540c36c025e6696f87fcef"><td class="memItemLeft" align="right" valign="top"><a id="aeb40077070540c36c025e6696f87fcef" name="aeb40077070540c36c025e6696f87fcef"></a>
boost::asio::ip::udp::endpoint&#160;</td><td class="memItemRight" valign="bottom"><b>GetCurrentUdpEndpoint</b> () const</td></tr>
<tr class="separator:aeb40077070540c36c025e6696f87fcef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e16198485dfa249c1127c932f475afc" id="r_a7e16198485dfa249c1127c932f475afc"><td class="memItemLeft" align="right" valign="top"><a id="a7e16198485dfa249c1127c932f475afc" name="a7e16198485dfa249c1127c932f475afc"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>QueueSendPacketsOperation_ThreadSafe</b> (std::shared_ptr&lt; std::vector&lt; <a class="el" href="struct_udp_send_packet_info.html">UdpSendPacketInfo</a> &gt; &gt; &amp;&amp;udpSendPacketInfoVecSharedPtr, const std::size_t numPacketsToSend)</td></tr>
<tr class="separator:a7e16198485dfa249c1127c932f475afc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94d88b5e4f892d975a27d3c45c30ed3e" id="r_a94d88b5e4f892d975a27d3c45c30ed3e"><td class="memItemLeft" align="right" valign="top"><a id="a94d88b5e4f892d975a27d3c45c30ed3e" name="a94d88b5e4f892d975a27d3c45c30ed3e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SetOnSentPacketsCallback</b> (const OnSentPacketsCallback_t &amp;callback)</td></tr>
<tr class="separator:a94d88b5e4f892d975a27d3c45c30ed3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a036914b9a5679c011649ea58b67e3442" id="r_a036914b9a5679c011649ea58b67e3442"><td class="memItemLeft" align="right" valign="top"><a id="a036914b9a5679c011649ea58b67e3442" name="a036914b9a5679c011649ea58b67e3442"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SetEndpointAndReconnect_ThreadSafe</b> (const boost::asio::ip::udp::endpoint &amp;remoteEndpoint)</td></tr>
<tr class="separator:a036914b9a5679c011649ea58b67e3442"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01fb9fc09079b243b5d3420a25b02d3c" id="r_a01fb9fc09079b243b5d3420a25b02d3c"><td class="memItemLeft" align="right" valign="top"><a id="a01fb9fc09079b243b5d3420a25b02d3c" name="a01fb9fc09079b243b5d3420a25b02d3c"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SetEndpointAndReconnect_ThreadSafe</b> (const std::string &amp;remoteHostname, const uint16_t remotePort)</td></tr>
<tr class="separator:a01fb9fc09079b243b5d3420a25b02d3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adeef70e0d1624c367ec1f90639cc5458" id="r_adeef70e0d1624c367ec1f90639cc5458"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adeef70e0d1624c367ec1f90639cc5458">DoHandleSocketShutdown</a> ()</td></tr>
<tr class="separator:adeef70e0d1624c367ec1f90639cc5458"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2088ea77f3199ebcdca9be708ef614b" id="r_ae2088ea77f3199ebcdca9be708ef614b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae2088ea77f3199ebcdca9be708ef614b">QueueAndTryPerformSendPacketsOperation_NotThreadSafe</a> (std::shared_ptr&lt; std::vector&lt; <a class="el" href="struct_udp_send_packet_info.html">UdpSendPacketInfo</a> &gt; &gt; &amp;udpSendPacketInfoVecSharedPtr, const std::size_t numPacketsToSend)</td></tr>
<tr class="separator:ae2088ea77f3199ebcdca9be708ef614b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="adeef70e0d1624c367ec1f90639cc5458" name="adeef70e0d1624c367ec1f90639cc5458"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adeef70e0d1624c367ec1f90639cc5458">&#9670;&#160;</a></span>DoHandleSocketShutdown()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void UdpBatchSender::Impl::DoHandleSocketShutdown </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Perform socket shutdown.</p>
<p>If a connection is not open, returns immediately. </p><dl class="section post"><dt>Postcondition</dt><dd>The underlying socket mechanism is ready to be reused after a successful call to UdpBatchSender::SetEndpointAndReconnect(). </dd></dl>

</div>
</div>
<a id="ae2088ea77f3199ebcdca9be708ef614b" name="ae2088ea77f3199ebcdca9be708ef614b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae2088ea77f3199ebcdca9be708ef614b">&#9670;&#160;</a></span>QueueAndTryPerformSendPacketsOperation_NotThreadSafe()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void UdpBatchSender::Impl::QueueAndTryPerformSendPacketsOperation_NotThreadSafe </td>
          <td>(</td>
          <td class="paramtype">std::shared_ptr&lt; std::vector&lt; <a class="el" href="struct_udp_send_packet_info.html">UdpSendPacketInfo</a> &gt; &gt; &amp;</td>          <td class="paramname"><span class="paramname"><em>udpSendPacketInfoVecSharedPtr</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const std::size_t</td>          <td class="paramname"><span class="paramname"><em>numPacketsToSend</em></span>&#160;)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Perform a packet batch send operation.</p>
<p>Performs a single synchronous batch send operation. After it finishes (successfully or otherwise), the callback stored in m_onSentPacketsCallback (if any) is invoked. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">constBufferVecs</td><td>The vector of data buffers to send. </td></tr>
    <tr><td class="paramname">underlyingDataToDeleteOnSentCallbackVec</td><td>The vector of underlying data buffers shared pointer. </td></tr>
    <tr><td class="paramname">underlyingCsDataToDeleteOnSentCallbackVec</td><td>The vector of underlying client service data to send shared pointers. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section post"><dt>Postcondition</dt><dd>The arguments to constBufferVecs, underlyingDataToDeleteOnSentCallbackVec and underlyingCsDataToDeleteOnSentCallbackVec are left in a moved-from state. </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>common/util/src/<a class="el" href="_udp_batch_sender_8cpp.html">UdpBatchSender.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="class_udp_batch_sender.html">UdpBatchSender</a></li><li class="navelem"><a class="el" href="class_udp_batch_sender_1_1_impl.html">Impl</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
