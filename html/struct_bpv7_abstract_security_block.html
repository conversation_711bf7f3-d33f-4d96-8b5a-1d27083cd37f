<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: Bpv7AbstractSecurityBlock Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('struct_bpv7_abstract_security_block.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="struct_bpv7_abstract_security_block-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Bpv7AbstractSecurityBlock Struct Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for Bpv7AbstractSecurityBlock:</div>
<div class="dyncontent">
 <div class="center">
  <img src="struct_bpv7_abstract_security_block.png" usemap="#Bpv7AbstractSecurityBlock_map" alt=""/>
  <map id="Bpv7AbstractSecurityBlock_map" name="Bpv7AbstractSecurityBlock_map">
<area href="struct_bpv7_canonical_block.html" alt="Bpv7CanonicalBlock" shape="rect" coords="96,0,279,24"/>
<area href="struct_bpv7_block_confidentiality_block.html" alt="Bpv7BlockConfidentialityBlock" shape="rect" coords="0,112,183,136"/>
<area href="struct_bpv7_block_integrity_block.html" alt="Bpv7BlockIntegrityBlock" shape="rect" coords="193,112,376,136"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-types" name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:ae80ec4bbb8bfdb1511ae6dd55f120f6c" id="r_ae80ec4bbb8bfdb1511ae6dd55f120f6c"><td class="memItemLeft" align="right" valign="top"><a id="ae80ec4bbb8bfdb1511ae6dd55f120f6c" name="ae80ec4bbb8bfdb1511ae6dd55f120f6c"></a>
typedef std::vector&lt; uint64_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>security_targets_t</b></td></tr>
<tr class="separator:ae80ec4bbb8bfdb1511ae6dd55f120f6c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44882f87c7414e6c558157d1c509a0f7" id="r_a44882f87c7414e6c558157d1c509a0f7"><td class="memItemLeft" align="right" valign="top"><a id="a44882f87c7414e6c558157d1c509a0f7" name="a44882f87c7414e6c558157d1c509a0f7"></a>
typedef uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>security_context_id_t</b></td></tr>
<tr class="separator:a44882f87c7414e6c558157d1c509a0f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad69c8c6379d477de5bbdb57e1add049" id="r_aad69c8c6379d477de5bbdb57e1add049"><td class="memItemLeft" align="right" valign="top"><a id="aad69c8c6379d477de5bbdb57e1add049" name="aad69c8c6379d477de5bbdb57e1add049"></a>
typedef uint8_t&#160;</td><td class="memItemRight" valign="bottom"><b>security_context_flags_t</b></td></tr>
<tr class="separator:aad69c8c6379d477de5bbdb57e1add049"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91998e1ef9ba928d4084b4935138c8d0" id="r_a91998e1ef9ba928d4084b4935138c8d0"><td class="memItemLeft" align="right" valign="top"><a id="a91998e1ef9ba928d4084b4935138c8d0" name="a91998e1ef9ba928d4084b4935138c8d0"></a>
typedef uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>id_t</b></td></tr>
<tr class="separator:a91998e1ef9ba928d4084b4935138c8d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3fbfdb6035e4620940575c484f814c6" id="r_ab3fbfdb6035e4620940575c484f814c6"><td class="memItemLeft" align="right" valign="top"><a id="ab3fbfdb6035e4620940575c484f814c6" name="ab3fbfdb6035e4620940575c484f814c6"></a>
typedef std::unique_ptr&lt; <a class="el" href="struct_bpv7_abstract_security_block_value_base.html">Bpv7AbstractSecurityBlockValueBase</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>value_ptr_t</b></td></tr>
<tr class="separator:ab3fbfdb6035e4620940575c484f814c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c02a3e2c7e54ff7d65fffa78d470ae2" id="r_a8c02a3e2c7e54ff7d65fffa78d470ae2"><td class="memItemLeft" align="right" valign="top"><a id="a8c02a3e2c7e54ff7d65fffa78d470ae2" name="a8c02a3e2c7e54ff7d65fffa78d470ae2"></a>
typedef std::pair&lt; id_t, value_ptr_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>id_value_pair_t</b></td></tr>
<tr class="separator:a8c02a3e2c7e54ff7d65fffa78d470ae2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ee925cba239b09295fab5c0d4cd5268" id="r_a1ee925cba239b09295fab5c0d4cd5268"><td class="memItemLeft" align="right" valign="top"><a id="a1ee925cba239b09295fab5c0d4cd5268" name="a1ee925cba239b09295fab5c0d4cd5268"></a>
typedef std::vector&lt; id_value_pair_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>id_value_pairs_vec_t</b></td></tr>
<tr class="separator:a1ee925cba239b09295fab5c0d4cd5268"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a132d286ac1dc23a86e3c6544ca2e8407" id="r_a132d286ac1dc23a86e3c6544ca2e8407"><td class="memItemLeft" align="right" valign="top"><a id="a132d286ac1dc23a86e3c6544ca2e8407" name="a132d286ac1dc23a86e3c6544ca2e8407"></a>
typedef id_t&#160;</td><td class="memItemRight" valign="bottom"><b>parameter_id_t</b></td></tr>
<tr class="separator:a132d286ac1dc23a86e3c6544ca2e8407"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6990d7867c5fa24284f288d7fe35149c" id="r_a6990d7867c5fa24284f288d7fe35149c"><td class="memItemLeft" align="right" valign="top"><a id="a6990d7867c5fa24284f288d7fe35149c" name="a6990d7867c5fa24284f288d7fe35149c"></a>
typedef value_ptr_t&#160;</td><td class="memItemRight" valign="bottom"><b>parameter_value_t</b></td></tr>
<tr class="separator:a6990d7867c5fa24284f288d7fe35149c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a896be72b1f37da5210f31e47cce69345" id="r_a896be72b1f37da5210f31e47cce69345"><td class="memItemLeft" align="right" valign="top"><a id="a896be72b1f37da5210f31e47cce69345" name="a896be72b1f37da5210f31e47cce69345"></a>
typedef id_value_pair_t&#160;</td><td class="memItemRight" valign="bottom"><b>security_context_parameter_t</b></td></tr>
<tr class="separator:a896be72b1f37da5210f31e47cce69345"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58868aa694b30c45c7010aff9626e49c" id="r_a58868aa694b30c45c7010aff9626e49c"><td class="memItemLeft" align="right" valign="top"><a id="a58868aa694b30c45c7010aff9626e49c" name="a58868aa694b30c45c7010aff9626e49c"></a>
typedef id_value_pairs_vec_t&#160;</td><td class="memItemRight" valign="bottom"><b>security_context_parameters_t</b></td></tr>
<tr class="separator:a58868aa694b30c45c7010aff9626e49c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6ec93e05a9e2cdeff4d910df8e85324" id="r_ac6ec93e05a9e2cdeff4d910df8e85324"><td class="memItemLeft" align="right" valign="top"><a id="ac6ec93e05a9e2cdeff4d910df8e85324" name="ac6ec93e05a9e2cdeff4d910df8e85324"></a>
typedef id_t&#160;</td><td class="memItemRight" valign="bottom"><b>security_result_id_t</b></td></tr>
<tr class="separator:ac6ec93e05a9e2cdeff4d910df8e85324"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32c70768ab2235df266915e49053cc5e" id="r_a32c70768ab2235df266915e49053cc5e"><td class="memItemLeft" align="right" valign="top"><a id="a32c70768ab2235df266915e49053cc5e" name="a32c70768ab2235df266915e49053cc5e"></a>
typedef value_ptr_t&#160;</td><td class="memItemRight" valign="bottom"><b>security_result_value_t</b></td></tr>
<tr class="separator:a32c70768ab2235df266915e49053cc5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a245bc00335b5bf38a626ea6dca7d13f4" id="r_a245bc00335b5bf38a626ea6dca7d13f4"><td class="memItemLeft" align="right" valign="top"><a id="a245bc00335b5bf38a626ea6dca7d13f4" name="a245bc00335b5bf38a626ea6dca7d13f4"></a>
typedef id_value_pair_t&#160;</td><td class="memItemRight" valign="bottom"><b>security_result_t</b></td></tr>
<tr class="separator:a245bc00335b5bf38a626ea6dca7d13f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3298a5a1501c812af075a99068e14b38" id="r_a3298a5a1501c812af075a99068e14b38"><td class="memItemLeft" align="right" valign="top"><a id="a3298a5a1501c812af075a99068e14b38" name="a3298a5a1501c812af075a99068e14b38"></a>
typedef id_value_pairs_vec_t&#160;</td><td class="memItemRight" valign="bottom"><b>security_results_t</b></td></tr>
<tr class="separator:a3298a5a1501c812af075a99068e14b38"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:acc50c2def80995a5ac8d5ba1b5c33d81" id="r_acc50c2def80995a5ac8d5ba1b5c33d81"><td class="memItemLeft" align="right" valign="top"><a id="acc50c2def80995a5ac8d5ba1b5c33d81" name="acc50c2def80995a5ac8d5ba1b5c33d81"></a>
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv7AbstractSecurityBlock</b> (const <a class="el" href="struct_bpv7_abstract_security_block.html">Bpv7AbstractSecurityBlock</a> &amp;o)=delete</td></tr>
<tr class="separator:acc50c2def80995a5ac8d5ba1b5c33d81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6adf13c6739e7ef1bd0bdd5f6217d82b" id="r_a6adf13c6739e7ef1bd0bdd5f6217d82b"><td class="memItemLeft" align="right" valign="top"><a id="a6adf13c6739e7ef1bd0bdd5f6217d82b" name="a6adf13c6739e7ef1bd0bdd5f6217d82b"></a>
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv7AbstractSecurityBlock</b> (<a class="el" href="struct_bpv7_abstract_security_block.html">Bpv7AbstractSecurityBlock</a> &amp;&amp;o)</td></tr>
<tr class="separator:a6adf13c6739e7ef1bd0bdd5f6217d82b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac566819bce7e48ac35d5f131d37d161e" id="r_ac566819bce7e48ac35d5f131d37d161e"><td class="memItemLeft" align="right" valign="top"><a id="ac566819bce7e48ac35d5f131d37d161e" name="ac566819bce7e48ac35d5f131d37d161e"></a>
BPCODEC_EXPORT <a class="el" href="struct_bpv7_abstract_security_block.html">Bpv7AbstractSecurityBlock</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (const <a class="el" href="struct_bpv7_abstract_security_block.html">Bpv7AbstractSecurityBlock</a> &amp;o)=delete</td></tr>
<tr class="separator:ac566819bce7e48ac35d5f131d37d161e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a64121eebe3b993266a6a18b45c2a95f1" id="r_a64121eebe3b993266a6a18b45c2a95f1"><td class="memItemLeft" align="right" valign="top"><a id="a64121eebe3b993266a6a18b45c2a95f1" name="a64121eebe3b993266a6a18b45c2a95f1"></a>
BPCODEC_EXPORT <a class="el" href="struct_bpv7_abstract_security_block.html">Bpv7AbstractSecurityBlock</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (<a class="el" href="struct_bpv7_abstract_security_block.html">Bpv7AbstractSecurityBlock</a> &amp;&amp;o)</td></tr>
<tr class="separator:a64121eebe3b993266a6a18b45c2a95f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3971f15c6181b33115effff342e6204f" id="r_a3971f15c6181b33115effff342e6204f"><td class="memItemLeft" align="right" valign="top"><a id="a3971f15c6181b33115effff342e6204f" name="a3971f15c6181b33115effff342e6204f"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator==</b> (const <a class="el" href="struct_bpv7_abstract_security_block.html">Bpv7AbstractSecurityBlock</a> &amp;o) const</td></tr>
<tr class="separator:a3971f15c6181b33115effff342e6204f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a7f4fce6424a6d62708a5ce4ae47f08" id="r_a3a7f4fce6424a6d62708a5ce4ae47f08"><td class="memItemLeft" align="right" valign="top"><a id="a3a7f4fce6424a6d62708a5ce4ae47f08" name="a3a7f4fce6424a6d62708a5ce4ae47f08"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator!=</b> (const <a class="el" href="struct_bpv7_abstract_security_block.html">Bpv7AbstractSecurityBlock</a> &amp;o) const</td></tr>
<tr class="separator:a3a7f4fce6424a6d62708a5ce4ae47f08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a562e4501d0745a2ce8bfa746861bdb37" id="r_a562e4501d0745a2ce8bfa746861bdb37"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a562e4501d0745a2ce8bfa746861bdb37">SetZero</a> () override</td></tr>
<tr class="separator:a562e4501d0745a2ce8bfa746861bdb37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a042dfba170f0512cb3dc4f6bd62df048" id="r_a042dfba170f0512cb3dc4f6bd62df048"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a042dfba170f0512cb3dc4f6bd62df048">SerializeBpv7</a> (uint8_t *serialization) override</td></tr>
<tr class="separator:a042dfba170f0512cb3dc4f6bd62df048"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6684d32b66925643314a3a9082f98a3" id="r_ad6684d32b66925643314a3a9082f98a3"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ad6684d32b66925643314a3a9082f98a3">GetCanonicalBlockTypeSpecificDataSerializationSize</a> () const override</td></tr>
<tr class="separator:ad6684d32b66925643314a3a9082f98a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adca07ea05a0640cbd42868f8a29e1063" id="r_adca07ea05a0640cbd42868f8a29e1063"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adca07ea05a0640cbd42868f8a29e1063">Virtual_DeserializeExtensionBlockDataBpv7</a> () override</td></tr>
<tr class="separator:adca07ea05a0640cbd42868f8a29e1063"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a416163529118e132d3aa428afa073f54" id="r_a416163529118e132d3aa428afa073f54"><td class="memItemLeft" align="right" valign="top"><a id="a416163529118e132d3aa428afa073f54" name="a416163529118e132d3aa428afa073f54"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>IsSecurityContextParametersPresent</b> () const</td></tr>
<tr class="separator:a416163529118e132d3aa428afa073f54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b85d4a6d7409ed04ea0a8e57d54e350" id="r_a6b85d4a6d7409ed04ea0a8e57d54e350"><td class="memItemLeft" align="right" valign="top"><a id="a6b85d4a6d7409ed04ea0a8e57d54e350" name="a6b85d4a6d7409ed04ea0a8e57d54e350"></a>
BPCODEC_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><b>SetSecurityContextParametersPresent</b> ()</td></tr>
<tr class="separator:a6b85d4a6d7409ed04ea0a8e57d54e350"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e5d7c2bdcf4d8634ee399e3099ad2e4" id="r_a7e5d7c2bdcf4d8634ee399e3099ad2e4"><td class="memItemLeft" align="right" valign="top"><a id="a7e5d7c2bdcf4d8634ee399e3099ad2e4" name="a7e5d7c2bdcf4d8634ee399e3099ad2e4"></a>
BPCODEC_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><b>ClearSecurityContextParametersPresent</b> ()</td></tr>
<tr class="separator:a7e5d7c2bdcf4d8634ee399e3099ad2e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40256ebd120d66421b2b93095e27332c" id="r_a40256ebd120d66421b2b93095e27332c"><td class="memItemLeft" align="right" valign="top"><a id="a40256ebd120d66421b2b93095e27332c" name="a40256ebd120d66421b2b93095e27332c"></a>
BPCODEC_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><b>SetSecurityContextId</b> (BPSEC_SECURITY_CONTEXT_IDENTIFIERS id)</td></tr>
<tr class="separator:a40256ebd120d66421b2b93095e27332c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_struct_bpv7_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_struct_bpv7_canonical_block')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a></td></tr>
<tr class="memitem:aecf94f77f6088be4097adb9096b05fbb inherit pub_methods_struct_bpv7_canonical_block" id="r_aecf94f77f6088be4097adb9096b05fbb"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv7CanonicalBlock</b> (const <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;o)</td></tr>
<tr class="separator:aecf94f77f6088be4097adb9096b05fbb inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6107c0a7f2a40d9321c1ea63d7a4912 inherit pub_methods_struct_bpv7_canonical_block" id="r_ab6107c0a7f2a40d9321c1ea63d7a4912"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>Bpv7CanonicalBlock</b> (<a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;&amp;o)</td></tr>
<tr class="separator:ab6107c0a7f2a40d9321c1ea63d7a4912 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6934022232ecb9345a8dc96a5b279c35 inherit pub_methods_struct_bpv7_canonical_block" id="r_a6934022232ecb9345a8dc96a5b279c35"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (const <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;o)</td></tr>
<tr class="separator:a6934022232ecb9345a8dc96a5b279c35 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed77c98d427fa491ef308ee29a31a755 inherit pub_methods_struct_bpv7_canonical_block" id="r_aed77c98d427fa491ef308ee29a31a755"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (<a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;&amp;o)</td></tr>
<tr class="separator:aed77c98d427fa491ef308ee29a31a755 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a618acb0922f387f51b12abd8fdbf50b6 inherit pub_methods_struct_bpv7_canonical_block" id="r_a618acb0922f387f51b12abd8fdbf50b6"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator==</b> (const <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;o) const</td></tr>
<tr class="separator:a618acb0922f387f51b12abd8fdbf50b6 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4e212819d6c369f15191a0ac09600d3 inherit pub_methods_struct_bpv7_canonical_block" id="r_ae4e212819d6c369f15191a0ac09600d3"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator!=</b> (const <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &amp;o) const</td></tr>
<tr class="separator:ae4e212819d6c369f15191a0ac09600d3 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2f3079662c86335b5bd331358ed9206 inherit pub_methods_struct_bpv7_canonical_block" id="r_ac2f3079662c86335b5bd331358ed9206"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>GetSerializationSize</b> (const bool isEncrypted) const</td></tr>
<tr class="separator:ac2f3079662c86335b5bd331358ed9206 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab683bc568dcbda62a0276dc05cce8814 inherit pub_methods_struct_bpv7_canonical_block" id="r_ab683bc568dcbda62a0276dc05cce8814"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>GetSerializationSizeOfAadPart</b> () const</td></tr>
<tr class="separator:ab683bc568dcbda62a0276dc05cce8814 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e342453d205ba05a5fa41bde0420d32 inherit pub_methods_struct_bpv7_canonical_block" id="r_a1e342453d205ba05a5fa41bde0420d32"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>SerializeAadPart</b> (uint8_t *serialization) const</td></tr>
<tr class="separator:a1e342453d205ba05a5fa41bde0420d32 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad190ba097fa3361b48506042a01f04f7 inherit pub_methods_struct_bpv7_canonical_block" id="r_ad190ba097fa3361b48506042a01f04f7"><td class="memItemLeft" align="right" valign="top">
BPCODEC_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><b>RecomputeCrcAfterDataModification</b> (uint8_t *serializationBase, const uint64_t sizeSerialized)</td></tr>
<tr class="separator:ad190ba097fa3361b48506042a01f04f7 inherit pub_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:afe5046024989626f9528b851e1953afd" id="r_afe5046024989626f9528b851e1953afd"><td class="memItemLeft" align="right" valign="top"><a id="afe5046024989626f9528b851e1953afd" name="afe5046024989626f9528b851e1953afd"></a>
static BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>SerializeIdValuePairsVecBpv7</b> (uint8_t *serialization, const id_value_pairs_vec_t &amp;idValuePairsVec, uint64_t bufferSize, bool encapsulatePairInArrayOfSizeOne)</td></tr>
<tr class="separator:afe5046024989626f9528b851e1953afd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa28c7c116ac56e506b24a11cf2957b3f" id="r_aa28c7c116ac56e506b24a11cf2957b3f"><td class="memItemLeft" align="right" valign="top"><a id="aa28c7c116ac56e506b24a11cf2957b3f" name="aa28c7c116ac56e506b24a11cf2957b3f"></a>
static BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>IdValuePairsVecBpv7SerializationSize</b> (const id_value_pairs_vec_t &amp;idValuePairsVec, bool encapsulatePairInArrayOfSizeOne)</td></tr>
<tr class="separator:aa28c7c116ac56e506b24a11cf2957b3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59c848f625fb7c2f0e4ae95c4a2bac1f" id="r_a59c848f625fb7c2f0e4ae95c4a2bac1f"><td class="memItemLeft" align="right" valign="top"><a id="a59c848f625fb7c2f0e4ae95c4a2bac1f" name="a59c848f625fb7c2f0e4ae95c4a2bac1f"></a>
static BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>DeserializeIdValuePairsVecBpv7</b> (uint8_t *serialization, uint64_t &amp;numBytesTakenToDecode, uint64_t bufferSize, id_value_pairs_vec_t &amp;idValuePairsVec, const BPSEC_SECURITY_CONTEXT_IDENTIFIERS securityContext, const bool isForSecurityParameters, const uint64_t maxElements, bool pairIsEncapsulateInArrayOfSizeOne)</td></tr>
<tr class="separator:a59c848f625fb7c2f0e4ae95c4a2bac1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afb1f3320c517d3c34a7c5020aa288b8f" id="r_afb1f3320c517d3c34a7c5020aa288b8f"><td class="memItemLeft" align="right" valign="top"><a id="afb1f3320c517d3c34a7c5020aa288b8f" name="afb1f3320c517d3c34a7c5020aa288b8f"></a>
static BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>DeserializeIdValuePairBpv7</b> (uint8_t *serialization, uint64_t &amp;numBytesTakenToDecode, uint64_t bufferSize, id_value_pair_t &amp;idValuePair, const BPSEC_SECURITY_CONTEXT_IDENTIFIERS securityContext, const bool isForSecurityParameters)</td></tr>
<tr class="separator:afb1f3320c517d3c34a7c5020aa288b8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac248fc3d40f6f57372b1c2d44056395a" id="r_ac248fc3d40f6f57372b1c2d44056395a"><td class="memItemLeft" align="right" valign="top"><a id="ac248fc3d40f6f57372b1c2d44056395a" name="ac248fc3d40f6f57372b1c2d44056395a"></a>
static BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>IsEqual</b> (const id_value_pairs_vec_t &amp;pVec1, const id_value_pairs_vec_t &amp;pVec2)</td></tr>
<tr class="separator:ac248fc3d40f6f57372b1c2d44056395a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_static_methods_struct_bpv7_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_static_methods_struct_bpv7_canonical_block')"><img src="closed.png" alt="-"/>&#160;Static Public Member Functions inherited from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a></td></tr>
<tr class="memitem:a2b0a572fb082540b24eb3a02d3162822 inherit pub_static_methods_struct_bpv7_canonical_block" id="r_a2b0a572fb082540b24eb3a02d3162822"><td class="memItemLeft" align="right" valign="top">
static BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>DeserializeBpv7</b> (std::unique_ptr&lt; <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &gt; &amp;canonicalPtr, uint8_t *serialization, uint64_t &amp;numBytesTakenToDecode, uint64_t bufferSize, const bool skipCrcVerify, const bool isAdminRecord, std::unique_ptr&lt; <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &gt; *blockNumberToRecycledCanonicalBlockArray, std::unique_ptr&lt; <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a> &gt; *recycledAdminRecord)</td></tr>
<tr class="separator:a2b0a572fb082540b24eb3a02d3162822 inherit pub_static_methods_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a5505a79365f669b82abb85cfe2c325d7" id="r_a5505a79365f669b82abb85cfe2c325d7"><td class="memItemLeft" align="right" valign="top"><a id="a5505a79365f669b82abb85cfe2c325d7" name="a5505a79365f669b82abb85cfe2c325d7"></a>
security_targets_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_securityTargets</b></td></tr>
<tr class="separator:a5505a79365f669b82abb85cfe2c325d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6dee428942e2ec599c8cb097219e5310" id="r_a6dee428942e2ec599c8cb097219e5310"><td class="memItemLeft" align="right" valign="top"><a id="a6dee428942e2ec599c8cb097219e5310" name="a6dee428942e2ec599c8cb097219e5310"></a>
security_context_id_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_securityContextId</b></td></tr>
<tr class="separator:a6dee428942e2ec599c8cb097219e5310"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81dddd265e2dd790bb1bd1524e232cc4" id="r_a81dddd265e2dd790bb1bd1524e232cc4"><td class="memItemLeft" align="right" valign="top"><a id="a81dddd265e2dd790bb1bd1524e232cc4" name="a81dddd265e2dd790bb1bd1524e232cc4"></a>
security_context_flags_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_securityContextFlags</b></td></tr>
<tr class="separator:a81dddd265e2dd790bb1bd1524e232cc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acc80b8f7a709aed734f902dab867c394" id="r_acc80b8f7a709aed734f902dab867c394"><td class="memItemLeft" align="right" valign="top"><a id="acc80b8f7a709aed734f902dab867c394" name="acc80b8f7a709aed734f902dab867c394"></a>
<a class="el" href="structcbhe__eid__t.html">cbhe_eid_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>m_securitySource</b></td></tr>
<tr class="separator:acc80b8f7a709aed734f902dab867c394"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0b7c822823da03e73ded1bd03abbe31" id="r_ad0b7c822823da03e73ded1bd03abbe31"><td class="memItemLeft" align="right" valign="top"><a id="ad0b7c822823da03e73ded1bd03abbe31" name="ad0b7c822823da03e73ded1bd03abbe31"></a>
security_context_parameters_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_securityContextParametersOptional</b></td></tr>
<tr class="separator:ad0b7c822823da03e73ded1bd03abbe31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d122a83afbcc846eff5efa0c741641c" id="r_a0d122a83afbcc846eff5efa0c741641c"><td class="memItemLeft" align="right" valign="top"><a id="a0d122a83afbcc846eff5efa0c741641c" name="a0d122a83afbcc846eff5efa0c741641c"></a>
security_results_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_securityResults</b></td></tr>
<tr class="separator:a0d122a83afbcc846eff5efa0c741641c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_attribs_struct_bpv7_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_attribs_struct_bpv7_canonical_block')"><img src="closed.png" alt="-"/>&#160;Public Attributes inherited from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a></td></tr>
<tr class="memitem:a1841b984116094640351af6cf79946ba inherit pub_attribs_struct_bpv7_canonical_block" id="r_a1841b984116094640351af6cf79946ba"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_blockNumber</b></td></tr>
<tr class="separator:a1841b984116094640351af6cf79946ba inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a770a12675f629dd69c7c1d14464ba708 inherit pub_attribs_struct_bpv7_canonical_block" id="r_a770a12675f629dd69c7c1d14464ba708"><td class="memItemLeft" align="right" valign="top">
BPV7_BLOCKFLAG&#160;</td><td class="memItemRight" valign="bottom"><b>m_blockProcessingControlFlags</b></td></tr>
<tr class="separator:a770a12675f629dd69c7c1d14464ba708 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae98d0fd4ae507d87a09f28c5bed10a71 inherit pub_attribs_struct_bpv7_canonical_block" id="r_ae98d0fd4ae507d87a09f28c5bed10a71"><td class="memItemLeft" align="right" valign="top">
uint8_t *&#160;</td><td class="memItemRight" valign="bottom"><b>m_dataPtr</b></td></tr>
<tr class="separator:ae98d0fd4ae507d87a09f28c5bed10a71 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83f08d9f511accaa9f4245085a3790a7 inherit pub_attribs_struct_bpv7_canonical_block" id="r_a83f08d9f511accaa9f4245085a3790a7"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_dataLength</b></td></tr>
<tr class="separator:a83f08d9f511accaa9f4245085a3790a7 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c08ff0ce312237b02ae98af81ed8d54 inherit pub_attribs_struct_bpv7_canonical_block" id="r_a5c08ff0ce312237b02ae98af81ed8d54"><td class="memItemLeft" align="right" valign="top">
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_computedCrc32</b></td></tr>
<tr class="separator:a5c08ff0ce312237b02ae98af81ed8d54 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab971291a81c13519c05f7e71978ffe63 inherit pub_attribs_struct_bpv7_canonical_block" id="r_ab971291a81c13519c05f7e71978ffe63"><td class="memItemLeft" align="right" valign="top">
uint16_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_computedCrc16</b></td></tr>
<tr class="separator:ab971291a81c13519c05f7e71978ffe63 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae440c38f8ace47da7b0b059c1b074fc4 inherit pub_attribs_struct_bpv7_canonical_block" id="r_ae440c38f8ace47da7b0b059c1b074fc4"><td class="memItemLeft" align="right" valign="top">
BPV7_BLOCK_TYPE_CODE&#160;</td><td class="memItemRight" valign="bottom"><b>m_blockTypeCode</b></td></tr>
<tr class="separator:ae440c38f8ace47da7b0b059c1b074fc4 inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88523f11d5dae13761efb28a4a430d9f inherit pub_attribs_struct_bpv7_canonical_block" id="r_a88523f11d5dae13761efb28a4a430d9f"><td class="memItemLeft" align="right" valign="top">
BPV7_CRC_TYPE&#160;</td><td class="memItemRight" valign="bottom"><b>m_crcType</b></td></tr>
<tr class="separator:a88523f11d5dae13761efb28a4a430d9f inherit pub_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pro-methods" name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:af1073f4322cf4d8d87067e32eea86291" id="r_af1073f4322cf4d8d87067e32eea86291"><td class="memItemLeft" align="right" valign="top"><a id="af1073f4322cf4d8d87067e32eea86291" name="af1073f4322cf4d8d87067e32eea86291"></a>
BPCODEC_EXPORT std::vector&lt; uint8_t &gt; *&#160;</td><td class="memItemRight" valign="bottom"><b>Protected_AppendAndGetSecurityResultByteStringPtr</b> (uint64_t resultType)</td></tr>
<tr class="separator:af1073f4322cf4d8d87067e32eea86291"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5fb044f83fa49a4f7cfbfb68ba1a6af" id="r_ad5fb044f83fa49a4f7cfbfb68ba1a6af"><td class="memItemLeft" align="right" valign="top"><a id="ad5fb044f83fa49a4f7cfbfb68ba1a6af" name="ad5fb044f83fa49a4f7cfbfb68ba1a6af"></a>
BPCODEC_EXPORT std::vector&lt; std::vector&lt; uint8_t &gt; * &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>Protected_GetAllSecurityResultsByteStringPtrs</b> (uint64_t resultType)</td></tr>
<tr class="separator:ad5fb044f83fa49a4f7cfbfb68ba1a6af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab91434e740fc7e4ada357efbd71e7e38" id="r_ab91434e740fc7e4ada357efbd71e7e38"><td class="memItemLeft" align="right" valign="top"><a id="ab91434e740fc7e4ada357efbd71e7e38" name="ab91434e740fc7e4ada357efbd71e7e38"></a>
BPCODEC_EXPORT std::vector&lt; uint8_t &gt; *&#160;</td><td class="memItemRight" valign="bottom"><b>Protected_AddAndGetByteStringParamPtr</b> (parameter_id_t parameter)</td></tr>
<tr class="separator:ab91434e740fc7e4ada357efbd71e7e38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a968350c510c4a804174d38e65cbd6ea1" id="r_a968350c510c4a804174d38e65cbd6ea1"><td class="memItemLeft" align="right" valign="top"><a id="a968350c510c4a804174d38e65cbd6ea1" name="a968350c510c4a804174d38e65cbd6ea1"></a>
BPCODEC_EXPORT std::vector&lt; uint8_t &gt; *&#160;</td><td class="memItemRight" valign="bottom"><b>Protected_GetByteStringParamPtr</b> (parameter_id_t parameter)</td></tr>
<tr class="separator:a968350c510c4a804174d38e65cbd6ea1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa59bb9440179e4856e028a8d4aa10247" id="r_aa59bb9440179e4856e028a8d4aa10247"><td class="memItemLeft" align="right" valign="top"><a id="aa59bb9440179e4856e028a8d4aa10247" name="aa59bb9440179e4856e028a8d4aa10247"></a>
BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>Protected_AddOrUpdateUintValueSecurityParameter</b> (parameter_id_t parameter, uint64_t uintValue)</td></tr>
<tr class="separator:aa59bb9440179e4856e028a8d4aa10247"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9101008f71a70526283055bb7f533caa" id="r_a9101008f71a70526283055bb7f533caa"><td class="memItemLeft" align="right" valign="top"><a id="a9101008f71a70526283055bb7f533caa" name="a9101008f71a70526283055bb7f533caa"></a>
BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>Protected_GetUintSecurityParameter</b> (parameter_id_t parameter, bool &amp;success) const</td></tr>
<tr class="separator:a9101008f71a70526283055bb7f533caa"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_static_attribs_struct_bpv7_canonical_block"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_static_attribs_struct_bpv7_canonical_block')"><img src="closed.png" alt="-"/>&#160;Static Public Attributes inherited from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a></td></tr>
<tr class="memitem:a6224499818798fc8f4976a1aa8d64171 inherit pub_static_attribs_struct_bpv7_canonical_block" id="r_a6224499818798fc8f4976a1aa8d64171"><td class="memItemLeft" align="right" valign="top">static constexpr uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_bpv7_canonical_block.html#a6224499818798fc8f4976a1aa8d64171">smallestSerializedCanonicalSize</a></td></tr>
<tr class="separator:a6224499818798fc8f4976a1aa8d64171 inherit pub_static_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15f2d6d077c3e0fbb3de7fb49657b1b8 inherit pub_static_attribs_struct_bpv7_canonical_block" id="r_a15f2d6d077c3e0fbb3de7fb49657b1b8"><td class="memItemLeft" align="right" valign="top">static constexpr uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="struct_bpv7_canonical_block.html#a15f2d6d077c3e0fbb3de7fb49657b1b8">largestZeroDataSerializedCanonicalSize</a></td></tr>
<tr class="separator:a15f2d6d077c3e0fbb3de7fb49657b1b8 inherit pub_static_attribs_struct_bpv7_canonical_block"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ad6684d32b66925643314a3a9082f98a3" name="ad6684d32b66925643314a3a9082f98a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad6684d32b66925643314a3a9082f98a3">&#9670;&#160;</a></span>GetCanonicalBlockTypeSpecificDataSerializationSize()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t Bpv7AbstractSecurityBlock::GetCanonicalBlockTypeSpecificDataSerializationSize </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a>.</p>

</div>
</div>
<a id="a042dfba170f0512cb3dc4f6bd62df048" name="a042dfba170f0512cb3dc4f6bd62df048"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a042dfba170f0512cb3dc4f6bd62df048">&#9670;&#160;</a></span>SerializeBpv7()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t Bpv7AbstractSecurityBlock::SerializeBpv7 </td>
          <td>(</td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>serialization</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a>.</p>

</div>
</div>
<a id="a562e4501d0745a2ce8bfa746861bdb37" name="a562e4501d0745a2ce8bfa746861bdb37"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a562e4501d0745a2ce8bfa746861bdb37">&#9670;&#160;</a></span>SetZero()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void Bpv7AbstractSecurityBlock::SetZero </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a>.</p>

</div>
</div>
<a id="adca07ea05a0640cbd42868f8a29e1063" name="adca07ea05a0640cbd42868f8a29e1063"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adca07ea05a0640cbd42868f8a29e1063">&#9670;&#160;</a></span>Virtual_DeserializeExtensionBlockDataBpv7()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Bpv7AbstractSecurityBlock::Virtual_DeserializeExtensionBlockDataBpv7 </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_bpv7_canonical_block.html">Bpv7CanonicalBlock</a>.</p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>common/bpcodec/include/codec/<a class="el" href="bpv7_8h_source.html">bpv7.h</a></li>
<li>common/bpcodec/src/codec/<a class="el" href="_bpv7_bp_sec_extension_blocks_8cpp.html">Bpv7BpSecExtensionBlocks.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_bpv7_abstract_security_block.html">Bpv7AbstractSecurityBlock</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
