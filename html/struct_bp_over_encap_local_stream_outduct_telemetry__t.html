<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: BpOverEncapLocalStreamOutductTelemetry_t Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('struct_bp_over_encap_local_stream_outduct_telemetry__t.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="struct_bp_over_encap_local_stream_outduct_telemetry__t-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">BpOverEncapLocalStreamOutductTelemetry_t Struct Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for BpOverEncapLocalStreamOutductTelemetry_t:</div>
<div class="dyncontent">
 <div class="center">
  <img src="struct_bp_over_encap_local_stream_outduct_telemetry__t.png" usemap="#BpOverEncapLocalStreamOutductTelemetry_5Ft_map" alt=""/>
  <map id="BpOverEncapLocalStreamOutductTelemetry_5Ft_map" name="BpOverEncapLocalStreamOutductTelemetry_5Ft_map">
<area href="struct_outduct_telemetry__t.html" alt="OutductTelemetry_t" shape="rect" coords="0,56,271,80"/>
<area href="class_json_serializable.html" alt="JsonSerializable" shape="rect" coords="0,0,271,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ac1d38836248262a79f403823284854b5" id="r_ac1d38836248262a79f403823284854b5"><td class="memItemLeft" align="right" valign="top">virtual TELEMETRY_DEFINITIONS_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ac1d38836248262a79f403823284854b5">operator==</a> (const <a class="el" href="struct_outduct_telemetry__t.html">OutductTelemetry_t</a> &amp;o) const override</td></tr>
<tr class="separator:ac1d38836248262a79f403823284854b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11c363f6975247c644778a548a3964d1" id="r_a11c363f6975247c644778a548a3964d1"><td class="memItemLeft" align="right" valign="top">virtual TELEMETRY_DEFINITIONS_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a11c363f6975247c644778a548a3964d1">operator!=</a> (const <a class="el" href="struct_outduct_telemetry__t.html">OutductTelemetry_t</a> &amp;o) const override</td></tr>
<tr class="separator:a11c363f6975247c644778a548a3964d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619de5504512666924045710ab2ef64e" id="r_a619de5504512666924045710ab2ef64e"><td class="memItemLeft" align="right" valign="top">virtual TELEMETRY_DEFINITIONS_EXPORT boost::property_tree::ptree&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a619de5504512666924045710ab2ef64e">GetNewPropertyTree</a> () const override</td></tr>
<tr class="separator:a619de5504512666924045710ab2ef64e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a05ce8ca02fdb5a9882403d80b91db9f5" id="r_a05ce8ca02fdb5a9882403d80b91db9f5"><td class="memItemLeft" align="right" valign="top">virtual TELEMETRY_DEFINITIONS_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a05ce8ca02fdb5a9882403d80b91db9f5">SetValuesFromPropertyTree</a> (const boost::property_tree::ptree &amp;pt) override</td></tr>
<tr class="separator:a05ce8ca02fdb5a9882403d80b91db9f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_struct_outduct_telemetry__t"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_struct_outduct_telemetry__t')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="struct_outduct_telemetry__t.html">OutductTelemetry_t</a></td></tr>
<tr class="memitem:aaa57598b7c07e4fc237190c0aaeccaa3 inherit pub_methods_struct_outduct_telemetry__t" id="r_aaa57598b7c07e4fc237190c0aaeccaa3"><td class="memItemLeft" align="right" valign="top">
TELEMETRY_DEFINITIONS_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>GetTotalBundlesQueued</b> () const</td></tr>
<tr class="separator:aaa57598b7c07e4fc237190c0aaeccaa3 inherit pub_methods_struct_outduct_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76c9a9998a6745604af6150bfef1e73e inherit pub_methods_struct_outduct_telemetry__t" id="r_a76c9a9998a6745604af6150bfef1e73e"><td class="memItemLeft" align="right" valign="top">
TELEMETRY_DEFINITIONS_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>GetTotalBundleBytesQueued</b> () const</td></tr>
<tr class="separator:a76c9a9998a6745604af6150bfef1e73e inherit pub_methods_struct_outduct_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_class_json_serializable"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_class_json_serializable')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="class_json_serializable.html">JsonSerializable</a></td></tr>
<tr class="memitem:a762c2815fb637e887df4d9e537f17ba3 inherit pub_methods_class_json_serializable" id="r_a762c2815fb637e887df4d9e537f17ba3"><td class="memItemLeft" align="right" valign="top">
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>ToJson</b> (bool pretty=true) const</td></tr>
<tr class="separator:a762c2815fb637e887df4d9e537f17ba3 inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50eb3c541e9a36332d6759bb22831472 inherit pub_methods_class_json_serializable" id="r_a50eb3c541e9a36332d6759bb22831472"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>ToJsonFile</b> (const boost::filesystem::path &amp;filePath, bool pretty=true) const</td></tr>
<tr class="separator:a50eb3c541e9a36332d6759bb22831472 inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52b3931dd681a8c817592e648977beb3 inherit pub_methods_class_json_serializable" id="r_a52b3931dd681a8c817592e648977beb3"><td class="memItemLeft" align="right" valign="top">
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>ToXml</b> () const</td></tr>
<tr class="separator:a52b3931dd681a8c817592e648977beb3 inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac14772bef92f861bedb43f6673d8541a inherit pub_methods_class_json_serializable" id="r_ac14772bef92f861bedb43f6673d8541a"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>ToXmlFile</b> (const std::string &amp;fileName, char indentCharacter=' ', int indentCount=2) const</td></tr>
<tr class="separator:ac14772bef92f861bedb43f6673d8541a inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3148d4262386d8617f82f4f8b0f1931d inherit pub_methods_class_json_serializable" id="r_a3148d4262386d8617f82f4f8b0f1931d"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>SetValuesFromJson</b> (const std::string &amp;jsonString)</td></tr>
<tr class="separator:a3148d4262386d8617f82f4f8b0f1931d inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1adb23c7bad19b506530ddb773cc64be inherit pub_methods_class_json_serializable" id="r_a1adb23c7bad19b506530ddb773cc64be"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>SetValuesFromJsonCharArray</b> (const char *data, const std::size_t size)</td></tr>
<tr class="separator:a1adb23c7bad19b506530ddb773cc64be inherit pub_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:aec680660b786085d39e773c96f4b6500" id="r_aec680660b786085d39e773c96f4b6500"><td class="memItemLeft" align="right" valign="top"><a id="aec680660b786085d39e773c96f4b6500" name="aec680660b786085d39e773c96f4b6500"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalEncapHeaderBytesSent</b></td></tr>
<tr class="separator:aec680660b786085d39e773c96f4b6500"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a61caaf4d7296ce5e1113c22b83a14fe9" id="r_a61caaf4d7296ce5e1113c22b83a14fe9"><td class="memItemLeft" align="right" valign="top"><a id="a61caaf4d7296ce5e1113c22b83a14fe9" name="a61caaf4d7296ce5e1113c22b83a14fe9"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalEncapHeaderBytesReceived</b></td></tr>
<tr class="separator:a61caaf4d7296ce5e1113c22b83a14fe9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1315aa41f4b466a7d979997ca2365ee" id="r_af1315aa41f4b466a7d979997ca2365ee"><td class="memItemLeft" align="right" valign="top"><a id="af1315aa41f4b466a7d979997ca2365ee" name="af1315aa41f4b466a7d979997ca2365ee"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_largestEncapHeaderSizeBytesSent</b></td></tr>
<tr class="separator:af1315aa41f4b466a7d979997ca2365ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac28098ec302d0a0624f051b11f7ab08" id="r_aac28098ec302d0a0624f051b11f7ab08"><td class="memItemLeft" align="right" valign="top"><a id="aac28098ec302d0a0624f051b11f7ab08" name="aac28098ec302d0a0624f051b11f7ab08"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_smallestEncapHeaderSizeBytesSent</b></td></tr>
<tr class="separator:aac28098ec302d0a0624f051b11f7ab08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec7158ba8374e696496349f883b72290" id="r_aec7158ba8374e696496349f883b72290"><td class="memItemLeft" align="right" valign="top"><a id="aec7158ba8374e696496349f883b72290" name="aec7158ba8374e696496349f883b72290"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_averageEncapHeaderSizeBytesSent</b></td></tr>
<tr class="separator:aec7158ba8374e696496349f883b72290"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83c655f4c1ca8473a37e7f427474ca6c" id="r_a83c655f4c1ca8473a37e7f427474ca6c"><td class="memItemLeft" align="right" valign="top"><a id="a83c655f4c1ca8473a37e7f427474ca6c" name="a83c655f4c1ca8473a37e7f427474ca6c"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundlesReceived</b></td></tr>
<tr class="separator:a83c655f4c1ca8473a37e7f427474ca6c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90a8c4aedec416d4d3e4c0e654c5a62f" id="r_a90a8c4aedec416d4d3e4c0e654c5a62f"><td class="memItemLeft" align="right" valign="top"><a id="a90a8c4aedec416d4d3e4c0e654c5a62f" name="a90a8c4aedec416d4d3e4c0e654c5a62f"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundleBytesReceived</b></td></tr>
<tr class="separator:a90a8c4aedec416d4d3e4c0e654c5a62f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_attribs_struct_outduct_telemetry__t"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_attribs_struct_outduct_telemetry__t')"><img src="closed.png" alt="-"/>&#160;Public Attributes inherited from <a class="el" href="struct_outduct_telemetry__t.html">OutductTelemetry_t</a></td></tr>
<tr class="memitem:a990c7f71ec6aa5aeabcf5fcd35b30b44 inherit pub_attribs_struct_outduct_telemetry__t" id="r_a990c7f71ec6aa5aeabcf5fcd35b30b44"><td class="memItemLeft" align="right" valign="top">
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>m_convergenceLayer</b></td></tr>
<tr class="separator:a990c7f71ec6aa5aeabcf5fcd35b30b44 inherit pub_attribs_struct_outduct_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a45241d2505fe58e6532781c6a040b198 inherit pub_attribs_struct_outduct_telemetry__t" id="r_a45241d2505fe58e6532781c6a040b198"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundlesAcked</b></td></tr>
<tr class="separator:a45241d2505fe58e6532781c6a040b198 inherit pub_attribs_struct_outduct_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa1a904b6804b71b413bdea60126e38c1 inherit pub_attribs_struct_outduct_telemetry__t" id="r_aa1a904b6804b71b413bdea60126e38c1"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundleBytesAcked</b></td></tr>
<tr class="separator:aa1a904b6804b71b413bdea60126e38c1 inherit pub_attribs_struct_outduct_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5195f83a37144955b554a692d2207ec5 inherit pub_attribs_struct_outduct_telemetry__t" id="r_a5195f83a37144955b554a692d2207ec5"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundlesSent</b></td></tr>
<tr class="separator:a5195f83a37144955b554a692d2207ec5 inherit pub_attribs_struct_outduct_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9e32d16d9c482eed5b0e828b067104df inherit pub_attribs_struct_outduct_telemetry__t" id="r_a9e32d16d9c482eed5b0e828b067104df"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundleBytesSent</b></td></tr>
<tr class="separator:a9e32d16d9c482eed5b0e828b067104df inherit pub_attribs_struct_outduct_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20f16bdb75ed0860f053feaa37ae63f8 inherit pub_attribs_struct_outduct_telemetry__t" id="r_a20f16bdb75ed0860f053feaa37ae63f8"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundlesFailedToSend</b></td></tr>
<tr class="separator:a20f16bdb75ed0860f053feaa37ae63f8 inherit pub_attribs_struct_outduct_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ac823445009088acf7df4ba9d55e773 inherit pub_attribs_struct_outduct_telemetry__t" id="r_a9ac823445009088acf7df4ba9d55e773"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>m_linkIsUpPhysically</b></td></tr>
<tr class="separator:a9ac823445009088acf7df4ba9d55e773 inherit pub_attribs_struct_outduct_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8edb5df31e5fd52ac7f90f4d8cc5e4b1 inherit pub_attribs_struct_outduct_telemetry__t" id="r_a8edb5df31e5fd52ac7f90f4d8cc5e4b1"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>m_linkIsUpPerTimeSchedule</b></td></tr>
<tr class="separator:a8edb5df31e5fd52ac7f90f4d8cc5e4b1 inherit pub_attribs_struct_outduct_telemetry__t"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_static_methods_class_json_serializable"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_static_methods_class_json_serializable')"><img src="closed.png" alt="-"/>&#160;Static Public Member Functions inherited from <a class="el" href="class_json_serializable.html">JsonSerializable</a></td></tr>
<tr class="memitem:a8c0d2f4adc3eae8076d7b6e763b5e8c9 inherit pub_static_methods_class_json_serializable" id="r_a8c0d2f4adc3eae8076d7b6e763b5e8c9"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>LoadTextFileIntoString</b> (const boost::filesystem::path &amp;filePath, std::string &amp;fileContentsAsString)</td></tr>
<tr class="separator:a8c0d2f4adc3eae8076d7b6e763b5e8c9 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50d0c6d42669b91106c6012f985d4ee1 inherit pub_static_methods_class_json_serializable" id="r_a50d0c6d42669b91106c6012f985d4ee1"><td class="memItemLeft" align="right" valign="top">
static void&#160;</td><td class="memItemRight" valign="bottom"><b>GetAllJsonKeys</b> (const std::string &amp;jsonText, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</td></tr>
<tr class="separator:a50d0c6d42669b91106c6012f985d4ee1 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad92085846035c93eb245e0cd9fed73c4 inherit pub_static_methods_class_json_serializable" id="r_ad92085846035c93eb245e0cd9fed73c4"><td class="memItemLeft" align="right" valign="top">
static void&#160;</td><td class="memItemRight" valign="bottom"><b>GetAllJsonKeysLineByLine</b> (std::istream &amp;stream, std::set&lt; std::string &gt; &amp;jsonKeysNoQuotesSetToAppend)</td></tr>
<tr class="separator:ad92085846035c93eb245e0cd9fed73c4 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94353f2da8521917c76a827829c9cc5a inherit pub_static_methods_class_json_serializable" id="r_a94353f2da8521917c76a827829c9cc5a"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>HasUnusedJsonVariablesInFilePath</b> (const <a class="el" href="class_json_serializable.html">JsonSerializable</a> &amp;config, const boost::filesystem::path &amp;originalUserJsonFilePath, std::string &amp;returnedErrorMessage)</td></tr>
<tr class="separator:a94353f2da8521917c76a827829c9cc5a inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d6a8897e5038c0938f9bae38dfeeccb inherit pub_static_methods_class_json_serializable" id="r_a1d6a8897e5038c0938f9bae38dfeeccb"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>HasUnusedJsonVariablesInString</b> (const <a class="el" href="class_json_serializable.html">JsonSerializable</a> &amp;config, const std::string &amp;originalUserJsonString, std::string &amp;returnedErrorMessage)</td></tr>
<tr class="separator:a1d6a8897e5038c0938f9bae38dfeeccb inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa5fbacf36fa5675eb162108f50ec928e inherit pub_static_methods_class_json_serializable" id="r_aa5fbacf36fa5675eb162108f50ec928e"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>HasUnusedJsonVariablesInStream</b> (const <a class="el" href="class_json_serializable.html">JsonSerializable</a> &amp;config, std::istream &amp;originalUserJsonStream, std::string &amp;returnedErrorMessage)</td></tr>
<tr class="separator:aa5fbacf36fa5675eb162108f50ec928e inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6ea55a68c508e3b2eb3da2aa5f6feb8 inherit pub_static_methods_class_json_serializable" id="r_af6ea55a68c508e3b2eb3da2aa5f6feb8"><td class="memItemLeft" align="right" valign="top">
static std::string&#160;</td><td class="memItemRight" valign="bottom"><b>PtToJsonString</b> (const boost::property_tree::ptree &amp;pt, bool pretty=true)</td></tr>
<tr class="separator:af6ea55a68c508e3b2eb3da2aa5f6feb8 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a571a9ee2657f4cf0a9f3835ebcac2890 inherit pub_static_methods_class_json_serializable" id="r_a571a9ee2657f4cf0a9f3835ebcac2890"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromJsonCharArray</b> (char *data, const std::size_t size, boost::property_tree::ptree &amp;pt)</td></tr>
<tr class="separator:a571a9ee2657f4cf0a9f3835ebcac2890 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e1f2f0c799de5c252ff1ab8f332b956 inherit pub_static_methods_class_json_serializable" id="r_a1e1f2f0c799de5c252ff1ab8f332b956"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromJsonStream</b> (std::istream &amp;jsonStream, boost::property_tree::ptree &amp;pt)</td></tr>
<tr class="separator:a1e1f2f0c799de5c252ff1ab8f332b956 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10d2706e6272b11b4eb69526457fa4d4 inherit pub_static_methods_class_json_serializable" id="r_a10d2706e6272b11b4eb69526457fa4d4"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromJsonString</b> (const std::string &amp;jsonStr, boost::property_tree::ptree &amp;pt)</td></tr>
<tr class="separator:a10d2706e6272b11b4eb69526457fa4d4 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a316f4084f79841c5ef63d2df58d8a909 inherit pub_static_methods_class_json_serializable" id="r_a316f4084f79841c5ef63d2df58d8a909"><td class="memItemLeft" align="right" valign="top">
static bool&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromJsonFilePath</b> (const boost::filesystem::path &amp;jsonFilePath, boost::property_tree::ptree &amp;pt)</td></tr>
<tr class="separator:a316f4084f79841c5ef63d2df58d8a909 inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37748556f4db393580d61a391e276c4d inherit pub_static_methods_class_json_serializable" id="r_a37748556f4db393580d61a391e276c4d"><td class="memItemLeft" align="right" valign="top">
static std::string&#160;</td><td class="memItemRight" valign="bottom"><b>PtToXmlString</b> (const boost::property_tree::ptree &amp;pt)</td></tr>
<tr class="separator:a37748556f4db393580d61a391e276c4d inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3155e9455ae6a5e776795ed5a7d0385b inherit pub_static_methods_class_json_serializable" id="r_a3155e9455ae6a5e776795ed5a7d0385b"><td class="memItemLeft" align="right" valign="top">
static boost::property_tree::ptree&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromXmlString</b> (const std::string &amp;jsonStr)</td></tr>
<tr class="separator:a3155e9455ae6a5e776795ed5a7d0385b inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3c9b14f4a0b0193549abc57695b30dd inherit pub_static_methods_class_json_serializable" id="r_ac3c9b14f4a0b0193549abc57695b30dd"><td class="memItemLeft" align="right" valign="top">
static boost::property_tree::ptree&#160;</td><td class="memItemRight" valign="bottom"><b>GetPropertyTreeFromXmlFile</b> (const std::string &amp;xmlFileName)</td></tr>
<tr class="separator:ac3c9b14f4a0b0193549abc57695b30dd inherit pub_static_methods_class_json_serializable"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a619de5504512666924045710ab2ef64e" name="a619de5504512666924045710ab2ef64e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a619de5504512666924045710ab2ef64e">&#9670;&#160;</a></span>GetNewPropertyTree()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">boost::property_tree::ptree BpOverEncapLocalStreamOutductTelemetry_t::GetNewPropertyTree </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_outduct_telemetry__t.html">OutductTelemetry_t</a>.</p>

</div>
</div>
<a id="a11c363f6975247c644778a548a3964d1" name="a11c363f6975247c644778a548a3964d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11c363f6975247c644778a548a3964d1">&#9670;&#160;</a></span>operator!=()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool BpOverEncapLocalStreamOutductTelemetry_t::operator!= </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="struct_outduct_telemetry__t.html">OutductTelemetry_t</a> &amp;</td>          <td class="paramname"><span class="paramname"><em>o</em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_outduct_telemetry__t.html">OutductTelemetry_t</a>.</p>

</div>
</div>
<a id="ac1d38836248262a79f403823284854b5" name="ac1d38836248262a79f403823284854b5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac1d38836248262a79f403823284854b5">&#9670;&#160;</a></span>operator==()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool BpOverEncapLocalStreamOutductTelemetry_t::operator== </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="struct_outduct_telemetry__t.html">OutductTelemetry_t</a> &amp;</td>          <td class="paramname"><span class="paramname"><em>o</em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_outduct_telemetry__t.html">OutductTelemetry_t</a>.</p>

</div>
</div>
<a id="a05ce8ca02fdb5a9882403d80b91db9f5" name="a05ce8ca02fdb5a9882403d80b91db9f5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a05ce8ca02fdb5a9882403d80b91db9f5">&#9670;&#160;</a></span>SetValuesFromPropertyTree()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool BpOverEncapLocalStreamOutductTelemetry_t::SetValuesFromPropertyTree </td>
          <td>(</td>
          <td class="paramtype">const boost::property_tree::ptree &amp;</td>          <td class="paramname"><span class="paramname"><em>pt</em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reimplemented from <a class="el" href="struct_outduct_telemetry__t.html">OutductTelemetry_t</a>.</p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>common/telemetry_definitions/include/<a class="el" href="_telemetry_definitions_8h_source.html">TelemetryDefinitions.h</a></li>
<li>common/telemetry_definitions/src/<b>TelemetryDefinitions.cpp</b></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_bp_over_encap_local_stream_outduct_telemetry__t.html">BpOverEncapLocalStreamOutductTelemetry_t</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
