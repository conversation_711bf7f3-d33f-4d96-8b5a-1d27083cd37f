<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: common/bpcodec/include/app_patterns/BpSourcePattern.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('_bp_source_pattern_8h_source.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">BpSourcePattern.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_bp_source_pattern_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span> </div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="preprocessor">#ifndef _BP_SOURCE_PATTERN_H</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span><span class="preprocessor">#define _BP_SOURCE_PATTERN_H 1</span></div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="preprocessor">#include &quot;bp_app_patterns_lib_export.h&quot;</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="preprocessor">#ifndef CLASS_VISIBILITY_BP_APP_PATTERNS_LIB</span></div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="preprocessor">#  ifdef _WIN32</span></div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span><span class="preprocessor">#    define CLASS_VISIBILITY_BP_APP_PATTERNS_LIB</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span><span class="preprocessor">#  else</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span><span class="preprocessor">#    define CLASS_VISIBILITY_BP_APP_PATTERNS_LIB BP_APP_PATTERNS_LIB_EXPORT</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span><span class="preprocessor">#  endif</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span><span class="preprocessor">#include &lt;boost/thread.hpp&gt;</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span><span class="preprocessor">#include &lt;boost/asio.hpp&gt;</span></div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span><span class="preprocessor">#include &quot;<a class="code" href="_outduct_manager_8h.html">OutductManager.h</a>&quot;</span></div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span><span class="preprocessor">#include &quot;<a class="code" href="_induct_manager_8h.html">InductManager.h</a>&quot;</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span><span class="preprocessor">#include &quot;<a class="code" href="bpv6_8h.html">codec/bpv6.h</a>&quot;</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span><span class="preprocessor">#include &quot;<a class="code" href="_custody_transfer_manager_8h.html">codec/CustodyTransferManager.h</a>&quot;</span></div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span><span class="preprocessor">#include &lt;queue&gt;</span></div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span><span class="preprocessor">#include &lt;unordered_set&gt;</span></div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span><span class="preprocessor">#include &quot;<a class="code" href="_padded_vector_uint8_8h.html">PaddedVectorUint8.h</a>&quot;</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span><span class="preprocessor">#include &lt;atomic&gt;</span></div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span> </div>
<div class="foldopen" id="foldopen00052" data-start="{" data-end="};">
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno"><a class="line" href="class_bp_source_pattern.html">   52</a></span><span class="keyword">class </span>CLASS_VISIBILITY_BP_APP_PATTERNS_LIB BpSourcePattern {</div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span><span class="keyword">public</span>:</div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span>    BP_APP_PATTERNS_LIB_EXPORT BpSourcePattern();</div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span>    BP_APP_PATTERNS_LIB_EXPORT <span class="keyword">virtual</span> ~BpSourcePattern();</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span>    BP_APP_PATTERNS_LIB_EXPORT <span class="keywordtype">void</span> Stop();</div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span>    BP_APP_PATTERNS_LIB_EXPORT <span class="keywordtype">void</span> Start(OutductsConfig_ptr &amp; outductsConfigPtr, InductsConfig_ptr &amp; inductsConfigPtr,</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span>        <span class="keyword">const</span> boost::filesystem::path&amp; bpSecConfigFilePath,</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span>        <span class="keywordtype">bool</span> custodyTransferUseAcs,</div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span>        <span class="keyword">const</span> <a class="code hl_struct" href="structcbhe__eid__t.html">cbhe_eid_t</a> &amp; myEid, <span class="keywordtype">double</span> bundleRate, <span class="keyword">const</span> <a class="code hl_struct" href="structcbhe__eid__t.html">cbhe_eid_t</a> &amp; finalDestEid, <span class="keyword">const</span> uint64_t myCustodianServiceId,</div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span>        <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> bundleSendTimeoutSeconds, <span class="keyword">const</span> uint64_t bundleLifetimeMilliseconds, <span class="keyword">const</span> uint64_t bundlePriority,</div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span>        <span class="keyword">const</span> <span class="keywordtype">bool</span> requireRxBundleBeforeNextTx = <span class="keyword">false</span>, <span class="keyword">const</span> <span class="keywordtype">bool</span> forceDisableCustody = <span class="keyword">false</span>, <span class="keyword">const</span> <span class="keywordtype">bool</span> useBpVersion7 = <span class="keyword">false</span>,</div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>        <span class="keyword">const</span> uint64_t claRate = 0);</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span> </div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span>    uint64_t m_bundleCount;</div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span>    uint64_t m_numRfc5050CustodyTransfers;</div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span>    uint64_t m_numAcsCustodyTransfers;</div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span>    uint64_t m_numAcsPacketsReceived;</div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span> </div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span>    uint64_t m_totalNonAdminRecordBpv6PayloadBytesRx;</div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span>    uint64_t m_totalNonAdminRecordBpv6BundleBytesRx;</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span>    uint64_t m_totalNonAdminRecordBpv6BundlesRx;</div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span> </div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span>    uint64_t m_totalNonAdminRecordBpv7PayloadBytesRx;</div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span>    uint64_t m_totalNonAdminRecordBpv7BundleBytesRx;</div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span>    uint64_t m_totalNonAdminRecordBpv7BundlesRx;</div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span> </div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span>    <a class="code hl_struct" href="struct_outduct_final_stats.html">OutductFinalStats</a> m_outductFinalStats;</div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span> </div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span><span class="keyword">protected</span>:</div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span>    BP_APP_PATTERNS_LIB_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> TryWaitForDataAvailable(<span class="keyword">const</span> boost::posix_time::time_duration&amp; timeout);</div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span>    <span class="keyword">virtual</span> uint64_t GetNextPayloadLength_Step1() = 0;</div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span>    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> CopyPayload_Step2(uint8_t * destinationBuffer) = 0;</div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span>    BP_APP_PATTERNS_LIB_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> ProcessNonAdminRecordBundlePayload(<span class="keyword">const</span> uint8_t * data, <span class="keyword">const</span> uint64_t size);</div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span>    BP_APP_PATTERNS_LIB_NO_EXPORT <span class="keywordtype">void</span> BpSourcePatternThreadFunc(<span class="keywordtype">double</span> bundleRate, <span class="keyword">const</span> boost::filesystem::path&amp; bpSecConfigFilePath);</div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span>    BP_APP_PATTERNS_LIB_NO_EXPORT <span class="keywordtype">void</span> WholeRxBundleReadyCallback(padded_vector_uint8_t &amp; wholeBundleVec);</div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>    BP_APP_PATTERNS_LIB_NO_EXPORT <span class="keywordtype">void</span> OnNewOpportunisticLinkCallback(<span class="keyword">const</span> uint64_t remoteNodeId, <a class="code hl_class" href="class_induct.html">Induct</a>* thisInductPtr, <span class="keywordtype">void</span>* sinkPtr);</div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span>    BP_APP_PATTERNS_LIB_NO_EXPORT <span class="keywordtype">void</span> OnDeletedOpportunisticLinkCallback(<span class="keyword">const</span> uint64_t remoteNodeId, <a class="code hl_class" href="class_induct.html">Induct</a>* thisInductPtr, <span class="keywordtype">void</span>* sinkPtrAboutToBeDeleted);</div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>    BP_APP_PATTERNS_LIB_NO_EXPORT <span class="keywordtype">void</span> OnFailedBundleVecSendCallback(padded_vector_uint8_t&amp; movableBundle, std::vector&lt;uint8_t&gt;&amp; userData, uint64_t outductUuid, <span class="keywordtype">bool</span> successCallbackCalled);</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span>    BP_APP_PATTERNS_LIB_NO_EXPORT <span class="keywordtype">void</span> OnSuccessfulBundleSendCallback(std::vector&lt;uint8_t&gt;&amp; userData, uint64_t outductUuid);</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span>    BP_APP_PATTERNS_LIB_NO_EXPORT <span class="keywordtype">void</span> OnOutductLinkStatusChangedCallback(<span class="keywordtype">bool</span> isLinkDownEvent, uint64_t outductUuid);</div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span> </div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span>    <a class="code hl_class" href="class_outduct_manager.html">OutductManager</a> m_outductManager;</div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span>    <a class="code hl_class" href="class_induct_manager.html">InductManager</a> m_inductManager;</div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span>    std::unique_ptr&lt;boost::thread&gt; m_bpSourcePatternThreadPtr;</div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span>    std::atomic&lt;bool&gt; m_running;</div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span>    <span class="keywordtype">bool</span> m_useCustodyTransfer;</div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span>    <span class="keywordtype">bool</span> m_custodyTransferUseAcs;</div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span>    <span class="keywordtype">bool</span> m_useInductForSendingBundles;</div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span>    <span class="keywordtype">bool</span> m_useBpVersion7;</div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span>    uint64_t m_claRate;</div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span>    <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> m_bundleSendTimeoutSeconds;</div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno">  104</span>    boost::posix_time::time_duration m_bundleSendTimeoutTimeDuration;</div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span>    uint64_t m_bundleLifetimeMilliseconds;</div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span>    uint64_t m_bundlePriority;</div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span>    <a class="code hl_struct" href="structcbhe__eid__t.html">cbhe_eid_t</a> m_finalDestinationEid;</div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span>    <a class="code hl_struct" href="structcbhe__eid__t.html">cbhe_eid_t</a> m_myEid;</div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span>    uint64_t m_myCustodianServiceId;</div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span>    <a class="code hl_struct" href="structcbhe__eid__t.html">cbhe_eid_t</a> m_myCustodianEid;</div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span>    std::string m_myCustodianEidUriString;</div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span>    boost::mutex m_mutexCtebSet;</div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span>    boost::mutex m_mutexBundleUuidSet;</div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span>    FragmentSet::data_fragment_set_t m_outstandingCtebCustodyIdsFragmentSet;</div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span>    std::set&lt;cbhe_bundle_uuid_nofragment_t&gt; m_cbheBundleUuidSet;</div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span>    <span class="keywordtype">bool</span> m_detectedNextCustodianSupportsCteb;</div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span>    <span class="keywordtype">bool</span> m_requireRxBundleBeforeNextTx;</div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno">  118</span>    std::atomic&lt;bool&gt; m_isWaitingForRxBundleBeforeNextTx;</div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno">  119</span>    std::atomic&lt;bool&gt; m_linkIsDown;</div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno">  120</span>    boost::mutex m_mutexQueueBundlesThatFailedToSend;</div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span>    <span class="keyword">typedef</span> std::pair&lt;uint64_t, uint64_t&gt; bundleid_payloadsize_pair_t;</div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span>    <span class="keyword">typedef</span> std::pair&lt;padded_vector_uint8_t, bundleid_payloadsize_pair_t&gt; bundle_userdata_pair_t;</div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno">  123</span>    std::queue&lt;bundle_userdata_pair_t&gt; m_queueBundlesThatFailedToSend;</div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span>    uint64_t m_nextBundleId;</div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span>    boost::mutex m_mutexCurrentlySendingBundleIdSet;</div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span>    std::unordered_set&lt;uint64_t&gt; m_currentlySendingBundleIdSet;</div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno">  127</span>    boost::mutex m_waitingForRxBundleBeforeNextTxMutex;</div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span>    boost::condition_variable m_waitingForRxBundleBeforeNextTxConditionVariable;</div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno">  129</span>    boost::condition_variable m_cvCurrentlySendingBundleIdSet;</div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno">  130</span>    uint64_t m_tcpclOpportunisticRemoteNodeId;</div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span>    <a class="code hl_class" href="class_induct.html">Induct</a> * m_tcpclInductPtr;</div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno">  132</span>    <span class="comment">//version 7 stuff</span></div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno">  133</span>    <a class="code hl_struct" href="structcbhe__eid__t.html">cbhe_eid_t</a> m_lastPreviousNode;</div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno">  134</span>    std::vector&lt;uint64_t&gt; m_hopCounts;</div>
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno">  135</span><span class="keyword">public</span>:</div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span>    std::atomic&lt;bool&gt; m_allOutductsReady;</div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span>};</div>
</div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span> </div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span> </div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span><span class="preprocessor">#endif </span><span class="comment">//_BP_SOURCE_PATTERN_H</span></div>
<div class="ttc" id="a_custody_transfer_manager_8h_html"><div class="ttname"><a href="_custody_transfer_manager_8h.html">CustodyTransferManager.h</a></div></div>
<div class="ttc" id="a_induct_manager_8h_html"><div class="ttname"><a href="_induct_manager_8h.html">InductManager.h</a></div></div>
<div class="ttc" id="a_outduct_manager_8h_html"><div class="ttname"><a href="_outduct_manager_8h.html">OutductManager.h</a></div></div>
<div class="ttc" id="a_padded_vector_uint8_8h_html"><div class="ttname"><a href="_padded_vector_uint8_8h.html">PaddedVectorUint8.h</a></div></div>
<div class="ttc" id="abpv6_8h_html"><div class="ttname"><a href="bpv6_8h.html">bpv6.h</a></div></div>
<div class="ttc" id="aclass_induct_html"><div class="ttname"><a href="class_induct.html">Induct</a></div><div class="ttdef"><b>Definition</b> Induct.h:49</div></div>
<div class="ttc" id="aclass_induct_manager_html"><div class="ttname"><a href="class_induct_manager.html">InductManager</a></div><div class="ttdef"><b>Definition</b> InductManager.h:26</div></div>
<div class="ttc" id="aclass_outduct_manager_html"><div class="ttname"><a href="class_outduct_manager.html">OutductManager</a></div><div class="ttdef"><b>Definition</b> OutductManager.h:34</div></div>
<div class="ttc" id="astruct_outduct_final_stats_html"><div class="ttname"><a href="struct_outduct_final_stats.html">OutductFinalStats</a></div><div class="ttdef"><b>Definition</b> Outduct.h:40</div></div>
<div class="ttc" id="astructcbhe__eid__t_html"><div class="ttname"><a href="structcbhe__eid__t.html">cbhe_eid_t</a></div><div class="ttdef"><b>Definition</b> Cbhe.h:35</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_bdd9a5d540de89e9fe90efdfc6973a4f.html">common</a></li><li class="navelem"><a class="el" href="dir_16abf196c0e88f5ebd440542ff6d0bc9.html">bpcodec</a></li><li class="navelem"><a class="el" href="dir_b6971db1e96437e27185f51165b8a592.html">include</a></li><li class="navelem"><a class="el" href="dir_f240f76ef4b5ab44b0cfce6d4c9006a2.html">app_patterns</a></li><li class="navelem"><a class="el" href="_bp_source_pattern_8h.html">BpSourcePattern.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
