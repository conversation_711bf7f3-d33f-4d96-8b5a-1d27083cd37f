<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: common/ltp/include/LtpIpcEngine.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('_ltp_ipc_engine_8h_source.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">LtpIpcEngine.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_ltp_ipc_engine_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span> </div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span> </div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="preprocessor">#ifndef _LTP_IPC_ENGINE_H</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="preprocessor">#define _LTP_IPC_ENGINE_H 1</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span><span class="preprocessor">#define BOOST_INTERPROCESS_FORCE_NATIVE_EMULATION</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span><span class="preprocessor">#include &lt;boost/interprocess/sync/interprocess_semaphore.hpp&gt;</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span><span class="preprocessor">#include &lt;boost/interprocess/shared_memory_object.hpp&gt;</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="preprocessor">#include &lt;boost/interprocess/mapped_region.hpp&gt;</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="preprocessor">#include &lt;boost/thread.hpp&gt;</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="preprocessor">#include &lt;boost/asio.hpp&gt;</span></div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span><span class="preprocessor">#include &lt;map&gt;</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span><span class="preprocessor">#include &lt;queue&gt;</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span><span class="preprocessor">#include &quot;<a class="code" href="_circular_index_buffer_single_producer_single_consumer_configurable_8h.html">CircularIndexBufferSingleProducerSingleConsumerConfigurable.h</a>&quot;</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span><span class="preprocessor">#include &quot;<a class="code" href="_ltp_engine_8h.html">LtpEngine.h</a>&quot;</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="preprocessor">#include &quot;<a class="code" href="_ltp_engine_config_8h.html">LtpEngineConfig.h</a>&quot;</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span><span class="preprocessor">#include &lt;atomic&gt;</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span> </div>
<div class="foldopen" id="foldopen00042" data-start="{" data-end="};">
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="class_ltp_ipc_engine.html">   42</a></span><span class="keyword">class </span>CLASS_VISIBILITY_LTP_LIB LtpIpcEngine : <span class="keyword">public</span> LtpEngine {</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span>    LtpIpcEngine() = <span class="keyword">delete</span>;</div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span><span class="keyword">public</span>:</div>
<div class="foldopen" id="foldopen00046" data-start="{" data-end="};">
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno"><a class="line" href="struct_ltp_ipc_engine_1_1_ipc_packet.html">   46</a></span>    <span class="keyword">struct </span><a class="code hl_struct" href="struct_ltp_ipc_engine_1_1_ipc_packet.html">IpcPacket</a> {</div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span>        <span class="comment">//unsigned int dataIndex; //don&#39;t use a data pointer since logical address may be different across processes</span></div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span>        <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> bytesTransferred;</div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span>    };</div>
</div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span> </div>
<div class="foldopen" id="foldopen00051" data-start="{" data-end="};">
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno"><a class="line" href="struct_ltp_ipc_engine_1_1_ipc_control.html">   51</a></span>    <span class="keyword">struct </span>IpcControl</div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span>    {</div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span>        IpcControl() = <span class="keyword">delete</span>;</div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span>        IpcControl(<span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> numCbElements, <span class="keyword">const</span> uint64_t bytesPerElement)</div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span>            : m_bytesPerElement(bytesPerElement),</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span>            m_waitUntilNotFull_postHasFreeSpace_semaphore(numCbElements - 1),</div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span>            m_waitUntilNotEmpty_postHasData_semaphore(0),</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span>            <a class="code hl_variable" href="struct_ltp_ipc_engine_1_1_ipc_control.html#ac65342453dbd64dd11241d8cc24a9ede">m_circularIndexBuffer</a>(numCbElements)</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span>        {}</div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span> </div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span>        <span class="keyword">const</span> uint64_t m_bytesPerElement;</div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span>        <span class="comment">//Semaphores to protect and synchronize access</span></div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>        boost::interprocess::interprocess_semaphore m_waitUntilNotFull_postHasFreeSpace_semaphore;</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span>        boost::interprocess::interprocess_semaphore m_waitUntilNotEmpty_postHasData_semaphore;</div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno"><a class="line" href="struct_ltp_ipc_engine_1_1_ipc_control.html#ac65342453dbd64dd11241d8cc24a9ede">   66</a></span>        <a class="code hl_class" href="class_circular_index_buffer_single_producer_single_consumer_configurable.html">CircularIndexBufferSingleProducerSingleConsumerConfigurable</a> <a class="code hl_variable" href="struct_ltp_ipc_engine_1_1_ipc_control.html#ac65342453dbd64dd11241d8cc24a9ede">m_circularIndexBuffer</a>;</div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span>    };</div>
</div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span> </div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span> </div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span>    LTP_LIB_EXPORT LtpIpcEngine(</div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span>        <span class="keyword">const</span> std::string&amp; myTxSharedMemoryName,</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span>        <span class="keyword">const</span> uint64_t maxUdpRxPacketSizeBytes,</div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span>        <span class="keyword">const</span> <a class="code hl_struct" href="struct_ltp_engine_config.html">LtpEngineConfig</a>&amp; ltpRxOrTxCfg);</div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span> </div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span> </div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span>    LTP_LIB_EXPORT <span class="keyword">virtual</span> ~LtpIpcEngine() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span> </div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span>    LTP_LIB_EXPORT <span class="keywordtype">bool</span> Connect(<span class="keyword">const</span> std::string&amp; remoteTxSharedMemoryName);</div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span> </div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span>    LTP_LIB_EXPORT <span class="keywordtype">void</span> Stop();</div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span>    </div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span>    LTP_LIB_EXPORT <span class="keywordtype">void</span> <a class="code hl_function" href="class_ltp_ipc_engine.html#a6d0fa5350acfc93fb3f6f4d958c3afff">Reset_ThreadSafe_Blocking</a>();</div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>    </div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span>    LTP_LIB_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code hl_function" href="class_ltp_engine.html#a97feb8f9afbd72c0059d3962a528582b">Reset</a>() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span>    </div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span>    </div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span>    </div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span>    LTP_LIB_EXPORT <span class="keywordtype">bool</span> ReadyToSend() const noexcept;</div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span> </div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span>private:</div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span>    LTP_LIB_NO_EXPORT virtual <span class="keywordtype">void</span> PacketInFullyProcessedCallback(<span class="keywordtype">bool</span> success) override;</div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span>    </div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span>    LTP_LIB_NO_EXPORT virtual <span class="keywordtype">void</span> SendPacket(const std::vector&lt;boost::asio::const_buffer&gt; &amp; constBufferVec,</div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span>        std::shared_ptr&lt;std::vector&lt;std::vector&lt;uint8_t&gt; &gt; &gt;&amp;&amp; underlyingDataToDeleteOnSentCallback,</div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno">  118</span>        std::shared_ptr&lt;<a class="code hl_class" href="class_ltp_client_service_data_to_send.html">LtpClientServiceDataToSend</a>&gt;&amp;&amp; underlyingCsDataToDeleteOnSentCallback) override;</div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno">  119</span>    </div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno">  120</span>    </div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span>    </div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno">  129</span>    LTP_LIB_NO_EXPORT virtual <span class="keywordtype">void</span> SendPackets(std::shared_ptr&lt;std::vector&lt;<a class="code hl_struct" href="struct_udp_send_packet_info.html">UdpSendPacketInfo</a>&gt; &gt;&amp;&amp; udpSendPacketInfoVecSharedPtr, const std::<span class="keywordtype">size_t</span> numPacketsToSend) override;</div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno">  130</span> </div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span>    LTP_LIB_NO_EXPORT <span class="keywordtype">void</span> DoSendPacket(const std::vector&lt;boost::asio::const_buffer&gt;&amp; constBufferVec);</div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno">  132</span> </div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno">  133</span>    LTP_LIB_NO_EXPORT <span class="keywordtype">void</span> ReadRemoteTxShmThreadFunc();</div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno">  134</span>    LTP_LIB_NO_EXPORT <span class="keywordtype">bool</span> VerifyIpcPacketReceive(uint8_t* data, std::<span class="keywordtype">size_t</span> bytesTransferred);</div>
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno">  135</span>    </div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span>    std::unique_ptr&lt;boost::thread&gt; m_readRemoteTxShmThreadPtr;</div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span> </div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span>    </div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span>    const std::<span class="keywordtype">string</span> m_myTxSharedMemoryName;</div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span>    const uint64_t M_REMOTE_ENGINE_ID;</div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span>    std::unique_ptr&lt;boost::interprocess::shared_memory_object&gt; m_myTxSharedMemoryObjectPtr;</div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span>    std::unique_ptr&lt;boost::interprocess::mapped_region&gt; m_myTxShmMappedRegionPtr;</div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span>    <a class="code hl_struct" href="struct_ltp_ipc_engine_1_1_ipc_control.html">IpcControl</a>* m_myTxIpcControlPtr;</div>
<div class="line"><a id="l00144" name="l00144"></a><span class="lineno">  144</span>    <a class="code hl_struct" href="struct_ltp_ipc_engine_1_1_ipc_packet.html">IpcPacket</a>* m_myTxIpcPacketCbArray;</div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span>    uint8_t* m_myTxIpcDataStart;</div>
<div class="line"><a id="l00146" name="l00146"></a><span class="lineno">  146</span> </div>
<div class="line"><a id="l00147" name="l00147"></a><span class="lineno">  147</span>    std::<span class="keywordtype">string</span> m_remoteTxSharedMemoryName;</div>
<div class="line"><a id="l00148" name="l00148"></a><span class="lineno">  148</span>    std::unique_ptr&lt;boost::interprocess::shared_memory_object&gt; m_remoteTxSharedMemoryObjectPtr;</div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno">  149</span>    std::unique_ptr&lt;boost::interprocess::mapped_region&gt; m_remoteTxShmMappedRegionPtr;</div>
<div class="line"><a id="l00150" name="l00150"></a><span class="lineno">  150</span>    <a class="code hl_struct" href="struct_ltp_ipc_engine_1_1_ipc_control.html">IpcControl</a>* m_remoteTxIpcControlPtr;</div>
<div class="line"><a id="l00151" name="l00151"></a><span class="lineno">  151</span>    <a class="code hl_struct" href="struct_ltp_ipc_engine_1_1_ipc_packet.html">IpcPacket</a>* m_remoteTxIpcPacketCbArray;</div>
<div class="line"><a id="l00152" name="l00152"></a><span class="lineno">  152</span>    uint8_t* m_remoteTxIpcDataStart;</div>
<div class="line"><a id="l00153" name="l00153"></a><span class="lineno">  153</span></div>
<div class="line"><a id="l00155" name="l00155"></a><span class="lineno">  155</span>    const <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> M_NUM_CIRCULAR_BUFFER_VECTORS;</div>
<div class="line"><a id="l00156" name="l00156"></a><span class="lineno">  156</span> </div>
<div class="line"><a id="l00157" name="l00157"></a><span class="lineno">  157</span> </div>
<div class="line"><a id="l00158" name="l00158"></a><span class="lineno">  158</span>    </div>
<div class="line"><a id="l00159" name="l00159"></a><span class="lineno">  159</span>    </div>
<div class="line"><a id="l00160" name="l00160"></a><span class="lineno">  160</span></div>
<div class="line"><a id="l00162" name="l00162"></a><span class="lineno">  162</span>    std::atomic&lt;<span class="keywordtype">bool</span>&gt; m_running;</div>
<div class="line"><a id="l00163" name="l00163"></a><span class="lineno">  163</span>    const <span class="keywordtype">bool</span> m_isInduct;</div>
<div class="line"><a id="l00164" name="l00164"></a><span class="lineno">  164</span> </div>
<div class="line"><a id="l00165" name="l00165"></a><span class="lineno">  165</span>    <span class="comment">//for safe unit test resets</span></div>
<div class="line"><a id="l00167" name="l00167"></a><span class="lineno">  167</span>    std::atomic&lt;<span class="keywordtype">bool</span>&gt; m_resetInProgress;</div>
<div class="line"><a id="l00169" name="l00169"></a><span class="lineno">  169</span>    boost::mutex m_resetMutex;</div>
<div class="line"><a id="l00171" name="l00171"></a><span class="lineno">  171</span>    boost::condition_variable m_resetConditionVariable;</div>
<div class="line"><a id="l00172" name="l00172"></a><span class="lineno">  172</span> </div>
<div class="line"><a id="l00173" name="l00173"></a><span class="lineno">  173</span>public:</div>
<div class="line"><a id="l00175" name="l00175"></a><span class="lineno"><a class="line" href="class_ltp_ipc_engine.html#a35db09847bf53dd652ceae07220f3ac7">  175</a></span>    std::atomic&lt;uint64_t&gt; <a class="code hl_variable" href="class_ltp_ipc_engine.html#a35db09847bf53dd652ceae07220f3ac7">m_countAsyncSendCalls</a>;</div>
<div class="line"><a id="l00177" name="l00177"></a><span class="lineno"><a class="line" href="class_ltp_ipc_engine.html#a959a436dc6776f5fd8540b25c781e4af">  177</a></span>    std::atomic&lt;uint64_t&gt; <a class="code hl_variable" href="class_ltp_ipc_engine.html#a959a436dc6776f5fd8540b25c781e4af">m_countAsyncSendCallbackCalls</a>; <span class="comment">//same as udp packets sent</span></div>
<div class="line"><a id="l00179" name="l00179"></a><span class="lineno"><a class="line" href="class_ltp_ipc_engine.html#a836cd302a35a82420bb9d4350197dcbe">  179</a></span>    std::atomic&lt;uint64_t&gt; <a class="code hl_variable" href="class_ltp_ipc_engine.html#a836cd302a35a82420bb9d4350197dcbe">m_countBatchSendCalls</a>;</div>
<div class="line"><a id="l00181" name="l00181"></a><span class="lineno"><a class="line" href="class_ltp_ipc_engine.html#a39cbcc94b8d1d1ec7e1ad036fd1ea9d5">  181</a></span>    std::atomic&lt;uint64_t&gt; <a class="code hl_variable" href="class_ltp_ipc_engine.html#a39cbcc94b8d1d1ec7e1ad036fd1ea9d5">m_countBatchSendCallbackCalls</a>;</div>
<div class="line"><a id="l00183" name="l00183"></a><span class="lineno"><a class="line" href="class_ltp_ipc_engine.html#afcbc62a48a61f5630265241e8c33e424">  183</a></span>    std::atomic&lt;uint64_t&gt; <a class="code hl_variable" href="class_ltp_ipc_engine.html#afcbc62a48a61f5630265241e8c33e424">m_countBatchUdpPacketsSent</a>;</div>
<div class="line"><a id="l00184" name="l00184"></a><span class="lineno">  184</span>    <span class="comment">//total udp packets sent is m_countAsyncSendCallbackCalls + m_countBatchUdpPacketsSent</span></div>
<div class="line"><a id="l00185" name="l00185"></a><span class="lineno">  185</span></div>
<div class="line"><a id="l00187" name="l00187"></a><span class="lineno"><a class="line" href="class_ltp_ipc_engine.html#afe9a219c3aedddda83656dea3f2a2746">  187</a></span>    std::atomic&lt;uint64_t&gt; <a class="code hl_variable" href="class_ltp_ipc_engine.html#afe9a219c3aedddda83656dea3f2a2746">m_countCircularBufferOverruns</a>;</div>
<div class="line"><a id="l00189" name="l00189"></a><span class="lineno"><a class="line" href="class_ltp_ipc_engine.html#aee0e649e6286556091001c717ce4de75">  189</a></span>    std::atomic&lt;uint64_t&gt; <a class="code hl_variable" href="class_ltp_ipc_engine.html#aee0e649e6286556091001c717ce4de75">m_countUdpPacketsReceived</a>;</div>
<div class="line"><a id="l00190" name="l00190"></a><span class="lineno">  190</span>};</div>
</div>
<div class="line"><a id="l00191" name="l00191"></a><span class="lineno">  191</span> </div>
<div class="line"><a id="l00192" name="l00192"></a><span class="lineno">  192</span> </div>
<div class="line"><a id="l00193" name="l00193"></a><span class="lineno">  193</span> </div>
<div class="line"><a id="l00194" name="l00194"></a><span class="lineno">  194</span><span class="preprocessor">#endif </span><span class="comment">//_LTP_IPC_ENGINE_H</span></div>
<div class="ttc" id="a_circular_index_buffer_single_producer_single_consumer_configurable_8h_html"><div class="ttname"><a href="_circular_index_buffer_single_producer_single_consumer_configurable_8h.html">CircularIndexBufferSingleProducerSingleConsumerConfigurable.h</a></div></div>
<div class="ttc" id="a_ltp_engine_8h_html"><div class="ttname"><a href="_ltp_engine_8h.html">LtpEngine.h</a></div></div>
<div class="ttc" id="a_ltp_engine_config_8h_html"><div class="ttname"><a href="_ltp_engine_config_8h.html">LtpEngineConfig.h</a></div></div>
<div class="ttc" id="aclass_circular_index_buffer_single_producer_single_consumer_configurable_html"><div class="ttname"><a href="class_circular_index_buffer_single_producer_single_consumer_configurable.html">CircularIndexBufferSingleProducerSingleConsumerConfigurable</a></div><div class="ttdef"><b>Definition</b> CircularIndexBufferSingleProducerSingleConsumerConfigurable.h:39</div></div>
<div class="ttc" id="aclass_ltp_client_service_data_to_send_html"><div class="ttname"><a href="class_ltp_client_service_data_to_send.html">LtpClientServiceDataToSend</a></div><div class="ttdef"><b>Definition</b> LtpClientServiceDataToSend.h:45</div></div>
<div class="ttc" id="aclass_ltp_engine_html_a97feb8f9afbd72c0059d3962a528582b"><div class="ttname"><a href="class_ltp_engine.html#a97feb8f9afbd72c0059d3962a528582b">LtpEngine::Reset</a></div><div class="ttdeci">virtual LTP_LIB_EXPORT void Reset()</div><div class="ttdef"><b>Definition</b> LtpEngine.cpp:275</div></div>
<div class="ttc" id="aclass_ltp_ipc_engine_html_a35db09847bf53dd652ceae07220f3ac7"><div class="ttname"><a href="class_ltp_ipc_engine.html#a35db09847bf53dd652ceae07220f3ac7">LtpIpcEngine::m_countAsyncSendCalls</a></div><div class="ttdeci">std::atomic&lt; uint64_t &gt; m_countAsyncSendCalls</div><div class="ttdoc">Total number of initiated send operations.</div><div class="ttdef"><b>Definition</b> LtpIpcEngine.h:175</div></div>
<div class="ttc" id="aclass_ltp_ipc_engine_html_a39cbcc94b8d1d1ec7e1ad036fd1ea9d5"><div class="ttname"><a href="class_ltp_ipc_engine.html#a39cbcc94b8d1d1ec7e1ad036fd1ea9d5">LtpIpcEngine::m_countBatchSendCallbackCalls</a></div><div class="ttdeci">std::atomic&lt; uint64_t &gt; m_countBatchSendCallbackCalls</div><div class="ttdoc">Total number of batch send operation completion handler invocations, indicates the number of complete...</div><div class="ttdef"><b>Definition</b> LtpIpcEngine.h:181</div></div>
<div class="ttc" id="aclass_ltp_ipc_engine_html_a6d0fa5350acfc93fb3f6f4d958c3afff"><div class="ttname"><a href="class_ltp_ipc_engine.html#a6d0fa5350acfc93fb3f6f4d958c3afff">LtpIpcEngine::Reset_ThreadSafe_Blocking</a></div><div class="ttdeci">LTP_LIB_EXPORT void Reset_ThreadSafe_Blocking()</div><div class="ttdef"><b>Definition</b> LtpIpcEngine.cpp:196</div></div>
<div class="ttc" id="aclass_ltp_ipc_engine_html_a836cd302a35a82420bb9d4350197dcbe"><div class="ttname"><a href="class_ltp_ipc_engine.html#a836cd302a35a82420bb9d4350197dcbe">LtpIpcEngine::m_countBatchSendCalls</a></div><div class="ttdeci">std::atomic&lt; uint64_t &gt; m_countBatchSendCalls</div><div class="ttdoc">Total number of initiated batch send operations through m_udpBatchSenderConnected.</div><div class="ttdef"><b>Definition</b> LtpIpcEngine.h:179</div></div>
<div class="ttc" id="aclass_ltp_ipc_engine_html_a959a436dc6776f5fd8540b25c781e4af"><div class="ttname"><a href="class_ltp_ipc_engine.html#a959a436dc6776f5fd8540b25c781e4af">LtpIpcEngine::m_countAsyncSendCallbackCalls</a></div><div class="ttdeci">std::atomic&lt; uint64_t &gt; m_countAsyncSendCallbackCalls</div><div class="ttdoc">Total number of send operation completion handler invocations, indicates the number of completed send...</div><div class="ttdef"><b>Definition</b> LtpIpcEngine.h:177</div></div>
<div class="ttc" id="aclass_ltp_ipc_engine_html_aee0e649e6286556091001c717ce4de75"><div class="ttname"><a href="class_ltp_ipc_engine.html#aee0e649e6286556091001c717ce4de75">LtpIpcEngine::m_countUdpPacketsReceived</a></div><div class="ttdeci">std::atomic&lt; uint64_t &gt; m_countUdpPacketsReceived</div><div class="ttdoc">Total number of packets received, includes number of dropped packets due to receive buffers being ful...</div><div class="ttdef"><b>Definition</b> LtpIpcEngine.h:189</div></div>
<div class="ttc" id="aclass_ltp_ipc_engine_html_afcbc62a48a61f5630265241e8c33e424"><div class="ttname"><a href="class_ltp_ipc_engine.html#afcbc62a48a61f5630265241e8c33e424">LtpIpcEngine::m_countBatchUdpPacketsSent</a></div><div class="ttdeci">std::atomic&lt; uint64_t &gt; m_countBatchUdpPacketsSent</div><div class="ttdoc">Total number of packets actually sent across batch send operations.</div><div class="ttdef"><b>Definition</b> LtpIpcEngine.h:183</div></div>
<div class="ttc" id="aclass_ltp_ipc_engine_html_afe9a219c3aedddda83656dea3f2a2746"><div class="ttname"><a href="class_ltp_ipc_engine.html#afe9a219c3aedddda83656dea3f2a2746">LtpIpcEngine::m_countCircularBufferOverruns</a></div><div class="ttdeci">std::atomic&lt; uint64_t &gt; m_countCircularBufferOverruns</div><div class="ttdoc">Total number of requests attempted to queue a packet for transmission while transmission buffers were...</div><div class="ttdef"><b>Definition</b> LtpIpcEngine.h:187</div></div>
<div class="ttc" id="astruct_ltp_engine_config_html"><div class="ttname"><a href="struct_ltp_engine_config.html">LtpEngineConfig</a></div><div class="ttdef"><b>Definition</b> LtpEngineConfig.h:29</div></div>
<div class="ttc" id="astruct_ltp_ipc_engine_1_1_ipc_control_html"><div class="ttname"><a href="struct_ltp_ipc_engine_1_1_ipc_control.html">LtpIpcEngine::IpcControl</a></div><div class="ttdef"><b>Definition</b> LtpIpcEngine.h:52</div></div>
<div class="ttc" id="astruct_ltp_ipc_engine_1_1_ipc_control_html_ac65342453dbd64dd11241d8cc24a9ede"><div class="ttname"><a href="struct_ltp_ipc_engine_1_1_ipc_control.html#ac65342453dbd64dd11241d8cc24a9ede">LtpIpcEngine::IpcControl::m_circularIndexBuffer</a></div><div class="ttdeci">CircularIndexBufferSingleProducerSingleConsumerConfigurable m_circularIndexBuffer</div><div class="ttdoc">Circular index buffer, used to index the circular vector of receive buffers.</div><div class="ttdef"><b>Definition</b> LtpIpcEngine.h:66</div></div>
<div class="ttc" id="astruct_ltp_ipc_engine_1_1_ipc_packet_html"><div class="ttname"><a href="struct_ltp_ipc_engine_1_1_ipc_packet.html">LtpIpcEngine::IpcPacket</a></div><div class="ttdef"><b>Definition</b> LtpIpcEngine.h:46</div></div>
<div class="ttc" id="astruct_udp_send_packet_info_html"><div class="ttname"><a href="struct_udp_send_packet_info.html">UdpSendPacketInfo</a></div><div class="ttdoc">UDP send operation context data.</div><div class="ttdef"><b>Definition</b> LtpClientServiceDataToSend.h:168</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_bdd9a5d540de89e9fe90efdfc6973a4f.html">common</a></li><li class="navelem"><a class="el" href="dir_a20834912dc4d1000db8cf473036c40b.html">ltp</a></li><li class="navelem"><a class="el" href="dir_c2a9dec5bc2fb8bc646fc7ea53c474f3.html">include</a></li><li class="navelem"><a class="el" href="_ltp_ipc_engine_8h.html">LtpIpcEngine.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
