<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: boost::asio::basic_dir_monitor_service&lt; DirMonitorImplementation &gt; Class Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('classboost_1_1asio_1_1basic__dir__monitor__service.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="classboost_1_1asio_1_1basic__dir__monitor__service-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">boost::asio::basic_dir_monitor_service&lt; DirMonitorImplementation &gt; Class Template Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for boost::asio::basic_dir_monitor_service&lt; DirMonitorImplementation &gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classboost_1_1asio_1_1basic__dir__monitor__service.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structboost_1_1asio_1_1basic__dir__monitor__service_1_1completion__key.html">completion_key</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classboost_1_1asio_1_1basic__dir__monitor__service_1_1monitor__operation.html">monitor_operation</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-types" name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a6109d577035cdaccd58159c4a6905edb" id="r_a6109d577035cdaccd58159c4a6905edb"><td class="memItemLeft" align="right" valign="top"><a id="a6109d577035cdaccd58159c4a6905edb" name="a6109d577035cdaccd58159c4a6905edb"></a>
typedef boost::shared_ptr&lt; DirMonitorImplementation &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>implementation_type</b></td></tr>
<tr class="separator:a6109d577035cdaccd58159c4a6905edb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5bc7d7665b485ead12d3ab732c386c81" id="r_a5bc7d7665b485ead12d3ab732c386c81"><td class="memItemLeft" align="right" valign="top"><a id="a5bc7d7665b485ead12d3ab732c386c81" name="a5bc7d7665b485ead12d3ab732c386c81"></a>
typedef std::shared_ptr&lt; DirMonitorImplementation &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>implementation_type</b></td></tr>
<tr class="separator:a5bc7d7665b485ead12d3ab732c386c81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6109d577035cdaccd58159c4a6905edb" id="r_a6109d577035cdaccd58159c4a6905edb"><td class="memItemLeft" align="right" valign="top"><a id="a6109d577035cdaccd58159c4a6905edb" name="a6109d577035cdaccd58159c4a6905edb"></a>
typedef boost::shared_ptr&lt; DirMonitorImplementation &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>implementation_type</b></td></tr>
<tr class="separator:a6109d577035cdaccd58159c4a6905edb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5bc7d7665b485ead12d3ab732c386c81" id="r_a5bc7d7665b485ead12d3ab732c386c81"><td class="memItemLeft" align="right" valign="top"><a id="a5bc7d7665b485ead12d3ab732c386c81" name="a5bc7d7665b485ead12d3ab732c386c81"></a>
typedef std::shared_ptr&lt; DirMonitorImplementation &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>implementation_type</b></td></tr>
<tr class="separator:a5bc7d7665b485ead12d3ab732c386c81"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a4c539bb50eeadc0833ff471725f22bfd" id="r_a4c539bb50eeadc0833ff471725f22bfd"><td class="memItemLeft" align="right" valign="top"><a id="a4c539bb50eeadc0833ff471725f22bfd" name="a4c539bb50eeadc0833ff471725f22bfd"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>basic_dir_monitor_service</b> (boost::asio::io_service &amp;io_service)</td></tr>
<tr class="separator:a4c539bb50eeadc0833ff471725f22bfd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a414b7346e2f52ec64168ab41eb0e560a" id="r_a414b7346e2f52ec64168ab41eb0e560a"><td class="memItemLeft" align="right" valign="top"><a id="a414b7346e2f52ec64168ab41eb0e560a" name="a414b7346e2f52ec64168ab41eb0e560a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>construct</b> (implementation_type &amp;impl)</td></tr>
<tr class="separator:a414b7346e2f52ec64168ab41eb0e560a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac15d2ac88960999f9b753f9d28716e6a" id="r_ac15d2ac88960999f9b753f9d28716e6a"><td class="memItemLeft" align="right" valign="top"><a id="ac15d2ac88960999f9b753f9d28716e6a" name="ac15d2ac88960999f9b753f9d28716e6a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>destroy</b> (implementation_type &amp;impl)</td></tr>
<tr class="separator:ac15d2ac88960999f9b753f9d28716e6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d2ab3fcef065b9fc11c133b9fb1ab98" id="r_a0d2ab3fcef065b9fc11c133b9fb1ab98"><td class="memItemLeft" align="right" valign="top"><a id="a0d2ab3fcef065b9fc11c133b9fb1ab98" name="a0d2ab3fcef065b9fc11c133b9fb1ab98"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>add_directory</b> (implementation_type &amp;impl, const std::string &amp;dirname)</td></tr>
<tr class="separator:a0d2ab3fcef065b9fc11c133b9fb1ab98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40a7a02c89bc9f7221c7360a75dcfcfa" id="r_a40a7a02c89bc9f7221c7360a75dcfcfa"><td class="memItemLeft" align="right" valign="top"><a id="a40a7a02c89bc9f7221c7360a75dcfcfa" name="a40a7a02c89bc9f7221c7360a75dcfcfa"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>remove_directory</b> (implementation_type &amp;impl, const std::string &amp;dirname)</td></tr>
<tr class="separator:a40a7a02c89bc9f7221c7360a75dcfcfa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8513f3bfc91b8db3204fa1bfd5c372" id="r_adf8513f3bfc91b8db3204fa1bfd5c372"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structboost_1_1asio_1_1dir__monitor__event.html">dir_monitor_event</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adf8513f3bfc91b8db3204fa1bfd5c372">monitor</a> (implementation_type &amp;impl, boost::system::error_code &amp;ec)</td></tr>
<tr class="separator:adf8513f3bfc91b8db3204fa1bfd5c372"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a002f0e244b09735e1c8ec803ef871727" id="r_a002f0e244b09735e1c8ec803ef871727"><td class="memTemplParams" colspan="2">template&lt;typename Handler&gt; </td></tr>
<tr class="memitem:a002f0e244b09735e1c8ec803ef871727"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="#a002f0e244b09735e1c8ec803ef871727">async_monitor</a> (implementation_type &amp;impl, Handler handler)</td></tr>
<tr class="separator:a002f0e244b09735e1c8ec803ef871727"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c539bb50eeadc0833ff471725f22bfd" id="r_a4c539bb50eeadc0833ff471725f22bfd"><td class="memItemLeft" align="right" valign="top"><a id="a4c539bb50eeadc0833ff471725f22bfd" name="a4c539bb50eeadc0833ff471725f22bfd"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>basic_dir_monitor_service</b> (boost::asio::io_service &amp;io_service)</td></tr>
<tr class="separator:a4c539bb50eeadc0833ff471725f22bfd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a414b7346e2f52ec64168ab41eb0e560a" id="r_a414b7346e2f52ec64168ab41eb0e560a"><td class="memItemLeft" align="right" valign="top"><a id="a414b7346e2f52ec64168ab41eb0e560a" name="a414b7346e2f52ec64168ab41eb0e560a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>construct</b> (implementation_type &amp;impl)</td></tr>
<tr class="separator:a414b7346e2f52ec64168ab41eb0e560a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac15d2ac88960999f9b753f9d28716e6a" id="r_ac15d2ac88960999f9b753f9d28716e6a"><td class="memItemLeft" align="right" valign="top"><a id="ac15d2ac88960999f9b753f9d28716e6a" name="ac15d2ac88960999f9b753f9d28716e6a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>destroy</b> (implementation_type &amp;impl)</td></tr>
<tr class="separator:ac15d2ac88960999f9b753f9d28716e6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d2ab3fcef065b9fc11c133b9fb1ab98" id="r_a0d2ab3fcef065b9fc11c133b9fb1ab98"><td class="memItemLeft" align="right" valign="top"><a id="a0d2ab3fcef065b9fc11c133b9fb1ab98" name="a0d2ab3fcef065b9fc11c133b9fb1ab98"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>add_directory</b> (implementation_type &amp;impl, const std::string &amp;dirname)</td></tr>
<tr class="separator:a0d2ab3fcef065b9fc11c133b9fb1ab98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40a7a02c89bc9f7221c7360a75dcfcfa" id="r_a40a7a02c89bc9f7221c7360a75dcfcfa"><td class="memItemLeft" align="right" valign="top"><a id="a40a7a02c89bc9f7221c7360a75dcfcfa" name="a40a7a02c89bc9f7221c7360a75dcfcfa"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>remove_directory</b> (implementation_type &amp;impl, const std::string &amp;dirname)</td></tr>
<tr class="separator:a40a7a02c89bc9f7221c7360a75dcfcfa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8513f3bfc91b8db3204fa1bfd5c372" id="r_adf8513f3bfc91b8db3204fa1bfd5c372"><td class="memItemLeft" align="right" valign="top"><a id="adf8513f3bfc91b8db3204fa1bfd5c372" name="adf8513f3bfc91b8db3204fa1bfd5c372"></a>
<a class="el" href="structboost_1_1asio_1_1dir__monitor__event.html">dir_monitor_event</a>&#160;</td><td class="memItemRight" valign="bottom"><b>monitor</b> (implementation_type &amp;impl, boost::system::error_code &amp;ec)</td></tr>
<tr class="separator:adf8513f3bfc91b8db3204fa1bfd5c372"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a002f0e244b09735e1c8ec803ef871727" id="r_a002f0e244b09735e1c8ec803ef871727"><td class="memTemplParams" colspan="2"><a id="a002f0e244b09735e1c8ec803ef871727" name="a002f0e244b09735e1c8ec803ef871727"></a>
template&lt;typename Handler&gt; </td></tr>
<tr class="memitem:a002f0e244b09735e1c8ec803ef871727"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><b>async_monitor</b> (implementation_type &amp;impl, Handler handler)</td></tr>
<tr class="separator:a002f0e244b09735e1c8ec803ef871727"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c539bb50eeadc0833ff471725f22bfd" id="r_a4c539bb50eeadc0833ff471725f22bfd"><td class="memItemLeft" align="right" valign="top"><a id="a4c539bb50eeadc0833ff471725f22bfd" name="a4c539bb50eeadc0833ff471725f22bfd"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>basic_dir_monitor_service</b> (boost::asio::io_service &amp;io_service)</td></tr>
<tr class="separator:a4c539bb50eeadc0833ff471725f22bfd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a414b7346e2f52ec64168ab41eb0e560a" id="r_a414b7346e2f52ec64168ab41eb0e560a"><td class="memItemLeft" align="right" valign="top"><a id="a414b7346e2f52ec64168ab41eb0e560a" name="a414b7346e2f52ec64168ab41eb0e560a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>construct</b> (implementation_type &amp;impl)</td></tr>
<tr class="separator:a414b7346e2f52ec64168ab41eb0e560a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac15d2ac88960999f9b753f9d28716e6a" id="r_ac15d2ac88960999f9b753f9d28716e6a"><td class="memItemLeft" align="right" valign="top"><a id="ac15d2ac88960999f9b753f9d28716e6a" name="ac15d2ac88960999f9b753f9d28716e6a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>destroy</b> (implementation_type &amp;impl)</td></tr>
<tr class="separator:ac15d2ac88960999f9b753f9d28716e6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d2ab3fcef065b9fc11c133b9fb1ab98" id="r_a0d2ab3fcef065b9fc11c133b9fb1ab98"><td class="memItemLeft" align="right" valign="top"><a id="a0d2ab3fcef065b9fc11c133b9fb1ab98" name="a0d2ab3fcef065b9fc11c133b9fb1ab98"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>add_directory</b> (implementation_type &amp;impl, const std::string &amp;dirname)</td></tr>
<tr class="separator:a0d2ab3fcef065b9fc11c133b9fb1ab98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40a7a02c89bc9f7221c7360a75dcfcfa" id="r_a40a7a02c89bc9f7221c7360a75dcfcfa"><td class="memItemLeft" align="right" valign="top"><a id="a40a7a02c89bc9f7221c7360a75dcfcfa" name="a40a7a02c89bc9f7221c7360a75dcfcfa"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>remove_directory</b> (implementation_type &amp;impl, const std::string &amp;dirname)</td></tr>
<tr class="separator:a40a7a02c89bc9f7221c7360a75dcfcfa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8513f3bfc91b8db3204fa1bfd5c372" id="r_adf8513f3bfc91b8db3204fa1bfd5c372"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structboost_1_1asio_1_1dir__monitor__event.html">dir_monitor_event</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#adf8513f3bfc91b8db3204fa1bfd5c372">monitor</a> (implementation_type &amp;impl, boost::system::error_code &amp;ec)</td></tr>
<tr class="separator:adf8513f3bfc91b8db3204fa1bfd5c372"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a002f0e244b09735e1c8ec803ef871727" id="r_a002f0e244b09735e1c8ec803ef871727"><td class="memTemplParams" colspan="2">template&lt;typename Handler&gt; </td></tr>
<tr class="memitem:a002f0e244b09735e1c8ec803ef871727"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="#a002f0e244b09735e1c8ec803ef871727">async_monitor</a> (implementation_type &amp;impl, Handler handler)</td></tr>
<tr class="separator:a002f0e244b09735e1c8ec803ef871727"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c539bb50eeadc0833ff471725f22bfd" id="r_a4c539bb50eeadc0833ff471725f22bfd"><td class="memItemLeft" align="right" valign="top"><a id="a4c539bb50eeadc0833ff471725f22bfd" name="a4c539bb50eeadc0833ff471725f22bfd"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>basic_dir_monitor_service</b> (boost::asio::io_service &amp;io_service)</td></tr>
<tr class="separator:a4c539bb50eeadc0833ff471725f22bfd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a414b7346e2f52ec64168ab41eb0e560a" id="r_a414b7346e2f52ec64168ab41eb0e560a"><td class="memItemLeft" align="right" valign="top"><a id="a414b7346e2f52ec64168ab41eb0e560a" name="a414b7346e2f52ec64168ab41eb0e560a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>construct</b> (implementation_type &amp;impl)</td></tr>
<tr class="separator:a414b7346e2f52ec64168ab41eb0e560a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac15d2ac88960999f9b753f9d28716e6a" id="r_ac15d2ac88960999f9b753f9d28716e6a"><td class="memItemLeft" align="right" valign="top"><a id="ac15d2ac88960999f9b753f9d28716e6a" name="ac15d2ac88960999f9b753f9d28716e6a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>destroy</b> (implementation_type &amp;impl)</td></tr>
<tr class="separator:ac15d2ac88960999f9b753f9d28716e6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d2ab3fcef065b9fc11c133b9fb1ab98" id="r_a0d2ab3fcef065b9fc11c133b9fb1ab98"><td class="memItemLeft" align="right" valign="top"><a id="a0d2ab3fcef065b9fc11c133b9fb1ab98" name="a0d2ab3fcef065b9fc11c133b9fb1ab98"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>add_directory</b> (implementation_type &amp;impl, const std::string &amp;dirname)</td></tr>
<tr class="separator:a0d2ab3fcef065b9fc11c133b9fb1ab98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40a7a02c89bc9f7221c7360a75dcfcfa" id="r_a40a7a02c89bc9f7221c7360a75dcfcfa"><td class="memItemLeft" align="right" valign="top"><a id="a40a7a02c89bc9f7221c7360a75dcfcfa" name="a40a7a02c89bc9f7221c7360a75dcfcfa"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>remove_directory</b> (implementation_type &amp;impl, const std::string &amp;dirname)</td></tr>
<tr class="separator:a40a7a02c89bc9f7221c7360a75dcfcfa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8513f3bfc91b8db3204fa1bfd5c372" id="r_adf8513f3bfc91b8db3204fa1bfd5c372"><td class="memItemLeft" align="right" valign="top"><a id="adf8513f3bfc91b8db3204fa1bfd5c372" name="adf8513f3bfc91b8db3204fa1bfd5c372"></a>
<a class="el" href="structboost_1_1asio_1_1dir__monitor__event.html">dir_monitor_event</a>&#160;</td><td class="memItemRight" valign="bottom"><b>monitor</b> (implementation_type &amp;impl, boost::system::error_code &amp;ec)</td></tr>
<tr class="separator:adf8513f3bfc91b8db3204fa1bfd5c372"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a002f0e244b09735e1c8ec803ef871727" id="r_a002f0e244b09735e1c8ec803ef871727"><td class="memTemplParams" colspan="2"><a id="a002f0e244b09735e1c8ec803ef871727" name="a002f0e244b09735e1c8ec803ef871727"></a>
template&lt;typename Handler&gt; </td></tr>
<tr class="memitem:a002f0e244b09735e1c8ec803ef871727"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><b>async_monitor</b> (implementation_type &amp;impl, Handler handler)</td></tr>
<tr class="separator:a002f0e244b09735e1c8ec803ef871727"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-attribs" name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a14df9dce7f266248c9b3159a4a80b408" id="r_a14df9dce7f266248c9b3159a4a80b408"><td class="memItemLeft" align="right" valign="top"><a id="a14df9dce7f266248c9b3159a4a80b408" name="a14df9dce7f266248c9b3159a4a80b408"></a>
static boost::asio::io_service::id&#160;</td><td class="memItemRight" valign="bottom"><b>id</b></td></tr>
<tr class="separator:a14df9dce7f266248c9b3159a4a80b408"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a002f0e244b09735e1c8ec803ef871727" name="a002f0e244b09735e1c8ec803ef871727"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a002f0e244b09735e1c8ec803ef871727">&#9670;&#160;</a></span>async_monitor() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DirMonitorImplementation = dir_monitor_impl&gt; </div>
<div class="memtemplate">
template&lt;typename Handler&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classboost_1_1asio_1_1basic__dir__monitor__service.html">boost::asio::basic_dir_monitor_service</a>&lt; DirMonitorImplementation &gt;::async_monitor </td>
          <td>(</td>
          <td class="paramtype">implementation_type &amp;</td>          <td class="paramname"><span class="paramname"><em>impl</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Handler</td>          <td class="paramname"><span class="paramname"><em>handler</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel inline">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Non-blocking event monitor. </p>

</div>
</div>
<a id="a002f0e244b09735e1c8ec803ef871727" name="a002f0e244b09735e1c8ec803ef871727"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a002f0e244b09735e1c8ec803ef871727">&#9670;&#160;</a></span>async_monitor() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DirMonitorImplementation = dir_monitor_impl&gt; </div>
<div class="memtemplate">
template&lt;typename Handler&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="classboost_1_1asio_1_1basic__dir__monitor__service.html">boost::asio::basic_dir_monitor_service</a>&lt; DirMonitorImplementation &gt;::async_monitor </td>
          <td>(</td>
          <td class="paramtype">implementation_type &amp;</td>          <td class="paramname"><span class="paramname"><em>impl</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Handler</td>          <td class="paramname"><span class="paramname"><em>handler</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel inline">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Non-blocking event monitor. </p>

</div>
</div>
<a id="adf8513f3bfc91b8db3204fa1bfd5c372" name="adf8513f3bfc91b8db3204fa1bfd5c372"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adf8513f3bfc91b8db3204fa1bfd5c372">&#9670;&#160;</a></span>monitor() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DirMonitorImplementation = dir_monitor_impl&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structboost_1_1asio_1_1dir__monitor__event.html">dir_monitor_event</a> <a class="el" href="classboost_1_1asio_1_1basic__dir__monitor__service.html">boost::asio::basic_dir_monitor_service</a>&lt; DirMonitorImplementation &gt;::monitor </td>
          <td>(</td>
          <td class="paramtype">implementation_type &amp;</td>          <td class="paramname"><span class="paramname"><em>impl</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">boost::system::error_code &amp;</td>          <td class="paramname"><span class="paramname"><em>ec</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel inline">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Blocking event monitor. </p>

</div>
</div>
<a id="adf8513f3bfc91b8db3204fa1bfd5c372" name="adf8513f3bfc91b8db3204fa1bfd5c372"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adf8513f3bfc91b8db3204fa1bfd5c372">&#9670;&#160;</a></span>monitor() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DirMonitorImplementation = dir_monitor_impl&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structboost_1_1asio_1_1dir__monitor__event.html">dir_monitor_event</a> <a class="el" href="classboost_1_1asio_1_1basic__dir__monitor__service.html">boost::asio::basic_dir_monitor_service</a>&lt; DirMonitorImplementation &gt;::monitor </td>
          <td>(</td>
          <td class="paramtype">implementation_type &amp;</td>          <td class="paramname"><span class="paramname"><em>impl</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">boost::system::error_code &amp;</td>          <td class="paramname"><span class="paramname"><em>ec</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel inline">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Blocking event monitor. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>common/util/include/dir_monitor/fsevents/<a class="el" href="fsevents_2basic__dir__monitor__service_8hpp_source.html">basic_dir_monitor_service.hpp</a></li>
<li>common/util/include/dir_monitor/inotify/<a class="el" href="inotify_2basic__dir__monitor__service_8hpp_source.html">basic_dir_monitor_service.hpp</a></li>
<li>common/util/include/dir_monitor/kqueue/<a class="el" href="kqueue_2basic__dir__monitor__service_8hpp_source.html">basic_dir_monitor_service.hpp</a></li>
<li>common/util/include/dir_monitor/windows/<a class="el" href="windows_2basic__dir__monitor__service_8hpp_source.html">basic_dir_monitor_service.hpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><b>boost</b></li><li class="navelem"><b>asio</b></li><li class="navelem"><a class="el" href="classboost_1_1asio_1_1basic__dir__monitor__service.html">basic_dir_monitor_service</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
