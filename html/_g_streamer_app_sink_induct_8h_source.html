<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: common/streaming/BpInduct/include/GStreamerAppSinkInduct.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('_g_streamer_app_sink_induct_8h_source.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">GStreamerAppSinkInduct.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_g_streamer_app_sink_induct_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span> </div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="preprocessor">#include &lt;gst/gst.h&gt;</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="preprocessor">#include &lt;gst/app/gstappsrc.h&gt;</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="preprocessor">#include &lt;gst/app/gstappsink.h&gt;</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span> </div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="preprocessor">#include &lt;boost/asio.hpp&gt;</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="preprocessor">#include &lt;boost/process.hpp&gt;</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span><span class="preprocessor">#include &lt;boost/smart_ptr/make_unique.hpp&gt;</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="preprocessor">#include &lt;boost/thread/thread.hpp&gt;</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span> </div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="preprocessor">#include &quot;<a class="code" href="_dtn_util_8h.html">DtnUtil.h</a>&quot;</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="preprocessor">#include &quot;<a class="code" href="_dtn_rtp_frame_8h.html">DtnRtpFrame.h</a>&quot;</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="preprocessor">#include &quot;<a class="code" href="_padded_vector_uint8_8h.html">PaddedVectorUint8.h</a>&quot;</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="preprocessor">#include &quot;streaming_lib_export.h&quot;</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span> </div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span><span class="keyword">typedef</span> boost::function&lt;void(padded_vector_uint8_t &amp; wholeBundleVec)&gt; WholeBundleReadyCallback_t;</div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span> </div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span> </div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span> </div>
<div class="foldopen" id="foldopen00032" data-start="{" data-end="};">
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno"><a class="line" href="class_g_streamer_app_sink_induct.html">   32</a></span><span class="keyword">class </span>GStreamerAppSinkInduct</div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span>{</div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="keyword">public</span>:</div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span> </div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span>    STREAMING_LIB_EXPORT GStreamerAppSinkInduct(std::string fileToStream);</div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span>    STREAMING_LIB_EXPORT ~GStreamerAppSinkInduct();</div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span>    STREAMING_LIB_EXPORT <span class="keyword">static</span> <span class="keywordtype">void</span> SetCallbackFunction(<span class="keyword">const</span> WholeBundleReadyCallback_t&amp; wholeBundleReadyCallback);</div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span>    std::string m_fileToStream;</div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span> </div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span>    std::unique_ptr&lt;boost::thread&gt; m_busMonitoringThread;</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span> </div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span>    STREAMING_LIB_NO_EXPORT <span class="keywordtype">void</span> OnBusMessages();</div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span>    <span class="comment">// setup functions</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span>    STREAMING_LIB_NO_EXPORT <span class="keywordtype">int</span> CreateElements();</div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span>    STREAMING_LIB_NO_EXPORT <span class="keywordtype">int</span> BuildPipeline();</div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span>    STREAMING_LIB_NO_EXPORT <span class="keywordtype">int</span> StartPlaying();</div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span> </div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span>    std::atomic&lt;bool&gt; m_running;</div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span> </div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span>    <span class="comment">// members</span></div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span>    GstBus *m_bus;</div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span>    GstMessage *m_gstMsg;</div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span>    </div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span>    GstElement *m_pipeline;</div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span>    GstElement *m_filesrc;</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span>    GstElement *m_qtdemux;</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span>    GstElement *m_h264parse;</div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span>    GstElement *m_h264timestamper;</div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span>    GstElement *m_rtph264pay;</div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span>    GstElement *m_appsink;</div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>    GstElement *m_progressreport;</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span>};</div>
</div>
<div class="ttc" id="a_dtn_rtp_frame_8h_html"><div class="ttname"><a href="_dtn_rtp_frame_8h.html">DtnRtpFrame.h</a></div></div>
<div class="ttc" id="a_dtn_util_8h_html"><div class="ttname"><a href="_dtn_util_8h.html">DtnUtil.h</a></div></div>
<div class="ttc" id="a_padded_vector_uint8_8h_html"><div class="ttname"><a href="_padded_vector_uint8_8h.html">PaddedVectorUint8.h</a></div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_bdd9a5d540de89e9fe90efdfc6973a4f.html">common</a></li><li class="navelem"><a class="el" href="dir_611ff6f9ae1e51cf5934afc0789a7cd0.html">streaming</a></li><li class="navelem"><a class="el" href="dir_80b2f01c7d220fbb02f205fe458e1c4a.html">BpInduct</a></li><li class="navelem"><a class="el" href="dir_eae185f3a6ee2dc40113ff76044a9274.html">include</a></li><li class="navelem"><a class="el" href="_g_streamer_app_sink_induct_8h.html">GStreamerAppSinkInduct.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
