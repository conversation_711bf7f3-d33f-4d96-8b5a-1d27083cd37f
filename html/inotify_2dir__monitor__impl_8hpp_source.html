<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: common/util/include/dir_monitor/inotify/dir_monitor_impl.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('inotify_2dir__monitor__impl_8hpp_source.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">dir_monitor_impl.hpp</div></div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">//</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment">// Copyright (c) 2008, 2009 Boris Schaeling &lt;<EMAIL>&gt;</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment">//</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment">// Distributed under the Boost Software License, Version 1.0. (See accompanying</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment">// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment">//</span></div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="preprocessor">#pragma once</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span> </div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="preprocessor">#include &lt;boost/asio.hpp&gt;</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="preprocessor">#include &lt;boost/bind/bind.hpp&gt;</span> <span class="comment">//don&#39;t include &lt;boost/bind.hpp&gt; to force using boost::placeholders::_1 instead of just _1</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="preprocessor">#include &lt;boost/bimap.hpp&gt;</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="preprocessor">#include &lt;boost/filesystem/path.hpp&gt;</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="preprocessor">#include &lt;boost/version.hpp&gt;</span></div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="preprocessor">#if (BOOST_VERSION &gt;= 107200)</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="preprocessor">#include &lt;boost/filesystem/directory.hpp&gt;</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="preprocessor">#include &lt;boost/filesystem/operations.hpp&gt;</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="preprocessor">#include &lt;boost/system/error_code.hpp&gt;</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="preprocessor">#include &lt;boost/system/system_error.hpp&gt;</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span> </div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="preprocessor">#include &lt;array&gt;</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span><span class="preprocessor">#include &lt;condition_variable&gt;</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="preprocessor">#include &lt;deque&gt;</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="preprocessor">#include &lt;memory&gt;</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="preprocessor">#include &lt;mutex&gt;</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span><span class="preprocessor">#include &lt;thread&gt;</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span> </div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span><span class="preprocessor">#include &lt;sys/inotify.h&gt;</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="preprocessor">#include &lt;errno.h&gt;</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span> </div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="keyword">namespace </span>boost {</div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="keyword">namespace </span>asio {</div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span> </div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span><span class="keyword">class </span><a class="code hl_class" href="classboost_1_1asio_1_1dir__monitor__impl.html">dir_monitor_impl</a></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span>{</div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span><span class="keyword">public</span>:</div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span>    dir_monitor_impl()</div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span>        : fd_(init_fd()),</div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span>        run_(true),</div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span>        inotify_work_(new boost::asio::io_service::work(inotify_io_service_)),</div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span>        inotify_work_thread_(boost::bind(&amp;boost::asio::io_service::run, &amp;inotify_io_service_)),</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span>        stream_descriptor_(new boost::asio::posix::stream_descriptor(inotify_io_service_, fd_))</div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span>    {</div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span>    }</div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span> </div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span>    <span class="keywordtype">void</span> add_directory(<span class="keyword">const</span> std::string &amp;dirname)</div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span>    {</div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span>        <span class="keywordtype">int</span> wd = inotify_add_watch(fd_, dirname.c_str(), IN_CREATE | IN_DELETE | IN_MODIFY | IN_MOVED_FROM | IN_MOVED_TO);</div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span>        <span class="keywordflow">if</span> (wd == -1)</div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span>        {</div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span><span class="comment">//As of Boost v1.66.0, get_system_category was deprecated with language: &quot;Boost.System deprecates the old names, but will provide them when the macro BOOST_SYSTEM_ENABLE_DEPRECATED is defined.&quot;</span></div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span><span class="comment">//Prior versions &lt;= 1.65.1 it said: &quot;Boost.System deprecates the old names, but continues to provide them unless macro BOOST_SYSTEM_NO_DEPRECATED is defined.&quot;</span></div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span><span class="preprocessor">#if (BOOST_VERSION &lt; 106600)</span></div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span>            boost::system::system_error e(boost::system::error_code(errno, boost::system::get_system_category()), <span class="stringliteral">&quot;boost::asio::dir_monitor_impl::add_directory: inotify_add_watch failed&quot;</span>);</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span>            boost::system::system_error e(boost::system::error_code(errno, boost::system::system_category()), <span class="stringliteral">&quot;boost::asio::dir_monitor_impl::add_directory: inotify_add_watch failed&quot;</span>);</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span>            boost::throw_exception(e);</div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span>        }</div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span> </div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span>        std::unique_lock&lt;std::mutex&gt; lock(watch_descriptors_mutex_);</div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>        watch_descriptors_.insert(watch_descriptors_t::value_type(wd, dirname));</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span>        lock.unlock();</div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span>        check_sub_directory(dirname, <span class="keyword">true</span>);</div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span>    }</div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span> </div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span>    <span class="keywordtype">void</span> remove_directory(<span class="keyword">const</span> std::string &amp;dirname)</div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span>    {</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span>        std::unique_lock&lt;std::mutex&gt; lock(watch_descriptors_mutex_);</div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span>        watch_descriptors_t::right_map::iterator it = watch_descriptors_.right.find(dirname);</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span>        <span class="keywordflow">if</span> (it != watch_descriptors_.right.end())</div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span>        {</div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span>            inotify_rm_watch(fd_, it-&gt;second);</div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span>            watch_descriptors_.right.erase(it);</div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span>            lock.unlock();</div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span>            check_sub_directory(dirname, <span class="keyword">false</span>);</div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span>        }</div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span>    }</div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span> </div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span>    <span class="keywordtype">void</span> check_sub_directory(<span class="keyword">const</span> std::string &amp;dirname, <span class="keywordtype">bool</span> add_sub_directory)</div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span>    {</div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span>        boost::filesystem::directory_iterator end;</div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span>        <span class="keywordflow">for</span> (boost::filesystem::directory_iterator iter(dirname); iter != end; ++iter) {</div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span>            <span class="keywordflow">if</span> (boost::filesystem::is_directory(*iter)) {</div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span>                <span class="keywordflow">if</span> (add_sub_directory) {</div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span>                    <span class="keywordflow">try</span> {</div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span>                        add_directory((*iter).path().string());</div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span>                    } <span class="keywordflow">catch</span> (<span class="keyword">const</span> std::exception&amp;) {</div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>                        <span class="keywordflow">continue</span>;</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span>                    }</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span>                } <span class="keywordflow">else</span> {</div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span>                    remove_directory((*iter).path().string());</div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span>                }</div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span>            }</div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span>        }</div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span>    }</div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span> </div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span>    <span class="keywordtype">void</span> destroy()</div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span>    {</div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span>        inotify_work_.reset();</div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span>        inotify_io_service_.stop();</div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span>        inotify_work_thread_.join();</div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno">  104</span>        stream_descriptor_.reset();</div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span> </div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span>        std::unique_lock&lt;std::mutex&gt; lock(events_mutex_);</div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span>        run_ = <span class="keyword">false</span>;</div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span>        events_cond_.notify_all();</div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span>    }</div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span> </div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span>    dir_monitor_event popfront_event(boost::system::error_code &amp;ec)</div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span>    {</div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span>        std::unique_lock&lt;std::mutex&gt; lock(events_mutex_);</div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span>        events_cond_.wait(lock, [&amp;]() { <span class="keywordflow">return</span> !(run_ &amp;&amp; events_.empty()); });</div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span>        </div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span>        dir_monitor_event ev;</div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span>        ec = boost::system::error_code();</div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno">  118</span>        <span class="keywordflow">if</span> (!run_)</div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno">  119</span>            ec = boost::asio::error::operation_aborted;</div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno">  120</span>        <span class="keywordflow">else</span> <span class="keywordflow">if</span> (!events_.empty())</div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span>        {</div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span>            ev = events_.front();</div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno">  123</span>            events_.pop_front();</div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span>        }</div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span>            </div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span>        <span class="keywordflow">return</span> ev;</div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno">  127</span>    }</div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span> </div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno">  129</span>    <span class="keywordtype">void</span> pushback_event(dir_monitor_event ev)</div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno">  130</span>    {</div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span>        std::unique_lock&lt;std::mutex&gt; lock(events_mutex_);</div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno">  132</span>        <span class="keywordflow">if</span> (run_)</div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno">  133</span>        {</div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno">  134</span>            events_.push_back(ev);</div>
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno">  135</span>            events_cond_.notify_all();</div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span>        }</div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span>    }</div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span> </div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span>    <span class="keywordtype">int</span> init_fd()</div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span>    {</div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span>        <span class="keywordtype">int</span> fd = inotify_init();</div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span>        <span class="keywordflow">if</span> (fd == -1)</div>
<div class="line"><a id="l00144" name="l00144"></a><span class="lineno">  144</span>        {</div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span><span class="comment">//As of Boost v1.66.0, get_system_category was deprecated with language: &quot;Boost.System deprecates the old names, but will provide them when the macro BOOST_SYSTEM_ENABLE_DEPRECATED is defined.&quot;</span></div>
<div class="line"><a id="l00146" name="l00146"></a><span class="lineno">  146</span><span class="comment">//Prior versions &lt;= 1.65.1 it said: &quot;Boost.System deprecates the old names, but continues to provide them unless macro BOOST_SYSTEM_NO_DEPRECATED is defined.&quot;</span></div>
<div class="line"><a id="l00147" name="l00147"></a><span class="lineno">  147</span><span class="preprocessor">#if (BOOST_VERSION &lt; 106600)</span></div>
<div class="line"><a id="l00148" name="l00148"></a><span class="lineno">  148</span>            boost::system::system_error e(boost::system::error_code(errno, boost::system::get_system_category()), <span class="stringliteral">&quot;boost::asio::dir_monitor_impl::init_fd: init_inotify failed&quot;</span>);</div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno">  149</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00150" name="l00150"></a><span class="lineno">  150</span>            boost::system::system_error e(boost::system::error_code(errno, boost::system::system_category()), <span class="stringliteral">&quot;boost::asio::dir_monitor_impl::init_fd: init_inotify failed&quot;</span>);</div>
<div class="line"><a id="l00151" name="l00151"></a><span class="lineno">  151</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00152" name="l00152"></a><span class="lineno">  152</span>            boost::throw_exception(e);</div>
<div class="line"><a id="l00153" name="l00153"></a><span class="lineno">  153</span>        }</div>
<div class="line"><a id="l00154" name="l00154"></a><span class="lineno">  154</span>        <span class="keywordflow">return</span> fd;</div>
<div class="line"><a id="l00155" name="l00155"></a><span class="lineno">  155</span>    }</div>
<div class="line"><a id="l00156" name="l00156"></a><span class="lineno">  156</span> </div>
<div class="line"><a id="l00157" name="l00157"></a><span class="lineno">  157</span><span class="keyword">public</span>:</div>
<div class="line"><a id="l00158" name="l00158"></a><span class="lineno">  158</span>    <span class="keywordtype">void</span> begin_read()</div>
<div class="line"><a id="l00159" name="l00159"></a><span class="lineno">  159</span>    {</div>
<div class="line"><a id="l00160" name="l00160"></a><span class="lineno">  160</span>        stream_descriptor_-&gt;async_read_some(boost::asio::buffer(read_buffer_),</div>
<div class="line"><a id="l00161" name="l00161"></a><span class="lineno">  161</span>            boost::bind(&amp;dir_monitor_impl::end_read, <span class="keyword">this</span>,</div>
<div class="line"><a id="l00162" name="l00162"></a><span class="lineno">  162</span>            boost::asio::placeholders::error, boost::asio::placeholders::bytes_transferred));</div>
<div class="line"><a id="l00163" name="l00163"></a><span class="lineno">  163</span>    }</div>
<div class="line"><a id="l00164" name="l00164"></a><span class="lineno">  164</span> </div>
<div class="line"><a id="l00165" name="l00165"></a><span class="lineno">  165</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00166" name="l00166"></a><span class="lineno">  166</span>    <span class="keywordtype">void</span> end_read(<span class="keyword">const</span> boost::system::error_code &amp;ec, std::size_t bytes_transferred)</div>
<div class="line"><a id="l00167" name="l00167"></a><span class="lineno">  167</span>    {</div>
<div class="line"><a id="l00168" name="l00168"></a><span class="lineno">  168</span>        <span class="keywordflow">if</span> (!ec)</div>
<div class="line"><a id="l00169" name="l00169"></a><span class="lineno">  169</span>        {</div>
<div class="line"><a id="l00170" name="l00170"></a><span class="lineno">  170</span>            pending_read_buffer_ += std::string(read_buffer_.data(), bytes_transferred);</div>
<div class="line"><a id="l00171" name="l00171"></a><span class="lineno">  171</span>            <span class="keywordflow">while</span> (pending_read_buffer_.size() &gt; <span class="keyword">sizeof</span>(inotify_event))</div>
<div class="line"><a id="l00172" name="l00172"></a><span class="lineno">  172</span>            {</div>
<div class="line"><a id="l00173" name="l00173"></a><span class="lineno">  173</span>                <span class="keyword">const</span> inotify_event *iev = <span class="keyword">reinterpret_cast&lt;</span><span class="keyword">const </span>inotify_event*<span class="keyword">&gt;</span>(pending_read_buffer_.data());</div>
<div class="line"><a id="l00174" name="l00174"></a><span class="lineno">  174</span>                <a class="code hl_enumeration" href="structboost_1_1asio_1_1dir__monitor__event.html#a2dddf073f2ebe99d976c6a844163e9ca">dir_monitor_event::event_type</a> type = dir_monitor_event::null;</div>
<div class="line"><a id="l00175" name="l00175"></a><span class="lineno">  175</span>                <span class="keywordflow">switch</span> (iev-&gt;mask)</div>
<div class="line"><a id="l00176" name="l00176"></a><span class="lineno">  176</span>                {</div>
<div class="line"><a id="l00177" name="l00177"></a><span class="lineno">  177</span>                <span class="keywordflow">case</span> IN_CREATE: type = dir_monitor_event::added; <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l00178" name="l00178"></a><span class="lineno">  178</span>                <span class="keywordflow">case</span> IN_DELETE: type = dir_monitor_event::removed; <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l00179" name="l00179"></a><span class="lineno">  179</span>                <span class="keywordflow">case</span> IN_MODIFY: type = dir_monitor_event::modified; <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l00180" name="l00180"></a><span class="lineno">  180</span>                <span class="keywordflow">case</span> IN_MOVED_FROM: type = dir_monitor_event::renamed_old_name; <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l00181" name="l00181"></a><span class="lineno">  181</span>                <span class="keywordflow">case</span> IN_MOVED_TO: type = dir_monitor_event::renamed_new_name; <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l00182" name="l00182"></a><span class="lineno">  182</span>                <span class="keywordflow">case</span> IN_CREATE | IN_ISDIR:</div>
<div class="line"><a id="l00183" name="l00183"></a><span class="lineno">  183</span>                    {</div>
<div class="line"><a id="l00184" name="l00184"></a><span class="lineno">  184</span>                        type = dir_monitor_event::added;</div>
<div class="line"><a id="l00185" name="l00185"></a><span class="lineno">  185</span>                        add_directory(get_dirname(iev-&gt;wd) + <span class="stringliteral">&quot;/&quot;</span> + iev-&gt;name);</div>
<div class="line"><a id="l00186" name="l00186"></a><span class="lineno">  186</span>                        <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l00187" name="l00187"></a><span class="lineno">  187</span>                    }</div>
<div class="line"><a id="l00188" name="l00188"></a><span class="lineno">  188</span>                }</div>
<div class="line"><a id="l00189" name="l00189"></a><span class="lineno">  189</span>                pushback_event(dir_monitor_event(boost::filesystem::path(get_dirname(iev-&gt;wd)) / iev-&gt;name, type));</div>
<div class="line"><a id="l00190" name="l00190"></a><span class="lineno">  190</span>                pending_read_buffer_.erase(0, <span class="keyword">sizeof</span>(inotify_event) + iev-&gt;len);</div>
<div class="line"><a id="l00191" name="l00191"></a><span class="lineno">  191</span>            }</div>
<div class="line"><a id="l00192" name="l00192"></a><span class="lineno">  192</span> </div>
<div class="line"><a id="l00193" name="l00193"></a><span class="lineno">  193</span>            begin_read();</div>
<div class="line"><a id="l00194" name="l00194"></a><span class="lineno">  194</span>        }</div>
<div class="line"><a id="l00195" name="l00195"></a><span class="lineno">  195</span>        <span class="keywordflow">else</span> <span class="keywordflow">if</span> (ec != boost::asio::error::operation_aborted)</div>
<div class="line"><a id="l00196" name="l00196"></a><span class="lineno">  196</span>        {</div>
<div class="line"><a id="l00197" name="l00197"></a><span class="lineno">  197</span>            boost::system::system_error e(ec);</div>
<div class="line"><a id="l00198" name="l00198"></a><span class="lineno">  198</span>            boost::throw_exception(e);</div>
<div class="line"><a id="l00199" name="l00199"></a><span class="lineno">  199</span>        }</div>
<div class="line"><a id="l00200" name="l00200"></a><span class="lineno">  200</span>    }</div>
<div class="line"><a id="l00201" name="l00201"></a><span class="lineno">  201</span> </div>
<div class="line"><a id="l00202" name="l00202"></a><span class="lineno">  202</span>    std::string get_dirname(<span class="keywordtype">int</span> wd)</div>
<div class="line"><a id="l00203" name="l00203"></a><span class="lineno">  203</span>    {</div>
<div class="line"><a id="l00204" name="l00204"></a><span class="lineno">  204</span>        std::unique_lock&lt;std::mutex&gt; lock(watch_descriptors_mutex_);</div>
<div class="line"><a id="l00205" name="l00205"></a><span class="lineno">  205</span>        watch_descriptors_t::left_map::iterator it = watch_descriptors_.left.find(wd);</div>
<div class="line"><a id="l00206" name="l00206"></a><span class="lineno">  206</span>        <span class="keywordflow">return</span> it != watch_descriptors_.left.end() ? it-&gt;second : <span class="stringliteral">&quot;&quot;</span>;</div>
<div class="line"><a id="l00207" name="l00207"></a><span class="lineno">  207</span>    }</div>
<div class="line"><a id="l00208" name="l00208"></a><span class="lineno">  208</span> </div>
<div class="line"><a id="l00209" name="l00209"></a><span class="lineno">  209</span>    <span class="keywordtype">int</span> fd_;</div>
<div class="line"><a id="l00210" name="l00210"></a><span class="lineno">  210</span>    <span class="keywordtype">bool</span> run_;</div>
<div class="line"><a id="l00211" name="l00211"></a><span class="lineno">  211</span>    boost::asio::io_service inotify_io_service_;</div>
<div class="line"><a id="l00212" name="l00212"></a><span class="lineno">  212</span>    std::unique_ptr&lt;boost::asio::io_service::work&gt; inotify_work_;</div>
<div class="line"><a id="l00213" name="l00213"></a><span class="lineno">  213</span>    std::thread inotify_work_thread_;</div>
<div class="line"><a id="l00214" name="l00214"></a><span class="lineno">  214</span>    </div>
<div class="line"><a id="l00215" name="l00215"></a><span class="lineno">  215</span>    std::unique_ptr&lt;boost::asio::posix::stream_descriptor&gt; stream_descriptor_;</div>
<div class="line"><a id="l00216" name="l00216"></a><span class="lineno">  216</span>    std::array&lt;char, 4096&gt; read_buffer_;</div>
<div class="line"><a id="l00217" name="l00217"></a><span class="lineno">  217</span>    std::string pending_read_buffer_;</div>
<div class="line"><a id="l00218" name="l00218"></a><span class="lineno">  218</span>    std::mutex watch_descriptors_mutex_;</div>
<div class="line"><a id="l00219" name="l00219"></a><span class="lineno">  219</span>    <span class="keyword">typedef</span> boost::bimap&lt;int, std::string&gt; watch_descriptors_t;</div>
<div class="line"><a id="l00220" name="l00220"></a><span class="lineno">  220</span>    watch_descriptors_t watch_descriptors_;</div>
<div class="line"><a id="l00221" name="l00221"></a><span class="lineno">  221</span>    std::mutex events_mutex_;</div>
<div class="line"><a id="l00222" name="l00222"></a><span class="lineno">  222</span>    std::condition_variable events_cond_;</div>
<div class="line"><a id="l00223" name="l00223"></a><span class="lineno">  223</span>    std::deque&lt;dir_monitor_event&gt; events_;</div>
<div class="line"><a id="l00224" name="l00224"></a><span class="lineno">  224</span>};</div>
<div class="line"><a id="l00225" name="l00225"></a><span class="lineno">  225</span> </div>
<div class="line"><a id="l00226" name="l00226"></a><span class="lineno">  226</span>}</div>
<div class="line"><a id="l00227" name="l00227"></a><span class="lineno">  227</span>}</div>
<div class="line"><a id="l00228" name="l00228"></a><span class="lineno">  228</span> </div>
<div class="ttc" id="aclassboost_1_1asio_1_1dir__monitor__impl_html"><div class="ttname"><a href="classboost_1_1asio_1_1dir__monitor__impl.html">boost::asio::dir_monitor_impl</a></div><div class="ttdef"><b>Definition</b> dir_monitor_impl.hpp:30</div></div>
<div class="ttc" id="astructboost_1_1asio_1_1dir__monitor__event_html_a2dddf073f2ebe99d976c6a844163e9ca"><div class="ttname"><a href="structboost_1_1asio_1_1dir__monitor__event.html#a2dddf073f2ebe99d976c6a844163e9ca">boost::asio::dir_monitor_event::event_type</a></div><div class="ttdeci">event_type</div><div class="ttdef"><b>Definition</b> basic_dir_monitor.hpp:20</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_bdd9a5d540de89e9fe90efdfc6973a4f.html">common</a></li><li class="navelem"><a class="el" href="dir_f7cbf1dfc4945ce4e61a395e67c06604.html">util</a></li><li class="navelem"><a class="el" href="dir_d8cd0b0498ce5da3d465b26b60aa31cc.html">include</a></li><li class="navelem"><a class="el" href="dir_27b3fea92ac7b94d089c1e442e430ee9.html">dir_monitor</a></li><li class="navelem"><a class="el" href="dir_cb0bc9f7160f7d3fe2457d5d24759188.html">inotify</a></li><li class="navelem"><b>dir_monitor_impl.hpp</b></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
