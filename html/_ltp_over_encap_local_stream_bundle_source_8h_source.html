<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: common/ltp/include/LtpOverEncapLocalStreamBundleSource.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('_ltp_over_encap_local_stream_bundle_source_8h_source.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">LtpOverEncapLocalStreamBundleSource.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_ltp_over_encap_local_stream_bundle_source_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span> </div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span><span class="preprocessor">#ifndef _LTP_OVER_ENCAP_LOCAL_STREAM_BUNDLE_SOURCE_H</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="preprocessor">#define _LTP_OVER_ENCAP_LOCAL_STREAM_BUNDLE_SOURCE_H 1</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span> </div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="preprocessor">#include &quot;<a class="code" href="_ltp_bundle_source_8h.html">LtpBundleSource.h</a>&quot;</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="preprocessor">#include &quot;<a class="code" href="_ltp_encap_local_stream_engine_8h.html">LtpEncapLocalStreamEngine.h</a>&quot;</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span> </div>
<div class="foldopen" id="foldopen00028" data-start="{" data-end="};">
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno"><a class="line" href="class_ltp_over_encap_local_stream_bundle_source.html">   28</a></span><span class="keyword">class </span>LtpOverEncapLocalStreamBundleSource : <span class="keyword">public</span> LtpBundleSource {</div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span>    LtpOverEncapLocalStreamBundleSource() = <span class="keyword">delete</span>;</div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span><span class="keyword">public</span>:</div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span>    LTP_LIB_EXPORT LtpOverEncapLocalStreamBundleSource(<span class="keyword">const</span> <a class="code hl_struct" href="struct_ltp_engine_config.html">LtpEngineConfig</a>&amp; ltpTxCfg);</div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span>    LTP_LIB_EXPORT <span class="keyword">virtual</span> ~LtpOverEncapLocalStreamBundleSource() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="keyword">protected</span>:</div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span>    LTP_LIB_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> ReadyToForward() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span>    LTP_LIB_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetLtpEnginePtr() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span>    LTP_LIB_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">void</span> GetTransportLayerSpecificTelem(<a class="code hl_struct" href="struct_ltp_outduct_telemetry__t.html">LtpOutductTelemetry_t</a>&amp; telem) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span> </div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="keyword">private</span>:</div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span>    <span class="comment">//ltp vars</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span>    std::unique_ptr&lt;LtpEncapLocalStreamEngine&gt; m_ltpEncapLocalStreamEnginePtr;</div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span>};</div>
</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span> </div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span> </div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span> </div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span><span class="preprocessor">#endif </span><span class="comment">//_LTP_OVER_ENCAP_LOCAL_STREAM_BUNDLE_SOURCE_H</span></div>
<div class="ttc" id="a_ltp_bundle_source_8h_html"><div class="ttname"><a href="_ltp_bundle_source_8h.html">LtpBundleSource.h</a></div></div>
<div class="ttc" id="a_ltp_encap_local_stream_engine_8h_html"><div class="ttname"><a href="_ltp_encap_local_stream_engine_8h.html">LtpEncapLocalStreamEngine.h</a></div></div>
<div class="ttc" id="astruct_ltp_engine_config_html"><div class="ttname"><a href="struct_ltp_engine_config.html">LtpEngineConfig</a></div><div class="ttdef"><b>Definition</b> LtpEngineConfig.h:29</div></div>
<div class="ttc" id="astruct_ltp_outduct_telemetry__t_html"><div class="ttname"><a href="struct_ltp_outduct_telemetry__t.html">LtpOutductTelemetry_t</a></div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.h:356</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_bdd9a5d540de89e9fe90efdfc6973a4f.html">common</a></li><li class="navelem"><a class="el" href="dir_a20834912dc4d1000db8cf473036c40b.html">ltp</a></li><li class="navelem"><a class="el" href="dir_c2a9dec5bc2fb8bc646fc7ea53c474f3.html">include</a></li><li class="navelem"><a class="el" href="_ltp_over_encap_local_stream_bundle_source_8h.html">LtpOverEncapLocalStreamBundleSource.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
