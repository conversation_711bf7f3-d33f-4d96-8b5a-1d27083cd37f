<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: Bpv7AdministrativeRecordContentBibePduMessage Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('struct_bpv7_administrative_record_content_bibe_pdu_message.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="struct_bpv7_administrative_record_content_bibe_pdu_message-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Bpv7AdministrativeRecordContentBibePduMessage Struct Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for Bpv7AdministrativeRecordContentBibePduMessage:</div>
<div class="dyncontent">
 <div class="center">
  <img src="struct_bpv7_administrative_record_content_bibe_pdu_message.png" usemap="#Bpv7AdministrativeRecordContentBibePduMessage_map" alt=""/>
  <map id="Bpv7AdministrativeRecordContentBibePduMessage_map" name="Bpv7AdministrativeRecordContentBibePduMessage_map">
<area href="struct_bpv7_administrative_record_content_base.html" alt="Bpv7AdministrativeRecordContentBase" shape="rect" coords="0,0,302,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a33d4b853b6005ced92a45939b8acb15e" id="r_a33d4b853b6005ced92a45939b8acb15e"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a33d4b853b6005ced92a45939b8acb15e">SerializeBpv7</a> (uint8_t *serialization, uint64_t bufferSize) override</td></tr>
<tr class="separator:a33d4b853b6005ced92a45939b8acb15e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae768987b205db0e91356d5d9456a9caf" id="r_ae768987b205db0e91356d5d9456a9caf"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#ae768987b205db0e91356d5d9456a9caf">GetSerializationSize</a> () const override</td></tr>
<tr class="separator:ae768987b205db0e91356d5d9456a9caf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a099e57685267e3e8071f731eb2feddc7" id="r_a099e57685267e3e8071f731eb2feddc7"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a099e57685267e3e8071f731eb2feddc7">DeserializeBpv7</a> (uint8_t *serialization, uint64_t &amp;numBytesTakenToDecode, uint64_t bufferSize) override</td></tr>
<tr class="separator:a099e57685267e3e8071f731eb2feddc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15a4f8a84079f506f508c96063d620c4" id="r_a15a4f8a84079f506f508c96063d620c4"><td class="memItemLeft" align="right" valign="top">virtual BPCODEC_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a15a4f8a84079f506f508c96063d620c4">IsEqual</a> (const <a class="el" href="struct_bpv7_administrative_record_content_base.html">Bpv7AdministrativeRecordContentBase</a> *otherPtr) const override</td></tr>
<tr class="separator:a15a4f8a84079f506f508c96063d620c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a2d06f9b9572368418fbbfcd1c9cdaa8a" id="r_a2d06f9b9572368418fbbfcd1c9cdaa8a"><td class="memItemLeft" align="right" valign="top"><a id="a2d06f9b9572368418fbbfcd1c9cdaa8a" name="a2d06f9b9572368418fbbfcd1c9cdaa8a"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_transmissionId</b></td></tr>
<tr class="separator:a2d06f9b9572368418fbbfcd1c9cdaa8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa0a19277550da3292646f88db09837d5" id="r_aa0a19277550da3292646f88db09837d5"><td class="memItemLeft" align="right" valign="top"><a id="aa0a19277550da3292646f88db09837d5" name="aa0a19277550da3292646f88db09837d5"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_custodyRetransmissionTime</b></td></tr>
<tr class="separator:aa0a19277550da3292646f88db09837d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a25e3103a74d8bd9ffea300ebb9ff7cf5" id="r_a25e3103a74d8bd9ffea300ebb9ff7cf5"><td class="memItemLeft" align="right" valign="top"><a id="a25e3103a74d8bd9ffea300ebb9ff7cf5" name="a25e3103a74d8bd9ffea300ebb9ff7cf5"></a>
uint8_t *&#160;</td><td class="memItemRight" valign="bottom"><b>m_encapsulatedBundlePtr</b></td></tr>
<tr class="separator:a25e3103a74d8bd9ffea300ebb9ff7cf5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73a5adf03fa642137a0448cef56371fc" id="r_a73a5adf03fa642137a0448cef56371fc"><td class="memItemLeft" align="right" valign="top"><a id="a73a5adf03fa642137a0448cef56371fc" name="a73a5adf03fa642137a0448cef56371fc"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_encapsulatedBundleLength</b></td></tr>
<tr class="separator:a73a5adf03fa642137a0448cef56371fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af443dedd00ec91dea0951f60083d74b5" id="r_af443dedd00ec91dea0951f60083d74b5"><td class="memItemLeft" align="right" valign="top"><a id="af443dedd00ec91dea0951f60083d74b5" name="af443dedd00ec91dea0951f60083d74b5"></a>
std::vector&lt; uint8_t &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>m_temporaryEncapsulatedBundleStorage</b></td></tr>
<tr class="separator:af443dedd00ec91dea0951f60083d74b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a099e57685267e3e8071f731eb2feddc7" name="a099e57685267e3e8071f731eb2feddc7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a099e57685267e3e8071f731eb2feddc7">&#9670;&#160;</a></span>DeserializeBpv7()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Bpv7AdministrativeRecordContentBibePduMessage::DeserializeBpv7 </td>
          <td>(</td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>serialization</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t &amp;</td>          <td class="paramname"><span class="paramname"><em>numBytesTakenToDecode</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t</td>          <td class="paramname"><span class="paramname"><em>bufferSize</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Implements <a class="el" href="struct_bpv7_administrative_record_content_base.html">Bpv7AdministrativeRecordContentBase</a>.</p>

</div>
</div>
<a id="ae768987b205db0e91356d5d9456a9caf" name="ae768987b205db0e91356d5d9456a9caf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae768987b205db0e91356d5d9456a9caf">&#9670;&#160;</a></span>GetSerializationSize()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t Bpv7AdministrativeRecordContentBibePduMessage::GetSerializationSize </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Implements <a class="el" href="struct_bpv7_administrative_record_content_base.html">Bpv7AdministrativeRecordContentBase</a>.</p>

</div>
</div>
<a id="a15a4f8a84079f506f508c96063d620c4" name="a15a4f8a84079f506f508c96063d620c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a15a4f8a84079f506f508c96063d620c4">&#9670;&#160;</a></span>IsEqual()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Bpv7AdministrativeRecordContentBibePduMessage::IsEqual </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="struct_bpv7_administrative_record_content_base.html">Bpv7AdministrativeRecordContentBase</a> *</td>          <td class="paramname"><span class="paramname"><em>otherPtr</em></span></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Implements <a class="el" href="struct_bpv7_administrative_record_content_base.html">Bpv7AdministrativeRecordContentBase</a>.</p>

</div>
</div>
<a id="a33d4b853b6005ced92a45939b8acb15e" name="a33d4b853b6005ced92a45939b8acb15e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a33d4b853b6005ced92a45939b8acb15e">&#9670;&#160;</a></span>SerializeBpv7()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t Bpv7AdministrativeRecordContentBibePduMessage::SerializeBpv7 </td>
          <td>(</td>
          <td class="paramtype">uint8_t *</td>          <td class="paramname"><span class="paramname"><em>serialization</em></span>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t</td>          <td class="paramname"><span class="paramname"><em>bufferSize</em></span>&#160;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Implements <a class="el" href="struct_bpv7_administrative_record_content_base.html">Bpv7AdministrativeRecordContentBase</a>.</p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>common/bpcodec/include/codec/<a class="el" href="bpv7_8h_source.html">bpv7.h</a></li>
<li>common/bpcodec/src/codec/<a class="el" href="_bpv7_administrative_records_8cpp.html">Bpv7AdministrativeRecords.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="struct_bpv7_administrative_record_content_bibe_pdu_message.html">Bpv7AdministrativeRecordContentBibePduMessage</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
