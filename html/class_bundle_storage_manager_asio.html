<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: BundleStorageManagerAsio Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('class_bundle_storage_manager_asio.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_bundle_storage_manager_asio-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">BundleStorageManagerAsio Class Reference</div></div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for BundleStorageManagerAsio:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bundle_storage_manager_asio.png" usemap="#BundleStorageManagerAsio_map" alt=""/>
  <map id="BundleStorageManagerAsio_map" name="BundleStorageManagerAsio_map">
<area href="class_bundle_storage_manager_base.html" alt="BundleStorageManagerBase" shape="rect" coords="0,0,174,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a39d26cd7971e7ef5662dcdaf536fee87" id="r_a39d26cd7971e7ef5662dcdaf536fee87"><td class="memItemLeft" align="right" valign="top"><a id="a39d26cd7971e7ef5662dcdaf536fee87" name="a39d26cd7971e7ef5662dcdaf536fee87"></a>
STORAGE_LIB_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>BundleStorageManagerAsio</b> (const boost::filesystem::path &amp;jsonConfigFilePath)</td></tr>
<tr class="separator:a39d26cd7971e7ef5662dcdaf536fee87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5cc25c032e74d9c2728f1f4bac46876" id="r_af5cc25c032e74d9c2728f1f4bac46876"><td class="memItemLeft" align="right" valign="top"><a id="af5cc25c032e74d9c2728f1f4bac46876" name="af5cc25c032e74d9c2728f1f4bac46876"></a>
STORAGE_LIB_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>BundleStorageManagerAsio</b> (const StorageConfig_ptr &amp;storageConfigPtr)</td></tr>
<tr class="separator:af5cc25c032e74d9c2728f1f4bac46876"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a234a1c34dc1f34e84b8478483e33a1c4" id="r_a234a1c34dc1f34e84b8478483e33a1c4"><td class="memItemLeft" align="right" valign="top">virtual STORAGE_LIB_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="#a234a1c34dc1f34e84b8478483e33a1c4">Start</a> () override</td></tr>
<tr class="separator:a234a1c34dc1f34e84b8478483e33a1c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_class_bundle_storage_manager_base"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_methods_class_bundle_storage_manager_base')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="class_bundle_storage_manager_base.html">BundleStorageManagerBase</a></td></tr>
<tr class="memitem:a13bd2000287b95e8b646fb0b9b12e86f inherit pub_methods_class_bundle_storage_manager_base" id="r_a13bd2000287b95e8b646fb0b9b12e86f"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>Push</b> (<a class="el" href="struct_bundle_storage_manager_session___write_to_disk.html">BundleStorageManagerSession_WriteToDisk</a> &amp;session, const <a class="el" href="struct_primary_block.html">PrimaryBlock</a> &amp;bundlePrimaryBlock, const uint64_t bundleSizeBytes, const uint64_t payloadSizeBytes, <a class="el" href="structcbhe__eid__t.html">cbhe_eid_t</a> *bundleEidMaskPtr=NULL)</td></tr>
<tr class="separator:a13bd2000287b95e8b646fb0b9b12e86f inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac5bc1c40a5bdbfb4b1850c2c6eec8297 inherit pub_methods_class_bundle_storage_manager_base" id="r_ac5bc1c40a5bdbfb4b1850c2c6eec8297"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT int&#160;</td><td class="memItemRight" valign="bottom"><b>PushSegment</b> (<a class="el" href="struct_bundle_storage_manager_session___write_to_disk.html">BundleStorageManagerSession_WriteToDisk</a> &amp;session, const <a class="el" href="struct_primary_block.html">PrimaryBlock</a> &amp;bundlePrimaryBlock, const uint64_t custodyId, const uint8_t *buf, std::size_t size)</td></tr>
<tr class="separator:ac5bc1c40a5bdbfb4b1850c2c6eec8297 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2df5fe10713cf31c7277343bf4b5f00 inherit pub_methods_class_bundle_storage_manager_base" id="r_ac2df5fe10713cf31c7277343bf4b5f00"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>PushAllSegments</b> (<a class="el" href="struct_bundle_storage_manager_session___write_to_disk.html">BundleStorageManagerSession_WriteToDisk</a> &amp;session, const <a class="el" href="struct_primary_block.html">PrimaryBlock</a> &amp;bundlePrimaryBlock, const uint64_t custodyId, const uint8_t *allData, const std::size_t allDataSize)</td></tr>
<tr class="separator:ac2df5fe10713cf31c7277343bf4b5f00 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2b7303bfc7d64b294a6e5b14b0203a1 inherit pub_methods_class_bundle_storage_manager_base" id="r_ad2b7303bfc7d64b294a6e5b14b0203a1"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>PopTop</b> (<a class="el" href="struct_bundle_storage_manager_session___read_from_disk.html">BundleStorageManagerSession_ReadFromDisk</a> &amp;session, const std::vector&lt; <a class="el" href="structcbhe__eid__t.html">cbhe_eid_t</a> &gt; &amp;availableDestinationEids)</td></tr>
<tr class="separator:ad2b7303bfc7d64b294a6e5b14b0203a1 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd36cf387f86038cc3b0af29eab8016b inherit pub_methods_class_bundle_storage_manager_base" id="r_acd36cf387f86038cc3b0af29eab8016b"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>PopTop</b> (<a class="el" href="struct_bundle_storage_manager_session___read_from_disk.html">BundleStorageManagerSession_ReadFromDisk</a> &amp;session, const std::vector&lt; uint64_t &gt; &amp;availableDestNodeIds)</td></tr>
<tr class="separator:acd36cf387f86038cc3b0af29eab8016b inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40b3c75247211df11230b558630912e7 inherit pub_methods_class_bundle_storage_manager_base" id="r_a40b3c75247211df11230b558630912e7"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>PopTop</b> (<a class="el" href="struct_bundle_storage_manager_session___read_from_disk.html">BundleStorageManagerSession_ReadFromDisk</a> &amp;session, const std::vector&lt; std::pair&lt; <a class="el" href="structcbhe__eid__t.html">cbhe_eid_t</a>, bool &gt; &gt; &amp;availableDests)</td></tr>
<tr class="separator:a40b3c75247211df11230b558630912e7 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a655545cf4f5d264d67cf86970bd8950e inherit pub_methods_class_bundle_storage_manager_base" id="r_a655545cf4f5d264d67cf86970bd8950e"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>ReturnTop</b> (<a class="el" href="struct_bundle_storage_manager_session___read_from_disk.html">BundleStorageManagerSession_ReadFromDisk</a> &amp;session)</td></tr>
<tr class="separator:a655545cf4f5d264d67cf86970bd8950e inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a549e78c4f659b9967f9873e72ad6ac2b inherit pub_methods_class_bundle_storage_manager_base" id="r_a549e78c4f659b9967f9873e72ad6ac2b"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>ReturnCustodyIdToAwaitingSend</b> (const uint64_t custodyId)</td></tr>
<tr class="separator:a549e78c4f659b9967f9873e72ad6ac2b inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affc84a95bc757fab6df68c5e714ae2f0 inherit pub_methods_class_bundle_storage_manager_base" id="r_affc84a95bc757fab6df68c5e714ae2f0"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT <a class="el" href="structcatalog__entry__t.html">catalog_entry_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><b>GetCatalogEntryPtrFromCustodyId</b> (const uint64_t custodyId)</td></tr>
<tr class="separator:affc84a95bc757fab6df68c5e714ae2f0 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac81e7c8ef2d19215d5037c2b47df1c8b inherit pub_methods_class_bundle_storage_manager_base" id="r_ac81e7c8ef2d19215d5037c2b47df1c8b"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT std::size_t&#160;</td><td class="memItemRight" valign="bottom"><b>TopSegment</b> (<a class="el" href="struct_bundle_storage_manager_session___read_from_disk.html">BundleStorageManagerSession_ReadFromDisk</a> &amp;session, void *buf)</td></tr>
<tr class="separator:ac81e7c8ef2d19215d5037c2b47df1c8b inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca67b7998c5d0749ccac923649e290c9 inherit pub_methods_class_bundle_storage_manager_base" id="r_aca67b7998c5d0749ccac923649e290c9"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>ReadFirstSegment</b> (<a class="el" href="struct_bundle_storage_manager_session___read_from_disk.html">BundleStorageManagerSession_ReadFromDisk</a> &amp;session, <a class="el" href="structcatalog__entry__t.html">catalog_entry_t</a> *enty, std::vector&lt; uint8_t &gt; &amp;buf)</td></tr>
<tr class="separator:aca67b7998c5d0749ccac923649e290c9 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae155bc1ef801cc07f836b17f39ccb887 inherit pub_methods_class_bundle_storage_manager_base" id="r_ae155bc1ef801cc07f836b17f39ccb887"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>ReadAllSegments</b> (<a class="el" href="struct_bundle_storage_manager_session___read_from_disk.html">BundleStorageManagerSession_ReadFromDisk</a> &amp;session, padded_vector_uint8_t &amp;buf)</td></tr>
<tr class="separator:ae155bc1ef801cc07f836b17f39ccb887 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5dd339b13c413f020c142c15cbdd9ed inherit pub_methods_class_bundle_storage_manager_base" id="r_af5dd339b13c413f020c142c15cbdd9ed"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>RemoveBundleFromDisk</b> (const <a class="el" href="structcatalog__entry__t.html">catalog_entry_t</a> *catalogEntryPtr, const uint64_t custodyId)</td></tr>
<tr class="separator:af5dd339b13c413f020c142c15cbdd9ed inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a130995ce392c8db5fac85a3eb853223a inherit pub_methods_class_bundle_storage_manager_base" id="r_a130995ce392c8db5fac85a3eb853223a"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>RemoveBundleFromDisk</b> (const uint64_t custodyId)</td></tr>
<tr class="separator:a130995ce392c8db5fac85a3eb853223a inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d165d250105b927ee15b399cd910ea1 inherit pub_methods_class_bundle_storage_manager_base" id="r_a6d165d250105b927ee15b399cd910ea1"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>RemoveReadBundleFromDisk</b> (const uint64_t custodyId)</td></tr>
<tr class="separator:a6d165d250105b927ee15b399cd910ea1 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ac63f9373f6edd08328502350cca5fe inherit pub_methods_class_bundle_storage_manager_base" id="r_a1ac63f9373f6edd08328502350cca5fe"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>RemoveReadBundleFromDisk</b> (<a class="el" href="struct_bundle_storage_manager_session___read_from_disk.html">BundleStorageManagerSession_ReadFromDisk</a> &amp;sessionRead)</td></tr>
<tr class="separator:a1ac63f9373f6edd08328502350cca5fe inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c41ac3e131be0bcd452bd39ddddcdd1 inherit pub_methods_class_bundle_storage_manager_base" id="r_a5c41ac3e131be0bcd452bd39ddddcdd1"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>RemoveReadBundleFromDisk</b> (const <a class="el" href="structcatalog__entry__t.html">catalog_entry_t</a> *catalogEntryPtr, const uint64_t custodyId)</td></tr>
<tr class="separator:a5c41ac3e131be0bcd452bd39ddddcdd1 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f944108dba37287497ddf702f70424c inherit pub_methods_class_bundle_storage_manager_base" id="r_a9f944108dba37287497ddf702f70424c"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT uint64_t *&#160;</td><td class="memItemRight" valign="bottom"><b>GetCustodyIdFromUuid</b> (const <a class="el" href="structcbhe__bundle__uuid__t.html">cbhe_bundle_uuid_t</a> &amp;bundleUuid)</td></tr>
<tr class="separator:a9f944108dba37287497ddf702f70424c inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9169910b133bd178300c2e7f81575db8 inherit pub_methods_class_bundle_storage_manager_base" id="r_a9169910b133bd178300c2e7f81575db8"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT uint64_t *&#160;</td><td class="memItemRight" valign="bottom"><b>GetCustodyIdFromUuid</b> (const <a class="el" href="structcbhe__bundle__uuid__nofragment__t.html">cbhe_bundle_uuid_nofragment_t</a> &amp;bundleUuid)</td></tr>
<tr class="separator:a9169910b133bd178300c2e7f81575db8 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa5c172f74aa6744cae1da9ebbcb29a69 inherit pub_methods_class_bundle_storage_manager_base" id="r_aa5c172f74aa6744cae1da9ebbcb29a69"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>GetStorageExpiringBeforeThresholdTelemetry</b> (<a class="el" href="struct_storage_expiring_before_threshold_telemetry__t.html">StorageExpiringBeforeThresholdTelemetry_t</a> &amp;telem)</td></tr>
<tr class="separator:aa5c172f74aa6744cae1da9ebbcb29a69 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a801e395c6503914a6fcf56c023b239e0 inherit pub_methods_class_bundle_storage_manager_base" id="r_a801e395c6503914a6fcf56c023b239e0"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT void&#160;</td><td class="memItemRight" valign="bottom"><b>GetExpiredBundleIds</b> (const uint64_t expiry, const uint64_t maxNumberToFind, std::vector&lt; uint64_t &gt; &amp;returnedIds)</td></tr>
<tr class="separator:a801e395c6503914a6fcf56c023b239e0 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac182e845329f88925ee10cd4416feee1 inherit pub_methods_class_bundle_storage_manager_base" id="r_ac182e845329f88925ee10cd4416feee1"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>RestoreFromDisk</b> (uint64_t *totalBundlesRestored, uint64_t *totalBytesRestored, uint64_t *totalSegmentsRestored)</td></tr>
<tr class="separator:ac182e845329f88925ee10cd4416feee1 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a456fbc5656764e522eed86bde7d2395d inherit pub_methods_class_bundle_storage_manager_base" id="r_a456fbc5656764e522eed86bde7d2395d"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT const <a class="el" href="class_memory_manager_tree_array.html">MemoryManagerTreeArray</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>GetMemoryManagerConstRef</b> () const</td></tr>
<tr class="separator:a456fbc5656764e522eed86bde7d2395d inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a902225846af2c3db5c7da76a8091ae4d inherit pub_methods_class_bundle_storage_manager_base" id="r_a902225846af2c3db5c7da76a8091ae4d"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT const <a class="el" href="class_bundle_storage_catalog.html">BundleStorageCatalog</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>GetBundleStorageCatalogConstRef</b> () const</td></tr>
<tr class="separator:a902225846af2c3db5c7da76a8091ae4d inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af19cc32f2e59431d3af82c93794c1480 inherit pub_methods_class_bundle_storage_manager_base" id="r_af19cc32f2e59431d3af82c93794c1480"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>GetFreeSpaceBytes</b> () const noexcept</td></tr>
<tr class="separator:af19cc32f2e59431d3af82c93794c1480 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff5eb6ef198551d6a3be1d316c4945fb inherit pub_methods_class_bundle_storage_manager_base" id="r_aff5eb6ef198551d6a3be1d316c4945fb"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>GetUsedSpaceBytes</b> () const noexcept</td></tr>
<tr class="separator:aff5eb6ef198551d6a3be1d316c4945fb inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed56dc56432fc04ff87f022f4cbc5b64 inherit pub_methods_class_bundle_storage_manager_base" id="r_aed56dc56432fc04ff87f022f4cbc5b64"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>GetTotalCapacityBytes</b> () const noexcept</td></tr>
<tr class="separator:aed56dc56432fc04ff87f022f4cbc5b64 inherit pub_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="inherited" name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_attribs_class_bundle_storage_manager_base"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pub_attribs_class_bundle_storage_manager_base')"><img src="closed.png" alt="-"/>&#160;Public Attributes inherited from <a class="el" href="class_bundle_storage_manager_base.html">BundleStorageManagerBase</a></td></tr>
<tr class="memitem:aa21f9c5948f5f75de926be639987cdbb inherit pub_attribs_class_bundle_storage_manager_base" id="r_aa21f9c5948f5f75de926be639987cdbb"><td class="memItemLeft" align="right" valign="top">
const unsigned int&#160;</td><td class="memItemRight" valign="bottom"><b>M_NUM_STORAGE_DISKS</b></td></tr>
<tr class="separator:aa21f9c5948f5f75de926be639987cdbb inherit pub_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0712101eb3e684a3ca5bde48e132fc46 inherit pub_attribs_class_bundle_storage_manager_base" id="r_a0712101eb3e684a3ca5bde48e132fc46"><td class="memItemLeft" align="right" valign="top">
const uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>M_TOTAL_STORAGE_CAPACITY_BYTES</b></td></tr>
<tr class="separator:a0712101eb3e684a3ca5bde48e132fc46 inherit pub_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26c0623bcdef133a117e2d1448bccc0a inherit pub_attribs_class_bundle_storage_manager_base" id="r_a26c0623bcdef133a117e2d1448bccc0a"><td class="memItemLeft" align="right" valign="top">
const uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>M_MAX_SEGMENTS</b></td></tr>
<tr class="separator:a26c0623bcdef133a117e2d1448bccc0a inherit pub_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7b87288208af7ffdd35fceaff7356b9 inherit pub_attribs_class_bundle_storage_manager_base" id="r_ad7b87288208af7ffdd35fceaff7356b9"><td class="memItemLeft" align="right" valign="top">
bool&#160;</td><td class="memItemRight" valign="bottom"><b>m_successfullyRestoredFromDisk</b></td></tr>
<tr class="separator:ad7b87288208af7ffdd35fceaff7356b9 inherit pub_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab9348eb0b6490a59f5be3954924752ce inherit pub_attribs_class_bundle_storage_manager_base" id="r_ab9348eb0b6490a59f5be3954924752ce"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBundlesRestored</b></td></tr>
<tr class="separator:ab9348eb0b6490a59f5be3954924752ce inherit pub_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf5d9ca22e22b8724892a6fa09ca3555 inherit pub_attribs_class_bundle_storage_manager_base" id="r_abf5d9ca22e22b8724892a6fa09ca3555"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalBytesRestored</b></td></tr>
<tr class="separator:abf5d9ca22e22b8724892a6fa09ca3555 inherit pub_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2164fc245b16ea3119acfafc6b783d60 inherit pub_attribs_class_bundle_storage_manager_base" id="r_a2164fc245b16ea3119acfafc6b783d60"><td class="memItemLeft" align="right" valign="top">
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_totalSegmentsRestored</b></td></tr>
<tr class="separator:a2164fc245b16ea3119acfafc6b783d60 inherit pub_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_class_bundle_storage_manager_base"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pro_methods_class_bundle_storage_manager_base')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="class_bundle_storage_manager_base.html">BundleStorageManagerBase</a></td></tr>
<tr class="memitem:ac3bb2c3a398ceb4e278ac7953a67fef5 inherit pro_methods_class_bundle_storage_manager_base" id="r_ac3bb2c3a398ceb4e278ac7953a67fef5"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>BundleStorageManagerBase</b> (const boost::filesystem::path &amp;jsonConfigFilePath)</td></tr>
<tr class="separator:ac3bb2c3a398ceb4e278ac7953a67fef5 inherit pro_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4b6fc8d660c278c09471621c7acec71c inherit pro_methods_class_bundle_storage_manager_base" id="r_a4b6fc8d660c278c09471621c7acec71c"><td class="memItemLeft" align="right" valign="top">
STORAGE_LIB_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>BundleStorageManagerBase</b> (const StorageConfig_ptr &amp;storageConfigPtr)</td></tr>
<tr class="separator:a4b6fc8d660c278c09471621c7acec71c inherit pro_methods_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_attribs_class_bundle_storage_manager_base"><td colspan="2" onclick="javascript:dynsection.toggleInherit('pro_attribs_class_bundle_storage_manager_base')"><img src="closed.png" alt="-"/>&#160;Protected Attributes inherited from <a class="el" href="class_bundle_storage_manager_base.html">BundleStorageManagerBase</a></td></tr>
<tr class="memitem:a7dd666be3d4a48324df1a536ac2b6b34 inherit pro_attribs_class_bundle_storage_manager_base" id="r_a7dd666be3d4a48324df1a536ac2b6b34"><td class="memItemLeft" align="right" valign="top">
StorageConfig_ptr&#160;</td><td class="memItemRight" valign="bottom"><b>m_storageConfigPtr</b></td></tr>
<tr class="separator:a7dd666be3d4a48324df1a536ac2b6b34 inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7efdd8f9dfe8772b306779cf78b5a4f inherit pro_attribs_class_bundle_storage_manager_base" id="r_ae7efdd8f9dfe8772b306779cf78b5a4f"><td class="memItemLeft" align="right" valign="top">
<a class="el" href="class_memory_manager_tree_array.html">MemoryManagerTreeArray</a>&#160;</td><td class="memItemRight" valign="bottom"><b>m_memoryManager</b></td></tr>
<tr class="separator:ae7efdd8f9dfe8772b306779cf78b5a4f inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4413c460174182d9d6841bddc241debc inherit pro_attribs_class_bundle_storage_manager_base" id="r_a4413c460174182d9d6841bddc241debc"><td class="memItemLeft" align="right" valign="top">
<a class="el" href="class_bundle_storage_catalog.html">BundleStorageCatalog</a>&#160;</td><td class="memItemRight" valign="bottom"><b>m_bundleStorageCatalog</b></td></tr>
<tr class="separator:a4413c460174182d9d6841bddc241debc inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae04e25f412a13e45b1760d712560b3cc inherit pro_attribs_class_bundle_storage_manager_base" id="r_ae04e25f412a13e45b1760d712560b3cc"><td class="memItemLeft" align="right" valign="top">
boost::mutex&#160;</td><td class="memItemRight" valign="bottom"><b>m_mutexMainThread</b></td></tr>
<tr class="separator:ae04e25f412a13e45b1760d712560b3cc inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9737d0f5058a3d7d62e374541d86331e inherit pro_attribs_class_bundle_storage_manager_base" id="r_a9737d0f5058a3d7d62e374541d86331e"><td class="memItemLeft" align="right" valign="top">
boost::condition_variable&#160;</td><td class="memItemRight" valign="bottom"><b>m_conditionVariableMainThread</b></td></tr>
<tr class="separator:a9737d0f5058a3d7d62e374541d86331e inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab951521278ee6c5b3f9cf4b3c63e1f22 inherit pro_attribs_class_bundle_storage_manager_base" id="r_ab951521278ee6c5b3f9cf4b3c63e1f22"><td class="memItemLeft" align="right" valign="top">
std::vector&lt; boost::filesystem::path &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>m_filePathsVec</b></td></tr>
<tr class="separator:ab951521278ee6c5b3f9cf4b3c63e1f22 inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3bb36fa1f66852bfc9470955744d69b5 inherit pro_attribs_class_bundle_storage_manager_base" id="r_a3bb36fa1f66852bfc9470955744d69b5"><td class="memItemLeft" align="right" valign="top">
std::vector&lt; unsigned int &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>m_tmpInitializerOfCircularIndexBuffersVec</b></td></tr>
<tr class="separator:a3bb36fa1f66852bfc9470955744d69b5 inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28fb849659d461177f2b5c9145dc7d5c inherit pro_attribs_class_bundle_storage_manager_base" id="r_a28fb849659d461177f2b5c9145dc7d5c"><td class="memItemLeft" align="right" valign="top">
std::vector&lt; <a class="el" href="class_circular_index_buffer_single_producer_single_consumer_configurable.html">CircularIndexBufferSingleProducerSingleConsumerConfigurable</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>m_circularIndexBuffersVec</b></td></tr>
<tr class="separator:a28fb849659d461177f2b5c9145dc7d5c inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a877adcb756a38252c907352a7dfcda01 inherit pro_attribs_class_bundle_storage_manager_base" id="r_a877adcb756a38252c907352a7dfcda01"><td class="memItemLeft" align="right" valign="top">
uint8_t *&#160;</td><td class="memItemRight" valign="bottom"><b>m_circularBufferBlockDataPtr</b></td></tr>
<tr class="separator:a877adcb756a38252c907352a7dfcda01 inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3db6ec18d0fe0b0e033d66e20dad6cfd inherit pro_attribs_class_bundle_storage_manager_base" id="r_a3db6ec18d0fe0b0e033d66e20dad6cfd"><td class="memItemLeft" align="right" valign="top">
segment_id_t *&#160;</td><td class="memItemRight" valign="bottom"><b>m_circularBufferSegmentIdsPtr</b></td></tr>
<tr class="separator:a3db6ec18d0fe0b0e033d66e20dad6cfd inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a96d693eb97c220e7d94bdc6eba97375e inherit pro_attribs_class_bundle_storage_manager_base" id="r_a96d693eb97c220e7d94bdc6eba97375e"><td class="memItemLeft" align="right" valign="top">
std::atomic&lt; std::atomic&lt; bool &gt; * &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>m_circularBufferIsReadCompletedPointers</b> [CIRCULAR_INDEX_BUFFER_SIZE *MAX_NUM_STORAGE_THREADS]</td></tr>
<tr class="separator:a96d693eb97c220e7d94bdc6eba97375e inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7db27eda4dcb19f0e8a282a81259087b inherit pro_attribs_class_bundle_storage_manager_base" id="r_a7db27eda4dcb19f0e8a282a81259087b"><td class="memItemLeft" align="right" valign="top">
std::atomic&lt; uint8_t * &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>m_circularBufferReadFromStoragePointers</b> [CIRCULAR_INDEX_BUFFER_SIZE *MAX_NUM_STORAGE_THREADS]</td></tr>
<tr class="separator:a7db27eda4dcb19f0e8a282a81259087b inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff7ce888d9f782a1997dad09b35cbae4 inherit pro_attribs_class_bundle_storage_manager_base" id="r_aff7ce888d9f782a1997dad09b35cbae4"><td class="memItemLeft" align="right" valign="top">
std::atomic&lt; bool &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>m_autoDeleteFilesOnExit</b></td></tr>
<tr class="separator:aff7ce888d9f782a1997dad09b35cbae4 inherit pro_attribs_class_bundle_storage_manager_base"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a234a1c34dc1f34e84b8478483e33a1c4" name="a234a1c34dc1f34e84b8478483e33a1c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a234a1c34dc1f34e84b8478483e33a1c4">&#9670;&#160;</a></span>Start()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BundleStorageManagerAsio::Start </td>
          <td>(</td>
          <td class="paramname"><span class="paramname"><em></em></span></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel override">override</span><span class="mlabel virtual">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Implements <a class="el" href="class_bundle_storage_manager_base.html">BundleStorageManagerBase</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>module/storage/include/<a class="el" href="_bundle_storage_manager_asio_8h_source.html">BundleStorageManagerAsio.h</a></li>
<li>module/storage/src/<a class="el" href="_bundle_storage_manager_asio_8cpp.html">BundleStorageManagerAsio.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="class_bundle_storage_manager_asio.html">BundleStorageManagerAsio</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
