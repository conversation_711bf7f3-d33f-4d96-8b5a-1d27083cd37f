<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: outduct_element_config_t Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('structoutduct__element__config__t.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structoutduct__element__config__t-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">outduct_element_config_t Struct Reference</div></div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aebec7a35fb08c52703e0c3f925273e6e" id="r_aebec7a35fb08c52703e0c3f925273e6e"><td class="memItemLeft" align="right" valign="top"><a id="aebec7a35fb08c52703e0c3f925273e6e" name="aebec7a35fb08c52703e0c3f925273e6e"></a>
CONFIG_LIB_EXPORT bool&#160;</td><td class="memItemRight" valign="bottom"><b>operator==</b> (const <a class="el" href="structoutduct__element__config__t.html">outduct_element_config_t</a> &amp;o) const</td></tr>
<tr class="separator:aebec7a35fb08c52703e0c3f925273e6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a731585c97631ce15e3ef68769aa3fcfd" id="r_a731585c97631ce15e3ef68769aa3fcfd"><td class="memItemLeft" align="right" valign="top"><a id="a731585c97631ce15e3ef68769aa3fcfd" name="a731585c97631ce15e3ef68769aa3fcfd"></a>
CONFIG_LIB_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>outduct_element_config_t</b> (const <a class="el" href="structoutduct__element__config__t.html">outduct_element_config_t</a> &amp;o)</td></tr>
<tr class="separator:a731585c97631ce15e3ef68769aa3fcfd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83fdcc96a33066783d5a25ef68f8928b" id="r_a83fdcc96a33066783d5a25ef68f8928b"><td class="memItemLeft" align="right" valign="top"><a id="a83fdcc96a33066783d5a25ef68f8928b" name="a83fdcc96a33066783d5a25ef68f8928b"></a>
CONFIG_LIB_EXPORT&#160;</td><td class="memItemRight" valign="bottom"><b>outduct_element_config_t</b> (<a class="el" href="structoutduct__element__config__t.html">outduct_element_config_t</a> &amp;&amp;o) noexcept</td></tr>
<tr class="separator:a83fdcc96a33066783d5a25ef68f8928b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a912cfef057c02d87ffadab1d8a143afb" id="r_a912cfef057c02d87ffadab1d8a143afb"><td class="memItemLeft" align="right" valign="top"><a id="a912cfef057c02d87ffadab1d8a143afb" name="a912cfef057c02d87ffadab1d8a143afb"></a>
CONFIG_LIB_EXPORT <a class="el" href="structoutduct__element__config__t.html">outduct_element_config_t</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (const <a class="el" href="structoutduct__element__config__t.html">outduct_element_config_t</a> &amp;o)</td></tr>
<tr class="separator:a912cfef057c02d87ffadab1d8a143afb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a45ac14add88aa267fe55a5c314064590" id="r_a45ac14add88aa267fe55a5c314064590"><td class="memItemLeft" align="right" valign="top"><a id="a45ac14add88aa267fe55a5c314064590" name="a45ac14add88aa267fe55a5c314064590"></a>
CONFIG_LIB_EXPORT <a class="el" href="structoutduct__element__config__t.html">outduct_element_config_t</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (<a class="el" href="structoutduct__element__config__t.html">outduct_element_config_t</a> &amp;&amp;o) noexcept</td></tr>
<tr class="separator:a45ac14add88aa267fe55a5c314064590"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a63e96da9d3041dc854ccd392223b0bff" id="r_a63e96da9d3041dc854ccd392223b0bff"><td class="memItemLeft" align="right" valign="top"><a id="a63e96da9d3041dc854ccd392223b0bff" name="a63e96da9d3041dc854ccd392223b0bff"></a>
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>name</b></td></tr>
<tr class="separator:a63e96da9d3041dc854ccd392223b0bff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a626dd2558439ddd6ecaa456b25b0f492" id="r_a626dd2558439ddd6ecaa456b25b0f492"><td class="memItemLeft" align="right" valign="top"><a id="a626dd2558439ddd6ecaa456b25b0f492" name="a626dd2558439ddd6ecaa456b25b0f492"></a>
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>convergenceLayer</b></td></tr>
<tr class="separator:a626dd2558439ddd6ecaa456b25b0f492"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a91b49209685417015eddfdba6f6de5" id="r_a2a91b49209685417015eddfdba6f6de5"><td class="memItemLeft" align="right" valign="top"><a id="a2a91b49209685417015eddfdba6f6de5" name="a2a91b49209685417015eddfdba6f6de5"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>nextHopNodeId</b></td></tr>
<tr class="separator:a2a91b49209685417015eddfdba6f6de5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af30d837910be5773b5d81d1c4413a527" id="r_af30d837910be5773b5d81d1c4413a527"><td class="memItemLeft" align="right" valign="top"><a id="af30d837910be5773b5d81d1c4413a527" name="af30d837910be5773b5d81d1c4413a527"></a>
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>remoteHostname</b></td></tr>
<tr class="separator:af30d837910be5773b5d81d1c4413a527"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46e3aa84106c19bd154be8bbbb215f57" id="r_a46e3aa84106c19bd154be8bbbb215f57"><td class="memItemLeft" align="right" valign="top"><a id="a46e3aa84106c19bd154be8bbbb215f57" name="a46e3aa84106c19bd154be8bbbb215f57"></a>
uint16_t&#160;</td><td class="memItemRight" valign="bottom"><b>remotePort</b></td></tr>
<tr class="separator:a46e3aa84106c19bd154be8bbbb215f57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afb7463285e01141c14b1a2c87a69a592" id="r_afb7463285e01141c14b1a2c87a69a592"><td class="memItemLeft" align="right" valign="top"><a id="afb7463285e01141c14b1a2c87a69a592" name="afb7463285e01141c14b1a2c87a69a592"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>maxNumberOfBundlesInPipeline</b></td></tr>
<tr class="separator:afb7463285e01141c14b1a2c87a69a592"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d1c24612edddc8d50883a821ee8a8f3" id="r_a0d1c24612edddc8d50883a821ee8a8f3"><td class="memItemLeft" align="right" valign="top"><a id="a0d1c24612edddc8d50883a821ee8a8f3" name="a0d1c24612edddc8d50883a821ee8a8f3"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>maxSumOfBundleBytesInPipeline</b></td></tr>
<tr class="separator:a0d1c24612edddc8d50883a821ee8a8f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a84601315e2dd07fa98270749b682075c" id="r_a84601315e2dd07fa98270749b682075c"><td class="memItemLeft" align="right" valign="top"><a id="a84601315e2dd07fa98270749b682075c" name="a84601315e2dd07fa98270749b682075c"></a>
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>bpEncapLocalSocketOrPipePath</b></td></tr>
<tr class="separator:a84601315e2dd07fa98270749b682075c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a546856b0b04bebcb2dcac72f90323657" id="r_a546856b0b04bebcb2dcac72f90323657"><td class="memItemLeft" align="right" valign="top"><a id="a546856b0b04bebcb2dcac72f90323657" name="a546856b0b04bebcb2dcac72f90323657"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>thisLtpEngineId</b></td></tr>
<tr class="separator:a546856b0b04bebcb2dcac72f90323657"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a102c1e56ca62d64c82975268a987c673" id="r_a102c1e56ca62d64c82975268a987c673"><td class="memItemLeft" align="right" valign="top"><a id="a102c1e56ca62d64c82975268a987c673" name="a102c1e56ca62d64c82975268a987c673"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>remoteLtpEngineId</b></td></tr>
<tr class="separator:a102c1e56ca62d64c82975268a987c673"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c278b27f74a0245fc06beb7686d9973" id="r_a8c278b27f74a0245fc06beb7686d9973"><td class="memItemLeft" align="right" valign="top"><a id="a8c278b27f74a0245fc06beb7686d9973" name="a8c278b27f74a0245fc06beb7686d9973"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>ltpDataSegmentMtu</b></td></tr>
<tr class="separator:a8c278b27f74a0245fc06beb7686d9973"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01e4a77b60e5dbbe433450919e7b5933" id="r_a01e4a77b60e5dbbe433450919e7b5933"><td class="memItemLeft" align="right" valign="top"><a id="a01e4a77b60e5dbbe433450919e7b5933" name="a01e4a77b60e5dbbe433450919e7b5933"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>oneWayLightTimeMs</b></td></tr>
<tr class="separator:a01e4a77b60e5dbbe433450919e7b5933"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba62e35538fe996c019787004ef189bd" id="r_aba62e35538fe996c019787004ef189bd"><td class="memItemLeft" align="right" valign="top"><a id="aba62e35538fe996c019787004ef189bd" name="aba62e35538fe996c019787004ef189bd"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>oneWayMarginTimeMs</b></td></tr>
<tr class="separator:aba62e35538fe996c019787004ef189bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7114723b4215cd93957dfd368b36e73c" id="r_a7114723b4215cd93957dfd368b36e73c"><td class="memItemLeft" align="right" valign="top"><a id="a7114723b4215cd93957dfd368b36e73c" name="a7114723b4215cd93957dfd368b36e73c"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>clientServiceId</b></td></tr>
<tr class="separator:a7114723b4215cd93957dfd368b36e73c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c913ca03e4912c2101a1ebf0e6b2991" id="r_a2c913ca03e4912c2101a1ebf0e6b2991"><td class="memItemLeft" align="right" valign="top"><a id="a2c913ca03e4912c2101a1ebf0e6b2991" name="a2c913ca03e4912c2101a1ebf0e6b2991"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>numRxCircularBufferElements</b></td></tr>
<tr class="separator:a2c913ca03e4912c2101a1ebf0e6b2991"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d0fa1391d12f33c0ac449bb850a6e75" id="r_a0d0fa1391d12f33c0ac449bb850a6e75"><td class="memItemLeft" align="right" valign="top"><a id="a0d0fa1391d12f33c0ac449bb850a6e75" name="a0d0fa1391d12f33c0ac449bb850a6e75"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>ltpMaxRetriesPerSerialNumber</b></td></tr>
<tr class="separator:a0d0fa1391d12f33c0ac449bb850a6e75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad23d095b46fb5bc9948f48693a9dcc69" id="r_ad23d095b46fb5bc9948f48693a9dcc69"><td class="memItemLeft" align="right" valign="top"><a id="ad23d095b46fb5bc9948f48693a9dcc69" name="ad23d095b46fb5bc9948f48693a9dcc69"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>ltpCheckpointEveryNthDataSegment</b></td></tr>
<tr class="separator:ad23d095b46fb5bc9948f48693a9dcc69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2f020a2c547bf15f3583f5cb6578e919" id="r_a2f020a2c547bf15f3583f5cb6578e919"><td class="memItemLeft" align="right" valign="top"><a id="a2f020a2c547bf15f3583f5cb6578e919" name="a2f020a2c547bf15f3583f5cb6578e919"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>ltpRandomNumberSizeBits</b></td></tr>
<tr class="separator:a2f020a2c547bf15f3583f5cb6578e919"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac001269cfdf605f0cdaa1bdc33c3b644" id="r_ac001269cfdf605f0cdaa1bdc33c3b644"><td class="memItemLeft" align="right" valign="top"><a id="ac001269cfdf605f0cdaa1bdc33c3b644" name="ac001269cfdf605f0cdaa1bdc33c3b644"></a>
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>ltpEncapLocalSocketOrPipePath</b></td></tr>
<tr class="separator:ac001269cfdf605f0cdaa1bdc33c3b644"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6a71fc30dbcbecd4e5de9d95757402d" id="r_af6a71fc30dbcbecd4e5de9d95757402d"><td class="memItemLeft" align="right" valign="top"><a id="af6a71fc30dbcbecd4e5de9d95757402d" name="af6a71fc30dbcbecd4e5de9d95757402d"></a>
uint16_t&#160;</td><td class="memItemRight" valign="bottom"><b>ltpSenderBoundPort</b></td></tr>
<tr class="separator:af6a71fc30dbcbecd4e5de9d95757402d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7b56ef22afa8c8e445a05d2cc0f77c8a" id="r_a7b56ef22afa8c8e445a05d2cc0f77c8a"><td class="memItemLeft" align="right" valign="top"><a id="a7b56ef22afa8c8e445a05d2cc0f77c8a" name="a7b56ef22afa8c8e445a05d2cc0f77c8a"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>ltpMaxUdpPacketsToSendPerSystemCall</b></td></tr>
<tr class="separator:a7b56ef22afa8c8e445a05d2cc0f77c8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca9c80478c536f250e76dc0f31002874" id="r_aca9c80478c536f250e76dc0f31002874"><td class="memItemLeft" align="right" valign="top"><a id="aca9c80478c536f250e76dc0f31002874" name="aca9c80478c536f250e76dc0f31002874"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>ltpSenderPingSecondsOrZeroToDisable</b></td></tr>
<tr class="separator:aca9c80478c536f250e76dc0f31002874"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27f593c3a10aa6f82df3ed9eb86d1ce9" id="r_a27f593c3a10aa6f82df3ed9eb86d1ce9"><td class="memItemLeft" align="right" valign="top"><a id="a27f593c3a10aa6f82df3ed9eb86d1ce9" name="a27f593c3a10aa6f82df3ed9eb86d1ce9"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>delaySendingOfDataSegmentsTimeMsOrZeroToDisable</b></td></tr>
<tr class="separator:a27f593c3a10aa6f82df3ed9eb86d1ce9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad4b85455f2ae13e86ae89c7ebbd99b3f" id="r_ad4b85455f2ae13e86ae89c7ebbd99b3f"><td class="memItemLeft" align="right" valign="top"><a id="ad4b85455f2ae13e86ae89c7ebbd99b3f" name="ad4b85455f2ae13e86ae89c7ebbd99b3f"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>keepActiveSessionDataOnDisk</b></td></tr>
<tr class="separator:ad4b85455f2ae13e86ae89c7ebbd99b3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d6e9fb99b49e317b656a513e3f57904" id="r_a5d6e9fb99b49e317b656a513e3f57904"><td class="memItemLeft" align="right" valign="top"><a id="a5d6e9fb99b49e317b656a513e3f57904" name="a5d6e9fb99b49e317b656a513e3f57904"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>activeSessionDataOnDiskNewFileDurationMs</b></td></tr>
<tr class="separator:a5d6e9fb99b49e317b656a513e3f57904"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a797ea5a6efb78cc58eeec7611e12cf2a" id="r_a797ea5a6efb78cc58eeec7611e12cf2a"><td class="memItemLeft" align="right" valign="top"><a id="a797ea5a6efb78cc58eeec7611e12cf2a" name="a797ea5a6efb78cc58eeec7611e12cf2a"></a>
boost::filesystem::path&#160;</td><td class="memItemRight" valign="bottom"><b>activeSessionDataOnDiskDirectory</b></td></tr>
<tr class="separator:a797ea5a6efb78cc58eeec7611e12cf2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c7b5b6146495e4960f8cd192c694fec" id="r_a6c7b5b6146495e4960f8cd192c694fec"><td class="memItemLeft" align="right" valign="top"><a id="a6c7b5b6146495e4960f8cd192c694fec" name="a6c7b5b6146495e4960f8cd192c694fec"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>rateLimitPrecisionMicroSec</b></td></tr>
<tr class="separator:a6c7b5b6146495e4960f8cd192c694fec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ad95eb5efde0e5d83ed07a7dbf887ba" id="r_a4ad95eb5efde0e5d83ed07a7dbf887ba"><td class="memItemLeft" align="right" valign="top"><a id="a4ad95eb5efde0e5d83ed07a7dbf887ba" name="a4ad95eb5efde0e5d83ed07a7dbf887ba"></a>
std::string&#160;</td><td class="memItemRight" valign="bottom"><b>comPort</b></td></tr>
<tr class="separator:a4ad95eb5efde0e5d83ed07a7dbf887ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0f018bf708c396bf4c4f6313c0ab7067" id="r_a0f018bf708c396bf4c4f6313c0ab7067"><td class="memItemLeft" align="right" valign="top"><a id="a0f018bf708c396bf4c4f6313c0ab7067" name="a0f018bf708c396bf4c4f6313c0ab7067"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>baudRate</b></td></tr>
<tr class="separator:a0f018bf708c396bf4c4f6313c0ab7067"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2d9ea144afe7c5e7ca7dfdf9fdcc554" id="r_ac2d9ea144afe7c5e7ca7dfdf9fdcc554"><td class="memItemLeft" align="right" valign="top"><a id="ac2d9ea144afe7c5e7ca7dfdf9fdcc554" name="ac2d9ea144afe7c5e7ca7dfdf9fdcc554"></a>
uint16_t&#160;</td><td class="memItemRight" valign="bottom"><b>keepAliveIntervalSeconds</b></td></tr>
<tr class="separator:ac2d9ea144afe7c5e7ca7dfdf9fdcc554"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9324de0d5d5c97c0cb972f527e2b3756" id="r_a9324de0d5d5c97c0cb972f527e2b3756"><td class="memItemLeft" align="right" valign="top"><a id="a9324de0d5d5c97c0cb972f527e2b3756" name="a9324de0d5d5c97c0cb972f527e2b3756"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>tcpclV3MyMaxTxSegmentSizeBytes</b></td></tr>
<tr class="separator:a9324de0d5d5c97c0cb972f527e2b3756"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a806c6b448d0fd8c608676112212e7df4" id="r_a806c6b448d0fd8c608676112212e7df4"><td class="memItemLeft" align="right" valign="top"><a id="a806c6b448d0fd8c608676112212e7df4" name="a806c6b448d0fd8c608676112212e7df4"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>tcpclAllowOpportunisticReceiveBundles</b></td></tr>
<tr class="separator:a806c6b448d0fd8c608676112212e7df4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67d7146d9a7c97d4313e5422d1fbf081" id="r_a67d7146d9a7c97d4313e5422d1fbf081"><td class="memItemLeft" align="right" valign="top"><a id="a67d7146d9a7c97d4313e5422d1fbf081" name="a67d7146d9a7c97d4313e5422d1fbf081"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><b>tcpclV4MyMaxRxSegmentSizeBytes</b></td></tr>
<tr class="separator:a67d7146d9a7c97d4313e5422d1fbf081"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98236427c6dc8795c32945a936fec7d0" id="r_a98236427c6dc8795c32945a936fec7d0"><td class="memItemLeft" align="right" valign="top"><a id="a98236427c6dc8795c32945a936fec7d0" name="a98236427c6dc8795c32945a936fec7d0"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>tryUseTls</b></td></tr>
<tr class="separator:a98236427c6dc8795c32945a936fec7d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ece1e98c46843c433beddeb94e32df9" id="r_a1ece1e98c46843c433beddeb94e32df9"><td class="memItemLeft" align="right" valign="top"><a id="a1ece1e98c46843c433beddeb94e32df9" name="a1ece1e98c46843c433beddeb94e32df9"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>tlsIsRequired</b></td></tr>
<tr class="separator:a1ece1e98c46843c433beddeb94e32df9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a01f0f303affc3ce7ca42b3fa23fb0aa0" id="r_a01f0f303affc3ce7ca42b3fa23fb0aa0"><td class="memItemLeft" align="right" valign="top"><a id="a01f0f303affc3ce7ca42b3fa23fb0aa0" name="a01f0f303affc3ce7ca42b3fa23fb0aa0"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>useTlsVersion1_3</b></td></tr>
<tr class="separator:a01f0f303affc3ce7ca42b3fa23fb0aa0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4f01239a6cbe133fdebb2c0bf5da87c" id="r_ae4f01239a6cbe133fdebb2c0bf5da87c"><td class="memItemLeft" align="right" valign="top"><a id="ae4f01239a6cbe133fdebb2c0bf5da87c" name="ae4f01239a6cbe133fdebb2c0bf5da87c"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>doX509CertificateVerification</b></td></tr>
<tr class="separator:ae4f01239a6cbe133fdebb2c0bf5da87c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6728b6672dfd702b69373d4b91579f0f" id="r_a6728b6672dfd702b69373d4b91579f0f"><td class="memItemLeft" align="right" valign="top"><a id="a6728b6672dfd702b69373d4b91579f0f" name="a6728b6672dfd702b69373d4b91579f0f"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>verifySubjectAltNameInX509Certificate</b></td></tr>
<tr class="separator:a6728b6672dfd702b69373d4b91579f0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adeaf00a14a14efdc51a1a1afa8224717" id="r_adeaf00a14a14efdc51a1a1afa8224717"><td class="memItemLeft" align="right" valign="top"><a id="adeaf00a14a14efdc51a1a1afa8224717" name="adeaf00a14a14efdc51a1a1afa8224717"></a>
boost::filesystem::path&#160;</td><td class="memItemRight" valign="bottom"><b>certificationAuthorityPemFileForVerification</b></td></tr>
<tr class="separator:adeaf00a14a14efdc51a1a1afa8224717"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<hr/>The documentation for this struct was generated from the following files:<ul>
<li>common/config/include/<a class="el" href="_outducts_config_8h_source.html">OutductsConfig.h</a></li>
<li>common/config/src/<a class="el" href="_outducts_config_8cpp.html">OutductsConfig.cpp</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="structoutduct__element__config__t.html">outduct_element_config_t</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
