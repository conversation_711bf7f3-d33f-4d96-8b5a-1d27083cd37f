<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: common/telemetry_definitions/include/TelemetryDefinitions.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('_telemetry_definitions_8h_source.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">TelemetryDefinitions.h</div></div>
</div><!--header-->
<div class="contents">
<a href="_telemetry_definitions_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span> </div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span><span class="preprocessor">#ifndef HDTN_TELEMETRY_H</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="preprocessor">#define HDTN_TELEMETRY_H 1</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span> </div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="preprocessor">#include &lt;cstdint&gt;</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="preprocessor">#include &lt;map&gt;</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span><span class="preprocessor">#include &lt;list&gt;</span></div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span> </div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span><span class="preprocessor">#include &lt;zmq.hpp&gt;</span></div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span> </div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="preprocessor">#include &quot;telemetry_definitions_export.h&quot;</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span><span class="preprocessor">#include &quot;<a class="code" href="_cbhe_8h.html">codec/Cbhe.h</a>&quot;</span></div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="preprocessor">#include &quot;<a class="code" href="_json_serializable_8h.html">JsonSerializable.h</a>&quot;</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span> </div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span><span class="preprocessor">#ifndef CLASS_VISIBILITY_TELEMETRY_DEFINITIONS</span></div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span><span class="preprocessor">#  ifdef _WIN32</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span><span class="preprocessor">#    define CLASS_VISIBILITY_TELEMETRY_DEFINITIONS</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span><span class="preprocessor">#  else</span></div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span><span class="preprocessor">#    define CLASS_VISIBILITY_TELEMETRY_DEFINITIONS TELEMETRY_DEFINITIONS_EXPORT</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="preprocessor">#  endif</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span> </div>
<div class="foldopen" id="foldopen00042" data-start="{" data-end="};">
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno"><a class="line" href="struct_storage_telemetry__t.html">   42</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS StorageTelemetry_t : <span class="keyword">public</span> JsonSerializable {</div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span>    TELEMETRY_DEFINITIONS_EXPORT StorageTelemetry_t();</div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span>    TELEMETRY_DEFINITIONS_EXPORT ~StorageTelemetry_t();</div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> StorageTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> StorageTelemetry_t&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span> </div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span> </div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span>    uint64_t m_timestampMilliseconds;</div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span> </div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span>    <span class="comment">//from ZmqStorageInterface</span></div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span>    uint64_t m_totalBundlesErasedFromStorageNoCustodyTransfer;</div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span>    uint64_t m_totalBundlesErasedFromStorageWithCustodyTransfer;</div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span>    uint64_t m_totalBundlesErasedFromStorageBecauseExpired;</div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span>    uint64_t m_totalBundlesRewrittenToStorageFromFailedEgressSend;</div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span>    uint64_t m_totalBundlesSentToEgressFromStorageReadFromDisk;</div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span>    uint64_t m_totalBundleBytesSentToEgressFromStorageReadFromDisk;</div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span>    uint64_t m_totalBundlesSentToEgressFromStorageForwardCutThrough;</div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span>    uint64_t m_totalBundleBytesSentToEgressFromStorageForwardCutThrough;</div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span>    uint64_t m_numRfc5050CustodyTransfers;</div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span>    uint64_t m_numAcsCustodyTransfers;</div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span>    uint64_t m_numAcsPacketsReceived;</div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span>    </div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span>    <span class="comment">//from BundleStorageCatalog</span></div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span>    uint64_t m_numBundlesOnDisk;</div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span>    uint64_t m_numBundleBytesOnDisk;</div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span>    uint64_t m_totalBundleWriteOperationsToDisk;</div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span>    uint64_t m_totalBundleByteWriteOperationsToDisk;</div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span>    uint64_t m_totalBundleEraseOperationsFromDisk;</div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span>    uint64_t m_totalBundleByteEraseOperationsFromDisk;</div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span> </div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span>    <span class="comment">//from BundleStorageManagerBase&#39;s MemoryManager</span></div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span>    uint64_t m_usedSpaceBytes;</div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span>    uint64_t m_freeSpaceBytes;</div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span>};</div>
</div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span> </div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span> </div>
<div class="foldopen" id="foldopen00080" data-start="{" data-end="};">
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno"><a class="line" href="struct_storage_expiring_before_threshold_telemetry__t.html">   80</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS StorageExpiringBeforeThresholdTelemetry_t : <span class="keyword">public</span> JsonSerializable {</div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span>    TELEMETRY_DEFINITIONS_EXPORT StorageExpiringBeforeThresholdTelemetry_t();</div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span>    TELEMETRY_DEFINITIONS_EXPORT ~StorageExpiringBeforeThresholdTelemetry_t();</div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> StorageExpiringBeforeThresholdTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> StorageExpiringBeforeThresholdTelemetry_t&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span> </div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span> </div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span>    uint64_t priority;</div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span>    uint64_t thresholdSecondsSinceStartOfYear2000;</div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span>    <span class="keyword">typedef</span> std::pair&lt;uint64_t, uint64_t&gt; bundle_count_plus_bundle_bytes_pair_t;</div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span>    std::map&lt;uint64_t, bundle_count_plus_bundle_bytes_pair_t&gt; mapNodeIdToExpiringBeforeThresholdCount;</div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span>};</div>
</div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span> </div>
<div class="foldopen" id="foldopen00095" data-start="{" data-end="};">
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno"><a class="line" href="struct_outduct_capability_telemetry__t.html">   95</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS OutductCapabilityTelemetry_t : <span class="keyword">public</span> JsonSerializable {</div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span>    TELEMETRY_DEFINITIONS_EXPORT OutductCapabilityTelemetry_t();</div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span> </div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span>    </div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span>    uint64_t outductArrayIndex; <span class="comment">//outductUuid</span></div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span>    uint64_t maxBundlesInPipeline;</div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span>    uint64_t maxBundleSizeBytesInPipeline;</div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span>    uint64_t nextHopNodeId;</div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span>    <span class="keywordtype">bool</span> assumedInitiallyDown;</div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno">  104</span>    std::list&lt;cbhe_eid_t&gt; finalDestinationEidList;</div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span>    std::list&lt;uint64_t&gt; finalDestinationNodeIdList;</div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span> </div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span>    TELEMETRY_DEFINITIONS_EXPORT OutductCapabilityTelemetry_t(<span class="keyword">const</span> OutductCapabilityTelemetry_t&amp; o); <span class="comment">//a copy constructor: X(const X&amp;)</span></div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span>    TELEMETRY_DEFINITIONS_EXPORT OutductCapabilityTelemetry_t(OutductCapabilityTelemetry_t&amp;&amp; o) <span class="keyword">noexcept</span>; <span class="comment">//a move constructor: X(X&amp;&amp;)</span></div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span>    TELEMETRY_DEFINITIONS_EXPORT OutductCapabilityTelemetry_t&amp; operator=(<span class="keyword">const</span> OutductCapabilityTelemetry_t&amp; o); <span class="comment">//a copy assignment: operator=(const X&amp;)</span></div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span>    TELEMETRY_DEFINITIONS_EXPORT OutductCapabilityTelemetry_t&amp; operator=(OutductCapabilityTelemetry_t&amp;&amp; o) <span class="keyword">noexcept</span>; <span class="comment">//a move assignment: operator=(X&amp;&amp;)</span></div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> OutductCapabilityTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> OutductCapabilityTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator !=</span></div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span> </div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span>};</div>
</div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span> </div>
<div class="foldopen" id="foldopen00118" data-start="{" data-end="};">
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno"><a class="line" href="struct_all_outduct_capabilities_telemetry__t.html">  118</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS AllOutductCapabilitiesTelemetry_t : <span class="keyword">public</span> JsonSerializable {</div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno">  119</span>    TELEMETRY_DEFINITIONS_EXPORT AllOutductCapabilitiesTelemetry_t();</div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno">  120</span> </div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span>    std::list&lt;OutductCapabilityTelemetry_t&gt; outductCapabilityTelemetryList;</div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span> </div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno">  123</span>    TELEMETRY_DEFINITIONS_EXPORT AllOutductCapabilitiesTelemetry_t(<span class="keyword">const</span> AllOutductCapabilitiesTelemetry_t&amp; o); <span class="comment">//a copy constructor: X(const X&amp;)</span></div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span>    TELEMETRY_DEFINITIONS_EXPORT AllOutductCapabilitiesTelemetry_t(AllOutductCapabilitiesTelemetry_t&amp;&amp; o) <span class="keyword">noexcept</span>; <span class="comment">//a move constructor: X(X&amp;&amp;)</span></div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span>    TELEMETRY_DEFINITIONS_EXPORT AllOutductCapabilitiesTelemetry_t&amp; operator=(<span class="keyword">const</span> AllOutductCapabilitiesTelemetry_t&amp; o); <span class="comment">//a copy assignment: operator=(const X&amp;)</span></div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span>    TELEMETRY_DEFINITIONS_EXPORT AllOutductCapabilitiesTelemetry_t&amp; operator=(AllOutductCapabilitiesTelemetry_t&amp;&amp; o) <span class="keyword">noexcept</span>; <span class="comment">//a move assignment: operator=(X&amp;&amp;)</span></div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno">  127</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> AllOutductCapabilitiesTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> AllOutductCapabilitiesTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator !=</span></div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno">  129</span> </div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno">  130</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno">  132</span>};</div>
</div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno">  133</span> </div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno">  134</span> </div>
<div class="foldopen" id="foldopen00135" data-start="{" data-end="};">
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno"><a class="line" href="struct_induct_connection_telemetry__t.html">  135</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS InductConnectionTelemetry_t : <span class="keyword">public</span> JsonSerializable {</div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span>    TELEMETRY_DEFINITIONS_EXPORT InductConnectionTelemetry_t();</div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~InductConnectionTelemetry_t();</div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span> </div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span> </div>
<div class="line"><a id="l00144" name="l00144"></a><span class="lineno">  144</span>    std::string m_connectionName;</div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span>    std::string m_inputName;</div>
<div class="line"><a id="l00146" name="l00146"></a><span class="lineno">  146</span>    uint64_t m_totalBundlesReceived;</div>
<div class="line"><a id="l00147" name="l00147"></a><span class="lineno">  147</span>    uint64_t m_totalBundleBytesReceived;</div>
<div class="line"><a id="l00148" name="l00148"></a><span class="lineno">  148</span>};</div>
</div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno">  149</span> </div>
<div class="foldopen" id="foldopen00150" data-start="{" data-end="};">
<div class="line"><a id="l00150" name="l00150"></a><span class="lineno"><a class="line" href="struct_stcp_induct_connection_telemetry__t.html">  150</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS StcpInductConnectionTelemetry_t : <span class="keyword">public</span> InductConnectionTelemetry_t {</div>
<div class="line"><a id="l00151" name="l00151"></a><span class="lineno">  151</span>    TELEMETRY_DEFINITIONS_EXPORT StcpInductConnectionTelemetry_t();</div>
<div class="line"><a id="l00152" name="l00152"></a><span class="lineno">  152</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~StcpInductConnectionTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00153" name="l00153"></a><span class="lineno">  153</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00154" name="l00154"></a><span class="lineno">  154</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00155" name="l00155"></a><span class="lineno">  155</span> </div>
<div class="line"><a id="l00156" name="l00156"></a><span class="lineno">  156</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00157" name="l00157"></a><span class="lineno">  157</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00158" name="l00158"></a><span class="lineno">  158</span> </div>
<div class="line"><a id="l00159" name="l00159"></a><span class="lineno">  159</span>    uint64_t m_totalStcpBytesReceived;</div>
<div class="line"><a id="l00160" name="l00160"></a><span class="lineno">  160</span>};</div>
</div>
<div class="line"><a id="l00161" name="l00161"></a><span class="lineno">  161</span> </div>
<div class="foldopen" id="foldopen00162" data-start="{" data-end="};">
<div class="line"><a id="l00162" name="l00162"></a><span class="lineno"><a class="line" href="struct_udp_induct_connection_telemetry__t.html">  162</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS UdpInductConnectionTelemetry_t : <span class="keyword">public</span> InductConnectionTelemetry_t {</div>
<div class="line"><a id="l00163" name="l00163"></a><span class="lineno">  163</span>    TELEMETRY_DEFINITIONS_EXPORT UdpInductConnectionTelemetry_t();</div>
<div class="line"><a id="l00164" name="l00164"></a><span class="lineno">  164</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~UdpInductConnectionTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00165" name="l00165"></a><span class="lineno">  165</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00166" name="l00166"></a><span class="lineno">  166</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00167" name="l00167"></a><span class="lineno">  167</span> </div>
<div class="line"><a id="l00168" name="l00168"></a><span class="lineno">  168</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00169" name="l00169"></a><span class="lineno">  169</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00170" name="l00170"></a><span class="lineno">  170</span> </div>
<div class="line"><a id="l00171" name="l00171"></a><span class="lineno">  171</span>    uint64_t m_countCircularBufferOverruns;</div>
<div class="line"><a id="l00172" name="l00172"></a><span class="lineno">  172</span>};</div>
</div>
<div class="line"><a id="l00173" name="l00173"></a><span class="lineno">  173</span> </div>
<div class="foldopen" id="foldopen00174" data-start="{" data-end="};">
<div class="line"><a id="l00174" name="l00174"></a><span class="lineno"><a class="line" href="struct_tcpcl_v3_induct_connection_telemetry__t.html">  174</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS TcpclV3InductConnectionTelemetry_t : <span class="keyword">public</span> InductConnectionTelemetry_t {</div>
<div class="line"><a id="l00175" name="l00175"></a><span class="lineno">  175</span>    TELEMETRY_DEFINITIONS_EXPORT TcpclV3InductConnectionTelemetry_t();</div>
<div class="line"><a id="l00176" name="l00176"></a><span class="lineno">  176</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~TcpclV3InductConnectionTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00177" name="l00177"></a><span class="lineno">  177</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00178" name="l00178"></a><span class="lineno">  178</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00179" name="l00179"></a><span class="lineno">  179</span> </div>
<div class="line"><a id="l00180" name="l00180"></a><span class="lineno">  180</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00181" name="l00181"></a><span class="lineno">  181</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00182" name="l00182"></a><span class="lineno">  182</span> </div>
<div class="line"><a id="l00183" name="l00183"></a><span class="lineno">  183</span>    uint64_t m_totalIncomingFragmentsAcked;</div>
<div class="line"><a id="l00184" name="l00184"></a><span class="lineno">  184</span>    uint64_t m_totalOutgoingFragmentsSent;</div>
<div class="line"><a id="l00185" name="l00185"></a><span class="lineno">  185</span>    <span class="comment">//bidirectionality (identical to OutductTelemetry_t)</span></div>
<div class="line"><a id="l00186" name="l00186"></a><span class="lineno">  186</span>    uint64_t m_totalBundlesSentAndAcked;</div>
<div class="line"><a id="l00187" name="l00187"></a><span class="lineno">  187</span>    uint64_t m_totalBundleBytesSentAndAcked;</div>
<div class="line"><a id="l00188" name="l00188"></a><span class="lineno">  188</span>    uint64_t m_totalBundlesSent;</div>
<div class="line"><a id="l00189" name="l00189"></a><span class="lineno">  189</span>    uint64_t m_totalBundleBytesSent;</div>
<div class="line"><a id="l00190" name="l00190"></a><span class="lineno">  190</span>    uint64_t m_totalBundlesFailedToSend;</div>
<div class="line"><a id="l00191" name="l00191"></a><span class="lineno">  191</span>};</div>
</div>
<div class="line"><a id="l00192" name="l00192"></a><span class="lineno">  192</span> </div>
<div class="foldopen" id="foldopen00193" data-start="{" data-end="};">
<div class="line"><a id="l00193" name="l00193"></a><span class="lineno"><a class="line" href="struct_tcpcl_v4_induct_connection_telemetry__t.html">  193</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS TcpclV4InductConnectionTelemetry_t : <span class="keyword">public</span> InductConnectionTelemetry_t {</div>
<div class="line"><a id="l00194" name="l00194"></a><span class="lineno">  194</span>    TELEMETRY_DEFINITIONS_EXPORT TcpclV4InductConnectionTelemetry_t();</div>
<div class="line"><a id="l00195" name="l00195"></a><span class="lineno">  195</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~TcpclV4InductConnectionTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00196" name="l00196"></a><span class="lineno">  196</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00197" name="l00197"></a><span class="lineno">  197</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00198" name="l00198"></a><span class="lineno">  198</span> </div>
<div class="line"><a id="l00199" name="l00199"></a><span class="lineno">  199</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00200" name="l00200"></a><span class="lineno">  200</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00201" name="l00201"></a><span class="lineno">  201</span> </div>
<div class="line"><a id="l00202" name="l00202"></a><span class="lineno">  202</span>    uint64_t m_totalIncomingFragmentsAcked;</div>
<div class="line"><a id="l00203" name="l00203"></a><span class="lineno">  203</span>    uint64_t m_totalOutgoingFragmentsSent;</div>
<div class="line"><a id="l00204" name="l00204"></a><span class="lineno">  204</span>    <span class="comment">//bidirectionality (identical to OutductTelemetry_t)</span></div>
<div class="line"><a id="l00205" name="l00205"></a><span class="lineno">  205</span>    uint64_t m_totalBundlesSentAndAcked;</div>
<div class="line"><a id="l00206" name="l00206"></a><span class="lineno">  206</span>    uint64_t m_totalBundleBytesSentAndAcked;</div>
<div class="line"><a id="l00207" name="l00207"></a><span class="lineno">  207</span>    uint64_t m_totalBundlesSent;</div>
<div class="line"><a id="l00208" name="l00208"></a><span class="lineno">  208</span>    uint64_t m_totalBundleBytesSent;</div>
<div class="line"><a id="l00209" name="l00209"></a><span class="lineno">  209</span>    uint64_t m_totalBundlesFailedToSend;</div>
<div class="line"><a id="l00210" name="l00210"></a><span class="lineno">  210</span>};</div>
</div>
<div class="line"><a id="l00211" name="l00211"></a><span class="lineno">  211</span> </div>
<div class="foldopen" id="foldopen00212" data-start="{" data-end="};">
<div class="line"><a id="l00212" name="l00212"></a><span class="lineno"><a class="line" href="struct_slip_over_uart_induct_connection_telemetry__t.html">  212</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS SlipOverUartInductConnectionTelemetry_t : <span class="keyword">public</span> InductConnectionTelemetry_t {</div>
<div class="line"><a id="l00213" name="l00213"></a><span class="lineno">  213</span>    TELEMETRY_DEFINITIONS_EXPORT SlipOverUartInductConnectionTelemetry_t();</div>
<div class="line"><a id="l00214" name="l00214"></a><span class="lineno">  214</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~SlipOverUartInductConnectionTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00215" name="l00215"></a><span class="lineno">  215</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00216" name="l00216"></a><span class="lineno">  216</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00217" name="l00217"></a><span class="lineno">  217</span> </div>
<div class="line"><a id="l00218" name="l00218"></a><span class="lineno">  218</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00219" name="l00219"></a><span class="lineno">  219</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00220" name="l00220"></a><span class="lineno">  220</span> </div>
<div class="line"><a id="l00221" name="l00221"></a><span class="lineno">  221</span>    uint64_t m_totalSlipBytesSent;</div>
<div class="line"><a id="l00222" name="l00222"></a><span class="lineno">  222</span>    uint64_t m_totalSlipBytesReceived;</div>
<div class="line"><a id="l00223" name="l00223"></a><span class="lineno">  223</span>    uint64_t m_totalReceivedChunks;</div>
<div class="line"><a id="l00224" name="l00224"></a><span class="lineno">  224</span>    uint64_t m_largestReceivedBytesPerChunk;</div>
<div class="line"><a id="l00225" name="l00225"></a><span class="lineno">  225</span>    uint64_t m_averageReceivedBytesPerChunk;</div>
<div class="line"><a id="l00226" name="l00226"></a><span class="lineno">  226</span>    <span class="comment">//bidirectionality (identical to OutductTelemetry_t)</span></div>
<div class="line"><a id="l00227" name="l00227"></a><span class="lineno">  227</span>    uint64_t m_totalBundlesSentAndAcked;</div>
<div class="line"><a id="l00228" name="l00228"></a><span class="lineno">  228</span>    uint64_t m_totalBundleBytesSentAndAcked;</div>
<div class="line"><a id="l00229" name="l00229"></a><span class="lineno">  229</span>    uint64_t m_totalBundlesSent;</div>
<div class="line"><a id="l00230" name="l00230"></a><span class="lineno">  230</span>    uint64_t m_totalBundleBytesSent;</div>
<div class="line"><a id="l00231" name="l00231"></a><span class="lineno">  231</span>    uint64_t m_totalBundlesFailedToSend;</div>
<div class="line"><a id="l00232" name="l00232"></a><span class="lineno">  232</span>};</div>
</div>
<div class="line"><a id="l00233" name="l00233"></a><span class="lineno">  233</span> </div>
<div class="foldopen" id="foldopen00234" data-start="{" data-end="};">
<div class="line"><a id="l00234" name="l00234"></a><span class="lineno"><a class="line" href="struct_bp_over_encap_local_stream_induct_connection_telemetry__t.html">  234</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS BpOverEncapLocalStreamInductConnectionTelemetry_t : <span class="keyword">public</span> InductConnectionTelemetry_t {</div>
<div class="line"><a id="l00235" name="l00235"></a><span class="lineno">  235</span>    TELEMETRY_DEFINITIONS_EXPORT BpOverEncapLocalStreamInductConnectionTelemetry_t();</div>
<div class="line"><a id="l00236" name="l00236"></a><span class="lineno">  236</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~BpOverEncapLocalStreamInductConnectionTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00237" name="l00237"></a><span class="lineno">  237</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00238" name="l00238"></a><span class="lineno">  238</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00239" name="l00239"></a><span class="lineno">  239</span> </div>
<div class="line"><a id="l00240" name="l00240"></a><span class="lineno">  240</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00241" name="l00241"></a><span class="lineno">  241</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00242" name="l00242"></a><span class="lineno">  242</span> </div>
<div class="line"><a id="l00243" name="l00243"></a><span class="lineno">  243</span>    uint64_t m_totalEncapHeaderBytesSent;</div>
<div class="line"><a id="l00244" name="l00244"></a><span class="lineno">  244</span>    uint64_t m_totalEncapHeaderBytesReceived;</div>
<div class="line"><a id="l00245" name="l00245"></a><span class="lineno">  245</span>    uint64_t m_largestEncapHeaderSizeBytesReceived;</div>
<div class="line"><a id="l00246" name="l00246"></a><span class="lineno">  246</span>    uint64_t m_smallestEncapHeaderSizeBytesReceived;</div>
<div class="line"><a id="l00247" name="l00247"></a><span class="lineno">  247</span>    uint64_t m_averageEncapHeaderSizeBytesReceived;</div>
<div class="line"><a id="l00248" name="l00248"></a><span class="lineno">  248</span>    <span class="comment">//bidirectionality (identical to OutductTelemetry_t)</span></div>
<div class="line"><a id="l00249" name="l00249"></a><span class="lineno">  249</span>    uint64_t m_totalBundlesSentAndAcked;</div>
<div class="line"><a id="l00250" name="l00250"></a><span class="lineno">  250</span>    uint64_t m_totalBundleBytesSentAndAcked;</div>
<div class="line"><a id="l00251" name="l00251"></a><span class="lineno">  251</span>    uint64_t m_totalBundlesSent;</div>
<div class="line"><a id="l00252" name="l00252"></a><span class="lineno">  252</span>    uint64_t m_totalBundleBytesSent;</div>
<div class="line"><a id="l00253" name="l00253"></a><span class="lineno">  253</span>    uint64_t m_totalBundlesFailedToSend;</div>
<div class="line"><a id="l00254" name="l00254"></a><span class="lineno">  254</span>};</div>
</div>
<div class="line"><a id="l00255" name="l00255"></a><span class="lineno">  255</span> </div>
<div class="foldopen" id="foldopen00256" data-start="{" data-end="};">
<div class="line"><a id="l00256" name="l00256"></a><span class="lineno"><a class="line" href="struct_ltp_induct_connection_telemetry__t.html">  256</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS LtpInductConnectionTelemetry_t : <span class="keyword">public</span> InductConnectionTelemetry_t {</div>
<div class="line"><a id="l00257" name="l00257"></a><span class="lineno">  257</span>    TELEMETRY_DEFINITIONS_EXPORT LtpInductConnectionTelemetry_t();</div>
<div class="line"><a id="l00258" name="l00258"></a><span class="lineno">  258</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~LtpInductConnectionTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00259" name="l00259"></a><span class="lineno">  259</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00260" name="l00260"></a><span class="lineno">  260</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> InductConnectionTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00261" name="l00261"></a><span class="lineno">  261</span> </div>
<div class="line"><a id="l00262" name="l00262"></a><span class="lineno">  262</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00263" name="l00263"></a><span class="lineno">  263</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00264" name="l00264"></a><span class="lineno">  264</span> </div>
<div class="line"><a id="l00265" name="l00265"></a><span class="lineno">  265</span>    <span class="comment">//session receiver stats</span></div>
<div class="line"><a id="l00266" name="l00266"></a><span class="lineno">  266</span>    uint64_t m_numReportSegmentTimerExpiredCallbacks;</div>
<div class="line"><a id="l00267" name="l00267"></a><span class="lineno">  267</span>    uint64_t m_numReportSegmentsUnableToBeIssued;</div>
<div class="line"><a id="l00268" name="l00268"></a><span class="lineno">  268</span>    uint64_t m_numReportSegmentsTooLargeAndNeedingSplit;</div>
<div class="line"><a id="l00269" name="l00269"></a><span class="lineno">  269</span>    uint64_t m_numReportSegmentsCreatedViaSplit;</div>
<div class="line"><a id="l00270" name="l00270"></a><span class="lineno">  270</span>    uint64_t m_numGapsFilledByOutOfOrderDataSegments;</div>
<div class="line"><a id="l00271" name="l00271"></a><span class="lineno">  271</span>    uint64_t m_numDelayedFullyClaimedPrimaryReportSegmentsSent;</div>
<div class="line"><a id="l00272" name="l00272"></a><span class="lineno">  272</span>    uint64_t m_numDelayedFullyClaimedSecondaryReportSegmentsSent;</div>
<div class="line"><a id="l00273" name="l00273"></a><span class="lineno">  273</span>    uint64_t m_numDelayedPartiallyClaimedPrimaryReportSegmentsSent;</div>
<div class="line"><a id="l00274" name="l00274"></a><span class="lineno">  274</span>    uint64_t m_numDelayedPartiallyClaimedSecondaryReportSegmentsSent;</div>
<div class="line"><a id="l00275" name="l00275"></a><span class="lineno">  275</span>    uint64_t m_totalCancelSegmentsStarted;</div>
<div class="line"><a id="l00276" name="l00276"></a><span class="lineno">  276</span>    uint64_t m_totalCancelSegmentSendRetries;</div>
<div class="line"><a id="l00277" name="l00277"></a><span class="lineno">  277</span>    uint64_t m_totalCancelSegmentsFailedToSend;</div>
<div class="line"><a id="l00278" name="l00278"></a><span class="lineno">  278</span>    uint64_t m_totalCancelSegmentsAcknowledged;</div>
<div class="line"><a id="l00279" name="l00279"></a><span class="lineno">  279</span>    uint64_t m_numRxSessionsCancelledBySender;</div>
<div class="line"><a id="l00280" name="l00280"></a><span class="lineno">  280</span>    uint64_t m_numStagnantRxSessionsDeleted;</div>
<div class="line"><a id="l00281" name="l00281"></a><span class="lineno">  281</span> </div>
<div class="line"><a id="l00282" name="l00282"></a><span class="lineno">  282</span>    <span class="comment">//ltp udp engine</span></div>
<div class="line"><a id="l00283" name="l00283"></a><span class="lineno">  283</span>    uint64_t m_countUdpPacketsSent;</div>
<div class="line"><a id="l00284" name="l00284"></a><span class="lineno">  284</span>    uint64_t m_countRxUdpCircularBufferOverruns;</div>
<div class="line"><a id="l00285" name="l00285"></a><span class="lineno">  285</span>    uint64_t m_countTxUdpPacketsLimitedByRate;</div>
<div class="line"><a id="l00286" name="l00286"></a><span class="lineno">  286</span>    </div>
<div class="line"><a id="l00287" name="l00287"></a><span class="lineno">  287</span>};</div>
</div>
<div class="line"><a id="l00288" name="l00288"></a><span class="lineno">  288</span> </div>
<div class="foldopen" id="foldopen00289" data-start="{" data-end="};">
<div class="line"><a id="l00289" name="l00289"></a><span class="lineno"><a class="line" href="struct_induct_telemetry__t.html">  289</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS InductTelemetry_t : <span class="keyword">public</span> JsonSerializable {</div>
<div class="line"><a id="l00290" name="l00290"></a><span class="lineno">  290</span>    TELEMETRY_DEFINITIONS_EXPORT InductTelemetry_t();</div>
<div class="line"><a id="l00291" name="l00291"></a><span class="lineno">  291</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> InductTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00292" name="l00292"></a><span class="lineno">  292</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> InductTelemetry_t&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00293" name="l00293"></a><span class="lineno">  293</span> </div>
<div class="line"><a id="l00294" name="l00294"></a><span class="lineno">  294</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00295" name="l00295"></a><span class="lineno">  295</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00296" name="l00296"></a><span class="lineno">  296</span> </div>
<div class="line"><a id="l00297" name="l00297"></a><span class="lineno">  297</span>    std::string m_convergenceLayer;</div>
<div class="line"><a id="l00298" name="l00298"></a><span class="lineno">  298</span>    std::list&lt;std::unique_ptr&lt;InductConnectionTelemetry_t&gt; &gt; m_listInductConnections;</div>
<div class="line"><a id="l00299" name="l00299"></a><span class="lineno">  299</span>};</div>
</div>
<div class="line"><a id="l00300" name="l00300"></a><span class="lineno">  300</span> </div>
<div class="foldopen" id="foldopen00301" data-start="{" data-end="};">
<div class="line"><a id="l00301" name="l00301"></a><span class="lineno"><a class="line" href="struct_all_induct_telemetry__t.html">  301</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS AllInductTelemetry_t : <span class="keyword">public</span> JsonSerializable {</div>
<div class="line"><a id="l00302" name="l00302"></a><span class="lineno">  302</span>    TELEMETRY_DEFINITIONS_EXPORT AllInductTelemetry_t();</div>
<div class="line"><a id="l00303" name="l00303"></a><span class="lineno">  303</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> AllInductTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00304" name="l00304"></a><span class="lineno">  304</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> AllInductTelemetry_t&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00305" name="l00305"></a><span class="lineno">  305</span> </div>
<div class="line"><a id="l00306" name="l00306"></a><span class="lineno">  306</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00307" name="l00307"></a><span class="lineno">  307</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00308" name="l00308"></a><span class="lineno">  308</span>    uint64_t m_timestampMilliseconds;</div>
<div class="line"><a id="l00309" name="l00309"></a><span class="lineno">  309</span>    <span class="comment">//ingress specific</span></div>
<div class="line"><a id="l00310" name="l00310"></a><span class="lineno">  310</span>    uint64_t m_bundleCountEgress;</div>
<div class="line"><a id="l00311" name="l00311"></a><span class="lineno">  311</span>    uint64_t m_bundleCountStorage;</div>
<div class="line"><a id="l00312" name="l00312"></a><span class="lineno">  312</span>    uint64_t m_bundleByteCountEgress;</div>
<div class="line"><a id="l00313" name="l00313"></a><span class="lineno">  313</span>    uint64_t m_bundleByteCountStorage;</div>
<div class="line"><a id="l00314" name="l00314"></a><span class="lineno">  314</span>    <span class="comment">//inducts specific</span></div>
<div class="line"><a id="l00315" name="l00315"></a><span class="lineno">  315</span>    std::list&lt;InductTelemetry_t&gt; m_listAllInducts;</div>
<div class="line"><a id="l00316" name="l00316"></a><span class="lineno">  316</span>};</div>
</div>
<div class="line"><a id="l00317" name="l00317"></a><span class="lineno">  317</span> </div>
<div class="line"><a id="l00318" name="l00318"></a><span class="lineno">  318</span> </div>
<div class="line"><a id="l00319" name="l00319"></a><span class="lineno">  319</span> </div>
<div class="foldopen" id="foldopen00320" data-start="{" data-end="};">
<div class="line"><a id="l00320" name="l00320"></a><span class="lineno"><a class="line" href="struct_outduct_telemetry__t.html">  320</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS OutductTelemetry_t : <span class="keyword">public</span> JsonSerializable</div>
<div class="line"><a id="l00321" name="l00321"></a><span class="lineno">  321</span>{</div>
<div class="line"><a id="l00322" name="l00322"></a><span class="lineno">  322</span>    TELEMETRY_DEFINITIONS_EXPORT OutductTelemetry_t();</div>
<div class="line"><a id="l00323" name="l00323"></a><span class="lineno">  323</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~OutductTelemetry_t();</div>
<div class="line"><a id="l00324" name="l00324"></a><span class="lineno">  324</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00325" name="l00325"></a><span class="lineno">  325</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00326" name="l00326"></a><span class="lineno">  326</span> </div>
<div class="line"><a id="l00327" name="l00327"></a><span class="lineno">  327</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00328" name="l00328"></a><span class="lineno">  328</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00329" name="l00329"></a><span class="lineno">  329</span> </div>
<div class="line"><a id="l00330" name="l00330"></a><span class="lineno">  330</span>    std::string m_convergenceLayer;</div>
<div class="line"><a id="l00331" name="l00331"></a><span class="lineno">  331</span>    uint64_t m_totalBundlesAcked;</div>
<div class="line"><a id="l00332" name="l00332"></a><span class="lineno">  332</span>    uint64_t m_totalBundleBytesAcked;</div>
<div class="line"><a id="l00333" name="l00333"></a><span class="lineno">  333</span>    uint64_t m_totalBundlesSent;</div>
<div class="line"><a id="l00334" name="l00334"></a><span class="lineno">  334</span>    uint64_t m_totalBundleBytesSent;</div>
<div class="line"><a id="l00335" name="l00335"></a><span class="lineno">  335</span>    uint64_t m_totalBundlesFailedToSend;</div>
<div class="line"><a id="l00336" name="l00336"></a><span class="lineno">  336</span>    <span class="keywordtype">bool</span> m_linkIsUpPhysically;</div>
<div class="line"><a id="l00337" name="l00337"></a><span class="lineno">  337</span>    <span class="keywordtype">bool</span> m_linkIsUpPerTimeSchedule;</div>
<div class="line"><a id="l00338" name="l00338"></a><span class="lineno">  338</span> </div>
<div class="line"><a id="l00339" name="l00339"></a><span class="lineno">  339</span>    TELEMETRY_DEFINITIONS_EXPORT uint64_t GetTotalBundlesQueued() <span class="keyword">const</span>;</div>
<div class="line"><a id="l00340" name="l00340"></a><span class="lineno">  340</span>    TELEMETRY_DEFINITIONS_EXPORT uint64_t GetTotalBundleBytesQueued() <span class="keyword">const</span>;</div>
<div class="line"><a id="l00341" name="l00341"></a><span class="lineno">  341</span>};</div>
</div>
<div class="line"><a id="l00342" name="l00342"></a><span class="lineno">  342</span> </div>
<div class="foldopen" id="foldopen00343" data-start="{" data-end="};">
<div class="line"><a id="l00343" name="l00343"></a><span class="lineno"><a class="line" href="struct_stcp_outduct_telemetry__t.html">  343</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS StcpOutductTelemetry_t : <span class="keyword">public</span> OutductTelemetry_t {</div>
<div class="line"><a id="l00344" name="l00344"></a><span class="lineno">  344</span>    TELEMETRY_DEFINITIONS_EXPORT StcpOutductTelemetry_t();</div>
<div class="line"><a id="l00345" name="l00345"></a><span class="lineno">  345</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~StcpOutductTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00346" name="l00346"></a><span class="lineno">  346</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00347" name="l00347"></a><span class="lineno">  347</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00348" name="l00348"></a><span class="lineno">  348</span> </div>
<div class="line"><a id="l00349" name="l00349"></a><span class="lineno">  349</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00350" name="l00350"></a><span class="lineno">  350</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00351" name="l00351"></a><span class="lineno">  351</span> </div>
<div class="line"><a id="l00352" name="l00352"></a><span class="lineno">  352</span>    uint64_t m_totalStcpBytesSent;</div>
<div class="line"><a id="l00353" name="l00353"></a><span class="lineno">  353</span>    uint64_t m_numTcpReconnectAttempts;</div>
<div class="line"><a id="l00354" name="l00354"></a><span class="lineno">  354</span>};</div>
</div>
<div class="line"><a id="l00355" name="l00355"></a><span class="lineno">  355</span> </div>
<div class="foldopen" id="foldopen00356" data-start="{" data-end="};">
<div class="line"><a id="l00356" name="l00356"></a><span class="lineno"><a class="line" href="struct_ltp_outduct_telemetry__t.html">  356</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS LtpOutductTelemetry_t : <span class="keyword">public</span> OutductTelemetry_t {</div>
<div class="line"><a id="l00357" name="l00357"></a><span class="lineno">  357</span>    TELEMETRY_DEFINITIONS_EXPORT LtpOutductTelemetry_t();</div>
<div class="line"><a id="l00358" name="l00358"></a><span class="lineno">  358</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~LtpOutductTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00359" name="l00359"></a><span class="lineno">  359</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00360" name="l00360"></a><span class="lineno">  360</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00361" name="l00361"></a><span class="lineno">  361</span> </div>
<div class="line"><a id="l00362" name="l00362"></a><span class="lineno">  362</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00363" name="l00363"></a><span class="lineno">  363</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00364" name="l00364"></a><span class="lineno">  364</span> </div>
<div class="line"><a id="l00365" name="l00365"></a><span class="lineno">  365</span>    <span class="comment">//ltp engine session sender stats</span></div>
<div class="line"><a id="l00366" name="l00366"></a><span class="lineno">  366</span>    uint64_t m_numCheckpointsExpired;</div>
<div class="line"><a id="l00367" name="l00367"></a><span class="lineno">  367</span>    uint64_t m_numDiscretionaryCheckpointsNotResent;</div>
<div class="line"><a id="l00368" name="l00368"></a><span class="lineno">  368</span>    uint64_t m_numDeletedFullyClaimedPendingReports;</div>
<div class="line"><a id="l00369" name="l00369"></a><span class="lineno">  369</span>    uint64_t m_totalCancelSegmentsStarted;</div>
<div class="line"><a id="l00370" name="l00370"></a><span class="lineno">  370</span>    uint64_t m_totalCancelSegmentSendRetries;</div>
<div class="line"><a id="l00371" name="l00371"></a><span class="lineno">  371</span>    uint64_t m_totalCancelSegmentsFailedToSend;</div>
<div class="line"><a id="l00372" name="l00372"></a><span class="lineno">  372</span>    uint64_t m_totalCancelSegmentsAcknowledged;</div>
<div class="line"><a id="l00373" name="l00373"></a><span class="lineno">  373</span>    uint64_t m_totalPingsStarted;</div>
<div class="line"><a id="l00374" name="l00374"></a><span class="lineno">  374</span>    uint64_t m_totalPingRetries;</div>
<div class="line"><a id="l00375" name="l00375"></a><span class="lineno">  375</span>    uint64_t m_totalPingsFailedToSend;</div>
<div class="line"><a id="l00376" name="l00376"></a><span class="lineno">  376</span>    uint64_t m_totalPingsAcknowledged;</div>
<div class="line"><a id="l00377" name="l00377"></a><span class="lineno">  377</span>    uint64_t m_numTxSessionsReturnedToStorage;</div>
<div class="line"><a id="l00378" name="l00378"></a><span class="lineno">  378</span>    uint64_t m_numTxSessionsCancelledByReceiver;</div>
<div class="line"><a id="l00379" name="l00379"></a><span class="lineno">  379</span> </div>
<div class="line"><a id="l00380" name="l00380"></a><span class="lineno">  380</span>    <span class="comment">//ltp udp engine</span></div>
<div class="line"><a id="l00381" name="l00381"></a><span class="lineno">  381</span>    uint64_t m_countUdpPacketsSent;</div>
<div class="line"><a id="l00382" name="l00382"></a><span class="lineno">  382</span>    uint64_t m_countRxUdpCircularBufferOverruns;</div>
<div class="line"><a id="l00383" name="l00383"></a><span class="lineno">  383</span>    uint64_t m_countTxUdpPacketsLimitedByRate;</div>
<div class="line"><a id="l00384" name="l00384"></a><span class="lineno">  384</span>};</div>
</div>
<div class="line"><a id="l00385" name="l00385"></a><span class="lineno">  385</span> </div>
<div class="foldopen" id="foldopen00386" data-start="{" data-end="};">
<div class="line"><a id="l00386" name="l00386"></a><span class="lineno"><a class="line" href="struct_tcpcl_v3_outduct_telemetry__t.html">  386</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS TcpclV3OutductTelemetry_t : <span class="keyword">public</span> OutductTelemetry_t {</div>
<div class="line"><a id="l00387" name="l00387"></a><span class="lineno">  387</span>    TELEMETRY_DEFINITIONS_EXPORT TcpclV3OutductTelemetry_t();</div>
<div class="line"><a id="l00388" name="l00388"></a><span class="lineno">  388</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~TcpclV3OutductTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00389" name="l00389"></a><span class="lineno">  389</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00390" name="l00390"></a><span class="lineno">  390</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00391" name="l00391"></a><span class="lineno">  391</span> </div>
<div class="line"><a id="l00392" name="l00392"></a><span class="lineno">  392</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00393" name="l00393"></a><span class="lineno">  393</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00394" name="l00394"></a><span class="lineno">  394</span> </div>
<div class="line"><a id="l00395" name="l00395"></a><span class="lineno">  395</span>    uint64_t m_totalFragmentsAcked;</div>
<div class="line"><a id="l00396" name="l00396"></a><span class="lineno">  396</span>    uint64_t m_totalFragmentsSent;</div>
<div class="line"><a id="l00397" name="l00397"></a><span class="lineno">  397</span>    <span class="comment">//bidirectionality (identical to InductConnectionTelemetry_t)</span></div>
<div class="line"><a id="l00398" name="l00398"></a><span class="lineno">  398</span>    uint64_t m_totalBundlesReceived;</div>
<div class="line"><a id="l00399" name="l00399"></a><span class="lineno">  399</span>    uint64_t m_totalBundleBytesReceived;</div>
<div class="line"><a id="l00400" name="l00400"></a><span class="lineno">  400</span>    uint64_t m_numTcpReconnectAttempts;</div>
<div class="line"><a id="l00401" name="l00401"></a><span class="lineno">  401</span>};</div>
</div>
<div class="line"><a id="l00402" name="l00402"></a><span class="lineno">  402</span> </div>
<div class="foldopen" id="foldopen00403" data-start="{" data-end="};">
<div class="line"><a id="l00403" name="l00403"></a><span class="lineno"><a class="line" href="struct_tcpcl_v4_outduct_telemetry__t.html">  403</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS TcpclV4OutductTelemetry_t : <span class="keyword">public</span> OutductTelemetry_t {</div>
<div class="line"><a id="l00404" name="l00404"></a><span class="lineno">  404</span>    TELEMETRY_DEFINITIONS_EXPORT TcpclV4OutductTelemetry_t();</div>
<div class="line"><a id="l00405" name="l00405"></a><span class="lineno">  405</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~TcpclV4OutductTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00406" name="l00406"></a><span class="lineno">  406</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00407" name="l00407"></a><span class="lineno">  407</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00408" name="l00408"></a><span class="lineno">  408</span> </div>
<div class="line"><a id="l00409" name="l00409"></a><span class="lineno">  409</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00410" name="l00410"></a><span class="lineno">  410</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00411" name="l00411"></a><span class="lineno">  411</span> </div>
<div class="line"><a id="l00412" name="l00412"></a><span class="lineno">  412</span>    uint64_t m_totalFragmentsAcked;</div>
<div class="line"><a id="l00413" name="l00413"></a><span class="lineno">  413</span>    uint64_t m_totalFragmentsSent;</div>
<div class="line"><a id="l00414" name="l00414"></a><span class="lineno">  414</span>    <span class="comment">//bidirectionality (identical to InductConnectionTelemetry_t)</span></div>
<div class="line"><a id="l00415" name="l00415"></a><span class="lineno">  415</span>    uint64_t m_totalBundlesReceived;</div>
<div class="line"><a id="l00416" name="l00416"></a><span class="lineno">  416</span>    uint64_t m_totalBundleBytesReceived;</div>
<div class="line"><a id="l00417" name="l00417"></a><span class="lineno">  417</span>    uint64_t m_numTcpReconnectAttempts;</div>
<div class="line"><a id="l00418" name="l00418"></a><span class="lineno">  418</span>};</div>
</div>
<div class="line"><a id="l00419" name="l00419"></a><span class="lineno">  419</span> </div>
<div class="foldopen" id="foldopen00420" data-start="{" data-end="};">
<div class="line"><a id="l00420" name="l00420"></a><span class="lineno"><a class="line" href="struct_slip_over_uart_outduct_telemetry__t.html">  420</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS SlipOverUartOutductTelemetry_t : <span class="keyword">public</span> OutductTelemetry_t {</div>
<div class="line"><a id="l00421" name="l00421"></a><span class="lineno">  421</span>    TELEMETRY_DEFINITIONS_EXPORT SlipOverUartOutductTelemetry_t();</div>
<div class="line"><a id="l00422" name="l00422"></a><span class="lineno">  422</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~SlipOverUartOutductTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00423" name="l00423"></a><span class="lineno">  423</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00424" name="l00424"></a><span class="lineno">  424</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00425" name="l00425"></a><span class="lineno">  425</span> </div>
<div class="line"><a id="l00426" name="l00426"></a><span class="lineno">  426</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00427" name="l00427"></a><span class="lineno">  427</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00428" name="l00428"></a><span class="lineno">  428</span> </div>
<div class="line"><a id="l00429" name="l00429"></a><span class="lineno">  429</span>    uint64_t m_totalSlipBytesSent;</div>
<div class="line"><a id="l00430" name="l00430"></a><span class="lineno">  430</span>    uint64_t m_totalSlipBytesReceived;</div>
<div class="line"><a id="l00431" name="l00431"></a><span class="lineno">  431</span>    uint64_t m_totalReceivedChunks;</div>
<div class="line"><a id="l00432" name="l00432"></a><span class="lineno">  432</span>    uint64_t m_largestReceivedBytesPerChunk;</div>
<div class="line"><a id="l00433" name="l00433"></a><span class="lineno">  433</span>    uint64_t m_averageReceivedBytesPerChunk;</div>
<div class="line"><a id="l00434" name="l00434"></a><span class="lineno">  434</span>    <span class="comment">//bidirectionality (identical to InductConnectionTelemetry_t)</span></div>
<div class="line"><a id="l00435" name="l00435"></a><span class="lineno">  435</span>    uint64_t m_totalBundlesReceived;</div>
<div class="line"><a id="l00436" name="l00436"></a><span class="lineno">  436</span>    uint64_t m_totalBundleBytesReceived;</div>
<div class="line"><a id="l00437" name="l00437"></a><span class="lineno">  437</span>};</div>
</div>
<div class="line"><a id="l00438" name="l00438"></a><span class="lineno">  438</span> </div>
<div class="foldopen" id="foldopen00439" data-start="{" data-end="};">
<div class="line"><a id="l00439" name="l00439"></a><span class="lineno"><a class="line" href="struct_bp_over_encap_local_stream_outduct_telemetry__t.html">  439</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS BpOverEncapLocalStreamOutductTelemetry_t : <span class="keyword">public</span> OutductTelemetry_t {</div>
<div class="line"><a id="l00440" name="l00440"></a><span class="lineno">  440</span>    TELEMETRY_DEFINITIONS_EXPORT BpOverEncapLocalStreamOutductTelemetry_t();</div>
<div class="line"><a id="l00441" name="l00441"></a><span class="lineno">  441</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~BpOverEncapLocalStreamOutductTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00442" name="l00442"></a><span class="lineno">  442</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00443" name="l00443"></a><span class="lineno">  443</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00444" name="l00444"></a><span class="lineno">  444</span> </div>
<div class="line"><a id="l00445" name="l00445"></a><span class="lineno">  445</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00446" name="l00446"></a><span class="lineno">  446</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00447" name="l00447"></a><span class="lineno">  447</span> </div>
<div class="line"><a id="l00448" name="l00448"></a><span class="lineno">  448</span>    uint64_t m_totalEncapHeaderBytesSent;</div>
<div class="line"><a id="l00449" name="l00449"></a><span class="lineno">  449</span>    uint64_t m_totalEncapHeaderBytesReceived;</div>
<div class="line"><a id="l00450" name="l00450"></a><span class="lineno">  450</span>    uint64_t m_largestEncapHeaderSizeBytesSent;</div>
<div class="line"><a id="l00451" name="l00451"></a><span class="lineno">  451</span>    uint64_t m_smallestEncapHeaderSizeBytesSent;</div>
<div class="line"><a id="l00452" name="l00452"></a><span class="lineno">  452</span>    uint64_t m_averageEncapHeaderSizeBytesSent;</div>
<div class="line"><a id="l00453" name="l00453"></a><span class="lineno">  453</span>    <span class="comment">//bidirectionality (identical to InductConnectionTelemetry_t)</span></div>
<div class="line"><a id="l00454" name="l00454"></a><span class="lineno">  454</span>    uint64_t m_totalBundlesReceived;</div>
<div class="line"><a id="l00455" name="l00455"></a><span class="lineno">  455</span>    uint64_t m_totalBundleBytesReceived;</div>
<div class="line"><a id="l00456" name="l00456"></a><span class="lineno">  456</span>};</div>
</div>
<div class="line"><a id="l00457" name="l00457"></a><span class="lineno">  457</span> </div>
<div class="foldopen" id="foldopen00458" data-start="{" data-end="};">
<div class="line"><a id="l00458" name="l00458"></a><span class="lineno"><a class="line" href="struct_udp_outduct_telemetry__t.html">  458</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS UdpOutductTelemetry_t : <span class="keyword">public</span> OutductTelemetry_t {</div>
<div class="line"><a id="l00459" name="l00459"></a><span class="lineno">  459</span>    TELEMETRY_DEFINITIONS_EXPORT UdpOutductTelemetry_t();</div>
<div class="line"><a id="l00460" name="l00460"></a><span class="lineno">  460</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~UdpOutductTelemetry_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00461" name="l00461"></a><span class="lineno">  461</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00462" name="l00462"></a><span class="lineno">  462</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> OutductTelemetry_t&amp; o) <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00463" name="l00463"></a><span class="lineno">  463</span> </div>
<div class="line"><a id="l00464" name="l00464"></a><span class="lineno">  464</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00465" name="l00465"></a><span class="lineno">  465</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00466" name="l00466"></a><span class="lineno">  466</span> </div>
<div class="line"><a id="l00467" name="l00467"></a><span class="lineno">  467</span>    uint64_t m_totalPacketsSent;</div>
<div class="line"><a id="l00468" name="l00468"></a><span class="lineno">  468</span>    uint64_t m_totalPacketBytesSent;</div>
<div class="line"><a id="l00469" name="l00469"></a><span class="lineno">  469</span>    uint64_t m_totalPacketsDequeuedForSend;</div>
<div class="line"><a id="l00470" name="l00470"></a><span class="lineno">  470</span>    uint64_t m_totalPacketBytesDequeuedForSend;</div>
<div class="line"><a id="l00471" name="l00471"></a><span class="lineno">  471</span>    uint64_t m_totalPacketsLimitedByRate;</div>
<div class="line"><a id="l00472" name="l00472"></a><span class="lineno">  472</span>};</div>
</div>
<div class="line"><a id="l00473" name="l00473"></a><span class="lineno">  473</span> </div>
<div class="foldopen" id="foldopen00474" data-start="{" data-end="};">
<div class="line"><a id="l00474" name="l00474"></a><span class="lineno"><a class="line" href="struct_all_outduct_telemetry__t.html">  474</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS AllOutductTelemetry_t : <span class="keyword">public</span> JsonSerializable {</div>
<div class="line"><a id="l00475" name="l00475"></a><span class="lineno">  475</span>    TELEMETRY_DEFINITIONS_EXPORT AllOutductTelemetry_t();</div>
<div class="line"><a id="l00476" name="l00476"></a><span class="lineno">  476</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> AllOutductTelemetry_t&amp; o) <span class="keyword">const</span>; <span class="comment">//operator ==</span></div>
<div class="line"><a id="l00477" name="l00477"></a><span class="lineno">  477</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> AllOutductTelemetry_t&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00478" name="l00478"></a><span class="lineno">  478</span> </div>
<div class="line"><a id="l00479" name="l00479"></a><span class="lineno">  479</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00480" name="l00480"></a><span class="lineno">  480</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00481" name="l00481"></a><span class="lineno">  481</span>    uint64_t m_timestampMilliseconds;</div>
<div class="line"><a id="l00482" name="l00482"></a><span class="lineno">  482</span>    uint64_t m_totalBundlesGivenToOutducts;</div>
<div class="line"><a id="l00483" name="l00483"></a><span class="lineno">  483</span>    uint64_t m_totalBundleBytesGivenToOutducts;</div>
<div class="line"><a id="l00484" name="l00484"></a><span class="lineno">  484</span>    uint64_t m_totalTcpclBundlesReceived;</div>
<div class="line"><a id="l00485" name="l00485"></a><span class="lineno">  485</span>    uint64_t m_totalTcpclBundleBytesReceived;</div>
<div class="line"><a id="l00486" name="l00486"></a><span class="lineno">  486</span>    uint64_t m_totalStorageToIngressOpportunisticBundles;</div>
<div class="line"><a id="l00487" name="l00487"></a><span class="lineno">  487</span>    uint64_t m_totalStorageToIngressOpportunisticBundleBytes;</div>
<div class="line"><a id="l00488" name="l00488"></a><span class="lineno">  488</span>    uint64_t m_totalBundlesSuccessfullySent;</div>
<div class="line"><a id="l00489" name="l00489"></a><span class="lineno">  489</span>    uint64_t m_totalBundleBytesSuccessfullySent;</div>
<div class="line"><a id="l00490" name="l00490"></a><span class="lineno">  490</span>    std::list&lt;std::unique_ptr&lt;OutductTelemetry_t&gt; &gt; m_listAllOutducts;</div>
<div class="line"><a id="l00491" name="l00491"></a><span class="lineno">  491</span>};</div>
</div>
<div class="line"><a id="l00492" name="l00492"></a><span class="lineno">  492</span> </div>
<div class="foldopen" id="foldopen00493" data-start="{" data-end="};">
<div class="line"><a id="l00493" name="l00493"></a><span class="lineno"><a class="line" href="struct_api_command__t.html">  493</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> : <span class="keyword">public</span> JsonSerializable {</div>
<div class="line"><a id="l00494" name="l00494"></a><span class="lineno">  494</span>    std::string m_apiCall;</div>
<div class="line"><a id="l00495" name="l00495"></a><span class="lineno">  495</span> </div>
<div class="line"><a id="l00496" name="l00496"></a><span class="lineno">  496</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>();</div>
<div class="line"><a id="l00497" name="l00497"></a><span class="lineno">  497</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">~ApiCommand_t</a>();</div>
<div class="line"><a id="l00498" name="l00498"></a><span class="lineno">  498</span> </div>
<div class="line"><a id="l00499" name="l00499"></a><span class="lineno">  499</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00500" name="l00500"></a><span class="lineno">  500</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00501" name="l00501"></a><span class="lineno">  501</span> </div>
<div class="line"><a id="l00502" name="l00502"></a><span class="lineno">  502</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00503" name="l00503"></a><span class="lineno">  503</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00504" name="l00504"></a><span class="lineno">  504</span> </div>
<div class="line"><a id="l00505" name="l00505"></a><span class="lineno">  505</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> std::shared_ptr&lt;ApiCommand_t&gt; CreateFromJson(<span class="keyword">const</span> std::string&amp; jsonStr);</div>
<div class="line"><a id="l00506" name="l00506"></a><span class="lineno">  506</span>};</div>
</div>
<div class="line"><a id="l00507" name="l00507"></a><span class="lineno">  507</span> </div>
<div class="foldopen" id="foldopen00508" data-start="{" data-end="};">
<div class="line"><a id="l00508" name="l00508"></a><span class="lineno"><a class="line" href="struct_get_storage_api_command__t.html">  508</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_get_storage_api_command__t.html#ae1ebd0ecea435a3bfdb0be87c8a3dc1e">GetStorageApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00509" name="l00509"></a><span class="lineno">  509</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_get_storage_api_command__t.html#ae1ebd0ecea435a3bfdb0be87c8a3dc1e">GetStorageApiCommand_t</a>();</div>
<div class="line"><a id="l00510" name="l00510"></a><span class="lineno">  510</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <a class="code hl_function" href="struct_get_storage_api_command__t.html#ae1ebd0ecea435a3bfdb0be87c8a3dc1e">~GetStorageApiCommand_t</a>() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00511" name="l00511"></a><span class="lineno"><a class="line" href="struct_get_storage_api_command__t.html#a9a9681d2e45a33049337f6c720911ac0">  511</a></span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string <a class="code hl_variable" href="struct_get_storage_api_command__t.html#a9a9681d2e45a33049337f6c720911ac0">name</a>;</div>
<div class="line"><a id="l00512" name="l00512"></a><span class="lineno">  512</span>};</div>
</div>
<div class="line"><a id="l00513" name="l00513"></a><span class="lineno">  513</span> </div>
<div class="foldopen" id="foldopen00514" data-start="{" data-end="};">
<div class="line"><a id="l00514" name="l00514"></a><span class="lineno"><a class="line" href="struct_get_outducts_api_command__t.html">  514</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_get_outducts_api_command__t.html#ac8d855c7c576cc773bcbd5b9edccd4d9">GetOutductsApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00515" name="l00515"></a><span class="lineno">  515</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_get_outducts_api_command__t.html#ac8d855c7c576cc773bcbd5b9edccd4d9">GetOutductsApiCommand_t</a>();</div>
<div class="line"><a id="l00516" name="l00516"></a><span class="lineno">  516</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <a class="code hl_function" href="struct_get_outducts_api_command__t.html#ac8d855c7c576cc773bcbd5b9edccd4d9">~GetOutductsApiCommand_t</a>() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00517" name="l00517"></a><span class="lineno">  517</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00518" name="l00518"></a><span class="lineno">  518</span>};</div>
</div>
<div class="line"><a id="l00519" name="l00519"></a><span class="lineno">  519</span> </div>
<div class="foldopen" id="foldopen00520" data-start="{" data-end="};">
<div class="line"><a id="l00520" name="l00520"></a><span class="lineno"><a class="line" href="struct_get_outduct_capabilities_api_command__t.html">  520</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_get_outduct_capabilities_api_command__t.html#a880110baabe839e54f698d9aa6dac224">GetOutductCapabilitiesApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00521" name="l00521"></a><span class="lineno">  521</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_get_outduct_capabilities_api_command__t.html#a880110baabe839e54f698d9aa6dac224">GetOutductCapabilitiesApiCommand_t</a>();</div>
<div class="line"><a id="l00522" name="l00522"></a><span class="lineno">  522</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <a class="code hl_function" href="struct_get_outduct_capabilities_api_command__t.html#a880110baabe839e54f698d9aa6dac224">~GetOutductCapabilitiesApiCommand_t</a>() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00523" name="l00523"></a><span class="lineno">  523</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00524" name="l00524"></a><span class="lineno">  524</span>};</div>
</div>
<div class="line"><a id="l00525" name="l00525"></a><span class="lineno">  525</span> </div>
<div class="foldopen" id="foldopen00526" data-start="{" data-end="};">
<div class="line"><a id="l00526" name="l00526"></a><span class="lineno"><a class="line" href="struct_get_inducts_api_command__t.html">  526</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_get_inducts_api_command__t.html#a9b5cd72de5818a9cba0ede1c69e94e1d">GetInductsApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00527" name="l00527"></a><span class="lineno">  527</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_get_inducts_api_command__t.html#a9b5cd72de5818a9cba0ede1c69e94e1d">GetInductsApiCommand_t</a>();</div>
<div class="line"><a id="l00528" name="l00528"></a><span class="lineno">  528</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_get_inducts_api_command__t.html#a9b5cd72de5818a9cba0ede1c69e94e1d">~GetInductsApiCommand_t</a>();</div>
<div class="line"><a id="l00529" name="l00529"></a><span class="lineno">  529</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00530" name="l00530"></a><span class="lineno">  530</span>};</div>
</div>
<div class="line"><a id="l00531" name="l00531"></a><span class="lineno">  531</span> </div>
<div class="foldopen" id="foldopen00532" data-start="{" data-end="};">
<div class="line"><a id="l00532" name="l00532"></a><span class="lineno"><a class="line" href="struct_ping_api_command__t.html">  532</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_ping_api_command__t.html#a6b59dd907433d32437cc99ee041ed430">PingApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00533" name="l00533"></a><span class="lineno">  533</span>    uint64_t m_nodeId;</div>
<div class="line"><a id="l00534" name="l00534"></a><span class="lineno">  534</span>    uint64_t m_pingServiceNumber;</div>
<div class="line"><a id="l00535" name="l00535"></a><span class="lineno">  535</span>    uint64_t m_bpVersion;</div>
<div class="line"><a id="l00536" name="l00536"></a><span class="lineno">  536</span> </div>
<div class="line"><a id="l00537" name="l00537"></a><span class="lineno">  537</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_ping_api_command__t.html#a6b59dd907433d32437cc99ee041ed430">PingApiCommand_t</a>();</div>
<div class="line"><a id="l00538" name="l00538"></a><span class="lineno">  538</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <a class="code hl_function" href="struct_ping_api_command__t.html#a6b59dd907433d32437cc99ee041ed430">~PingApiCommand_t</a>() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00539" name="l00539"></a><span class="lineno">  539</span> </div>
<div class="line"><a id="l00540" name="l00540"></a><span class="lineno">  540</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00541" name="l00541"></a><span class="lineno">  541</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00542" name="l00542"></a><span class="lineno">  542</span> </div>
<div class="line"><a id="l00543" name="l00543"></a><span class="lineno">  543</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00544" name="l00544"></a><span class="lineno">  544</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00545" name="l00545"></a><span class="lineno">  545</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00546" name="l00546"></a><span class="lineno">  546</span>};</div>
</div>
<div class="line"><a id="l00547" name="l00547"></a><span class="lineno">  547</span> </div>
<div class="foldopen" id="foldopen00548" data-start="{" data-end="};">
<div class="line"><a id="l00548" name="l00548"></a><span class="lineno"><a class="line" href="struct_upload_contact_plan_api_command__t.html">  548</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_upload_contact_plan_api_command__t.html#a4b8974e67fd03325b1fe00e5a1d208ff">UploadContactPlanApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00549" name="l00549"></a><span class="lineno">  549</span>    std::string m_contactPlanJson;</div>
<div class="line"><a id="l00550" name="l00550"></a><span class="lineno">  550</span> </div>
<div class="line"><a id="l00551" name="l00551"></a><span class="lineno">  551</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_upload_contact_plan_api_command__t.html#a4b8974e67fd03325b1fe00e5a1d208ff">UploadContactPlanApiCommand_t</a>();</div>
<div class="line"><a id="l00552" name="l00552"></a><span class="lineno">  552</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <a class="code hl_function" href="struct_upload_contact_plan_api_command__t.html#a4b8974e67fd03325b1fe00e5a1d208ff">~UploadContactPlanApiCommand_t</a>() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00553" name="l00553"></a><span class="lineno">  553</span> </div>
<div class="line"><a id="l00554" name="l00554"></a><span class="lineno">  554</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00555" name="l00555"></a><span class="lineno">  555</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00556" name="l00556"></a><span class="lineno">  556</span> </div>
<div class="line"><a id="l00557" name="l00557"></a><span class="lineno">  557</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00558" name="l00558"></a><span class="lineno">  558</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00559" name="l00559"></a><span class="lineno">  559</span> </div>
<div class="line"><a id="l00560" name="l00560"></a><span class="lineno">  560</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00561" name="l00561"></a><span class="lineno">  561</span>};</div>
</div>
<div class="line"><a id="l00562" name="l00562"></a><span class="lineno">  562</span> </div>
<div class="foldopen" id="foldopen00563" data-start="{" data-end="};">
<div class="line"><a id="l00563" name="l00563"></a><span class="lineno"><a class="line" href="struct_get_expiring_storage_api_command__t.html">  563</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_get_expiring_storage_api_command__t.html#abdaa3e5d2da44b4c1a9514158cffebee">GetExpiringStorageApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00564" name="l00564"></a><span class="lineno">  564</span>    uint64_t m_priority;</div>
<div class="line"><a id="l00565" name="l00565"></a><span class="lineno">  565</span>    uint64_t m_thresholdSecondsFromNow;</div>
<div class="line"><a id="l00566" name="l00566"></a><span class="lineno">  566</span> </div>
<div class="line"><a id="l00567" name="l00567"></a><span class="lineno">  567</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_get_expiring_storage_api_command__t.html#abdaa3e5d2da44b4c1a9514158cffebee">GetExpiringStorageApiCommand_t</a>();</div>
<div class="line"><a id="l00568" name="l00568"></a><span class="lineno">  568</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <a class="code hl_function" href="struct_get_expiring_storage_api_command__t.html#abdaa3e5d2da44b4c1a9514158cffebee">~GetExpiringStorageApiCommand_t</a>() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00569" name="l00569"></a><span class="lineno">  569</span> </div>
<div class="line"><a id="l00570" name="l00570"></a><span class="lineno">  570</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00571" name="l00571"></a><span class="lineno">  571</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00572" name="l00572"></a><span class="lineno">  572</span> </div>
<div class="line"><a id="l00573" name="l00573"></a><span class="lineno">  573</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00574" name="l00574"></a><span class="lineno">  574</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00575" name="l00575"></a><span class="lineno">  575</span> </div>
<div class="line"><a id="l00576" name="l00576"></a><span class="lineno">  576</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00577" name="l00577"></a><span class="lineno">  577</span>};</div>
</div>
<div class="line"><a id="l00578" name="l00578"></a><span class="lineno">  578</span> </div>
<div class="foldopen" id="foldopen00579" data-start="{" data-end="};">
<div class="line"><a id="l00579" name="l00579"></a><span class="lineno"><a class="line" href="struct_update_bp_sec_api_command__t.html">  579</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_update_bp_sec_api_command__t.html#ae181f7526b42aab7718896052fb3d669">UpdateBpSecApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00580" name="l00580"></a><span class="lineno">  580</span>    std::string m_bpSecJson;</div>
<div class="line"><a id="l00581" name="l00581"></a><span class="lineno">  581</span> </div>
<div class="line"><a id="l00582" name="l00582"></a><span class="lineno">  582</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_update_bp_sec_api_command__t.html#ae181f7526b42aab7718896052fb3d669">UpdateBpSecApiCommand_t</a>();</div>
<div class="line"><a id="l00583" name="l00583"></a><span class="lineno">  583</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <a class="code hl_function" href="struct_update_bp_sec_api_command__t.html#ae181f7526b42aab7718896052fb3d669">~UpdateBpSecApiCommand_t</a>() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00584" name="l00584"></a><span class="lineno">  584</span> </div>
<div class="line"><a id="l00585" name="l00585"></a><span class="lineno">  585</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00586" name="l00586"></a><span class="lineno">  586</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00587" name="l00587"></a><span class="lineno">  587</span> </div>
<div class="line"><a id="l00588" name="l00588"></a><span class="lineno">  588</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00589" name="l00589"></a><span class="lineno">  589</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00590" name="l00590"></a><span class="lineno">  590</span> </div>
<div class="line"><a id="l00591" name="l00591"></a><span class="lineno">  591</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00592" name="l00592"></a><span class="lineno">  592</span>};</div>
</div>
<div class="line"><a id="l00593" name="l00593"></a><span class="lineno">  593</span> </div>
<div class="foldopen" id="foldopen00594" data-start="{" data-end="};">
<div class="line"><a id="l00594" name="l00594"></a><span class="lineno"><a class="line" href="struct_get_bp_sec_api_command__t.html">  594</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_get_bp_sec_api_command__t.html#a09c48c967450978055c25d0d56a2c33f">GetBpSecApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00595" name="l00595"></a><span class="lineno">  595</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_get_bp_sec_api_command__t.html#a09c48c967450978055c25d0d56a2c33f">GetBpSecApiCommand_t</a>();</div>
<div class="line"><a id="l00596" name="l00596"></a><span class="lineno">  596</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <a class="code hl_function" href="struct_get_bp_sec_api_command__t.html#a09c48c967450978055c25d0d56a2c33f">~GetBpSecApiCommand_t</a>() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00597" name="l00597"></a><span class="lineno">  597</span> </div>
<div class="line"><a id="l00598" name="l00598"></a><span class="lineno">  598</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00599" name="l00599"></a><span class="lineno">  599</span>};</div>
</div>
<div class="line"><a id="l00600" name="l00600"></a><span class="lineno">  600</span> </div>
<div class="foldopen" id="foldopen00601" data-start="{" data-end="};">
<div class="line"><a id="l00601" name="l00601"></a><span class="lineno"><a class="line" href="struct_set_max_send_rate_api_command__t.html">  601</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_set_max_send_rate_api_command__t.html#a6e5d493101e9d2cbb823649a6cee1c1f">SetMaxSendRateApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00602" name="l00602"></a><span class="lineno">  602</span>    uint64_t m_rateBitsPerSec;</div>
<div class="line"><a id="l00603" name="l00603"></a><span class="lineno">  603</span>    uint64_t m_outduct;</div>
<div class="line"><a id="l00604" name="l00604"></a><span class="lineno">  604</span> </div>
<div class="line"><a id="l00605" name="l00605"></a><span class="lineno">  605</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_set_max_send_rate_api_command__t.html#a6e5d493101e9d2cbb823649a6cee1c1f">SetMaxSendRateApiCommand_t</a>();</div>
<div class="line"><a id="l00606" name="l00606"></a><span class="lineno">  606</span> </div>
<div class="line"><a id="l00607" name="l00607"></a><span class="lineno">  607</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00608" name="l00608"></a><span class="lineno">  608</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00609" name="l00609"></a><span class="lineno">  609</span> </div>
<div class="line"><a id="l00610" name="l00610"></a><span class="lineno">  610</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00611" name="l00611"></a><span class="lineno">  611</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00612" name="l00612"></a><span class="lineno">  612</span> </div>
<div class="line"><a id="l00613" name="l00613"></a><span class="lineno">  613</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00614" name="l00614"></a><span class="lineno">  614</span>};</div>
</div>
<div class="line"><a id="l00615" name="l00615"></a><span class="lineno">  615</span> </div>
<div class="foldopen" id="foldopen00616" data-start="{" data-end="};">
<div class="line"><a id="l00616" name="l00616"></a><span class="lineno"><a class="line" href="struct_get_hdtn_config_api_command__t.html">  616</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_get_hdtn_config_api_command__t.html#aea13a47b9380790e45839750b5d2d874">GetHdtnConfigApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00617" name="l00617"></a><span class="lineno">  617</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_get_hdtn_config_api_command__t.html#aea13a47b9380790e45839750b5d2d874">GetHdtnConfigApiCommand_t</a>();</div>
<div class="line"><a id="l00618" name="l00618"></a><span class="lineno">  618</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00619" name="l00619"></a><span class="lineno">  619</span>};</div>
</div>
<div class="line"><a id="l00620" name="l00620"></a><span class="lineno">  620</span> </div>
<div class="foldopen" id="foldopen00621" data-start="{" data-end="};">
<div class="line"><a id="l00621" name="l00621"></a><span class="lineno"><a class="line" href="struct_get_hdtn_version_api_command__t.html">  621</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS GetHdtnVersionApiCommand_t : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00622" name="l00622"></a><span class="lineno">  622</span>    TELEMETRY_DEFINITIONS_EXPORT GetHdtnVersionApiCommand_t();</div>
<div class="line"><a id="l00623" name="l00623"></a><span class="lineno">  623</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> ~GetHdtnVersionApiCommand_t() <span class="keyword">override</span>;</div>
<div class="line"><a id="l00624" name="l00624"></a><span class="lineno">  624</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00625" name="l00625"></a><span class="lineno">  625</span>};</div>
</div>
<div class="line"><a id="l00626" name="l00626"></a><span class="lineno">  626</span> </div>
<div class="line"><a id="l00627" name="l00627"></a><span class="lineno">  627</span> </div>
<div class="foldopen" id="foldopen00628" data-start="{" data-end="};">
<div class="line"><a id="l00628" name="l00628"></a><span class="lineno"><a class="line" href="struct_set_link_down_api_command__t.html">  628</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_set_link_down_api_command__t.html#a3e89b858dd05519a0e2206254813f5e0">SetLinkDownApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00629" name="l00629"></a><span class="lineno">  629</span>    uint64_t m_index;</div>
<div class="line"><a id="l00630" name="l00630"></a><span class="lineno">  630</span> </div>
<div class="line"><a id="l00631" name="l00631"></a><span class="lineno">  631</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_set_link_down_api_command__t.html#a3e89b858dd05519a0e2206254813f5e0">SetLinkDownApiCommand_t</a>();</div>
<div class="line"><a id="l00632" name="l00632"></a><span class="lineno">  632</span> </div>
<div class="line"><a id="l00633" name="l00633"></a><span class="lineno">  633</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00634" name="l00634"></a><span class="lineno">  634</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00635" name="l00635"></a><span class="lineno">  635</span> </div>
<div class="line"><a id="l00636" name="l00636"></a><span class="lineno">  636</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00637" name="l00637"></a><span class="lineno">  637</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00638" name="l00638"></a><span class="lineno">  638</span> </div>
<div class="line"><a id="l00639" name="l00639"></a><span class="lineno">  639</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00640" name="l00640"></a><span class="lineno">  640</span>};</div>
</div>
<div class="line"><a id="l00641" name="l00641"></a><span class="lineno">  641</span> </div>
<div class="line"><a id="l00642" name="l00642"></a><span class="lineno">  642</span> </div>
<div class="foldopen" id="foldopen00643" data-start="{" data-end="};">
<div class="line"><a id="l00643" name="l00643"></a><span class="lineno"><a class="line" href="struct_set_link_up_api_command__t.html">  643</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_set_link_up_api_command__t.html#add030f0e174bd793d011e6e15aa2885d">SetLinkUpApiCommand_t</a> : <span class="keyword">public</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a> {</div>
<div class="line"><a id="l00644" name="l00644"></a><span class="lineno">  644</span>    uint64_t m_index;</div>
<div class="line"><a id="l00645" name="l00645"></a><span class="lineno">  645</span> </div>
<div class="line"><a id="l00646" name="l00646"></a><span class="lineno">  646</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_set_link_up_api_command__t.html#add030f0e174bd793d011e6e15aa2885d">SetLinkUpApiCommand_t</a>();</div>
<div class="line"><a id="l00647" name="l00647"></a><span class="lineno">  647</span> </div>
<div class="line"><a id="l00648" name="l00648"></a><span class="lineno">  648</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00649" name="l00649"></a><span class="lineno">  649</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> <a class="code hl_function" href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t</a>&amp; o) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00650" name="l00650"></a><span class="lineno">  650</span> </div>
<div class="line"><a id="l00651" name="l00651"></a><span class="lineno">  651</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00652" name="l00652"></a><span class="lineno">  652</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00653" name="l00653"></a><span class="lineno">  653</span> </div>
<div class="line"><a id="l00654" name="l00654"></a><span class="lineno">  654</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">static</span> <span class="keyword">const</span> std::string name;</div>
<div class="line"><a id="l00655" name="l00655"></a><span class="lineno">  655</span>};</div>
</div>
<div class="line"><a id="l00656" name="l00656"></a><span class="lineno">  656</span> </div>
<div class="foldopen" id="foldopen00657" data-start="{" data-end="};">
<div class="line"><a id="l00657" name="l00657"></a><span class="lineno"><a class="line" href="struct_api_resp__t.html">  657</a></span><span class="keyword">struct </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS <a class="code hl_function" href="struct_api_resp__t.html#aa23cbcda5cace96e01f1f02b31889537">ApiResp_t</a> : <span class="keyword">public</span> JsonSerializable {</div>
<div class="line"><a id="l00658" name="l00658"></a><span class="lineno">  658</span>    <span class="keywordtype">bool</span> m_success;</div>
<div class="line"><a id="l00659" name="l00659"></a><span class="lineno">  659</span>    std::string m_message;</div>
<div class="line"><a id="l00660" name="l00660"></a><span class="lineno">  660</span> </div>
<div class="line"><a id="l00661" name="l00661"></a><span class="lineno">  661</span>    TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_function" href="struct_api_resp__t.html#aa23cbcda5cace96e01f1f02b31889537">ApiResp_t</a>();</div>
<div class="line"><a id="l00662" name="l00662"></a><span class="lineno">  662</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <a class="code hl_function" href="struct_api_resp__t.html#aa23cbcda5cace96e01f1f02b31889537">~ApiResp_t</a>();</div>
<div class="line"><a id="l00663" name="l00663"></a><span class="lineno">  663</span> </div>
<div class="line"><a id="l00664" name="l00664"></a><span class="lineno">  664</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> boost::property_tree::ptree GetNewPropertyTree() <span class="keyword">const override</span>;</div>
<div class="line"><a id="l00665" name="l00665"></a><span class="lineno">  665</span>    TELEMETRY_DEFINITIONS_EXPORT <span class="keyword">virtual</span> <span class="keywordtype">bool</span> SetValuesFromPropertyTree(<span class="keyword">const</span> boost::property_tree::ptree&amp; pt) <span class="keyword">override</span>;</div>
<div class="line"><a id="l00666" name="l00666"></a><span class="lineno">  666</span>};</div>
</div>
<div class="line"><a id="l00667" name="l00667"></a><span class="lineno">  667</span></div>
<div class="line"><a id="l00673" name="l00673"></a><span class="lineno">  673</span><span class="keyword">static</span> <span class="keyword">constexpr</span> uint8_t ZMQ_CONNECTION_ID_LEN = 5;</div>
<div class="line"><a id="l00674" name="l00674"></a><span class="lineno">  674</span> </div>
<div class="foldopen" id="foldopen00675" data-start="{" data-end="};">
<div class="line"><a id="l00675" name="l00675"></a><span class="lineno"><a class="line" href="class_zmq_connection_id__t.html">  675</a></span><span class="keyword">class </span>CLASS_VISIBILITY_TELEMETRY_DEFINITIONS ZmqConnectionId_t {</div>
<div class="line"><a id="l00676" name="l00676"></a><span class="lineno">  676</span>    <span class="keyword">public</span>:</div>
<div class="line"><a id="l00677" name="l00677"></a><span class="lineno">  677</span>        TELEMETRY_DEFINITIONS_EXPORT ZmqConnectionId_t();</div>
<div class="line"><a id="l00678" name="l00678"></a><span class="lineno">  678</span> </div>
<div class="line"><a id="l00679" name="l00679"></a><span class="lineno">  679</span>        <span class="comment">// This constructor generates custom ZMQ connection IDs. It accepts a single byte (uint8_t)</span></div>
<div class="line"><a id="l00680" name="l00680"></a><span class="lineno">  680</span>        <span class="comment">// and assigns it to the last byte of the ID, while prepending all other bytes with 0&#39;s.</span></div>
<div class="line"><a id="l00681" name="l00681"></a><span class="lineno">  681</span>        TELEMETRY_DEFINITIONS_EXPORT ZmqConnectionId_t(<span class="keyword">const</span> uint8_t val);</div>
<div class="line"><a id="l00682" name="l00682"></a><span class="lineno">  682</span> </div>
<div class="line"><a id="l00683" name="l00683"></a><span class="lineno">  683</span>        <span class="comment">// Convert ZmqConnectionId to a zmq::message_t</span></div>
<div class="line"><a id="l00684" name="l00684"></a><span class="lineno">  684</span>        TELEMETRY_DEFINITIONS_EXPORT <a class="code hl_class" href="classzmq_1_1message__t.html">zmq::message_t</a> Msg();</div>
<div class="line"><a id="l00685" name="l00685"></a><span class="lineno">  685</span> </div>
<div class="line"><a id="l00686" name="l00686"></a><span class="lineno">  686</span>        <span class="comment">// Compare two ZmqConnectionId objects</span></div>
<div class="line"><a id="l00687" name="l00687"></a><span class="lineno">  687</span>        TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> ZmqConnectionId_t&amp; other) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00688" name="l00688"></a><span class="lineno">  688</span> </div>
<div class="line"><a id="l00689" name="l00689"></a><span class="lineno">  689</span>        <span class="comment">// Compare ZmqConnectionId object with a zmq message</span></div>
<div class="line"><a id="l00690" name="l00690"></a><span class="lineno">  690</span>        TELEMETRY_DEFINITIONS_EXPORT <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> <a class="code hl_class" href="classzmq_1_1message__t.html">zmq::message_t</a>&amp; msg) <span class="keyword">const</span>;</div>
<div class="line"><a id="l00691" name="l00691"></a><span class="lineno">  691</span> </div>
<div class="line"><a id="l00692" name="l00692"></a><span class="lineno">  692</span>    <span class="keyword">private</span>:</div>
<div class="line"><a id="l00693" name="l00693"></a><span class="lineno">  693</span>        std::array&lt;uint8_t, ZMQ_CONNECTION_ID_LEN&gt; m_id;</div>
<div class="line"><a id="l00694" name="l00694"></a><span class="lineno">  694</span>};</div>
</div>
<div class="line"><a id="l00695" name="l00695"></a><span class="lineno">  695</span></div>
<div class="line"><a id="l00700" name="l00700"></a><span class="lineno">  700</span><span class="keyword">static</span> <a class="code hl_class" href="class_zmq_connection_id__t.html">ZmqConnectionId_t</a> TELEM_REQ_CONN_ID = <a class="code hl_class" href="class_zmq_connection_id__t.html">ZmqConnectionId_t</a>(1);</div>
<div class="line"><a id="l00701" name="l00701"></a><span class="lineno">  701</span><span class="keyword">static</span> <a class="code hl_class" href="class_zmq_connection_id__t.html">ZmqConnectionId_t</a> GUI_REQ_CONN_ID = <a class="code hl_class" href="class_zmq_connection_id__t.html">ZmqConnectionId_t</a>(2);</div>
<div class="line"><a id="l00702" name="l00702"></a><span class="lineno">  702</span> </div>
<div class="line"><a id="l00703" name="l00703"></a><span class="lineno">  703</span><span class="preprocessor">#endif </span><span class="comment">// HDTN_TELEMETRY_H</span></div>
<div class="ttc" id="a_cbhe_8h_html"><div class="ttname"><a href="_cbhe_8h.html">Cbhe.h</a></div></div>
<div class="ttc" id="a_json_serializable_8h_html"><div class="ttname"><a href="_json_serializable_8h.html">JsonSerializable.h</a></div></div>
<div class="ttc" id="aclass_zmq_connection_id__t_html"><div class="ttname"><a href="class_zmq_connection_id__t.html">ZmqConnectionId_t</a></div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.h:675</div></div>
<div class="ttc" id="aclasszmq_1_1message__t_html"><div class="ttname"><a href="classzmq_1_1message__t.html">zmq::message_t</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:382</div></div>
<div class="ttc" id="astruct_api_command__t_html_a1aebcd99dd9eed18498c4638bc46a04d"><div class="ttname"><a href="struct_api_command__t.html#a1aebcd99dd9eed18498c4638bc46a04d">ApiCommand_t::ApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT ApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1598</div></div>
<div class="ttc" id="astruct_api_resp__t_html_aa23cbcda5cace96e01f1f02b31889537"><div class="ttname"><a href="struct_api_resp__t.html#aa23cbcda5cace96e01f1f02b31889537">ApiResp_t::ApiResp_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT ApiResp_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:2099</div></div>
<div class="ttc" id="astruct_get_bp_sec_api_command__t_html_a09c48c967450978055c25d0d56a2c33f"><div class="ttname"><a href="struct_get_bp_sec_api_command__t.html#a09c48c967450978055c25d0d56a2c33f">GetBpSecApiCommand_t::GetBpSecApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT GetBpSecApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1942</div></div>
<div class="ttc" id="astruct_get_expiring_storage_api_command__t_html_abdaa3e5d2da44b4c1a9514158cffebee"><div class="ttname"><a href="struct_get_expiring_storage_api_command__t.html#abdaa3e5d2da44b4c1a9514158cffebee">GetExpiringStorageApiCommand_t::GetExpiringStorageApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT GetExpiringStorageApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1845</div></div>
<div class="ttc" id="astruct_get_hdtn_config_api_command__t_html_aea13a47b9380790e45839750b5d2d874"><div class="ttname"><a href="struct_get_hdtn_config_api_command__t.html#aea13a47b9380790e45839750b5d2d874">GetHdtnConfigApiCommand_t::GetHdtnConfigApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT GetHdtnConfigApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1695</div></div>
<div class="ttc" id="astruct_get_inducts_api_command__t_html_a9b5cd72de5818a9cba0ede1c69e94e1d"><div class="ttname"><a href="struct_get_inducts_api_command__t.html#a9b5cd72de5818a9cba0ede1c69e94e1d">GetInductsApiCommand_t::GetInductsApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT GetInductsApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1714</div></div>
<div class="ttc" id="astruct_get_outduct_capabilities_api_command__t_html_a880110baabe839e54f698d9aa6dac224"><div class="ttname"><a href="struct_get_outduct_capabilities_api_command__t.html#a880110baabe839e54f698d9aa6dac224">GetOutductCapabilitiesApiCommand_t::GetOutductCapabilitiesApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT GetOutductCapabilitiesApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1734</div></div>
<div class="ttc" id="astruct_get_outducts_api_command__t_html_ac8d855c7c576cc773bcbd5b9edccd4d9"><div class="ttname"><a href="struct_get_outducts_api_command__t.html#ac8d855c7c576cc773bcbd5b9edccd4d9">GetOutductsApiCommand_t::GetOutductsApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT GetOutductsApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1724</div></div>
<div class="ttc" id="astruct_get_storage_api_command__t_html_a9a9681d2e45a33049337f6c720911ac0"><div class="ttname"><a href="struct_get_storage_api_command__t.html#a9a9681d2e45a33049337f6c720911ac0">GetStorageApiCommand_t::name</a></div><div class="ttdeci">static TELEMETRY_DEFINITIONS_EXPORT const std::string name</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.h:511</div></div>
<div class="ttc" id="astruct_get_storage_api_command__t_html_ae1ebd0ecea435a3bfdb0be87c8a3dc1e"><div class="ttname"><a href="struct_get_storage_api_command__t.html#ae1ebd0ecea435a3bfdb0be87c8a3dc1e">GetStorageApiCommand_t::GetStorageApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT GetStorageApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1704</div></div>
<div class="ttc" id="astruct_ping_api_command__t_html_a6b59dd907433d32437cc99ee041ed430"><div class="ttname"><a href="struct_ping_api_command__t.html#a6b59dd907433d32437cc99ee041ed430">PingApiCommand_t::PingApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT PingApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1745</div></div>
<div class="ttc" id="astruct_set_link_down_api_command__t_html_a3e89b858dd05519a0e2206254813f5e0"><div class="ttname"><a href="struct_set_link_down_api_command__t.html#a3e89b858dd05519a0e2206254813f5e0">SetLinkDownApiCommand_t::SetLinkDownApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT SetLinkDownApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:2005</div></div>
<div class="ttc" id="astruct_set_link_up_api_command__t_html_add030f0e174bd793d011e6e15aa2885d"><div class="ttname"><a href="struct_set_link_up_api_command__t.html#add030f0e174bd793d011e6e15aa2885d">SetLinkUpApiCommand_t::SetLinkUpApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT SetLinkUpApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:2052</div></div>
<div class="ttc" id="astruct_set_max_send_rate_api_command__t_html_a6e5d493101e9d2cbb823649a6cee1c1f"><div class="ttname"><a href="struct_set_max_send_rate_api_command__t.html#a6e5d493101e9d2cbb823649a6cee1c1f">SetMaxSendRateApiCommand_t::SetMaxSendRateApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT SetMaxSendRateApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1954</div></div>
<div class="ttc" id="astruct_update_bp_sec_api_command__t_html_ae181f7526b42aab7718896052fb3d669"><div class="ttname"><a href="struct_update_bp_sec_api_command__t.html#ae181f7526b42aab7718896052fb3d669">UpdateBpSecApiCommand_t::UpdateBpSecApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT UpdateBpSecApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1895</div></div>
<div class="ttc" id="astruct_upload_contact_plan_api_command__t_html_a4b8974e67fd03325b1fe00e5a1d208ff"><div class="ttname"><a href="struct_upload_contact_plan_api_command__t.html#a4b8974e67fd03325b1fe00e5a1d208ff">UploadContactPlanApiCommand_t::UploadContactPlanApiCommand_t</a></div><div class="ttdeci">TELEMETRY_DEFINITIONS_EXPORT UploadContactPlanApiCommand_t()</div><div class="ttdef"><b>Definition</b> TelemetryDefinitions.cpp:1798</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_bdd9a5d540de89e9fe90efdfc6973a4f.html">common</a></li><li class="navelem"><a class="el" href="dir_eeb0ec16497547ca96b1f770924cf945.html">telemetry_definitions</a></li><li class="navelem"><a class="el" href="dir_40cac0b020290e75d91710518823fbfc.html">include</a></li><li class="navelem"><a class="el" href="_telemetry_definitions_8h.html">TelemetryDefinitions.h</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
