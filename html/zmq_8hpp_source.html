<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.13.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>HDTN: common/util/include/zmq.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<script type="text/javascript" src="clipboard.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="cookie.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
  $(function() { init_search(); });
/* @license-end */
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">HDTN
   </div>
  </td>
    <td>        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <span id="MSearchSelect"                onmouseover="return searchBox.OnSearchSelectShow()"                onmouseout="return searchBox.OnSearchSelectHide()">&#160;</span>
          <input type="text" id="MSearchField" value="" placeholder="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.svg" alt=""/></a>
          </span>
        </div>
</td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.13.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() { codefold.init(0); });
/* @license-end */
</script>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function(){initNavTree('zmq_8hpp_source.html',''); initResizable(true); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

<div class="header">
  <div class="headertitle"><div class="title">zmq.hpp</div></div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a id="l00001" name="l00001"></a><span class="lineno">    1</span><span class="comment">/*</span></div>
<div class="line"><a id="l00002" name="l00002"></a><span class="lineno">    2</span><span class="comment">    Copyright (c) 2016-2017 ZeroMQ community</span></div>
<div class="line"><a id="l00003" name="l00003"></a><span class="lineno">    3</span><span class="comment">    Copyright (c) 2009-2011 250bpm s.r.o.</span></div>
<div class="line"><a id="l00004" name="l00004"></a><span class="lineno">    4</span><span class="comment">    Copyright (c) 2011 Botond Ballo</span></div>
<div class="line"><a id="l00005" name="l00005"></a><span class="lineno">    5</span><span class="comment">    Copyright (c) 2007-2009 iMatix Corporation</span></div>
<div class="line"><a id="l00006" name="l00006"></a><span class="lineno">    6</span><span class="comment"></span> </div>
<div class="line"><a id="l00007" name="l00007"></a><span class="lineno">    7</span><span class="comment">    Permission is hereby granted, free of charge, to any person obtaining a copy</span></div>
<div class="line"><a id="l00008" name="l00008"></a><span class="lineno">    8</span><span class="comment">    of this software and associated documentation files (the &quot;Software&quot;), to</span></div>
<div class="line"><a id="l00009" name="l00009"></a><span class="lineno">    9</span><span class="comment">    deal in the Software without restriction, including without limitation the</span></div>
<div class="line"><a id="l00010" name="l00010"></a><span class="lineno">   10</span><span class="comment">    rights to use, copy, modify, merge, publish, distribute, sublicense, and/or</span></div>
<div class="line"><a id="l00011" name="l00011"></a><span class="lineno">   11</span><span class="comment">    sell copies of the Software, and to permit persons to whom the Software is</span></div>
<div class="line"><a id="l00012" name="l00012"></a><span class="lineno">   12</span><span class="comment">    furnished to do so, subject to the following conditions:</span></div>
<div class="line"><a id="l00013" name="l00013"></a><span class="lineno">   13</span><span class="comment"></span> </div>
<div class="line"><a id="l00014" name="l00014"></a><span class="lineno">   14</span><span class="comment">    The above copyright notice and this permission notice shall be included in</span></div>
<div class="line"><a id="l00015" name="l00015"></a><span class="lineno">   15</span><span class="comment">    all copies or substantial portions of the Software.</span></div>
<div class="line"><a id="l00016" name="l00016"></a><span class="lineno">   16</span><span class="comment"></span> </div>
<div class="line"><a id="l00017" name="l00017"></a><span class="lineno">   17</span><span class="comment">    THE SOFTWARE IS PROVIDED &quot;AS IS&quot;, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR</span></div>
<div class="line"><a id="l00018" name="l00018"></a><span class="lineno">   18</span><span class="comment">    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,</span></div>
<div class="line"><a id="l00019" name="l00019"></a><span class="lineno">   19</span><span class="comment">    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE</span></div>
<div class="line"><a id="l00020" name="l00020"></a><span class="lineno">   20</span><span class="comment">    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER</span></div>
<div class="line"><a id="l00021" name="l00021"></a><span class="lineno">   21</span><span class="comment">    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING</span></div>
<div class="line"><a id="l00022" name="l00022"></a><span class="lineno">   22</span><span class="comment">    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS</span></div>
<div class="line"><a id="l00023" name="l00023"></a><span class="lineno">   23</span><span class="comment">    IN THE SOFTWARE.</span></div>
<div class="line"><a id="l00024" name="l00024"></a><span class="lineno">   24</span><span class="comment">*/</span></div>
<div class="line"><a id="l00025" name="l00025"></a><span class="lineno">   25</span> </div>
<div class="line"><a id="l00026" name="l00026"></a><span class="lineno">   26</span><span class="preprocessor">#ifndef __ZMQ_HPP_INCLUDED__</span></div>
<div class="line"><a id="l00027" name="l00027"></a><span class="lineno">   27</span><span class="preprocessor">#define __ZMQ_HPP_INCLUDED__</span></div>
<div class="line"><a id="l00028" name="l00028"></a><span class="lineno">   28</span> </div>
<div class="line"><a id="l00029" name="l00029"></a><span class="lineno">   29</span><span class="preprocessor">#ifdef _WIN32</span></div>
<div class="line"><a id="l00030" name="l00030"></a><span class="lineno">   30</span><span class="preprocessor">#ifndef NOMINMAX</span></div>
<div class="line"><a id="l00031" name="l00031"></a><span class="lineno">   31</span><span class="preprocessor">#define NOMINMAX</span></div>
<div class="line"><a id="l00032" name="l00032"></a><span class="lineno">   32</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00033" name="l00033"></a><span class="lineno">   33</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00034" name="l00034"></a><span class="lineno">   34</span> </div>
<div class="line"><a id="l00035" name="l00035"></a><span class="lineno">   35</span><span class="comment">// included here for _HAS_CXX* macros</span></div>
<div class="line"><a id="l00036" name="l00036"></a><span class="lineno">   36</span><span class="preprocessor">#include &lt;zmq.h&gt;</span></div>
<div class="line"><a id="l00037" name="l00037"></a><span class="lineno">   37</span> </div>
<div class="line"><a id="l00038" name="l00038"></a><span class="lineno">   38</span><span class="preprocessor">#if defined(_MSVC_LANG)</span></div>
<div class="line"><a id="l00039" name="l00039"></a><span class="lineno">   39</span><span class="preprocessor">#define CPPZMQ_LANG _MSVC_LANG</span></div>
<div class="line"><a id="l00040" name="l00040"></a><span class="lineno">   40</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00041" name="l00041"></a><span class="lineno">   41</span><span class="preprocessor">#define CPPZMQ_LANG __cplusplus</span></div>
<div class="line"><a id="l00042" name="l00042"></a><span class="lineno">   42</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00043" name="l00043"></a><span class="lineno">   43</span><span class="comment">// overwrite if specific language macros indicate higher version</span></div>
<div class="line"><a id="l00044" name="l00044"></a><span class="lineno">   44</span><span class="preprocessor">#if defined(_HAS_CXX14) &amp;&amp; _HAS_CXX14 &amp;&amp; CPPZMQ_LANG &lt; 201402L</span></div>
<div class="line"><a id="l00045" name="l00045"></a><span class="lineno">   45</span><span class="preprocessor">#undef CPPZMQ_LANG</span></div>
<div class="line"><a id="l00046" name="l00046"></a><span class="lineno">   46</span><span class="preprocessor">#define CPPZMQ_LANG 201402L</span></div>
<div class="line"><a id="l00047" name="l00047"></a><span class="lineno">   47</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00048" name="l00048"></a><span class="lineno">   48</span><span class="preprocessor">#if defined(_HAS_CXX17) &amp;&amp; _HAS_CXX17 &amp;&amp; CPPZMQ_LANG &lt; 201703L</span></div>
<div class="line"><a id="l00049" name="l00049"></a><span class="lineno">   49</span><span class="preprocessor">#undef CPPZMQ_LANG</span></div>
<div class="line"><a id="l00050" name="l00050"></a><span class="lineno">   50</span><span class="preprocessor">#define CPPZMQ_LANG 201703L</span></div>
<div class="line"><a id="l00051" name="l00051"></a><span class="lineno">   51</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00052" name="l00052"></a><span class="lineno">   52</span> </div>
<div class="line"><a id="l00053" name="l00053"></a><span class="lineno">   53</span><span class="comment">// macros defined if has a specific standard or greater</span></div>
<div class="line"><a id="l00054" name="l00054"></a><span class="lineno">   54</span><span class="preprocessor">#if CPPZMQ_LANG &gt;= 201103L || (defined(_MSC_VER) &amp;&amp; _MSC_VER &gt;= 1900)</span></div>
<div class="line"><a id="l00055" name="l00055"></a><span class="lineno">   55</span><span class="preprocessor">#define ZMQ_CPP11</span></div>
<div class="line"><a id="l00056" name="l00056"></a><span class="lineno">   56</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00057" name="l00057"></a><span class="lineno">   57</span><span class="preprocessor">#if CPPZMQ_LANG &gt;= 201402L</span></div>
<div class="line"><a id="l00058" name="l00058"></a><span class="lineno">   58</span><span class="preprocessor">#define ZMQ_CPP14</span></div>
<div class="line"><a id="l00059" name="l00059"></a><span class="lineno">   59</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00060" name="l00060"></a><span class="lineno">   60</span><span class="preprocessor">#if CPPZMQ_LANG &gt;= 201703L</span></div>
<div class="line"><a id="l00061" name="l00061"></a><span class="lineno">   61</span><span class="preprocessor">#define ZMQ_CPP17</span></div>
<div class="line"><a id="l00062" name="l00062"></a><span class="lineno">   62</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00063" name="l00063"></a><span class="lineno">   63</span> </div>
<div class="line"><a id="l00064" name="l00064"></a><span class="lineno">   64</span><span class="preprocessor">#if defined(ZMQ_CPP14) &amp;&amp; !defined(_MSC_VER)</span></div>
<div class="line"><a id="l00065" name="l00065"></a><span class="lineno">   65</span><span class="preprocessor">#define ZMQ_DEPRECATED(msg) [[deprecated(msg)]]</span></div>
<div class="line"><a id="l00066" name="l00066"></a><span class="lineno">   66</span><span class="preprocessor">#elif defined(_MSC_VER)</span></div>
<div class="line"><a id="l00067" name="l00067"></a><span class="lineno">   67</span><span class="preprocessor">#define ZMQ_DEPRECATED(msg) __declspec(deprecated(msg))</span></div>
<div class="line"><a id="l00068" name="l00068"></a><span class="lineno">   68</span><span class="preprocessor">#elif defined(__GNUC__)</span></div>
<div class="line"><a id="l00069" name="l00069"></a><span class="lineno">   69</span><span class="preprocessor">#define ZMQ_DEPRECATED(msg) __attribute__((deprecated(msg)))</span></div>
<div class="line"><a id="l00070" name="l00070"></a><span class="lineno">   70</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00071" name="l00071"></a><span class="lineno">   71</span> </div>
<div class="line"><a id="l00072" name="l00072"></a><span class="lineno">   72</span><span class="preprocessor">#if defined(ZMQ_CPP17)</span></div>
<div class="line"><a id="l00073" name="l00073"></a><span class="lineno">   73</span><span class="preprocessor">#define ZMQ_NODISCARD [[nodiscard]]</span></div>
<div class="line"><a id="l00074" name="l00074"></a><span class="lineno">   74</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00075" name="l00075"></a><span class="lineno">   75</span><span class="preprocessor">#define ZMQ_NODISCARD</span></div>
<div class="line"><a id="l00076" name="l00076"></a><span class="lineno">   76</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00077" name="l00077"></a><span class="lineno">   77</span> </div>
<div class="line"><a id="l00078" name="l00078"></a><span class="lineno">   78</span><span class="preprocessor">#if defined(ZMQ_CPP11)</span></div>
<div class="line"><a id="l00079" name="l00079"></a><span class="lineno">   79</span><span class="preprocessor">#define ZMQ_NOTHROW noexcept</span></div>
<div class="line"><a id="l00080" name="l00080"></a><span class="lineno">   80</span><span class="preprocessor">#define ZMQ_EXPLICIT explicit</span></div>
<div class="line"><a id="l00081" name="l00081"></a><span class="lineno">   81</span><span class="preprocessor">#define ZMQ_OVERRIDE override</span></div>
<div class="line"><a id="l00082" name="l00082"></a><span class="lineno">   82</span><span class="preprocessor">#define ZMQ_NULLPTR nullptr</span></div>
<div class="line"><a id="l00083" name="l00083"></a><span class="lineno">   83</span><span class="preprocessor">#define ZMQ_CONSTEXPR_FN constexpr</span></div>
<div class="line"><a id="l00084" name="l00084"></a><span class="lineno">   84</span><span class="preprocessor">#define ZMQ_CONSTEXPR_VAR constexpr</span></div>
<div class="line"><a id="l00085" name="l00085"></a><span class="lineno">   85</span><span class="preprocessor">#define ZMQ_CPP11_DEPRECATED(msg) ZMQ_DEPRECATED(msg)</span></div>
<div class="line"><a id="l00086" name="l00086"></a><span class="lineno">   86</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00087" name="l00087"></a><span class="lineno">   87</span><span class="preprocessor">#define ZMQ_NOTHROW throw()</span></div>
<div class="line"><a id="l00088" name="l00088"></a><span class="lineno">   88</span><span class="preprocessor">#define ZMQ_EXPLICIT</span></div>
<div class="line"><a id="l00089" name="l00089"></a><span class="lineno">   89</span><span class="preprocessor">#define ZMQ_OVERRIDE</span></div>
<div class="line"><a id="l00090" name="l00090"></a><span class="lineno">   90</span><span class="preprocessor">#define ZMQ_NULLPTR 0</span></div>
<div class="line"><a id="l00091" name="l00091"></a><span class="lineno">   91</span><span class="preprocessor">#define ZMQ_CONSTEXPR_FN</span></div>
<div class="line"><a id="l00092" name="l00092"></a><span class="lineno">   92</span><span class="preprocessor">#define ZMQ_CONSTEXPR_VAR const</span></div>
<div class="line"><a id="l00093" name="l00093"></a><span class="lineno">   93</span><span class="preprocessor">#define ZMQ_CPP11_DEPRECATED(msg)</span></div>
<div class="line"><a id="l00094" name="l00094"></a><span class="lineno">   94</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00095" name="l00095"></a><span class="lineno">   95</span><span class="preprocessor">#if defined(ZMQ_CPP14) &amp;&amp; (!defined(_MSC_VER) || _MSC_VER &gt; 1900)</span></div>
<div class="line"><a id="l00096" name="l00096"></a><span class="lineno">   96</span><span class="preprocessor">#define ZMQ_EXTENDED_CONSTEXPR</span></div>
<div class="line"><a id="l00097" name="l00097"></a><span class="lineno">   97</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00098" name="l00098"></a><span class="lineno">   98</span><span class="preprocessor">#if defined(ZMQ_CPP17)</span></div>
<div class="line"><a id="l00099" name="l00099"></a><span class="lineno">   99</span><span class="preprocessor">#define ZMQ_INLINE_VAR inline</span></div>
<div class="line"><a id="l00100" name="l00100"></a><span class="lineno">  100</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00101" name="l00101"></a><span class="lineno">  101</span><span class="preprocessor">#define ZMQ_INLINE_VAR</span></div>
<div class="line"><a id="l00102" name="l00102"></a><span class="lineno">  102</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00103" name="l00103"></a><span class="lineno">  103</span> </div>
<div class="line"><a id="l00104" name="l00104"></a><span class="lineno">  104</span><span class="preprocessor">#include &lt;cassert&gt;</span></div>
<div class="line"><a id="l00105" name="l00105"></a><span class="lineno">  105</span><span class="preprocessor">#include &lt;cstring&gt;</span></div>
<div class="line"><a id="l00106" name="l00106"></a><span class="lineno">  106</span> </div>
<div class="line"><a id="l00107" name="l00107"></a><span class="lineno">  107</span><span class="preprocessor">#include &lt;algorithm&gt;</span></div>
<div class="line"><a id="l00108" name="l00108"></a><span class="lineno">  108</span><span class="preprocessor">#include &lt;exception&gt;</span></div>
<div class="line"><a id="l00109" name="l00109"></a><span class="lineno">  109</span><span class="preprocessor">#include &lt;iomanip&gt;</span></div>
<div class="line"><a id="l00110" name="l00110"></a><span class="lineno">  110</span><span class="preprocessor">#include &lt;sstream&gt;</span></div>
<div class="line"><a id="l00111" name="l00111"></a><span class="lineno">  111</span><span class="preprocessor">#include &lt;string&gt;</span></div>
<div class="line"><a id="l00112" name="l00112"></a><span class="lineno">  112</span><span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a id="l00113" name="l00113"></a><span class="lineno">  113</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l00114" name="l00114"></a><span class="lineno">  114</span><span class="preprocessor">#include &lt;array&gt;</span></div>
<div class="line"><a id="l00115" name="l00115"></a><span class="lineno">  115</span><span class="preprocessor">#include &lt;chrono&gt;</span></div>
<div class="line"><a id="l00116" name="l00116"></a><span class="lineno">  116</span><span class="preprocessor">#include &lt;tuple&gt;</span></div>
<div class="line"><a id="l00117" name="l00117"></a><span class="lineno">  117</span><span class="preprocessor">#include &lt;memory&gt;</span></div>
<div class="line"><a id="l00118" name="l00118"></a><span class="lineno">  118</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00119" name="l00119"></a><span class="lineno">  119</span> </div>
<div class="line"><a id="l00120" name="l00120"></a><span class="lineno">  120</span><span class="preprocessor">#if defined(__has_include) &amp;&amp; defined(ZMQ_CPP17)</span></div>
<div class="line"><a id="l00121" name="l00121"></a><span class="lineno">  121</span><span class="preprocessor">#define CPPZMQ_HAS_INCLUDE_CPP17(X) __has_include(X)</span></div>
<div class="line"><a id="l00122" name="l00122"></a><span class="lineno">  122</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00123" name="l00123"></a><span class="lineno">  123</span><span class="preprocessor">#define CPPZMQ_HAS_INCLUDE_CPP17(X) 0</span></div>
<div class="line"><a id="l00124" name="l00124"></a><span class="lineno">  124</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00125" name="l00125"></a><span class="lineno">  125</span> </div>
<div class="line"><a id="l00126" name="l00126"></a><span class="lineno">  126</span><span class="preprocessor">#if CPPZMQ_HAS_INCLUDE_CPP17(&lt;optional&gt;) &amp;&amp; !defined(CPPZMQ_HAS_OPTIONAL)</span></div>
<div class="line"><a id="l00127" name="l00127"></a><span class="lineno">  127</span><span class="preprocessor">#define CPPZMQ_HAS_OPTIONAL 1</span></div>
<div class="line"><a id="l00128" name="l00128"></a><span class="lineno">  128</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00129" name="l00129"></a><span class="lineno">  129</span><span class="preprocessor">#ifndef CPPZMQ_HAS_OPTIONAL</span></div>
<div class="line"><a id="l00130" name="l00130"></a><span class="lineno">  130</span><span class="preprocessor">#define CPPZMQ_HAS_OPTIONAL 0</span></div>
<div class="line"><a id="l00131" name="l00131"></a><span class="lineno">  131</span><span class="preprocessor">#elif CPPZMQ_HAS_OPTIONAL</span></div>
<div class="line"><a id="l00132" name="l00132"></a><span class="lineno">  132</span><span class="preprocessor">#include &lt;optional&gt;</span></div>
<div class="line"><a id="l00133" name="l00133"></a><span class="lineno">  133</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00134" name="l00134"></a><span class="lineno">  134</span> </div>
<div class="line"><a id="l00135" name="l00135"></a><span class="lineno">  135</span><span class="preprocessor">#if CPPZMQ_HAS_INCLUDE_CPP17(&lt;string_view&gt;) &amp;&amp; !defined(CPPZMQ_HAS_STRING_VIEW)</span></div>
<div class="line"><a id="l00136" name="l00136"></a><span class="lineno">  136</span><span class="preprocessor">#define CPPZMQ_HAS_STRING_VIEW 1</span></div>
<div class="line"><a id="l00137" name="l00137"></a><span class="lineno">  137</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00138" name="l00138"></a><span class="lineno">  138</span><span class="preprocessor">#ifndef CPPZMQ_HAS_STRING_VIEW</span></div>
<div class="line"><a id="l00139" name="l00139"></a><span class="lineno">  139</span><span class="preprocessor">#define CPPZMQ_HAS_STRING_VIEW 0</span></div>
<div class="line"><a id="l00140" name="l00140"></a><span class="lineno">  140</span><span class="preprocessor">#elif CPPZMQ_HAS_STRING_VIEW</span></div>
<div class="line"><a id="l00141" name="l00141"></a><span class="lineno">  141</span><span class="preprocessor">#include &lt;string_view&gt;</span></div>
<div class="line"><a id="l00142" name="l00142"></a><span class="lineno">  142</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00143" name="l00143"></a><span class="lineno">  143</span> </div>
<div class="line"><a id="l00144" name="l00144"></a><span class="lineno">  144</span><span class="comment">/*  Version macros for compile-time API version detection                     */</span></div>
<div class="line"><a id="l00145" name="l00145"></a><span class="lineno">  145</span><span class="preprocessor">#define CPPZMQ_VERSION_MAJOR 4</span></div>
<div class="line"><a id="l00146" name="l00146"></a><span class="lineno">  146</span><span class="preprocessor">#define CPPZMQ_VERSION_MINOR 7</span></div>
<div class="line"><a id="l00147" name="l00147"></a><span class="lineno">  147</span><span class="preprocessor">#define CPPZMQ_VERSION_PATCH 1</span></div>
<div class="line"><a id="l00148" name="l00148"></a><span class="lineno">  148</span> </div>
<div class="line"><a id="l00149" name="l00149"></a><span class="lineno">  149</span><span class="preprocessor">#define CPPZMQ_VERSION                                                              \</span></div>
<div class="line"><a id="l00150" name="l00150"></a><span class="lineno">  150</span><span class="preprocessor">    ZMQ_MAKE_VERSION(CPPZMQ_VERSION_MAJOR, CPPZMQ_VERSION_MINOR,                    \</span></div>
<div class="line"><a id="l00151" name="l00151"></a><span class="lineno">  151</span><span class="preprocessor">                     CPPZMQ_VERSION_PATCH)</span></div>
<div class="line"><a id="l00152" name="l00152"></a><span class="lineno">  152</span> </div>
<div class="line"><a id="l00153" name="l00153"></a><span class="lineno">  153</span><span class="comment">//  Detect whether the compiler supports C++11 rvalue references.</span></div>
<div class="line"><a id="l00154" name="l00154"></a><span class="lineno">  154</span><span class="preprocessor">#if (defined(__GNUC__) &amp;&amp; (__GNUC__ &gt; 4 || (__GNUC__ == 4 &amp;&amp; __GNUC_MINOR__ &gt; 2))   \</span></div>
<div class="line"><a id="l00155" name="l00155"></a><span class="lineno">  155</span><span class="preprocessor">     &amp;&amp; defined(__GXX_EXPERIMENTAL_CXX0X__))</span></div>
<div class="line"><a id="l00156" name="l00156"></a><span class="lineno">  156</span><span class="preprocessor">#define ZMQ_HAS_RVALUE_REFS</span></div>
<div class="line"><a id="l00157" name="l00157"></a><span class="lineno">  157</span><span class="preprocessor">#define ZMQ_DELETED_FUNCTION = delete</span></div>
<div class="line"><a id="l00158" name="l00158"></a><span class="lineno">  158</span><span class="preprocessor">#elif defined(__clang__)</span></div>
<div class="line"><a id="l00159" name="l00159"></a><span class="lineno">  159</span><span class="preprocessor">#if __has_feature(cxx_rvalue_references)</span></div>
<div class="line"><a id="l00160" name="l00160"></a><span class="lineno">  160</span><span class="preprocessor">#define ZMQ_HAS_RVALUE_REFS</span></div>
<div class="line"><a id="l00161" name="l00161"></a><span class="lineno">  161</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00162" name="l00162"></a><span class="lineno">  162</span> </div>
<div class="line"><a id="l00163" name="l00163"></a><span class="lineno">  163</span><span class="preprocessor">#if __has_feature(cxx_deleted_functions)</span></div>
<div class="line"><a id="l00164" name="l00164"></a><span class="lineno">  164</span><span class="preprocessor">#define ZMQ_DELETED_FUNCTION = delete</span></div>
<div class="line"><a id="l00165" name="l00165"></a><span class="lineno">  165</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00166" name="l00166"></a><span class="lineno">  166</span><span class="preprocessor">#define ZMQ_DELETED_FUNCTION</span></div>
<div class="line"><a id="l00167" name="l00167"></a><span class="lineno">  167</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00168" name="l00168"></a><span class="lineno">  168</span><span class="preprocessor">#elif defined(_MSC_VER) &amp;&amp; (_MSC_VER &gt;= 1900)</span></div>
<div class="line"><a id="l00169" name="l00169"></a><span class="lineno">  169</span><span class="preprocessor">#define ZMQ_HAS_RVALUE_REFS</span></div>
<div class="line"><a id="l00170" name="l00170"></a><span class="lineno">  170</span><span class="preprocessor">#define ZMQ_DELETED_FUNCTION = delete</span></div>
<div class="line"><a id="l00171" name="l00171"></a><span class="lineno">  171</span><span class="preprocessor">#elif defined(_MSC_VER) &amp;&amp; (_MSC_VER &gt;= 1600)</span></div>
<div class="line"><a id="l00172" name="l00172"></a><span class="lineno">  172</span><span class="preprocessor">#define ZMQ_HAS_RVALUE_REFS</span></div>
<div class="line"><a id="l00173" name="l00173"></a><span class="lineno">  173</span><span class="preprocessor">#define ZMQ_DELETED_FUNCTION</span></div>
<div class="line"><a id="l00174" name="l00174"></a><span class="lineno">  174</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00175" name="l00175"></a><span class="lineno">  175</span><span class="preprocessor">#define ZMQ_DELETED_FUNCTION</span></div>
<div class="line"><a id="l00176" name="l00176"></a><span class="lineno">  176</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00177" name="l00177"></a><span class="lineno">  177</span> </div>
<div class="line"><a id="l00178" name="l00178"></a><span class="lineno">  178</span><span class="preprocessor">#if defined(ZMQ_CPP11) &amp;&amp; !defined(__llvm__) &amp;&amp; !defined(__INTEL_COMPILER)          \</span></div>
<div class="line"><a id="l00179" name="l00179"></a><span class="lineno">  179</span><span class="preprocessor">  &amp;&amp; defined(__GNUC__) &amp;&amp; __GNUC__ &lt; 5</span></div>
<div class="line"><a id="l00180" name="l00180"></a><span class="lineno">  180</span><span class="preprocessor">#define ZMQ_CPP11_PARTIAL</span></div>
<div class="line"><a id="l00181" name="l00181"></a><span class="lineno">  181</span><span class="preprocessor">#elif defined(__GLIBCXX__) &amp;&amp; __GLIBCXX__ &lt; 20160805</span></div>
<div class="line"><a id="l00182" name="l00182"></a><span class="lineno">  182</span><span class="comment">//the date here is the last date of gcc 4.9.4, which</span></div>
<div class="line"><a id="l00183" name="l00183"></a><span class="lineno">  183</span><span class="comment">// effectively means libstdc++ from gcc 5.5 and higher won&#39;t trigger this branch</span></div>
<div class="line"><a id="l00184" name="l00184"></a><span class="lineno">  184</span><span class="preprocessor">#define ZMQ_CPP11_PARTIAL</span></div>
<div class="line"><a id="l00185" name="l00185"></a><span class="lineno">  185</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00186" name="l00186"></a><span class="lineno">  186</span> </div>
<div class="line"><a id="l00187" name="l00187"></a><span class="lineno">  187</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l00188" name="l00188"></a><span class="lineno">  188</span><span class="preprocessor">#ifdef ZMQ_CPP11_PARTIAL</span></div>
<div class="line"><a id="l00189" name="l00189"></a><span class="lineno">  189</span><span class="preprocessor">#define ZMQ_IS_TRIVIALLY_COPYABLE(T) __has_trivial_copy(T)</span></div>
<div class="line"><a id="l00190" name="l00190"></a><span class="lineno">  190</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00191" name="l00191"></a><span class="lineno">  191</span><span class="preprocessor">#include &lt;type_traits&gt;</span></div>
<div class="line"><a id="l00192" name="l00192"></a><span class="lineno">  192</span><span class="preprocessor">#define ZMQ_IS_TRIVIALLY_COPYABLE(T) std::is_trivially_copyable&lt;T&gt;::value</span></div>
<div class="line"><a id="l00193" name="l00193"></a><span class="lineno">  193</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00194" name="l00194"></a><span class="lineno">  194</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00195" name="l00195"></a><span class="lineno">  195</span> </div>
<div class="line"><a id="l00196" name="l00196"></a><span class="lineno">  196</span><span class="preprocessor">#if ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(3, 3, 0)</span></div>
<div class="line"><a id="l00197" name="l00197"></a><span class="lineno">  197</span><span class="preprocessor">#define ZMQ_NEW_MONITOR_EVENT_LAYOUT</span></div>
<div class="line"><a id="l00198" name="l00198"></a><span class="lineno">  198</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00199" name="l00199"></a><span class="lineno">  199</span> </div>
<div class="line"><a id="l00200" name="l00200"></a><span class="lineno">  200</span><span class="preprocessor">#if ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(4, 1, 0)</span></div>
<div class="line"><a id="l00201" name="l00201"></a><span class="lineno">  201</span><span class="preprocessor">#define ZMQ_HAS_PROXY_STEERABLE</span></div>
<div class="line"><a id="l00202" name="l00202"></a><span class="lineno">  202</span><span class="comment">/*  Socket event data  */</span></div>
<div class="foldopen" id="foldopen00203" data-start="{" data-end="};">
<div class="line"><a id="l00203" name="l00203"></a><span class="lineno"><a class="line" href="structzmq__event__t.html">  203</a></span><span class="keyword">typedef</span> <span class="keyword">struct</span></div>
<div class="line"><a id="l00204" name="l00204"></a><span class="lineno">  204</span>{</div>
<div class="line"><a id="l00205" name="l00205"></a><span class="lineno">  205</span>    uint16_t event; <span class="comment">// id of the event as bitfield</span></div>
<div class="line"><a id="l00206" name="l00206"></a><span class="lineno">  206</span>    int32_t value;  <span class="comment">// value is either error code, fd or reconnect interval</span></div>
<div class="line"><a id="l00207" name="l00207"></a><span class="lineno">  207</span>} <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a>;</div>
</div>
<div class="line"><a id="l00208" name="l00208"></a><span class="lineno">  208</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00209" name="l00209"></a><span class="lineno">  209</span> </div>
<div class="line"><a id="l00210" name="l00210"></a><span class="lineno">  210</span><span class="comment">// Avoid using deprecated message receive function when possible</span></div>
<div class="line"><a id="l00211" name="l00211"></a><span class="lineno">  211</span><span class="preprocessor">#if ZMQ_VERSION &lt; ZMQ_MAKE_VERSION(3, 2, 0)</span></div>
<div class="line"><a id="l00212" name="l00212"></a><span class="lineno">  212</span><span class="preprocessor">#define zmq_msg_recv(msg, socket, flags) zmq_recvmsg(socket, msg, flags)</span></div>
<div class="line"><a id="l00213" name="l00213"></a><span class="lineno">  213</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00214" name="l00214"></a><span class="lineno">  214</span> </div>
<div class="line"><a id="l00215" name="l00215"></a><span class="lineno">  215</span> </div>
<div class="line"><a id="l00216" name="l00216"></a><span class="lineno">  216</span><span class="comment">// In order to prevent unused variable warnings when building in non-debug</span></div>
<div class="line"><a id="l00217" name="l00217"></a><span class="lineno">  217</span><span class="comment">// mode use this macro to make assertions.</span></div>
<div class="line"><a id="l00218" name="l00218"></a><span class="lineno">  218</span><span class="preprocessor">#ifndef NDEBUG</span></div>
<div class="line"><a id="l00219" name="l00219"></a><span class="lineno">  219</span><span class="preprocessor">#define ZMQ_ASSERT(expression) assert(expression)</span></div>
<div class="line"><a id="l00220" name="l00220"></a><span class="lineno">  220</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00221" name="l00221"></a><span class="lineno">  221</span><span class="preprocessor">#define ZMQ_ASSERT(expression) (void) (expression)</span></div>
<div class="line"><a id="l00222" name="l00222"></a><span class="lineno">  222</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00223" name="l00223"></a><span class="lineno">  223</span> </div>
<div class="line"><a id="l00224" name="l00224"></a><span class="lineno">  224</span><span class="keyword">namespace </span>zmq</div>
<div class="line"><a id="l00225" name="l00225"></a><span class="lineno">  225</span>{</div>
<div class="line"><a id="l00226" name="l00226"></a><span class="lineno">  226</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l00227" name="l00227"></a><span class="lineno">  227</span><span class="keyword">namespace </span>detail</div>
<div class="line"><a id="l00228" name="l00228"></a><span class="lineno">  228</span>{</div>
<div class="line"><a id="l00229" name="l00229"></a><span class="lineno">  229</span><span class="keyword">namespace </span>ranges</div>
<div class="line"><a id="l00230" name="l00230"></a><span class="lineno">  230</span>{</div>
<div class="line"><a id="l00231" name="l00231"></a><span class="lineno">  231</span><span class="keyword">using </span>std::begin;</div>
<div class="line"><a id="l00232" name="l00232"></a><span class="lineno">  232</span><span class="keyword">using </span>std::end;</div>
<div class="line"><a id="l00233" name="l00233"></a><span class="lineno">  233</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">auto</span> begin(T &amp;&amp;r) -&gt; <span class="keyword">decltype</span>(begin(std::forward&lt;T&gt;(r)))</div>
<div class="line"><a id="l00234" name="l00234"></a><span class="lineno">  234</span>{</div>
<div class="line"><a id="l00235" name="l00235"></a><span class="lineno">  235</span>    <span class="keywordflow">return</span> begin(std::forward&lt;T&gt;(r));</div>
<div class="line"><a id="l00236" name="l00236"></a><span class="lineno">  236</span>}</div>
<div class="line"><a id="l00237" name="l00237"></a><span class="lineno">  237</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">auto</span> end(T &amp;&amp;r) -&gt; <span class="keyword">decltype</span>(end(std::forward&lt;T&gt;(r)))</div>
<div class="line"><a id="l00238" name="l00238"></a><span class="lineno">  238</span>{</div>
<div class="line"><a id="l00239" name="l00239"></a><span class="lineno">  239</span>    <span class="keywordflow">return</span> end(std::forward&lt;T&gt;(r));</div>
<div class="line"><a id="l00240" name="l00240"></a><span class="lineno">  240</span>}</div>
<div class="line"><a id="l00241" name="l00241"></a><span class="lineno">  241</span>} <span class="comment">// namespace ranges</span></div>
<div class="line"><a id="l00242" name="l00242"></a><span class="lineno">  242</span> </div>
<div class="line"><a id="l00243" name="l00243"></a><span class="lineno">  243</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">using </span>void_t = void;</div>
<div class="line"><a id="l00244" name="l00244"></a><span class="lineno">  244</span> </div>
<div class="line"><a id="l00245" name="l00245"></a><span class="lineno">  245</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> Iter&gt;</div>
<div class="line"><a id="l00246" name="l00246"></a><span class="lineno">  246</span><span class="keyword">using </span>iter_value_t = <span class="keyword">typename</span> std::iterator_traits&lt;Iter&gt;::value_type;</div>
<div class="line"><a id="l00247" name="l00247"></a><span class="lineno">  247</span> </div>
<div class="line"><a id="l00248" name="l00248"></a><span class="lineno">  248</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> Range&gt;</div>
<div class="line"><a id="l00249" name="l00249"></a><span class="lineno">  249</span><span class="keyword">using </span>range_iter_t = <span class="keyword">decltype</span>(</div>
<div class="line"><a id="l00250" name="l00250"></a><span class="lineno">  250</span>  ranges::begin(std::declval&lt;<span class="keyword">typename</span> std::remove_reference&lt;Range&gt;::type &amp;&gt;()));</div>
<div class="line"><a id="l00251" name="l00251"></a><span class="lineno">  251</span> </div>
<div class="line"><a id="l00252" name="l00252"></a><span class="lineno">  252</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> Range&gt; <span class="keyword">using </span>range_value_t = iter_value_t&lt;range_iter_t&lt;Range&gt;&gt;;</div>
<div class="line"><a id="l00253" name="l00253"></a><span class="lineno">  253</span> </div>
<div class="line"><a id="l00254" name="l00254"></a><span class="lineno">  254</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> = <span class="keywordtype">void</span>&gt; <span class="keyword">struct </span>is_range : std::false_type</div>
<div class="line"><a id="l00255" name="l00255"></a><span class="lineno">  255</span>{</div>
<div class="line"><a id="l00256" name="l00256"></a><span class="lineno">  256</span>};</div>
<div class="line"><a id="l00257" name="l00257"></a><span class="lineno">  257</span> </div>
<div class="line"><a id="l00258" name="l00258"></a><span class="lineno">  258</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt;</div>
<div class="line"><a id="l00259" name="l00259"></a><span class="lineno">  259</span><span class="keyword">struct </span>is_range&lt;</div>
<div class="line"><a id="l00260" name="l00260"></a><span class="lineno">  260</span>  T,</div>
<div class="line"><a id="l00261" name="l00261"></a><span class="lineno">  261</span>  void_t&lt;decltype(</div>
<div class="line"><a id="l00262" name="l00262"></a><span class="lineno">  262</span>    ranges::begin(std::declval&lt;typename std::remove_reference&lt;T&gt;::type &amp;&gt;())</div>
<div class="line"><a id="l00263" name="l00263"></a><span class="lineno">  263</span>    == ranges::end(std::declval&lt;typename std::remove_reference&lt;T&gt;::type &amp;&gt;()))&gt;&gt;</div>
<div class="line"><a id="l00264" name="l00264"></a><span class="lineno">  264</span>    : std::true_type</div>
<div class="line"><a id="l00265" name="l00265"></a><span class="lineno">  265</span>{</div>
<div class="line"><a id="l00266" name="l00266"></a><span class="lineno">  266</span>};</div>
<div class="line"><a id="l00267" name="l00267"></a><span class="lineno">  267</span> </div>
<div class="line"><a id="l00268" name="l00268"></a><span class="lineno">  268</span>} <span class="comment">// namespace detail</span></div>
<div class="line"><a id="l00269" name="l00269"></a><span class="lineno">  269</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00270" name="l00270"></a><span class="lineno">  270</span> </div>
<div class="line"><a id="l00271" name="l00271"></a><span class="lineno">  271</span><span class="keyword">typedef</span> zmq_free_fn free_fn;</div>
<div class="line"><a id="l00272" name="l00272"></a><span class="lineno">  272</span><span class="keyword">typedef</span> zmq_pollitem_t pollitem_t;</div>
<div class="line"><a id="l00273" name="l00273"></a><span class="lineno">  273</span> </div>
<div class="foldopen" id="foldopen00274" data-start="{" data-end="};">
<div class="line"><a id="l00274" name="l00274"></a><span class="lineno"><a class="line" href="classzmq_1_1error__t.html">  274</a></span><span class="keyword">class </span>error_t : <span class="keyword">public</span> std::exception</div>
<div class="line"><a id="l00275" name="l00275"></a><span class="lineno">  275</span>{</div>
<div class="line"><a id="l00276" name="l00276"></a><span class="lineno">  276</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l00277" name="l00277"></a><span class="lineno">  277</span>    error_t() ZMQ_NOTHROW : errnum(zmq_errno()) {}</div>
<div class="line"><a id="l00278" name="l00278"></a><span class="lineno">  278</span>    <span class="keyword">explicit</span> error_t(<span class="keywordtype">int</span> err) ZMQ_NOTHROW : errnum(err) {}</div>
<div class="line"><a id="l00279" name="l00279"></a><span class="lineno">  279</span>    <span class="keyword">virtual</span> <span class="keyword">const</span> <span class="keywordtype">char</span> *what() <span class="keyword">const</span> ZMQ_NOTHROW ZMQ_OVERRIDE</div>
<div class="line"><a id="l00280" name="l00280"></a><span class="lineno">  280</span>    {</div>
<div class="line"><a id="l00281" name="l00281"></a><span class="lineno">  281</span>        <span class="keywordflow">return</span> zmq_strerror(errnum);</div>
<div class="line"><a id="l00282" name="l00282"></a><span class="lineno">  282</span>    }</div>
<div class="line"><a id="l00283" name="l00283"></a><span class="lineno">  283</span>    <span class="keywordtype">int</span> num() <span class="keyword">const</span> ZMQ_NOTHROW { <span class="keywordflow">return</span> errnum; }</div>
<div class="line"><a id="l00284" name="l00284"></a><span class="lineno">  284</span> </div>
<div class="line"><a id="l00285" name="l00285"></a><span class="lineno">  285</span>  <span class="keyword">private</span>:</div>
<div class="line"><a id="l00286" name="l00286"></a><span class="lineno">  286</span>    <span class="keywordtype">int</span> errnum;</div>
<div class="line"><a id="l00287" name="l00287"></a><span class="lineno">  287</span>};</div>
</div>
<div class="line"><a id="l00288" name="l00288"></a><span class="lineno">  288</span> </div>
<div class="line"><a id="l00289" name="l00289"></a><span class="lineno">  289</span><span class="keyword">inline</span> <span class="keywordtype">int</span> poll(zmq_pollitem_t *items_, <span class="keywordtype">size_t</span> nitems_, <span class="keywordtype">long</span> timeout_ = -1)</div>
<div class="line"><a id="l00290" name="l00290"></a><span class="lineno">  290</span>{</div>
<div class="line"><a id="l00291" name="l00291"></a><span class="lineno">  291</span>    <span class="keywordtype">int</span> rc = zmq_poll(items_, <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(nitems_), timeout_);</div>
<div class="line"><a id="l00292" name="l00292"></a><span class="lineno">  292</span>    <span class="keywordflow">if</span> (rc &lt; 0)</div>
<div class="line"><a id="l00293" name="l00293"></a><span class="lineno">  293</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00294" name="l00294"></a><span class="lineno">  294</span>    <span class="keywordflow">return</span> rc;</div>
<div class="line"><a id="l00295" name="l00295"></a><span class="lineno">  295</span>}</div>
<div class="line"><a id="l00296" name="l00296"></a><span class="lineno">  296</span> </div>
<div class="line"><a id="l00297" name="l00297"></a><span class="lineno">  297</span>ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use poll taking non-const items&quot;</span>)</div>
<div class="line"><a id="l00298" name="l00298"></a><span class="lineno">  298</span>inline <span class="keywordtype">int</span> poll(zmq_pollitem_t const *items_, <span class="keywordtype">size_t</span> nitems_, <span class="keywordtype">long</span> timeout_ = -1)</div>
<div class="line"><a id="l00299" name="l00299"></a><span class="lineno">  299</span>{</div>
<div class="line"><a id="l00300" name="l00300"></a><span class="lineno">  300</span>    <span class="keywordflow">return</span> poll(<span class="keyword">const_cast&lt;</span>zmq_pollitem_t *<span class="keyword">&gt;</span>(items_), nitems_, timeout_);</div>
<div class="line"><a id="l00301" name="l00301"></a><span class="lineno">  301</span>}</div>
<div class="line"><a id="l00302" name="l00302"></a><span class="lineno">  302</span> </div>
<div class="line"><a id="l00303" name="l00303"></a><span class="lineno">  303</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l00304" name="l00304"></a><span class="lineno">  304</span>ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use poll taking non-const items&quot;</span>)</div>
<div class="line"><a id="l00305" name="l00305"></a><span class="lineno">  305</span>inline <span class="keywordtype">int</span></div>
<div class="line"><a id="l00306" name="l00306"></a><span class="lineno">  306</span>poll(zmq_pollitem_t const *items, <span class="keywordtype">size_t</span> nitems, std::chrono::milliseconds timeout)</div>
<div class="line"><a id="l00307" name="l00307"></a><span class="lineno">  307</span>{</div>
<div class="line"><a id="l00308" name="l00308"></a><span class="lineno">  308</span>    <span class="keywordflow">return</span> poll(<span class="keyword">const_cast&lt;</span>zmq_pollitem_t *<span class="keyword">&gt;</span>(items), nitems,</div>
<div class="line"><a id="l00309" name="l00309"></a><span class="lineno">  309</span>                <span class="keyword">static_cast&lt;</span><span class="keywordtype">long</span><span class="keyword">&gt;</span>(timeout.count()));</div>
<div class="line"><a id="l00310" name="l00310"></a><span class="lineno">  310</span>}</div>
<div class="line"><a id="l00311" name="l00311"></a><span class="lineno">  311</span> </div>
<div class="line"><a id="l00312" name="l00312"></a><span class="lineno">  312</span>ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use poll taking non-const items&quot;</span>)</div>
<div class="line"><a id="l00313" name="l00313"></a><span class="lineno">  313</span>inline <span class="keywordtype">int</span> poll(std::vector&lt;zmq_pollitem_t&gt; const &amp;items,</div>
<div class="line"><a id="l00314" name="l00314"></a><span class="lineno">  314</span>                std::chrono::milliseconds timeout)</div>
<div class="line"><a id="l00315" name="l00315"></a><span class="lineno">  315</span>{</div>
<div class="line"><a id="l00316" name="l00316"></a><span class="lineno">  316</span>    <span class="keywordflow">return</span> poll(<span class="keyword">const_cast&lt;</span>zmq_pollitem_t *<span class="keyword">&gt;</span>(items.data()), items.size(),</div>
<div class="line"><a id="l00317" name="l00317"></a><span class="lineno">  317</span>                <span class="keyword">static_cast&lt;</span><span class="keywordtype">long</span><span class="keyword">&gt;</span>(timeout.count()));</div>
<div class="line"><a id="l00318" name="l00318"></a><span class="lineno">  318</span>}</div>
<div class="line"><a id="l00319" name="l00319"></a><span class="lineno">  319</span> </div>
<div class="line"><a id="l00320" name="l00320"></a><span class="lineno">  320</span>ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use poll taking non-const items&quot;</span>)</div>
<div class="line"><a id="l00321" name="l00321"></a><span class="lineno">  321</span>inline <span class="keywordtype">int</span> poll(std::vector&lt;zmq_pollitem_t&gt; const &amp;items, <span class="keywordtype">long</span> timeout_ = -1)</div>
<div class="line"><a id="l00322" name="l00322"></a><span class="lineno">  322</span>{</div>
<div class="line"><a id="l00323" name="l00323"></a><span class="lineno">  323</span>    <span class="keywordflow">return</span> poll(<span class="keyword">const_cast&lt;</span>zmq_pollitem_t *<span class="keyword">&gt;</span>(items.data()), items.size(), timeout_);</div>
<div class="line"><a id="l00324" name="l00324"></a><span class="lineno">  324</span>}</div>
<div class="line"><a id="l00325" name="l00325"></a><span class="lineno">  325</span> </div>
<div class="line"><a id="l00326" name="l00326"></a><span class="lineno">  326</span><span class="keyword">inline</span> <span class="keywordtype">int</span></div>
<div class="line"><a id="l00327" name="l00327"></a><span class="lineno">  327</span>poll(zmq_pollitem_t *items, <span class="keywordtype">size_t</span> nitems, std::chrono::milliseconds timeout)</div>
<div class="line"><a id="l00328" name="l00328"></a><span class="lineno">  328</span>{</div>
<div class="line"><a id="l00329" name="l00329"></a><span class="lineno">  329</span>    <span class="keywordflow">return</span> poll(items, nitems, <span class="keyword">static_cast&lt;</span><span class="keywordtype">long</span><span class="keyword">&gt;</span>(timeout.count()));</div>
<div class="line"><a id="l00330" name="l00330"></a><span class="lineno">  330</span>}</div>
<div class="line"><a id="l00331" name="l00331"></a><span class="lineno">  331</span> </div>
<div class="line"><a id="l00332" name="l00332"></a><span class="lineno">  332</span><span class="keyword">inline</span> <span class="keywordtype">int</span> poll(std::vector&lt;zmq_pollitem_t&gt; &amp;items,</div>
<div class="line"><a id="l00333" name="l00333"></a><span class="lineno">  333</span>                std::chrono::milliseconds timeout)</div>
<div class="line"><a id="l00334" name="l00334"></a><span class="lineno">  334</span>{</div>
<div class="line"><a id="l00335" name="l00335"></a><span class="lineno">  335</span>    <span class="keywordflow">return</span> poll(items.data(), items.size(), <span class="keyword">static_cast&lt;</span><span class="keywordtype">long</span><span class="keyword">&gt;</span>(timeout.count()));</div>
<div class="line"><a id="l00336" name="l00336"></a><span class="lineno">  336</span>}</div>
<div class="line"><a id="l00337" name="l00337"></a><span class="lineno">  337</span> </div>
<div class="line"><a id="l00338" name="l00338"></a><span class="lineno">  338</span>ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use poll taking std::chrono instead of long&quot;</span>)</div>
<div class="line"><a id="l00339" name="l00339"></a><span class="lineno">  339</span>inline <span class="keywordtype">int</span> poll(std::vector&lt;zmq_pollitem_t&gt; &amp;items, <span class="keywordtype">long</span> timeout_ = -1)</div>
<div class="line"><a id="l00340" name="l00340"></a><span class="lineno">  340</span>{</div>
<div class="line"><a id="l00341" name="l00341"></a><span class="lineno">  341</span>    <span class="keywordflow">return</span> poll(items.data(), items.size(), timeout_);</div>
<div class="line"><a id="l00342" name="l00342"></a><span class="lineno">  342</span>}</div>
<div class="line"><a id="l00343" name="l00343"></a><span class="lineno">  343</span> </div>
<div class="line"><a id="l00344" name="l00344"></a><span class="lineno">  344</span><span class="keyword">template</span>&lt;std::<span class="keywordtype">size_t</span> SIZE&gt;</div>
<div class="line"><a id="l00345" name="l00345"></a><span class="lineno">  345</span><span class="keyword">inline</span> <span class="keywordtype">int</span> poll(std::array&lt;zmq_pollitem_t, SIZE&gt; &amp;items,</div>
<div class="line"><a id="l00346" name="l00346"></a><span class="lineno">  346</span>                std::chrono::milliseconds timeout)</div>
<div class="line"><a id="l00347" name="l00347"></a><span class="lineno">  347</span>{</div>
<div class="line"><a id="l00348" name="l00348"></a><span class="lineno">  348</span>    <span class="keywordflow">return</span> poll(items.data(), items.size(), <span class="keyword">static_cast&lt;</span><span class="keywordtype">long</span><span class="keyword">&gt;</span>(timeout.count()));</div>
<div class="line"><a id="l00349" name="l00349"></a><span class="lineno">  349</span>}</div>
<div class="line"><a id="l00350" name="l00350"></a><span class="lineno">  350</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00351" name="l00351"></a><span class="lineno">  351</span> </div>
<div class="line"><a id="l00352" name="l00352"></a><span class="lineno">  352</span> </div>
<div class="line"><a id="l00353" name="l00353"></a><span class="lineno">  353</span><span class="keyword">inline</span> <span class="keywordtype">void</span> version(<span class="keywordtype">int</span> *major_, <span class="keywordtype">int</span> *minor_, <span class="keywordtype">int</span> *patch_)</div>
<div class="line"><a id="l00354" name="l00354"></a><span class="lineno">  354</span>{</div>
<div class="line"><a id="l00355" name="l00355"></a><span class="lineno">  355</span>    zmq_version(major_, minor_, patch_);</div>
<div class="line"><a id="l00356" name="l00356"></a><span class="lineno">  356</span>}</div>
<div class="line"><a id="l00357" name="l00357"></a><span class="lineno">  357</span> </div>
<div class="line"><a id="l00358" name="l00358"></a><span class="lineno">  358</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l00359" name="l00359"></a><span class="lineno">  359</span><span class="keyword">inline</span> std::tuple&lt;int, int, int&gt; version()</div>
<div class="line"><a id="l00360" name="l00360"></a><span class="lineno">  360</span>{</div>
<div class="line"><a id="l00361" name="l00361"></a><span class="lineno">  361</span>    std::tuple&lt;int, int, int&gt; v;</div>
<div class="line"><a id="l00362" name="l00362"></a><span class="lineno">  362</span>    zmq_version(&amp;std::get&lt;0&gt;(v), &amp;std::get&lt;1&gt;(v), &amp;std::get&lt;2&gt;(v));</div>
<div class="line"><a id="l00363" name="l00363"></a><span class="lineno">  363</span>    <span class="keywordflow">return</span> v;</div>
<div class="line"><a id="l00364" name="l00364"></a><span class="lineno">  364</span>}</div>
<div class="line"><a id="l00365" name="l00365"></a><span class="lineno">  365</span> </div>
<div class="line"><a id="l00366" name="l00366"></a><span class="lineno">  366</span><span class="preprocessor">#if !defined(ZMQ_CPP11_PARTIAL)</span></div>
<div class="line"><a id="l00367" name="l00367"></a><span class="lineno">  367</span><span class="keyword">namespace </span>detail</div>
<div class="line"><a id="l00368" name="l00368"></a><span class="lineno">  368</span>{</div>
<div class="line"><a id="l00369" name="l00369"></a><span class="lineno">  369</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">struct </span>is_char_type</div>
<div class="line"><a id="l00370" name="l00370"></a><span class="lineno">  370</span>{</div>
<div class="line"><a id="l00371" name="l00371"></a><span class="lineno">  371</span>    <span class="comment">// true if character type for string literals in C++11</span></div>
<div class="line"><a id="l00372" name="l00372"></a><span class="lineno">  372</span>    <span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="keywordtype">bool</span> value =</div>
<div class="line"><a id="l00373" name="l00373"></a><span class="lineno">  373</span>      std::is_same&lt;T, char&gt;::value || std::is_same&lt;T, wchar_t&gt;::value</div>
<div class="line"><a id="l00374" name="l00374"></a><span class="lineno">  374</span>      || std::is_same&lt;T, char16_t&gt;::value || std::is_same&lt;T, char32_t&gt;::value;</div>
<div class="line"><a id="l00375" name="l00375"></a><span class="lineno">  375</span>};</div>
<div class="line"><a id="l00376" name="l00376"></a><span class="lineno">  376</span>}</div>
<div class="line"><a id="l00377" name="l00377"></a><span class="lineno">  377</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00378" name="l00378"></a><span class="lineno">  378</span> </div>
<div class="line"><a id="l00379" name="l00379"></a><span class="lineno">  379</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00380" name="l00380"></a><span class="lineno">  380</span> </div>
<div class="foldopen" id="foldopen00381" data-start="{" data-end="};">
<div class="line"><a id="l00381" name="l00381"></a><span class="lineno"><a class="line" href="classzmq_1_1message__t.html">  381</a></span><span class="keyword">class </span>message_t</div>
<div class="line"><a id="l00382" name="l00382"></a><span class="lineno">  382</span>{</div>
<div class="line"><a id="l00383" name="l00383"></a><span class="lineno">  383</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l00384" name="l00384"></a><span class="lineno">  384</span>    message_t() ZMQ_NOTHROW</div>
<div class="line"><a id="l00385" name="l00385"></a><span class="lineno">  385</span>    {</div>
<div class="line"><a id="l00386" name="l00386"></a><span class="lineno">  386</span>        <span class="keywordtype">int</span> rc = zmq_msg_init(&amp;msg);</div>
<div class="line"><a id="l00387" name="l00387"></a><span class="lineno">  387</span>        ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l00388" name="l00388"></a><span class="lineno">  388</span>    }</div>
<div class="line"><a id="l00389" name="l00389"></a><span class="lineno">  389</span> </div>
<div class="line"><a id="l00390" name="l00390"></a><span class="lineno">  390</span>    <span class="keyword">explicit</span> message_t(<span class="keywordtype">size_t</span> size_)</div>
<div class="line"><a id="l00391" name="l00391"></a><span class="lineno">  391</span>    {</div>
<div class="line"><a id="l00392" name="l00392"></a><span class="lineno">  392</span>        <span class="keywordtype">int</span> rc = zmq_msg_init_size(&amp;msg, size_);</div>
<div class="line"><a id="l00393" name="l00393"></a><span class="lineno">  393</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00394" name="l00394"></a><span class="lineno">  394</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00395" name="l00395"></a><span class="lineno">  395</span>    }</div>
<div class="line"><a id="l00396" name="l00396"></a><span class="lineno">  396</span> </div>
<div class="line"><a id="l00397" name="l00397"></a><span class="lineno">  397</span>    <span class="keyword">template</span>&lt;<span class="keyword">class</span> ForwardIter&gt; message_t(ForwardIter first, ForwardIter last)</div>
<div class="line"><a id="l00398" name="l00398"></a><span class="lineno">  398</span>    {</div>
<div class="line"><a id="l00399" name="l00399"></a><span class="lineno">  399</span>        <span class="keyword">typedef</span> <span class="keyword">typename</span> std::iterator_traits&lt;ForwardIter&gt;::value_type value_t;</div>
<div class="line"><a id="l00400" name="l00400"></a><span class="lineno">  400</span> </div>
<div class="line"><a id="l00401" name="l00401"></a><span class="lineno">  401</span>        assert(std::distance(first, last) &gt;= 0);</div>
<div class="line"><a id="l00402" name="l00402"></a><span class="lineno">  402</span>        <span class="keywordtype">size_t</span> <span class="keyword">const</span> size_ =</div>
<div class="line"><a id="l00403" name="l00403"></a><span class="lineno">  403</span>          <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(std::distance(first, last)) * <span class="keyword">sizeof</span>(value_t);</div>
<div class="line"><a id="l00404" name="l00404"></a><span class="lineno">  404</span>        <span class="keywordtype">int</span> <span class="keyword">const</span> rc = zmq_msg_init_size(&amp;msg, size_);</div>
<div class="line"><a id="l00405" name="l00405"></a><span class="lineno">  405</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00406" name="l00406"></a><span class="lineno">  406</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00407" name="l00407"></a><span class="lineno">  407</span>        std::copy(first, last, data&lt;value_t&gt;());</div>
<div class="line"><a id="l00408" name="l00408"></a><span class="lineno">  408</span>    }</div>
<div class="line"><a id="l00409" name="l00409"></a><span class="lineno">  409</span> </div>
<div class="line"><a id="l00410" name="l00410"></a><span class="lineno">  410</span>    message_t(<span class="keyword">const</span> <span class="keywordtype">void</span> *data_, <span class="keywordtype">size_t</span> size_)</div>
<div class="line"><a id="l00411" name="l00411"></a><span class="lineno">  411</span>    {</div>
<div class="line"><a id="l00412" name="l00412"></a><span class="lineno">  412</span>        <span class="keywordtype">int</span> rc = zmq_msg_init_size(&amp;msg, size_);</div>
<div class="line"><a id="l00413" name="l00413"></a><span class="lineno">  413</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00414" name="l00414"></a><span class="lineno">  414</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00415" name="l00415"></a><span class="lineno">  415</span>        <span class="keywordflow">if</span> (size_) {</div>
<div class="line"><a id="l00416" name="l00416"></a><span class="lineno">  416</span>            <span class="comment">// this constructor allows (nullptr, 0),</span></div>
<div class="line"><a id="l00417" name="l00417"></a><span class="lineno">  417</span>            <span class="comment">// memcpy with a null pointer is UB</span></div>
<div class="line"><a id="l00418" name="l00418"></a><span class="lineno">  418</span>            memcpy(data(), data_, size_);</div>
<div class="line"><a id="l00419" name="l00419"></a><span class="lineno">  419</span>        }</div>
<div class="line"><a id="l00420" name="l00420"></a><span class="lineno">  420</span>    }</div>
<div class="line"><a id="l00421" name="l00421"></a><span class="lineno">  421</span> </div>
<div class="line"><a id="l00422" name="l00422"></a><span class="lineno">  422</span>    message_t(<span class="keywordtype">void</span> *data_, <span class="keywordtype">size_t</span> size_, free_fn *ffn_, <span class="keywordtype">void</span> *hint_ = ZMQ_NULLPTR)</div>
<div class="line"><a id="l00423" name="l00423"></a><span class="lineno">  423</span>    {</div>
<div class="line"><a id="l00424" name="l00424"></a><span class="lineno">  424</span>        <span class="keywordtype">int</span> rc = zmq_msg_init_data(&amp;msg, data_, size_, ffn_, hint_);</div>
<div class="line"><a id="l00425" name="l00425"></a><span class="lineno">  425</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00426" name="l00426"></a><span class="lineno">  426</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00427" name="l00427"></a><span class="lineno">  427</span>    }</div>
<div class="line"><a id="l00428" name="l00428"></a><span class="lineno">  428</span> </div>
<div class="line"><a id="l00429" name="l00429"></a><span class="lineno">  429</span>    <span class="comment">// overload set of string-like types and generic containers</span></div>
<div class="line"><a id="l00430" name="l00430"></a><span class="lineno">  430</span><span class="preprocessor">#if defined(ZMQ_CPP11) &amp;&amp; !defined(ZMQ_CPP11_PARTIAL)</span></div>
<div class="line"><a id="l00431" name="l00431"></a><span class="lineno">  431</span>    <span class="comment">// NOTE this constructor will include the null terminator</span></div>
<div class="line"><a id="l00432" name="l00432"></a><span class="lineno">  432</span>    <span class="comment">// when called with a string literal.</span></div>
<div class="line"><a id="l00433" name="l00433"></a><span class="lineno">  433</span>    <span class="comment">// An overload taking const char* can not be added because</span></div>
<div class="line"><a id="l00434" name="l00434"></a><span class="lineno">  434</span>    <span class="comment">// it would be preferred over this function and break compatiblity.</span></div>
<div class="line"><a id="l00435" name="l00435"></a><span class="lineno">  435</span>    <span class="keyword">template</span>&lt;</div>
<div class="line"><a id="l00436" name="l00436"></a><span class="lineno">  436</span>      <span class="keyword">class </span>Char,</div>
<div class="line"><a id="l00437" name="l00437"></a><span class="lineno">  437</span>      <span class="keywordtype">size_t</span> N,</div>
<div class="line"><a id="l00438" name="l00438"></a><span class="lineno">  438</span>      <span class="keyword">typename</span> = <span class="keyword">typename</span> std::enable_if&lt;detail::is_char_type&lt;Char&gt;::value&gt;::type&gt;</div>
<div class="line"><a id="l00439" name="l00439"></a><span class="lineno">  439</span>    ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.7.0, use constructors taking iterators, (pointer, size) &quot;</span></div>
<div class="line"><a id="l00440" name="l00440"></a><span class="lineno">  440</span>                   <span class="stringliteral">&quot;or strings instead&quot;</span>)</div>
<div class="line"><a id="l00441" name="l00441"></a><span class="lineno">  441</span>    <span class="keyword">explicit</span> message_t(<span class="keyword">const</span> Char (&amp;data)[N]) :</div>
<div class="line"><a id="l00442" name="l00442"></a><span class="lineno">  442</span>        message_t(detail::ranges::begin(data), detail::ranges::end(data))</div>
<div class="line"><a id="l00443" name="l00443"></a><span class="lineno">  443</span>    {</div>
<div class="line"><a id="l00444" name="l00444"></a><span class="lineno">  444</span>    }</div>
<div class="line"><a id="l00445" name="l00445"></a><span class="lineno">  445</span> </div>
<div class="line"><a id="l00446" name="l00446"></a><span class="lineno">  446</span>    <span class="keyword">template</span>&lt;<span class="keyword">class </span>Range,</div>
<div class="line"><a id="l00447" name="l00447"></a><span class="lineno">  447</span>             <span class="keyword">typename</span> = <span class="keyword">typename</span> std::enable_if&lt;</div>
<div class="line"><a id="l00448" name="l00448"></a><span class="lineno">  448</span>               detail::is_range&lt;Range&gt;::value</div>
<div class="line"><a id="l00449" name="l00449"></a><span class="lineno">  449</span>               &amp;&amp; ZMQ_IS_TRIVIALLY_COPYABLE(detail::range_value_t&lt;Range&gt;)</div>
<div class="line"><a id="l00450" name="l00450"></a><span class="lineno">  450</span>               &amp;&amp; !detail::is_char_type&lt;detail::range_value_t&lt;Range&gt;&gt;::value</div>
<div class="line"><a id="l00451" name="l00451"></a><span class="lineno">  451</span>               &amp;&amp; !std::is_same&lt;Range, message_t&gt;::value&gt;::type&gt;</div>
<div class="line"><a id="l00452" name="l00452"></a><span class="lineno">  452</span>    <span class="keyword">explicit</span> message_t(<span class="keyword">const</span> Range &amp;rng) :</div>
<div class="line"><a id="l00453" name="l00453"></a><span class="lineno">  453</span>        message_t(detail::ranges::begin(rng), detail::ranges::end(rng))</div>
<div class="line"><a id="l00454" name="l00454"></a><span class="lineno">  454</span>    {</div>
<div class="line"><a id="l00455" name="l00455"></a><span class="lineno">  455</span>    }</div>
<div class="line"><a id="l00456" name="l00456"></a><span class="lineno">  456</span> </div>
<div class="line"><a id="l00457" name="l00457"></a><span class="lineno">  457</span>    <span class="keyword">explicit</span> message_t(<span class="keyword">const</span> std::string &amp;<a class="code hl_function" href="classzmq_1_1message__t.html#aed6502a922a73cda7f47d47689617d03">str</a>) : message_t(<a class="code hl_function" href="classzmq_1_1message__t.html#aed6502a922a73cda7f47d47689617d03">str</a>.data(), <a class="code hl_function" href="classzmq_1_1message__t.html#aed6502a922a73cda7f47d47689617d03">str</a>.size()) {}</div>
<div class="line"><a id="l00458" name="l00458"></a><span class="lineno">  458</span> </div>
<div class="line"><a id="l00459" name="l00459"></a><span class="lineno">  459</span><span class="preprocessor">#if CPPZMQ_HAS_STRING_VIEW</span></div>
<div class="line"><a id="l00460" name="l00460"></a><span class="lineno">  460</span>    <span class="keyword">explicit</span> message_t(std::string_view <a class="code hl_function" href="classzmq_1_1message__t.html#aed6502a922a73cda7f47d47689617d03">str</a>) : message_t(<a class="code hl_function" href="classzmq_1_1message__t.html#aed6502a922a73cda7f47d47689617d03">str</a>.data(), <a class="code hl_function" href="classzmq_1_1message__t.html#aed6502a922a73cda7f47d47689617d03">str</a>.size()) {}</div>
<div class="line"><a id="l00461" name="l00461"></a><span class="lineno">  461</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00462" name="l00462"></a><span class="lineno">  462</span> </div>
<div class="line"><a id="l00463" name="l00463"></a><span class="lineno">  463</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00464" name="l00464"></a><span class="lineno">  464</span> </div>
<div class="line"><a id="l00465" name="l00465"></a><span class="lineno">  465</span><span class="preprocessor">#ifdef ZMQ_HAS_RVALUE_REFS</span></div>
<div class="line"><a id="l00466" name="l00466"></a><span class="lineno">  466</span>    message_t(message_t &amp;&amp;rhs) ZMQ_NOTHROW : msg(rhs.msg)</div>
<div class="line"><a id="l00467" name="l00467"></a><span class="lineno">  467</span>    {</div>
<div class="line"><a id="l00468" name="l00468"></a><span class="lineno">  468</span>        <span class="keywordtype">int</span> rc = zmq_msg_init(&amp;rhs.msg);</div>
<div class="line"><a id="l00469" name="l00469"></a><span class="lineno">  469</span>        ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l00470" name="l00470"></a><span class="lineno">  470</span>    }</div>
<div class="line"><a id="l00471" name="l00471"></a><span class="lineno">  471</span> </div>
<div class="line"><a id="l00472" name="l00472"></a><span class="lineno">  472</span>    message_t &amp;operator=(message_t &amp;&amp;rhs) ZMQ_NOTHROW</div>
<div class="line"><a id="l00473" name="l00473"></a><span class="lineno">  473</span>    {</div>
<div class="line"><a id="l00474" name="l00474"></a><span class="lineno">  474</span>        std::swap(msg, rhs.msg);</div>
<div class="line"><a id="l00475" name="l00475"></a><span class="lineno">  475</span>        <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a id="l00476" name="l00476"></a><span class="lineno">  476</span>    }</div>
<div class="line"><a id="l00477" name="l00477"></a><span class="lineno">  477</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00478" name="l00478"></a><span class="lineno">  478</span> </div>
<div class="line"><a id="l00479" name="l00479"></a><span class="lineno">  479</span>    ~message_t() ZMQ_NOTHROW</div>
<div class="line"><a id="l00480" name="l00480"></a><span class="lineno">  480</span>    {</div>
<div class="line"><a id="l00481" name="l00481"></a><span class="lineno">  481</span>        <span class="keywordtype">int</span> rc = zmq_msg_close(&amp;msg);</div>
<div class="line"><a id="l00482" name="l00482"></a><span class="lineno">  482</span>        ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l00483" name="l00483"></a><span class="lineno">  483</span>    }</div>
<div class="line"><a id="l00484" name="l00484"></a><span class="lineno">  484</span> </div>
<div class="line"><a id="l00485" name="l00485"></a><span class="lineno">  485</span>    <span class="keywordtype">void</span> rebuild()</div>
<div class="line"><a id="l00486" name="l00486"></a><span class="lineno">  486</span>    {</div>
<div class="line"><a id="l00487" name="l00487"></a><span class="lineno">  487</span>        <span class="keywordtype">int</span> rc = zmq_msg_close(&amp;msg);</div>
<div class="line"><a id="l00488" name="l00488"></a><span class="lineno">  488</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00489" name="l00489"></a><span class="lineno">  489</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00490" name="l00490"></a><span class="lineno">  490</span>        rc = zmq_msg_init(&amp;msg);</div>
<div class="line"><a id="l00491" name="l00491"></a><span class="lineno">  491</span>        ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l00492" name="l00492"></a><span class="lineno">  492</span>    }</div>
<div class="line"><a id="l00493" name="l00493"></a><span class="lineno">  493</span> </div>
<div class="line"><a id="l00494" name="l00494"></a><span class="lineno">  494</span>    <span class="keywordtype">void</span> rebuild(<span class="keywordtype">size_t</span> size_)</div>
<div class="line"><a id="l00495" name="l00495"></a><span class="lineno">  495</span>    {</div>
<div class="line"><a id="l00496" name="l00496"></a><span class="lineno">  496</span>        <span class="keywordtype">int</span> rc = zmq_msg_close(&amp;msg);</div>
<div class="line"><a id="l00497" name="l00497"></a><span class="lineno">  497</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00498" name="l00498"></a><span class="lineno">  498</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00499" name="l00499"></a><span class="lineno">  499</span>        rc = zmq_msg_init_size(&amp;msg, size_);</div>
<div class="line"><a id="l00500" name="l00500"></a><span class="lineno">  500</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00501" name="l00501"></a><span class="lineno">  501</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00502" name="l00502"></a><span class="lineno">  502</span>    }</div>
<div class="line"><a id="l00503" name="l00503"></a><span class="lineno">  503</span> </div>
<div class="line"><a id="l00504" name="l00504"></a><span class="lineno">  504</span>    <span class="keywordtype">void</span> rebuild(<span class="keyword">const</span> <span class="keywordtype">void</span> *data_, <span class="keywordtype">size_t</span> size_)</div>
<div class="line"><a id="l00505" name="l00505"></a><span class="lineno">  505</span>    {</div>
<div class="line"><a id="l00506" name="l00506"></a><span class="lineno">  506</span>        <span class="keywordtype">int</span> rc = zmq_msg_close(&amp;msg);</div>
<div class="line"><a id="l00507" name="l00507"></a><span class="lineno">  507</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00508" name="l00508"></a><span class="lineno">  508</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00509" name="l00509"></a><span class="lineno">  509</span>        rc = zmq_msg_init_size(&amp;msg, size_);</div>
<div class="line"><a id="l00510" name="l00510"></a><span class="lineno">  510</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00511" name="l00511"></a><span class="lineno">  511</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00512" name="l00512"></a><span class="lineno">  512</span>        memcpy(data(), data_, size_);</div>
<div class="line"><a id="l00513" name="l00513"></a><span class="lineno">  513</span>    }</div>
<div class="line"><a id="l00514" name="l00514"></a><span class="lineno">  514</span> </div>
<div class="line"><a id="l00515" name="l00515"></a><span class="lineno">  515</span>    <span class="keywordtype">void</span> rebuild(<span class="keywordtype">void</span> *data_, <span class="keywordtype">size_t</span> size_, free_fn *ffn_, <span class="keywordtype">void</span> *hint_ = ZMQ_NULLPTR)</div>
<div class="line"><a id="l00516" name="l00516"></a><span class="lineno">  516</span>    {</div>
<div class="line"><a id="l00517" name="l00517"></a><span class="lineno">  517</span>        <span class="keywordtype">int</span> rc = zmq_msg_close(&amp;msg);</div>
<div class="line"><a id="l00518" name="l00518"></a><span class="lineno">  518</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00519" name="l00519"></a><span class="lineno">  519</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00520" name="l00520"></a><span class="lineno">  520</span>        rc = zmq_msg_init_data(&amp;msg, data_, size_, ffn_, hint_);</div>
<div class="line"><a id="l00521" name="l00521"></a><span class="lineno">  521</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00522" name="l00522"></a><span class="lineno">  522</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00523" name="l00523"></a><span class="lineno">  523</span>    }</div>
<div class="line"><a id="l00524" name="l00524"></a><span class="lineno">  524</span> </div>
<div class="line"><a id="l00525" name="l00525"></a><span class="lineno">  525</span>    ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use move taking non-const reference instead&quot;</span>)</div>
<div class="line"><a id="l00526" name="l00526"></a><span class="lineno">  526</span>    <span class="keywordtype">void</span> move(message_t <span class="keyword">const</span> *msg_)</div>
<div class="line"><a id="l00527" name="l00527"></a><span class="lineno">  527</span>    {</div>
<div class="line"><a id="l00528" name="l00528"></a><span class="lineno">  528</span>        <span class="keywordtype">int</span> rc = zmq_msg_move(&amp;msg, <span class="keyword">const_cast&lt;</span>zmq_msg_t *<span class="keyword">&gt;</span>(msg_-&gt;handle()));</div>
<div class="line"><a id="l00529" name="l00529"></a><span class="lineno">  529</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00530" name="l00530"></a><span class="lineno">  530</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00531" name="l00531"></a><span class="lineno">  531</span>    }</div>
<div class="line"><a id="l00532" name="l00532"></a><span class="lineno">  532</span> </div>
<div class="line"><a id="l00533" name="l00533"></a><span class="lineno">  533</span>    <span class="keywordtype">void</span> move(message_t &amp;msg_)</div>
<div class="line"><a id="l00534" name="l00534"></a><span class="lineno">  534</span>    {</div>
<div class="line"><a id="l00535" name="l00535"></a><span class="lineno">  535</span>        <span class="keywordtype">int</span> rc = zmq_msg_move(&amp;msg, msg_.handle());</div>
<div class="line"><a id="l00536" name="l00536"></a><span class="lineno">  536</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00537" name="l00537"></a><span class="lineno">  537</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00538" name="l00538"></a><span class="lineno">  538</span>    }</div>
<div class="line"><a id="l00539" name="l00539"></a><span class="lineno">  539</span> </div>
<div class="line"><a id="l00540" name="l00540"></a><span class="lineno">  540</span>    ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use copy taking non-const reference instead&quot;</span>)</div>
<div class="line"><a id="l00541" name="l00541"></a><span class="lineno">  541</span>    <span class="keywordtype">void</span> copy(message_t <span class="keyword">const</span> *msg_)</div>
<div class="line"><a id="l00542" name="l00542"></a><span class="lineno">  542</span>    {</div>
<div class="line"><a id="l00543" name="l00543"></a><span class="lineno">  543</span>        <span class="keywordtype">int</span> rc = zmq_msg_copy(&amp;msg, <span class="keyword">const_cast&lt;</span>zmq_msg_t *<span class="keyword">&gt;</span>(msg_-&gt;handle()));</div>
<div class="line"><a id="l00544" name="l00544"></a><span class="lineno">  544</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00545" name="l00545"></a><span class="lineno">  545</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00546" name="l00546"></a><span class="lineno">  546</span>    }</div>
<div class="line"><a id="l00547" name="l00547"></a><span class="lineno">  547</span> </div>
<div class="line"><a id="l00548" name="l00548"></a><span class="lineno">  548</span>    <span class="keywordtype">void</span> copy(message_t &amp;msg_)</div>
<div class="line"><a id="l00549" name="l00549"></a><span class="lineno">  549</span>    {</div>
<div class="line"><a id="l00550" name="l00550"></a><span class="lineno">  550</span>        <span class="keywordtype">int</span> rc = zmq_msg_copy(&amp;msg, msg_.handle());</div>
<div class="line"><a id="l00551" name="l00551"></a><span class="lineno">  551</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00552" name="l00552"></a><span class="lineno">  552</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00553" name="l00553"></a><span class="lineno">  553</span>    }</div>
<div class="line"><a id="l00554" name="l00554"></a><span class="lineno">  554</span> </div>
<div class="line"><a id="l00555" name="l00555"></a><span class="lineno">  555</span>    <span class="keywordtype">bool</span> more() <span class="keyword">const</span> ZMQ_NOTHROW</div>
<div class="line"><a id="l00556" name="l00556"></a><span class="lineno">  556</span>    {</div>
<div class="line"><a id="l00557" name="l00557"></a><span class="lineno">  557</span>        <span class="keywordtype">int</span> rc = zmq_msg_more(<span class="keyword">const_cast&lt;</span>zmq_msg_t *<span class="keyword">&gt;</span>(&amp;msg));</div>
<div class="line"><a id="l00558" name="l00558"></a><span class="lineno">  558</span>        <span class="keywordflow">return</span> rc != 0;</div>
<div class="line"><a id="l00559" name="l00559"></a><span class="lineno">  559</span>    }</div>
<div class="line"><a id="l00560" name="l00560"></a><span class="lineno">  560</span> </div>
<div class="line"><a id="l00561" name="l00561"></a><span class="lineno">  561</span>    <span class="keywordtype">void</span> *data() ZMQ_NOTHROW { <span class="keywordflow">return</span> zmq_msg_data(&amp;msg); }</div>
<div class="line"><a id="l00562" name="l00562"></a><span class="lineno">  562</span> </div>
<div class="line"><a id="l00563" name="l00563"></a><span class="lineno">  563</span>    <span class="keyword">const</span> <span class="keywordtype">void</span> *data() <span class="keyword">const</span> ZMQ_NOTHROW</div>
<div class="line"><a id="l00564" name="l00564"></a><span class="lineno">  564</span>    {</div>
<div class="line"><a id="l00565" name="l00565"></a><span class="lineno">  565</span>        <span class="keywordflow">return</span> zmq_msg_data(<span class="keyword">const_cast&lt;</span>zmq_msg_t *<span class="keyword">&gt;</span>(&amp;msg));</div>
<div class="line"><a id="l00566" name="l00566"></a><span class="lineno">  566</span>    }</div>
<div class="line"><a id="l00567" name="l00567"></a><span class="lineno">  567</span> </div>
<div class="line"><a id="l00568" name="l00568"></a><span class="lineno">  568</span>    <span class="keywordtype">size_t</span> size() <span class="keyword">const</span> ZMQ_NOTHROW</div>
<div class="line"><a id="l00569" name="l00569"></a><span class="lineno">  569</span>    {</div>
<div class="line"><a id="l00570" name="l00570"></a><span class="lineno">  570</span>        <span class="keywordflow">return</span> zmq_msg_size(<span class="keyword">const_cast&lt;</span>zmq_msg_t *<span class="keyword">&gt;</span>(&amp;msg));</div>
<div class="line"><a id="l00571" name="l00571"></a><span class="lineno">  571</span>    }</div>
<div class="line"><a id="l00572" name="l00572"></a><span class="lineno">  572</span> </div>
<div class="line"><a id="l00573" name="l00573"></a><span class="lineno">  573</span>    ZMQ_NODISCARD <span class="keywordtype">bool</span> empty() <span class="keyword">const</span> ZMQ_NOTHROW { <span class="keywordflow">return</span> size() == 0u; }</div>
<div class="line"><a id="l00574" name="l00574"></a><span class="lineno">  574</span> </div>
<div class="line"><a id="l00575" name="l00575"></a><span class="lineno">  575</span>    <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt; T *data() ZMQ_NOTHROW { <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>T *<span class="keyword">&gt;</span>(data()); }</div>
<div class="line"><a id="l00576" name="l00576"></a><span class="lineno">  576</span> </div>
<div class="line"><a id="l00577" name="l00577"></a><span class="lineno">  577</span>    <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt; T <span class="keyword">const</span> *data() <span class="keyword">const</span> ZMQ_NOTHROW</div>
<div class="line"><a id="l00578" name="l00578"></a><span class="lineno">  578</span>    {</div>
<div class="line"><a id="l00579" name="l00579"></a><span class="lineno">  579</span>        <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>T <span class="keyword">const </span>*<span class="keyword">&gt;</span>(data());</div>
<div class="line"><a id="l00580" name="l00580"></a><span class="lineno">  580</span>    }</div>
<div class="line"><a id="l00581" name="l00581"></a><span class="lineno">  581</span> </div>
<div class="line"><a id="l00582" name="l00582"></a><span class="lineno">  582</span>    ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.3.0, use operator== instead&quot;</span>)</div>
<div class="line"><a id="l00583" name="l00583"></a><span class="lineno">  583</span>    <span class="keywordtype">bool</span> equal(<span class="keyword">const</span> message_t *other) <span class="keyword">const</span> ZMQ_NOTHROW { <span class="keywordflow">return</span> *<span class="keyword">this</span> == *other; }</div>
<div class="line"><a id="l00584" name="l00584"></a><span class="lineno">  584</span> </div>
<div class="line"><a id="l00585" name="l00585"></a><span class="lineno">  585</span>    <span class="keywordtype">bool</span> operator==(<span class="keyword">const</span> message_t &amp;other) <span class="keyword">const</span> ZMQ_NOTHROW</div>
<div class="line"><a id="l00586" name="l00586"></a><span class="lineno">  586</span>    {</div>
<div class="line"><a id="l00587" name="l00587"></a><span class="lineno">  587</span>        <span class="keyword">const</span> <span class="keywordtype">size_t</span> my_size = size();</div>
<div class="line"><a id="l00588" name="l00588"></a><span class="lineno">  588</span>        <span class="keywordflow">return</span> my_size == other.size() &amp;&amp; 0 == memcmp(data(), other.data(), my_size);</div>
<div class="line"><a id="l00589" name="l00589"></a><span class="lineno">  589</span>    }</div>
<div class="line"><a id="l00590" name="l00590"></a><span class="lineno">  590</span> </div>
<div class="line"><a id="l00591" name="l00591"></a><span class="lineno">  591</span>    <span class="keywordtype">bool</span> operator!=(<span class="keyword">const</span> message_t &amp;other) <span class="keyword">const</span> ZMQ_NOTHROW</div>
<div class="line"><a id="l00592" name="l00592"></a><span class="lineno">  592</span>    {</div>
<div class="line"><a id="l00593" name="l00593"></a><span class="lineno">  593</span>        <span class="keywordflow">return</span> !(*<span class="keyword">this</span> == other);</div>
<div class="line"><a id="l00594" name="l00594"></a><span class="lineno">  594</span>    }</div>
<div class="line"><a id="l00595" name="l00595"></a><span class="lineno">  595</span> </div>
<div class="line"><a id="l00596" name="l00596"></a><span class="lineno">  596</span><span class="preprocessor">#if ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(3, 2, 0)</span></div>
<div class="line"><a id="l00597" name="l00597"></a><span class="lineno">  597</span>    <span class="keywordtype">int</span> get(<span class="keywordtype">int</span> property_)</div>
<div class="line"><a id="l00598" name="l00598"></a><span class="lineno">  598</span>    {</div>
<div class="line"><a id="l00599" name="l00599"></a><span class="lineno">  599</span>        <span class="keywordtype">int</span> value = zmq_msg_get(&amp;msg, property_);</div>
<div class="line"><a id="l00600" name="l00600"></a><span class="lineno">  600</span>        <span class="keywordflow">if</span> (value == -1)</div>
<div class="line"><a id="l00601" name="l00601"></a><span class="lineno">  601</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00602" name="l00602"></a><span class="lineno">  602</span>        <span class="keywordflow">return</span> value;</div>
<div class="line"><a id="l00603" name="l00603"></a><span class="lineno">  603</span>    }</div>
<div class="line"><a id="l00604" name="l00604"></a><span class="lineno">  604</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00605" name="l00605"></a><span class="lineno">  605</span> </div>
<div class="line"><a id="l00606" name="l00606"></a><span class="lineno">  606</span><span class="preprocessor">#if ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(4, 1, 0)</span></div>
<div class="line"><a id="l00607" name="l00607"></a><span class="lineno">  607</span>    <span class="keyword">const</span> <span class="keywordtype">char</span> *gets(<span class="keyword">const</span> <span class="keywordtype">char</span> *property_)</div>
<div class="line"><a id="l00608" name="l00608"></a><span class="lineno">  608</span>    {</div>
<div class="line"><a id="l00609" name="l00609"></a><span class="lineno">  609</span>        <span class="keyword">const</span> <span class="keywordtype">char</span> *value = zmq_msg_gets(&amp;msg, property_);</div>
<div class="line"><a id="l00610" name="l00610"></a><span class="lineno">  610</span>        <span class="keywordflow">if</span> (value == ZMQ_NULLPTR)</div>
<div class="line"><a id="l00611" name="l00611"></a><span class="lineno">  611</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00612" name="l00612"></a><span class="lineno">  612</span>        <span class="keywordflow">return</span> value;</div>
<div class="line"><a id="l00613" name="l00613"></a><span class="lineno">  613</span>    }</div>
<div class="line"><a id="l00614" name="l00614"></a><span class="lineno">  614</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00615" name="l00615"></a><span class="lineno">  615</span> </div>
<div class="line"><a id="l00616" name="l00616"></a><span class="lineno">  616</span><span class="preprocessor">#if defined(ZMQ_BUILD_DRAFT_API) &amp;&amp; ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(4, 2, 0)</span></div>
<div class="line"><a id="l00617" name="l00617"></a><span class="lineno">  617</span>    uint32_t routing_id()<span class="keyword"> const</span></div>
<div class="line"><a id="l00618" name="l00618"></a><span class="lineno">  618</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l00619" name="l00619"></a><span class="lineno">  619</span>        <span class="keywordflow">return</span> zmq_msg_routing_id(<span class="keyword">const_cast&lt;</span>zmq_msg_t *<span class="keyword">&gt;</span>(&amp;msg));</div>
<div class="line"><a id="l00620" name="l00620"></a><span class="lineno">  620</span>    }</div>
<div class="line"><a id="l00621" name="l00621"></a><span class="lineno">  621</span> </div>
<div class="line"><a id="l00622" name="l00622"></a><span class="lineno">  622</span>    <span class="keywordtype">void</span> set_routing_id(uint32_t routing_id)</div>
<div class="line"><a id="l00623" name="l00623"></a><span class="lineno">  623</span>    {</div>
<div class="line"><a id="l00624" name="l00624"></a><span class="lineno">  624</span>        <span class="keywordtype">int</span> rc = zmq_msg_set_routing_id(&amp;msg, routing_id);</div>
<div class="line"><a id="l00625" name="l00625"></a><span class="lineno">  625</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00626" name="l00626"></a><span class="lineno">  626</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00627" name="l00627"></a><span class="lineno">  627</span>    }</div>
<div class="line"><a id="l00628" name="l00628"></a><span class="lineno">  628</span> </div>
<div class="line"><a id="l00629" name="l00629"></a><span class="lineno">  629</span>    <span class="keyword">const</span> <span class="keywordtype">char</span> *group()<span class="keyword"> const</span></div>
<div class="line"><a id="l00630" name="l00630"></a><span class="lineno">  630</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l00631" name="l00631"></a><span class="lineno">  631</span>        <span class="keywordflow">return</span> zmq_msg_group(<span class="keyword">const_cast&lt;</span>zmq_msg_t *<span class="keyword">&gt;</span>(&amp;msg));</div>
<div class="line"><a id="l00632" name="l00632"></a><span class="lineno">  632</span>    }</div>
<div class="line"><a id="l00633" name="l00633"></a><span class="lineno">  633</span> </div>
<div class="line"><a id="l00634" name="l00634"></a><span class="lineno">  634</span>    <span class="keywordtype">void</span> set_group(<span class="keyword">const</span> <span class="keywordtype">char</span> *group)</div>
<div class="line"><a id="l00635" name="l00635"></a><span class="lineno">  635</span>    {</div>
<div class="line"><a id="l00636" name="l00636"></a><span class="lineno">  636</span>        <span class="keywordtype">int</span> rc = zmq_msg_set_group(&amp;msg, group);</div>
<div class="line"><a id="l00637" name="l00637"></a><span class="lineno">  637</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l00638" name="l00638"></a><span class="lineno">  638</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00639" name="l00639"></a><span class="lineno">  639</span>    }</div>
<div class="line"><a id="l00640" name="l00640"></a><span class="lineno">  640</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00641" name="l00641"></a><span class="lineno">  641</span> </div>
<div class="line"><a id="l00642" name="l00642"></a><span class="lineno">  642</span>    <span class="comment">// interpret message content as a string</span></div>
<div class="line"><a id="l00643" name="l00643"></a><span class="lineno">  643</span>    std::string to_string()<span class="keyword"> const</span></div>
<div class="line"><a id="l00644" name="l00644"></a><span class="lineno">  644</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l00645" name="l00645"></a><span class="lineno">  645</span>        <span class="keywordflow">return</span> std::string(<span class="keyword">static_cast&lt;</span><span class="keyword">const </span><span class="keywordtype">char</span> *<span class="keyword">&gt;</span>(data()), size());</div>
<div class="line"><a id="l00646" name="l00646"></a><span class="lineno">  646</span>    }</div>
<div class="line"><a id="l00647" name="l00647"></a><span class="lineno">  647</span><span class="preprocessor">#if CPPZMQ_HAS_STRING_VIEW</span></div>
<div class="line"><a id="l00648" name="l00648"></a><span class="lineno">  648</span>    <span class="comment">// interpret message content as a string</span></div>
<div class="line"><a id="l00649" name="l00649"></a><span class="lineno">  649</span>    std::string_view to_string_view() <span class="keyword">const</span> <span class="keyword">noexcept</span></div>
<div class="line"><a id="l00650" name="l00650"></a><span class="lineno">  650</span>    {</div>
<div class="line"><a id="l00651" name="l00651"></a><span class="lineno">  651</span>        <span class="keywordflow">return</span> std::string_view(<span class="keyword">static_cast&lt;</span><span class="keyword">const </span><span class="keywordtype">char</span> *<span class="keyword">&gt;</span>(data()), size());</div>
<div class="line"><a id="l00652" name="l00652"></a><span class="lineno">  652</span>    }</div>
<div class="line"><a id="l00653" name="l00653"></a><span class="lineno">  653</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00654" name="l00654"></a><span class="lineno">  654</span></div>
<div class="foldopen" id="foldopen00661" data-start="{" data-end="}">
<div class="line"><a id="l00661" name="l00661"></a><span class="lineno"><a class="line" href="classzmq_1_1message__t.html#aed6502a922a73cda7f47d47689617d03">  661</a></span>    std::string <a class="code hl_function" href="classzmq_1_1message__t.html#aed6502a922a73cda7f47d47689617d03">str</a>()<span class="keyword"> const</span></div>
<div class="line"><a id="l00662" name="l00662"></a><span class="lineno">  662</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l00663" name="l00663"></a><span class="lineno">  663</span>        <span class="comment">// Partly mutuated from the same method in zmq::multipart_t</span></div>
<div class="line"><a id="l00664" name="l00664"></a><span class="lineno">  664</span>        std::stringstream os;</div>
<div class="line"><a id="l00665" name="l00665"></a><span class="lineno">  665</span> </div>
<div class="line"><a id="l00666" name="l00666"></a><span class="lineno">  666</span>        <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> *msg_data = this-&gt;data&lt;unsigned char&gt;();</div>
<div class="line"><a id="l00667" name="l00667"></a><span class="lineno">  667</span>        <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> byte;</div>
<div class="line"><a id="l00668" name="l00668"></a><span class="lineno">  668</span>        <span class="keywordtype">size_t</span> size = this-&gt;size();</div>
<div class="line"><a id="l00669" name="l00669"></a><span class="lineno">  669</span>        <span class="keywordtype">int</span> is_ascii[2] = {0, 0};</div>
<div class="line"><a id="l00670" name="l00670"></a><span class="lineno">  670</span> </div>
<div class="line"><a id="l00671" name="l00671"></a><span class="lineno">  671</span>        os &lt;&lt; <span class="stringliteral">&quot;zmq::message_t [size &quot;</span> &lt;&lt; std::dec &lt;&lt; std::setw(3)</div>
<div class="line"><a id="l00672" name="l00672"></a><span class="lineno">  672</span>           &lt;&lt; std::setfill(<span class="charliteral">&#39;0&#39;</span>) &lt;&lt; size &lt;&lt; <span class="stringliteral">&quot;] (&quot;</span>;</div>
<div class="line"><a id="l00673" name="l00673"></a><span class="lineno">  673</span>        <span class="comment">// Totally arbitrary</span></div>
<div class="line"><a id="l00674" name="l00674"></a><span class="lineno">  674</span>        <span class="keywordflow">if</span> (size &gt;= 1000) {</div>
<div class="line"><a id="l00675" name="l00675"></a><span class="lineno">  675</span>            os &lt;&lt; <span class="stringliteral">&quot;... too big to print)&quot;</span>;</div>
<div class="line"><a id="l00676" name="l00676"></a><span class="lineno">  676</span>        } <span class="keywordflow">else</span> {</div>
<div class="line"><a id="l00677" name="l00677"></a><span class="lineno">  677</span>            <span class="keywordflow">while</span> (size--) {</div>
<div class="line"><a id="l00678" name="l00678"></a><span class="lineno">  678</span>                <span class="keywordtype">byte</span> = *msg_data++;</div>
<div class="line"><a id="l00679" name="l00679"></a><span class="lineno">  679</span> </div>
<div class="line"><a id="l00680" name="l00680"></a><span class="lineno">  680</span>                is_ascii[1] = (<span class="keywordtype">byte</span> &gt;= 32 &amp;&amp; <span class="keywordtype">byte</span> &lt; 127);</div>
<div class="line"><a id="l00681" name="l00681"></a><span class="lineno">  681</span>                <span class="keywordflow">if</span> (is_ascii[1] != is_ascii[0])</div>
<div class="line"><a id="l00682" name="l00682"></a><span class="lineno">  682</span>                    os &lt;&lt; <span class="stringliteral">&quot; &quot;</span>; <span class="comment">// Separate text/non text</span></div>
<div class="line"><a id="l00683" name="l00683"></a><span class="lineno">  683</span> </div>
<div class="line"><a id="l00684" name="l00684"></a><span class="lineno">  684</span>                <span class="keywordflow">if</span> (is_ascii[1]) {</div>
<div class="line"><a id="l00685" name="l00685"></a><span class="lineno">  685</span>                    os &lt;&lt; byte;</div>
<div class="line"><a id="l00686" name="l00686"></a><span class="lineno">  686</span>                } <span class="keywordflow">else</span> {</div>
<div class="line"><a id="l00687" name="l00687"></a><span class="lineno">  687</span>                    os &lt;&lt; std::hex &lt;&lt; std::uppercase &lt;&lt; std::setw(2)</div>
<div class="line"><a id="l00688" name="l00688"></a><span class="lineno">  688</span>                       &lt;&lt; std::setfill(<span class="charliteral">&#39;0&#39;</span>) &lt;&lt; <span class="keyword">static_cast&lt;</span><span class="keywordtype">short</span><span class="keyword">&gt;</span>(byte);</div>
<div class="line"><a id="l00689" name="l00689"></a><span class="lineno">  689</span>                }</div>
<div class="line"><a id="l00690" name="l00690"></a><span class="lineno">  690</span>                is_ascii[0] = is_ascii[1];</div>
<div class="line"><a id="l00691" name="l00691"></a><span class="lineno">  691</span>            }</div>
<div class="line"><a id="l00692" name="l00692"></a><span class="lineno">  692</span>            os &lt;&lt; <span class="stringliteral">&quot;)&quot;</span>;</div>
<div class="line"><a id="l00693" name="l00693"></a><span class="lineno">  693</span>        }</div>
<div class="line"><a id="l00694" name="l00694"></a><span class="lineno">  694</span>        <span class="keywordflow">return</span> os.str();</div>
<div class="line"><a id="l00695" name="l00695"></a><span class="lineno">  695</span>    }</div>
</div>
<div class="line"><a id="l00696" name="l00696"></a><span class="lineno">  696</span> </div>
<div class="line"><a id="l00697" name="l00697"></a><span class="lineno">  697</span>    <span class="keywordtype">void</span> swap(<a class="code hl_class" href="classzmq_1_1message__t.html">message_t</a> &amp;other) ZMQ_NOTHROW</div>
<div class="line"><a id="l00698" name="l00698"></a><span class="lineno">  698</span>    {</div>
<div class="line"><a id="l00699" name="l00699"></a><span class="lineno">  699</span>        <span class="comment">// this assumes zmq::msg_t from libzmq is trivially relocatable</span></div>
<div class="line"><a id="l00700" name="l00700"></a><span class="lineno">  700</span>        std::swap(msg, other.msg);</div>
<div class="line"><a id="l00701" name="l00701"></a><span class="lineno">  701</span>    }</div>
<div class="line"><a id="l00702" name="l00702"></a><span class="lineno">  702</span> </div>
<div class="line"><a id="l00703" name="l00703"></a><span class="lineno">  703</span>    ZMQ_NODISCARD zmq_msg_t *handle() ZMQ_NOTHROW { <span class="keywordflow">return</span> &amp;msg; }</div>
<div class="line"><a id="l00704" name="l00704"></a><span class="lineno">  704</span>    ZMQ_NODISCARD <span class="keyword">const</span> zmq_msg_t *handle() const ZMQ_NOTHROW { <span class="keywordflow">return</span> &amp;msg; }</div>
<div class="line"><a id="l00705" name="l00705"></a><span class="lineno">  705</span> </div>
<div class="line"><a id="l00706" name="l00706"></a><span class="lineno">  706</span>  <span class="keyword">private</span>:</div>
<div class="line"><a id="l00707" name="l00707"></a><span class="lineno">  707</span>    <span class="comment">//  The underlying message</span></div>
<div class="line"><a id="l00708" name="l00708"></a><span class="lineno">  708</span>    zmq_msg_t msg;</div>
<div class="line"><a id="l00709" name="l00709"></a><span class="lineno">  709</span> </div>
<div class="line"><a id="l00710" name="l00710"></a><span class="lineno">  710</span>    <span class="comment">//  Disable implicit message copying, so that users won&#39;t use shared</span></div>
<div class="line"><a id="l00711" name="l00711"></a><span class="lineno">  711</span>    <span class="comment">//  messages (less efficient) without being aware of the fact.</span></div>
<div class="line"><a id="l00712" name="l00712"></a><span class="lineno">  712</span>    message_t(<span class="keyword">const</span> message_t &amp;) ZMQ_DELETED_FUNCTION;</div>
<div class="line"><a id="l00713" name="l00713"></a><span class="lineno">  713</span>    <span class="keywordtype">void</span> operator=(<span class="keyword">const</span> message_t &amp;) ZMQ_DELETED_FUNCTION;</div>
<div class="line"><a id="l00714" name="l00714"></a><span class="lineno">  714</span>};</div>
</div>
<div class="line"><a id="l00715" name="l00715"></a><span class="lineno">  715</span> </div>
<div class="line"><a id="l00716" name="l00716"></a><span class="lineno">  716</span><span class="keyword">inline</span> <span class="keywordtype">void</span> swap(<a class="code hl_class" href="classzmq_1_1message__t.html">message_t</a> &amp;a, <a class="code hl_class" href="classzmq_1_1message__t.html">message_t</a> &amp;b) ZMQ_NOTHROW</div>
<div class="line"><a id="l00717" name="l00717"></a><span class="lineno">  717</span>{</div>
<div class="line"><a id="l00718" name="l00718"></a><span class="lineno">  718</span>    a.swap(b);</div>
<div class="line"><a id="l00719" name="l00719"></a><span class="lineno">  719</span>}</div>
<div class="line"><a id="l00720" name="l00720"></a><span class="lineno">  720</span> </div>
<div class="line"><a id="l00721" name="l00721"></a><span class="lineno">  721</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l00722" name="l00722"></a><span class="lineno">  722</span><span class="keyword">enum class</span> ctxopt</div>
<div class="line"><a id="l00723" name="l00723"></a><span class="lineno">  723</span>{</div>
<div class="line"><a id="l00724" name="l00724"></a><span class="lineno">  724</span><span class="preprocessor">#ifdef ZMQ_BLOCKY</span></div>
<div class="line"><a id="l00725" name="l00725"></a><span class="lineno">  725</span>    blocky = ZMQ_BLOCKY,</div>
<div class="line"><a id="l00726" name="l00726"></a><span class="lineno">  726</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00727" name="l00727"></a><span class="lineno">  727</span><span class="preprocessor">#ifdef ZMQ_IO_THREADS</span></div>
<div class="line"><a id="l00728" name="l00728"></a><span class="lineno">  728</span>    io_threads = ZMQ_IO_THREADS,</div>
<div class="line"><a id="l00729" name="l00729"></a><span class="lineno">  729</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00730" name="l00730"></a><span class="lineno">  730</span><span class="preprocessor">#ifdef ZMQ_THREAD_SCHED_POLICY</span></div>
<div class="line"><a id="l00731" name="l00731"></a><span class="lineno">  731</span>    thread_sched_policy = ZMQ_THREAD_SCHED_POLICY,</div>
<div class="line"><a id="l00732" name="l00732"></a><span class="lineno">  732</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00733" name="l00733"></a><span class="lineno">  733</span><span class="preprocessor">#ifdef ZMQ_THREAD_PRIORITY</span></div>
<div class="line"><a id="l00734" name="l00734"></a><span class="lineno">  734</span>    thread_priority = ZMQ_THREAD_PRIORITY,</div>
<div class="line"><a id="l00735" name="l00735"></a><span class="lineno">  735</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00736" name="l00736"></a><span class="lineno">  736</span><span class="preprocessor">#ifdef ZMQ_THREAD_AFFINITY_CPU_ADD</span></div>
<div class="line"><a id="l00737" name="l00737"></a><span class="lineno">  737</span>    thread_affinity_cpu_add = ZMQ_THREAD_AFFINITY_CPU_ADD,</div>
<div class="line"><a id="l00738" name="l00738"></a><span class="lineno">  738</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00739" name="l00739"></a><span class="lineno">  739</span><span class="preprocessor">#ifdef ZMQ_THREAD_AFFINITY_CPU_REMOVE</span></div>
<div class="line"><a id="l00740" name="l00740"></a><span class="lineno">  740</span>    thread_affinity_cpu_remove = ZMQ_THREAD_AFFINITY_CPU_REMOVE,</div>
<div class="line"><a id="l00741" name="l00741"></a><span class="lineno">  741</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00742" name="l00742"></a><span class="lineno">  742</span><span class="preprocessor">#ifdef ZMQ_THREAD_NAME_PREFIX</span></div>
<div class="line"><a id="l00743" name="l00743"></a><span class="lineno">  743</span>    thread_name_prefix = ZMQ_THREAD_NAME_PREFIX,</div>
<div class="line"><a id="l00744" name="l00744"></a><span class="lineno">  744</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00745" name="l00745"></a><span class="lineno">  745</span><span class="preprocessor">#ifdef ZMQ_MAX_MSGSZ</span></div>
<div class="line"><a id="l00746" name="l00746"></a><span class="lineno">  746</span>    max_msgsz = ZMQ_MAX_MSGSZ,</div>
<div class="line"><a id="l00747" name="l00747"></a><span class="lineno">  747</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00748" name="l00748"></a><span class="lineno">  748</span><span class="preprocessor">#ifdef ZMQ_ZERO_COPY_RECV</span></div>
<div class="line"><a id="l00749" name="l00749"></a><span class="lineno">  749</span>    zero_copy_recv = ZMQ_ZERO_COPY_RECV,</div>
<div class="line"><a id="l00750" name="l00750"></a><span class="lineno">  750</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00751" name="l00751"></a><span class="lineno">  751</span><span class="preprocessor">#ifdef ZMQ_MAX_SOCKETS</span></div>
<div class="line"><a id="l00752" name="l00752"></a><span class="lineno">  752</span>    max_sockets = ZMQ_MAX_SOCKETS,</div>
<div class="line"><a id="l00753" name="l00753"></a><span class="lineno">  753</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00754" name="l00754"></a><span class="lineno">  754</span><span class="preprocessor">#ifdef ZMQ_SOCKET_LIMIT</span></div>
<div class="line"><a id="l00755" name="l00755"></a><span class="lineno">  755</span>    socket_limit = ZMQ_SOCKET_LIMIT,</div>
<div class="line"><a id="l00756" name="l00756"></a><span class="lineno">  756</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00757" name="l00757"></a><span class="lineno">  757</span><span class="preprocessor">#ifdef ZMQ_IPV6</span></div>
<div class="line"><a id="l00758" name="l00758"></a><span class="lineno">  758</span>    ipv6 = ZMQ_IPV6,</div>
<div class="line"><a id="l00759" name="l00759"></a><span class="lineno">  759</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00760" name="l00760"></a><span class="lineno">  760</span><span class="preprocessor">#ifdef ZMQ_MSG_T_SIZE</span></div>
<div class="line"><a id="l00761" name="l00761"></a><span class="lineno">  761</span>    msg_t_size = ZMQ_MSG_T_SIZE</div>
<div class="line"><a id="l00762" name="l00762"></a><span class="lineno">  762</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00763" name="l00763"></a><span class="lineno">  763</span>};</div>
<div class="line"><a id="l00764" name="l00764"></a><span class="lineno">  764</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00765" name="l00765"></a><span class="lineno">  765</span> </div>
<div class="foldopen" id="foldopen00766" data-start="{" data-end="};">
<div class="line"><a id="l00766" name="l00766"></a><span class="lineno"><a class="line" href="classzmq_1_1context__t.html">  766</a></span><span class="keyword">class </span>context_t</div>
<div class="line"><a id="l00767" name="l00767"></a><span class="lineno">  767</span>{</div>
<div class="line"><a id="l00768" name="l00768"></a><span class="lineno">  768</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l00769" name="l00769"></a><span class="lineno">  769</span>    context_t()</div>
<div class="line"><a id="l00770" name="l00770"></a><span class="lineno">  770</span>    {</div>
<div class="line"><a id="l00771" name="l00771"></a><span class="lineno">  771</span>        ptr = zmq_ctx_new();</div>
<div class="line"><a id="l00772" name="l00772"></a><span class="lineno">  772</span>        <span class="keywordflow">if</span> (ptr == ZMQ_NULLPTR)</div>
<div class="line"><a id="l00773" name="l00773"></a><span class="lineno">  773</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00774" name="l00774"></a><span class="lineno">  774</span>    }</div>
<div class="line"><a id="l00775" name="l00775"></a><span class="lineno">  775</span> </div>
<div class="line"><a id="l00776" name="l00776"></a><span class="lineno">  776</span> </div>
<div class="line"><a id="l00777" name="l00777"></a><span class="lineno">  777</span>    <span class="keyword">explicit</span> context_t(<span class="keywordtype">int</span> io_threads_, <span class="keywordtype">int</span> max_sockets_ = ZMQ_MAX_SOCKETS_DFLT)</div>
<div class="line"><a id="l00778" name="l00778"></a><span class="lineno">  778</span>    {</div>
<div class="line"><a id="l00779" name="l00779"></a><span class="lineno">  779</span>        ptr = zmq_ctx_new();</div>
<div class="line"><a id="l00780" name="l00780"></a><span class="lineno">  780</span>        <span class="keywordflow">if</span> (ptr == ZMQ_NULLPTR)</div>
<div class="line"><a id="l00781" name="l00781"></a><span class="lineno">  781</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00782" name="l00782"></a><span class="lineno">  782</span> </div>
<div class="line"><a id="l00783" name="l00783"></a><span class="lineno">  783</span>        <span class="keywordtype">int</span> rc = zmq_ctx_set(ptr, ZMQ_IO_THREADS, io_threads_);</div>
<div class="line"><a id="l00784" name="l00784"></a><span class="lineno">  784</span>        ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l00785" name="l00785"></a><span class="lineno">  785</span> </div>
<div class="line"><a id="l00786" name="l00786"></a><span class="lineno">  786</span>        rc = zmq_ctx_set(ptr, ZMQ_MAX_SOCKETS, max_sockets_);</div>
<div class="line"><a id="l00787" name="l00787"></a><span class="lineno">  787</span>        ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l00788" name="l00788"></a><span class="lineno">  788</span>    }</div>
<div class="line"><a id="l00789" name="l00789"></a><span class="lineno">  789</span> </div>
<div class="line"><a id="l00790" name="l00790"></a><span class="lineno">  790</span><span class="preprocessor">#ifdef ZMQ_HAS_RVALUE_REFS</span></div>
<div class="line"><a id="l00791" name="l00791"></a><span class="lineno">  791</span>    context_t(context_t &amp;&amp;rhs) ZMQ_NOTHROW : ptr(rhs.ptr) { rhs.ptr = ZMQ_NULLPTR; }</div>
<div class="line"><a id="l00792" name="l00792"></a><span class="lineno">  792</span>    context_t &amp;operator=(context_t &amp;&amp;rhs) ZMQ_NOTHROW</div>
<div class="line"><a id="l00793" name="l00793"></a><span class="lineno">  793</span>    {</div>
<div class="line"><a id="l00794" name="l00794"></a><span class="lineno">  794</span>        close();</div>
<div class="line"><a id="l00795" name="l00795"></a><span class="lineno">  795</span>        std::swap(ptr, rhs.ptr);</div>
<div class="line"><a id="l00796" name="l00796"></a><span class="lineno">  796</span>        <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a id="l00797" name="l00797"></a><span class="lineno">  797</span>    }</div>
<div class="line"><a id="l00798" name="l00798"></a><span class="lineno">  798</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00799" name="l00799"></a><span class="lineno">  799</span> </div>
<div class="line"><a id="l00800" name="l00800"></a><span class="lineno">  800</span>    ~context_t() ZMQ_NOTHROW { close(); }</div>
<div class="line"><a id="l00801" name="l00801"></a><span class="lineno">  801</span> </div>
<div class="line"><a id="l00802" name="l00802"></a><span class="lineno">  802</span>    ZMQ_CPP11_DEPRECATED(<span class="stringliteral">&quot;from 4.7.0, use set taking zmq::ctxopt instead&quot;</span>)</div>
<div class="line"><a id="l00803" name="l00803"></a><span class="lineno">  803</span>    <span class="keywordtype">int</span> setctxopt(<span class="keywordtype">int</span> option_, <span class="keywordtype">int</span> optval_)</div>
<div class="line"><a id="l00804" name="l00804"></a><span class="lineno">  804</span>    {</div>
<div class="line"><a id="l00805" name="l00805"></a><span class="lineno">  805</span>        <span class="keywordtype">int</span> rc = zmq_ctx_set(ptr, option_, optval_);</div>
<div class="line"><a id="l00806" name="l00806"></a><span class="lineno">  806</span>        ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l00807" name="l00807"></a><span class="lineno">  807</span>        <span class="keywordflow">return</span> rc;</div>
<div class="line"><a id="l00808" name="l00808"></a><span class="lineno">  808</span>    }</div>
<div class="line"><a id="l00809" name="l00809"></a><span class="lineno">  809</span> </div>
<div class="line"><a id="l00810" name="l00810"></a><span class="lineno">  810</span>    ZMQ_CPP11_DEPRECATED(<span class="stringliteral">&quot;from 4.7.0, use get taking zmq::ctxopt instead&quot;</span>)</div>
<div class="line"><a id="l00811" name="l00811"></a><span class="lineno">  811</span>    <span class="keywordtype">int</span> getctxopt(<span class="keywordtype">int</span> option_) { <span class="keywordflow">return</span> zmq_ctx_get(ptr, option_); }</div>
<div class="line"><a id="l00812" name="l00812"></a><span class="lineno">  812</span> </div>
<div class="line"><a id="l00813" name="l00813"></a><span class="lineno">  813</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l00814" name="l00814"></a><span class="lineno">  814</span>    <span class="keywordtype">void</span> set(ctxopt option, <span class="keywordtype">int</span> optval)</div>
<div class="line"><a id="l00815" name="l00815"></a><span class="lineno">  815</span>    {</div>
<div class="line"><a id="l00816" name="l00816"></a><span class="lineno">  816</span>        <span class="keywordtype">int</span> rc = zmq_ctx_set(ptr, <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(option), optval);</div>
<div class="line"><a id="l00817" name="l00817"></a><span class="lineno">  817</span>        <span class="keywordflow">if</span> (rc == -1)</div>
<div class="line"><a id="l00818" name="l00818"></a><span class="lineno">  818</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00819" name="l00819"></a><span class="lineno">  819</span>    }</div>
<div class="line"><a id="l00820" name="l00820"></a><span class="lineno">  820</span> </div>
<div class="line"><a id="l00821" name="l00821"></a><span class="lineno">  821</span>    ZMQ_NODISCARD <span class="keywordtype">int</span> get(ctxopt option)</div>
<div class="line"><a id="l00822" name="l00822"></a><span class="lineno">  822</span>    {</div>
<div class="line"><a id="l00823" name="l00823"></a><span class="lineno">  823</span>        <span class="keywordtype">int</span> rc = zmq_ctx_get(ptr, <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(option));</div>
<div class="line"><a id="l00824" name="l00824"></a><span class="lineno">  824</span>        <span class="comment">// some options have a default value of -1</span></div>
<div class="line"><a id="l00825" name="l00825"></a><span class="lineno">  825</span>        <span class="comment">// which is unfortunate, and may result in errors</span></div>
<div class="line"><a id="l00826" name="l00826"></a><span class="lineno">  826</span>        <span class="comment">// that don&#39;t make sense</span></div>
<div class="line"><a id="l00827" name="l00827"></a><span class="lineno">  827</span>        <span class="keywordflow">if</span> (rc == -1)</div>
<div class="line"><a id="l00828" name="l00828"></a><span class="lineno">  828</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l00829" name="l00829"></a><span class="lineno">  829</span>        <span class="keywordflow">return</span> rc;</div>
<div class="line"><a id="l00830" name="l00830"></a><span class="lineno">  830</span>    }</div>
<div class="line"><a id="l00831" name="l00831"></a><span class="lineno">  831</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00832" name="l00832"></a><span class="lineno">  832</span> </div>
<div class="line"><a id="l00833" name="l00833"></a><span class="lineno">  833</span>    <span class="comment">// Terminates context (see also shutdown()).</span></div>
<div class="line"><a id="l00834" name="l00834"></a><span class="lineno">  834</span>    <span class="keywordtype">void</span> close() ZMQ_NOTHROW</div>
<div class="line"><a id="l00835" name="l00835"></a><span class="lineno">  835</span>    {</div>
<div class="line"><a id="l00836" name="l00836"></a><span class="lineno">  836</span>        <span class="keywordflow">if</span> (ptr == ZMQ_NULLPTR)</div>
<div class="line"><a id="l00837" name="l00837"></a><span class="lineno">  837</span>            <span class="keywordflow">return</span>;</div>
<div class="line"><a id="l00838" name="l00838"></a><span class="lineno">  838</span> </div>
<div class="line"><a id="l00839" name="l00839"></a><span class="lineno">  839</span>        <span class="keywordtype">int</span> rc;</div>
<div class="line"><a id="l00840" name="l00840"></a><span class="lineno">  840</span>        <span class="keywordflow">do</span> {</div>
<div class="line"><a id="l00841" name="l00841"></a><span class="lineno">  841</span>            rc = zmq_ctx_destroy(ptr);</div>
<div class="line"><a id="l00842" name="l00842"></a><span class="lineno">  842</span>        } <span class="keywordflow">while</span> (rc == -1 &amp;&amp; errno == EINTR);</div>
<div class="line"><a id="l00843" name="l00843"></a><span class="lineno">  843</span> </div>
<div class="line"><a id="l00844" name="l00844"></a><span class="lineno">  844</span>        ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l00845" name="l00845"></a><span class="lineno">  845</span>        ptr = ZMQ_NULLPTR;</div>
<div class="line"><a id="l00846" name="l00846"></a><span class="lineno">  846</span>    }</div>
<div class="line"><a id="l00847" name="l00847"></a><span class="lineno">  847</span> </div>
<div class="line"><a id="l00848" name="l00848"></a><span class="lineno">  848</span>    <span class="comment">// Shutdown context in preparation for termination (close()).</span></div>
<div class="line"><a id="l00849" name="l00849"></a><span class="lineno">  849</span>    <span class="comment">// Causes all blocking socket operations and any further</span></div>
<div class="line"><a id="l00850" name="l00850"></a><span class="lineno">  850</span>    <span class="comment">// socket operations to return with ETERM.</span></div>
<div class="line"><a id="l00851" name="l00851"></a><span class="lineno">  851</span>    <span class="keywordtype">void</span> shutdown() ZMQ_NOTHROW</div>
<div class="line"><a id="l00852" name="l00852"></a><span class="lineno">  852</span>    {</div>
<div class="line"><a id="l00853" name="l00853"></a><span class="lineno">  853</span>        <span class="keywordflow">if</span> (ptr == ZMQ_NULLPTR)</div>
<div class="line"><a id="l00854" name="l00854"></a><span class="lineno">  854</span>            <span class="keywordflow">return</span>;</div>
<div class="line"><a id="l00855" name="l00855"></a><span class="lineno">  855</span>        <span class="keywordtype">int</span> rc = zmq_ctx_shutdown(ptr);</div>
<div class="line"><a id="l00856" name="l00856"></a><span class="lineno">  856</span>        ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l00857" name="l00857"></a><span class="lineno">  857</span>    }</div>
<div class="line"><a id="l00858" name="l00858"></a><span class="lineno">  858</span> </div>
<div class="line"><a id="l00859" name="l00859"></a><span class="lineno">  859</span>    <span class="comment">//  Be careful with this, it&#39;s probably only useful for</span></div>
<div class="line"><a id="l00860" name="l00860"></a><span class="lineno">  860</span>    <span class="comment">//  using the C api together with an existing C++ api.</span></div>
<div class="line"><a id="l00861" name="l00861"></a><span class="lineno">  861</span>    <span class="comment">//  Normally you should never need to use this.</span></div>
<div class="line"><a id="l00862" name="l00862"></a><span class="lineno">  862</span>    ZMQ_EXPLICIT <span class="keyword">operator</span> <span class="keywordtype">void</span> *() ZMQ_NOTHROW { <span class="keywordflow">return</span> ptr; }</div>
<div class="line"><a id="l00863" name="l00863"></a><span class="lineno">  863</span> </div>
<div class="line"><a id="l00864" name="l00864"></a><span class="lineno">  864</span>    ZMQ_EXPLICIT <span class="keyword">operator</span> <span class="keywordtype">void</span> <span class="keyword">const</span> *() <span class="keyword">const</span> ZMQ_NOTHROW { <span class="keywordflow">return</span> ptr; }</div>
<div class="line"><a id="l00865" name="l00865"></a><span class="lineno">  865</span> </div>
<div class="line"><a id="l00866" name="l00866"></a><span class="lineno">  866</span>    ZMQ_NODISCARD <span class="keywordtype">void</span> *handle() ZMQ_NOTHROW { <span class="keywordflow">return</span> ptr; }</div>
<div class="line"><a id="l00867" name="l00867"></a><span class="lineno">  867</span> </div>
<div class="line"><a id="l00868" name="l00868"></a><span class="lineno">  868</span>    ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.7.0, use handle() != nullptr instead&quot;</span>)</div>
<div class="line"><a id="l00869" name="l00869"></a><span class="lineno">  869</span>    <span class="keyword">operator</span> bool() <span class="keyword">const</span> ZMQ_NOTHROW { <span class="keywordflow">return</span> ptr != ZMQ_NULLPTR; }</div>
<div class="line"><a id="l00870" name="l00870"></a><span class="lineno">  870</span> </div>
<div class="line"><a id="l00871" name="l00871"></a><span class="lineno">  871</span>    <span class="keywordtype">void</span> swap(context_t &amp;other) ZMQ_NOTHROW { std::swap(ptr, other.ptr); }</div>
<div class="line"><a id="l00872" name="l00872"></a><span class="lineno">  872</span> </div>
<div class="line"><a id="l00873" name="l00873"></a><span class="lineno">  873</span>  <span class="keyword">private</span>:</div>
<div class="line"><a id="l00874" name="l00874"></a><span class="lineno">  874</span>    <span class="keywordtype">void</span> *ptr;</div>
<div class="line"><a id="l00875" name="l00875"></a><span class="lineno">  875</span> </div>
<div class="line"><a id="l00876" name="l00876"></a><span class="lineno">  876</span>    context_t(<span class="keyword">const</span> context_t &amp;) ZMQ_DELETED_FUNCTION;</div>
<div class="line"><a id="l00877" name="l00877"></a><span class="lineno">  877</span>    <span class="keywordtype">void</span> operator=(<span class="keyword">const</span> context_t &amp;) ZMQ_DELETED_FUNCTION;</div>
<div class="line"><a id="l00878" name="l00878"></a><span class="lineno">  878</span>};</div>
</div>
<div class="line"><a id="l00879" name="l00879"></a><span class="lineno">  879</span> </div>
<div class="line"><a id="l00880" name="l00880"></a><span class="lineno">  880</span><span class="keyword">inline</span> <span class="keywordtype">void</span> swap(<a class="code hl_class" href="classzmq_1_1context__t.html">context_t</a> &amp;a, <a class="code hl_class" href="classzmq_1_1context__t.html">context_t</a> &amp;b) ZMQ_NOTHROW</div>
<div class="line"><a id="l00881" name="l00881"></a><span class="lineno">  881</span>{</div>
<div class="line"><a id="l00882" name="l00882"></a><span class="lineno">  882</span>    a.swap(b);</div>
<div class="line"><a id="l00883" name="l00883"></a><span class="lineno">  883</span>}</div>
<div class="line"><a id="l00884" name="l00884"></a><span class="lineno">  884</span> </div>
<div class="line"><a id="l00885" name="l00885"></a><span class="lineno">  885</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l00886" name="l00886"></a><span class="lineno">  886</span> </div>
<div class="line"><a id="l00887" name="l00887"></a><span class="lineno">  887</span><span class="keyword">struct </span>recv_buffer_size</div>
<div class="line"><a id="l00888" name="l00888"></a><span class="lineno">  888</span>{</div>
<div class="line"><a id="l00889" name="l00889"></a><span class="lineno">  889</span>    <span class="keywordtype">size_t</span> size;             <span class="comment">// number of bytes written to buffer</span></div>
<div class="line"><a id="l00890" name="l00890"></a><span class="lineno">  890</span>    <span class="keywordtype">size_t</span> untruncated_size; <span class="comment">// untruncated message size in bytes</span></div>
<div class="line"><a id="l00891" name="l00891"></a><span class="lineno">  891</span> </div>
<div class="line"><a id="l00892" name="l00892"></a><span class="lineno">  892</span>    ZMQ_NODISCARD <span class="keywordtype">bool</span> truncated() const noexcept</div>
<div class="line"><a id="l00893" name="l00893"></a><span class="lineno">  893</span>    {</div>
<div class="line"><a id="l00894" name="l00894"></a><span class="lineno">  894</span>        <span class="keywordflow">return</span> size != untruncated_size;</div>
<div class="line"><a id="l00895" name="l00895"></a><span class="lineno">  895</span>    }</div>
<div class="line"><a id="l00896" name="l00896"></a><span class="lineno">  896</span>};</div>
<div class="line"><a id="l00897" name="l00897"></a><span class="lineno">  897</span> </div>
<div class="line"><a id="l00898" name="l00898"></a><span class="lineno">  898</span><span class="preprocessor">#if CPPZMQ_HAS_OPTIONAL</span></div>
<div class="line"><a id="l00899" name="l00899"></a><span class="lineno">  899</span> </div>
<div class="line"><a id="l00900" name="l00900"></a><span class="lineno">  900</span><span class="keyword">using </span>send_result_t = std::optional&lt;size_t&gt;;</div>
<div class="line"><a id="l00901" name="l00901"></a><span class="lineno">  901</span><span class="keyword">using </span>recv_result_t = std::optional&lt;size_t&gt;;</div>
<div class="line"><a id="l00902" name="l00902"></a><span class="lineno">  902</span><span class="keyword">using </span>recv_buffer_result_t = std::optional&lt;recv_buffer_size&gt;;</div>
<div class="line"><a id="l00903" name="l00903"></a><span class="lineno">  903</span> </div>
<div class="line"><a id="l00904" name="l00904"></a><span class="lineno">  904</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l00905" name="l00905"></a><span class="lineno">  905</span> </div>
<div class="line"><a id="l00906" name="l00906"></a><span class="lineno">  906</span><span class="keyword">namespace </span>detail</div>
<div class="line"><a id="l00907" name="l00907"></a><span class="lineno">  907</span>{</div>
<div class="line"><a id="l00908" name="l00908"></a><span class="lineno">  908</span><span class="comment">// A C++11 type emulating the most basic</span></div>
<div class="line"><a id="l00909" name="l00909"></a><span class="lineno">  909</span><span class="comment">// operations of std::optional for trivial types</span></div>
<div class="line"><a id="l00910" name="l00910"></a><span class="lineno">  910</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">class </span>trivial_optional</div>
<div class="line"><a id="l00911" name="l00911"></a><span class="lineno">  911</span>{</div>
<div class="line"><a id="l00912" name="l00912"></a><span class="lineno">  912</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l00913" name="l00913"></a><span class="lineno">  913</span>    <span class="keyword">static_assert</span>(std::is_trivial&lt;T&gt;::value, <span class="stringliteral">&quot;T must be trivial&quot;</span>);</div>
<div class="line"><a id="l00914" name="l00914"></a><span class="lineno">  914</span>    <span class="keyword">using </span>value_type = T;</div>
<div class="line"><a id="l00915" name="l00915"></a><span class="lineno">  915</span> </div>
<div class="line"><a id="l00916" name="l00916"></a><span class="lineno">  916</span>    trivial_optional() = <span class="keywordflow">default</span>;</div>
<div class="line"><a id="l00917" name="l00917"></a><span class="lineno">  917</span>    trivial_optional(T value) noexcept : _value(value), _has_value(<span class="keyword">true</span>) {}</div>
<div class="line"><a id="l00918" name="l00918"></a><span class="lineno">  918</span> </div>
<div class="line"><a id="l00919" name="l00919"></a><span class="lineno">  919</span>    <span class="keyword">const</span> T *operator-&gt;() const noexcept</div>
<div class="line"><a id="l00920" name="l00920"></a><span class="lineno">  920</span>    {</div>
<div class="line"><a id="l00921" name="l00921"></a><span class="lineno">  921</span>        assert(_has_value);</div>
<div class="line"><a id="l00922" name="l00922"></a><span class="lineno">  922</span>        <span class="keywordflow">return</span> &amp;_value;</div>
<div class="line"><a id="l00923" name="l00923"></a><span class="lineno">  923</span>    }</div>
<div class="line"><a id="l00924" name="l00924"></a><span class="lineno">  924</span>    T *operator-&gt;() noexcept</div>
<div class="line"><a id="l00925" name="l00925"></a><span class="lineno">  925</span>    {</div>
<div class="line"><a id="l00926" name="l00926"></a><span class="lineno">  926</span>        assert(_has_value);</div>
<div class="line"><a id="l00927" name="l00927"></a><span class="lineno">  927</span>        <span class="keywordflow">return</span> &amp;_value;</div>
<div class="line"><a id="l00928" name="l00928"></a><span class="lineno">  928</span>    }</div>
<div class="line"><a id="l00929" name="l00929"></a><span class="lineno">  929</span> </div>
<div class="line"><a id="l00930" name="l00930"></a><span class="lineno">  930</span>    <span class="keyword">const</span> T &amp;operator*() const noexcept</div>
<div class="line"><a id="l00931" name="l00931"></a><span class="lineno">  931</span>    {</div>
<div class="line"><a id="l00932" name="l00932"></a><span class="lineno">  932</span>        assert(_has_value);</div>
<div class="line"><a id="l00933" name="l00933"></a><span class="lineno">  933</span>        <span class="keywordflow">return</span> _value;</div>
<div class="line"><a id="l00934" name="l00934"></a><span class="lineno">  934</span>    }</div>
<div class="line"><a id="l00935" name="l00935"></a><span class="lineno">  935</span>    T &amp;operator*() noexcept</div>
<div class="line"><a id="l00936" name="l00936"></a><span class="lineno">  936</span>    {</div>
<div class="line"><a id="l00937" name="l00937"></a><span class="lineno">  937</span>        assert(_has_value);</div>
<div class="line"><a id="l00938" name="l00938"></a><span class="lineno">  938</span>        <span class="keywordflow">return</span> _value;</div>
<div class="line"><a id="l00939" name="l00939"></a><span class="lineno">  939</span>    }</div>
<div class="line"><a id="l00940" name="l00940"></a><span class="lineno">  940</span> </div>
<div class="line"><a id="l00941" name="l00941"></a><span class="lineno">  941</span>    T &amp;value()</div>
<div class="line"><a id="l00942" name="l00942"></a><span class="lineno">  942</span>    {</div>
<div class="line"><a id="l00943" name="l00943"></a><span class="lineno">  943</span>        <span class="keywordflow">if</span> (!_has_value)</div>
<div class="line"><a id="l00944" name="l00944"></a><span class="lineno">  944</span>            <span class="keywordflow">throw</span> std::exception();</div>
<div class="line"><a id="l00945" name="l00945"></a><span class="lineno">  945</span>        <span class="keywordflow">return</span> _value;</div>
<div class="line"><a id="l00946" name="l00946"></a><span class="lineno">  946</span>    }</div>
<div class="line"><a id="l00947" name="l00947"></a><span class="lineno">  947</span>    <span class="keyword">const</span> T &amp;value()<span class="keyword"> const</span></div>
<div class="line"><a id="l00948" name="l00948"></a><span class="lineno">  948</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l00949" name="l00949"></a><span class="lineno">  949</span>        <span class="keywordflow">if</span> (!_has_value)</div>
<div class="line"><a id="l00950" name="l00950"></a><span class="lineno">  950</span>            <span class="keywordflow">throw</span> std::exception();</div>
<div class="line"><a id="l00951" name="l00951"></a><span class="lineno">  951</span>        <span class="keywordflow">return</span> _value;</div>
<div class="line"><a id="l00952" name="l00952"></a><span class="lineno">  952</span>    }</div>
<div class="line"><a id="l00953" name="l00953"></a><span class="lineno">  953</span> </div>
<div class="line"><a id="l00954" name="l00954"></a><span class="lineno">  954</span>    <span class="keyword">explicit</span> <span class="keyword">operator</span> bool() const noexcept { <span class="keywordflow">return</span> _has_value; }</div>
<div class="line"><a id="l00955" name="l00955"></a><span class="lineno">  955</span>    <span class="keywordtype">bool</span> has_value() const noexcept { <span class="keywordflow">return</span> _has_value; }</div>
<div class="line"><a id="l00956" name="l00956"></a><span class="lineno">  956</span> </div>
<div class="line"><a id="l00957" name="l00957"></a><span class="lineno">  957</span>  <span class="keyword">private</span>:</div>
<div class="line"><a id="l00958" name="l00958"></a><span class="lineno">  958</span>    T _value{};</div>
<div class="line"><a id="l00959" name="l00959"></a><span class="lineno">  959</span>    <span class="keywordtype">bool</span> _has_value{<span class="keyword">false</span>};</div>
<div class="line"><a id="l00960" name="l00960"></a><span class="lineno">  960</span>};</div>
<div class="line"><a id="l00961" name="l00961"></a><span class="lineno">  961</span>} <span class="comment">// namespace detail</span></div>
<div class="line"><a id="l00962" name="l00962"></a><span class="lineno">  962</span> </div>
<div class="line"><a id="l00963" name="l00963"></a><span class="lineno">  963</span><span class="keyword">using </span>send_result_t = detail::trivial_optional&lt;size_t&gt;;</div>
<div class="line"><a id="l00964" name="l00964"></a><span class="lineno">  964</span><span class="keyword">using </span>recv_result_t = detail::trivial_optional&lt;size_t&gt;;</div>
<div class="line"><a id="l00965" name="l00965"></a><span class="lineno">  965</span><span class="keyword">using </span>recv_buffer_result_t = detail::trivial_optional&lt;recv_buffer_size&gt;;</div>
<div class="line"><a id="l00966" name="l00966"></a><span class="lineno">  966</span> </div>
<div class="line"><a id="l00967" name="l00967"></a><span class="lineno">  967</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l00968" name="l00968"></a><span class="lineno">  968</span> </div>
<div class="line"><a id="l00969" name="l00969"></a><span class="lineno">  969</span><span class="keyword">namespace </span>detail</div>
<div class="line"><a id="l00970" name="l00970"></a><span class="lineno">  970</span>{</div>
<div class="line"><a id="l00971" name="l00971"></a><span class="lineno">  971</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">constexpr</span> T enum_bit_or(T a, T b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l00972" name="l00972"></a><span class="lineno">  972</span>{</div>
<div class="line"><a id="l00973" name="l00973"></a><span class="lineno">  973</span>    <span class="keyword">static_assert</span>(std::is_enum&lt;T&gt;::value, <span class="stringliteral">&quot;must be enum&quot;</span>);</div>
<div class="line"><a id="l00974" name="l00974"></a><span class="lineno">  974</span>    <span class="keyword">using </span>U = <span class="keyword">typename</span> std::underlying_type&lt;T&gt;::type;</div>
<div class="line"><a id="l00975" name="l00975"></a><span class="lineno">  975</span>    <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(<span class="keyword">static_cast&lt;</span>U<span class="keyword">&gt;</span>(a) | <span class="keyword">static_cast&lt;</span>U<span class="keyword">&gt;</span>(b));</div>
<div class="line"><a id="l00976" name="l00976"></a><span class="lineno">  976</span>}</div>
<div class="line"><a id="l00977" name="l00977"></a><span class="lineno">  977</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">constexpr</span> T enum_bit_and(T a, T b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l00978" name="l00978"></a><span class="lineno">  978</span>{</div>
<div class="line"><a id="l00979" name="l00979"></a><span class="lineno">  979</span>    <span class="keyword">static_assert</span>(std::is_enum&lt;T&gt;::value, <span class="stringliteral">&quot;must be enum&quot;</span>);</div>
<div class="line"><a id="l00980" name="l00980"></a><span class="lineno">  980</span>    <span class="keyword">using </span>U = <span class="keyword">typename</span> std::underlying_type&lt;T&gt;::type;</div>
<div class="line"><a id="l00981" name="l00981"></a><span class="lineno">  981</span>    <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(<span class="keyword">static_cast&lt;</span>U<span class="keyword">&gt;</span>(a) &amp; <span class="keyword">static_cast&lt;</span>U<span class="keyword">&gt;</span>(b));</div>
<div class="line"><a id="l00982" name="l00982"></a><span class="lineno">  982</span>}</div>
<div class="line"><a id="l00983" name="l00983"></a><span class="lineno">  983</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">constexpr</span> T enum_bit_xor(T a, T b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l00984" name="l00984"></a><span class="lineno">  984</span>{</div>
<div class="line"><a id="l00985" name="l00985"></a><span class="lineno">  985</span>    <span class="keyword">static_assert</span>(std::is_enum&lt;T&gt;::value, <span class="stringliteral">&quot;must be enum&quot;</span>);</div>
<div class="line"><a id="l00986" name="l00986"></a><span class="lineno">  986</span>    <span class="keyword">using </span>U = <span class="keyword">typename</span> std::underlying_type&lt;T&gt;::type;</div>
<div class="line"><a id="l00987" name="l00987"></a><span class="lineno">  987</span>    <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(<span class="keyword">static_cast&lt;</span>U<span class="keyword">&gt;</span>(a) ^ <span class="keyword">static_cast&lt;</span>U<span class="keyword">&gt;</span>(b));</div>
<div class="line"><a id="l00988" name="l00988"></a><span class="lineno">  988</span>}</div>
<div class="line"><a id="l00989" name="l00989"></a><span class="lineno">  989</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">constexpr</span> T enum_bit_not(T a) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l00990" name="l00990"></a><span class="lineno">  990</span>{</div>
<div class="line"><a id="l00991" name="l00991"></a><span class="lineno">  991</span>    <span class="keyword">static_assert</span>(std::is_enum&lt;T&gt;::value, <span class="stringliteral">&quot;must be enum&quot;</span>);</div>
<div class="line"><a id="l00992" name="l00992"></a><span class="lineno">  992</span>    <span class="keyword">using </span>U = <span class="keyword">typename</span> std::underlying_type&lt;T&gt;::type;</div>
<div class="line"><a id="l00993" name="l00993"></a><span class="lineno">  993</span>    <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span>T<span class="keyword">&gt;</span>(~static_cast&lt;U&gt;(a));</div>
<div class="line"><a id="l00994" name="l00994"></a><span class="lineno">  994</span>}</div>
<div class="line"><a id="l00995" name="l00995"></a><span class="lineno">  995</span>} <span class="comment">// namespace detail</span></div>
<div class="line"><a id="l00996" name="l00996"></a><span class="lineno">  996</span> </div>
<div class="line"><a id="l00997" name="l00997"></a><span class="lineno">  997</span><span class="comment">// partially satisfies named requirement BitmaskType</span></div>
<div class="line"><a id="l00998" name="l00998"></a><span class="lineno">  998</span><span class="keyword">enum class</span> send_flags : <span class="keywordtype">int</span></div>
<div class="line"><a id="l00999" name="l00999"></a><span class="lineno">  999</span>{</div>
<div class="line"><a id="l01000" name="l01000"></a><span class="lineno"> 1000</span>    none = 0,</div>
<div class="line"><a id="l01001" name="l01001"></a><span class="lineno"> 1001</span>    dontwait = ZMQ_DONTWAIT,</div>
<div class="line"><a id="l01002" name="l01002"></a><span class="lineno"> 1002</span>    sndmore = ZMQ_SNDMORE</div>
<div class="line"><a id="l01003" name="l01003"></a><span class="lineno"> 1003</span>};</div>
<div class="line"><a id="l01004" name="l01004"></a><span class="lineno"> 1004</span> </div>
<div class="line"><a id="l01005" name="l01005"></a><span class="lineno"> 1005</span><span class="keyword">constexpr</span> send_flags operator|(send_flags a, send_flags b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01006" name="l01006"></a><span class="lineno"> 1006</span>{</div>
<div class="line"><a id="l01007" name="l01007"></a><span class="lineno"> 1007</span>    <span class="keywordflow">return</span> detail::enum_bit_or(a, b);</div>
<div class="line"><a id="l01008" name="l01008"></a><span class="lineno"> 1008</span>}</div>
<div class="line"><a id="l01009" name="l01009"></a><span class="lineno"> 1009</span><span class="keyword">constexpr</span> send_flags operator&amp;(send_flags a, send_flags b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01010" name="l01010"></a><span class="lineno"> 1010</span>{</div>
<div class="line"><a id="l01011" name="l01011"></a><span class="lineno"> 1011</span>    <span class="keywordflow">return</span> detail::enum_bit_and(a, b);</div>
<div class="line"><a id="l01012" name="l01012"></a><span class="lineno"> 1012</span>}</div>
<div class="line"><a id="l01013" name="l01013"></a><span class="lineno"> 1013</span><span class="keyword">constexpr</span> send_flags operator^(send_flags a, send_flags b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01014" name="l01014"></a><span class="lineno"> 1014</span>{</div>
<div class="line"><a id="l01015" name="l01015"></a><span class="lineno"> 1015</span>    <span class="keywordflow">return</span> detail::enum_bit_xor(a, b);</div>
<div class="line"><a id="l01016" name="l01016"></a><span class="lineno"> 1016</span>}</div>
<div class="line"><a id="l01017" name="l01017"></a><span class="lineno"> 1017</span><span class="keyword">constexpr</span> send_flags operator~(send_flags a) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01018" name="l01018"></a><span class="lineno"> 1018</span>{</div>
<div class="line"><a id="l01019" name="l01019"></a><span class="lineno"> 1019</span>    <span class="keywordflow">return</span> detail::enum_bit_not(a);</div>
<div class="line"><a id="l01020" name="l01020"></a><span class="lineno"> 1020</span>}</div>
<div class="line"><a id="l01021" name="l01021"></a><span class="lineno"> 1021</span> </div>
<div class="line"><a id="l01022" name="l01022"></a><span class="lineno"> 1022</span><span class="comment">// partially satisfies named requirement BitmaskType</span></div>
<div class="line"><a id="l01023" name="l01023"></a><span class="lineno"> 1023</span><span class="keyword">enum class</span> recv_flags : <span class="keywordtype">int</span></div>
<div class="line"><a id="l01024" name="l01024"></a><span class="lineno"> 1024</span>{</div>
<div class="line"><a id="l01025" name="l01025"></a><span class="lineno"> 1025</span>    none = 0,</div>
<div class="line"><a id="l01026" name="l01026"></a><span class="lineno"> 1026</span>    dontwait = ZMQ_DONTWAIT</div>
<div class="line"><a id="l01027" name="l01027"></a><span class="lineno"> 1027</span>};</div>
<div class="line"><a id="l01028" name="l01028"></a><span class="lineno"> 1028</span> </div>
<div class="line"><a id="l01029" name="l01029"></a><span class="lineno"> 1029</span><span class="keyword">constexpr</span> recv_flags operator|(recv_flags a, recv_flags b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01030" name="l01030"></a><span class="lineno"> 1030</span>{</div>
<div class="line"><a id="l01031" name="l01031"></a><span class="lineno"> 1031</span>    <span class="keywordflow">return</span> detail::enum_bit_or(a, b);</div>
<div class="line"><a id="l01032" name="l01032"></a><span class="lineno"> 1032</span>}</div>
<div class="line"><a id="l01033" name="l01033"></a><span class="lineno"> 1033</span><span class="keyword">constexpr</span> recv_flags operator&amp;(recv_flags a, recv_flags b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01034" name="l01034"></a><span class="lineno"> 1034</span>{</div>
<div class="line"><a id="l01035" name="l01035"></a><span class="lineno"> 1035</span>    <span class="keywordflow">return</span> detail::enum_bit_and(a, b);</div>
<div class="line"><a id="l01036" name="l01036"></a><span class="lineno"> 1036</span>}</div>
<div class="line"><a id="l01037" name="l01037"></a><span class="lineno"> 1037</span><span class="keyword">constexpr</span> recv_flags operator^(recv_flags a, recv_flags b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01038" name="l01038"></a><span class="lineno"> 1038</span>{</div>
<div class="line"><a id="l01039" name="l01039"></a><span class="lineno"> 1039</span>    <span class="keywordflow">return</span> detail::enum_bit_xor(a, b);</div>
<div class="line"><a id="l01040" name="l01040"></a><span class="lineno"> 1040</span>}</div>
<div class="line"><a id="l01041" name="l01041"></a><span class="lineno"> 1041</span><span class="keyword">constexpr</span> recv_flags operator~(recv_flags a) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01042" name="l01042"></a><span class="lineno"> 1042</span>{</div>
<div class="line"><a id="l01043" name="l01043"></a><span class="lineno"> 1043</span>    <span class="keywordflow">return</span> detail::enum_bit_not(a);</div>
<div class="line"><a id="l01044" name="l01044"></a><span class="lineno"> 1044</span>}</div>
<div class="line"><a id="l01045" name="l01045"></a><span class="lineno"> 1045</span> </div>
<div class="line"><a id="l01046" name="l01046"></a><span class="lineno"> 1046</span> </div>
<div class="line"><a id="l01047" name="l01047"></a><span class="lineno"> 1047</span><span class="comment">// mutable_buffer, const_buffer and buffer are based on</span></div>
<div class="line"><a id="l01048" name="l01048"></a><span class="lineno"> 1048</span><span class="comment">// the Networking TS specification, draft:</span></div>
<div class="line"><a id="l01049" name="l01049"></a><span class="lineno"> 1049</span><span class="comment">// http://www.open-std.org/jtc1/sc22/wg21/docs/papers/2018/n4771.pdf</span></div>
<div class="line"><a id="l01050" name="l01050"></a><span class="lineno"> 1050</span> </div>
<div class="line"><a id="l01051" name="l01051"></a><span class="lineno"> 1051</span><span class="keyword">class </span>mutable_buffer</div>
<div class="line"><a id="l01052" name="l01052"></a><span class="lineno"> 1052</span>{</div>
<div class="line"><a id="l01053" name="l01053"></a><span class="lineno"> 1053</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l01054" name="l01054"></a><span class="lineno"> 1054</span>    <span class="keyword">constexpr</span> mutable_buffer() noexcept : _data(<span class="keywordtype">nullptr</span>), _size(0) {}</div>
<div class="line"><a id="l01055" name="l01055"></a><span class="lineno"> 1055</span>    <span class="keyword">constexpr</span> mutable_buffer(<span class="keywordtype">void</span> *p, <span class="keywordtype">size_t</span> n) noexcept : _data(p), _size(n)</div>
<div class="line"><a id="l01056" name="l01056"></a><span class="lineno"> 1056</span>    {</div>
<div class="line"><a id="l01057" name="l01057"></a><span class="lineno"> 1057</span><span class="preprocessor">#ifdef ZMQ_EXTENDED_CONSTEXPR</span></div>
<div class="line"><a id="l01058" name="l01058"></a><span class="lineno"> 1058</span>        assert(p != <span class="keyword">nullptr</span> || n == 0);</div>
<div class="line"><a id="l01059" name="l01059"></a><span class="lineno"> 1059</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01060" name="l01060"></a><span class="lineno"> 1060</span>    }</div>
<div class="line"><a id="l01061" name="l01061"></a><span class="lineno"> 1061</span> </div>
<div class="line"><a id="l01062" name="l01062"></a><span class="lineno"> 1062</span>    <span class="keyword">constexpr</span> <span class="keywordtype">void</span> *data() const noexcept { <span class="keywordflow">return</span> _data; }</div>
<div class="line"><a id="l01063" name="l01063"></a><span class="lineno"> 1063</span>    <span class="keyword">constexpr</span> <span class="keywordtype">size_t</span> size() const noexcept { <span class="keywordflow">return</span> _size; }</div>
<div class="line"><a id="l01064" name="l01064"></a><span class="lineno"> 1064</span>    mutable_buffer &amp;operator+=(<span class="keywordtype">size_t</span> n) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01065" name="l01065"></a><span class="lineno"> 1065</span>    {</div>
<div class="line"><a id="l01066" name="l01066"></a><span class="lineno"> 1066</span>        <span class="comment">// (std::min) is a workaround for when a min macro is defined</span></div>
<div class="line"><a id="l01067" name="l01067"></a><span class="lineno"> 1067</span>        <span class="keyword">const</span> <span class="keyword">auto</span> shift = (std::min)(n, _size);</div>
<div class="line"><a id="l01068" name="l01068"></a><span class="lineno"> 1068</span>        _data = <span class="keyword">static_cast&lt;</span><span class="keywordtype">char</span> *<span class="keyword">&gt;</span>(_data) + shift;</div>
<div class="line"><a id="l01069" name="l01069"></a><span class="lineno"> 1069</span>        _size -= shift;</div>
<div class="line"><a id="l01070" name="l01070"></a><span class="lineno"> 1070</span>        <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a id="l01071" name="l01071"></a><span class="lineno"> 1071</span>    }</div>
<div class="line"><a id="l01072" name="l01072"></a><span class="lineno"> 1072</span> </div>
<div class="line"><a id="l01073" name="l01073"></a><span class="lineno"> 1073</span>  <span class="keyword">private</span>:</div>
<div class="line"><a id="l01074" name="l01074"></a><span class="lineno"> 1074</span>    <span class="keywordtype">void</span> *_data;</div>
<div class="line"><a id="l01075" name="l01075"></a><span class="lineno"> 1075</span>    <span class="keywordtype">size_t</span> _size;</div>
<div class="line"><a id="l01076" name="l01076"></a><span class="lineno"> 1076</span>};</div>
<div class="line"><a id="l01077" name="l01077"></a><span class="lineno"> 1077</span> </div>
<div class="line"><a id="l01078" name="l01078"></a><span class="lineno"> 1078</span><span class="keyword">inline</span> mutable_buffer operator+(<span class="keyword">const</span> mutable_buffer &amp;mb, <span class="keywordtype">size_t</span> n) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01079" name="l01079"></a><span class="lineno"> 1079</span>{</div>
<div class="line"><a id="l01080" name="l01080"></a><span class="lineno"> 1080</span>    <span class="keywordflow">return</span> mutable_buffer(<span class="keyword">static_cast&lt;</span><span class="keywordtype">char</span> *<span class="keyword">&gt;</span>(mb.data()) + (std::min)(n, mb.size()),</div>
<div class="line"><a id="l01081" name="l01081"></a><span class="lineno"> 1081</span>                          mb.size() - (std::min)(n, mb.size()));</div>
<div class="line"><a id="l01082" name="l01082"></a><span class="lineno"> 1082</span>}</div>
<div class="line"><a id="l01083" name="l01083"></a><span class="lineno"> 1083</span><span class="keyword">inline</span> mutable_buffer operator+(<span class="keywordtype">size_t</span> n, <span class="keyword">const</span> mutable_buffer &amp;mb) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01084" name="l01084"></a><span class="lineno"> 1084</span>{</div>
<div class="line"><a id="l01085" name="l01085"></a><span class="lineno"> 1085</span>    <span class="keywordflow">return</span> mb + n;</div>
<div class="line"><a id="l01086" name="l01086"></a><span class="lineno"> 1086</span>}</div>
<div class="line"><a id="l01087" name="l01087"></a><span class="lineno"> 1087</span> </div>
<div class="line"><a id="l01088" name="l01088"></a><span class="lineno"> 1088</span><span class="keyword">class </span>const_buffer</div>
<div class="line"><a id="l01089" name="l01089"></a><span class="lineno"> 1089</span>{</div>
<div class="line"><a id="l01090" name="l01090"></a><span class="lineno"> 1090</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l01091" name="l01091"></a><span class="lineno"> 1091</span>    <span class="keyword">constexpr</span> const_buffer() noexcept : _data(<span class="keywordtype">nullptr</span>), _size(0) {}</div>
<div class="line"><a id="l01092" name="l01092"></a><span class="lineno"> 1092</span>    <span class="keyword">constexpr</span> const_buffer(<span class="keyword">const</span> <span class="keywordtype">void</span> *p, <span class="keywordtype">size_t</span> n) noexcept : _data(p), _size(n)</div>
<div class="line"><a id="l01093" name="l01093"></a><span class="lineno"> 1093</span>    {</div>
<div class="line"><a id="l01094" name="l01094"></a><span class="lineno"> 1094</span><span class="preprocessor">#ifdef ZMQ_EXTENDED_CONSTEXPR</span></div>
<div class="line"><a id="l01095" name="l01095"></a><span class="lineno"> 1095</span>        assert(p != <span class="keyword">nullptr</span> || n == 0);</div>
<div class="line"><a id="l01096" name="l01096"></a><span class="lineno"> 1096</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01097" name="l01097"></a><span class="lineno"> 1097</span>    }</div>
<div class="line"><a id="l01098" name="l01098"></a><span class="lineno"> 1098</span>    <span class="keyword">constexpr</span> const_buffer(<span class="keyword">const</span> mutable_buffer &amp;mb) noexcept :</div>
<div class="line"><a id="l01099" name="l01099"></a><span class="lineno"> 1099</span>        _data(mb.data()),</div>
<div class="line"><a id="l01100" name="l01100"></a><span class="lineno"> 1100</span>        _size(mb.size())</div>
<div class="line"><a id="l01101" name="l01101"></a><span class="lineno"> 1101</span>    {</div>
<div class="line"><a id="l01102" name="l01102"></a><span class="lineno"> 1102</span>    }</div>
<div class="line"><a id="l01103" name="l01103"></a><span class="lineno"> 1103</span> </div>
<div class="line"><a id="l01104" name="l01104"></a><span class="lineno"> 1104</span>    <span class="keyword">constexpr</span> <span class="keyword">const</span> <span class="keywordtype">void</span> *data() const noexcept { <span class="keywordflow">return</span> _data; }</div>
<div class="line"><a id="l01105" name="l01105"></a><span class="lineno"> 1105</span>    <span class="keyword">constexpr</span> <span class="keywordtype">size_t</span> size() const noexcept { <span class="keywordflow">return</span> _size; }</div>
<div class="line"><a id="l01106" name="l01106"></a><span class="lineno"> 1106</span>    const_buffer &amp;operator+=(<span class="keywordtype">size_t</span> n) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01107" name="l01107"></a><span class="lineno"> 1107</span>    {</div>
<div class="line"><a id="l01108" name="l01108"></a><span class="lineno"> 1108</span>        <span class="keyword">const</span> <span class="keyword">auto</span> shift = (std::min)(n, _size);</div>
<div class="line"><a id="l01109" name="l01109"></a><span class="lineno"> 1109</span>        _data = <span class="keyword">static_cast&lt;</span><span class="keyword">const </span><span class="keywordtype">char</span> *<span class="keyword">&gt;</span>(_data) + shift;</div>
<div class="line"><a id="l01110" name="l01110"></a><span class="lineno"> 1110</span>        _size -= shift;</div>
<div class="line"><a id="l01111" name="l01111"></a><span class="lineno"> 1111</span>        <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a id="l01112" name="l01112"></a><span class="lineno"> 1112</span>    }</div>
<div class="line"><a id="l01113" name="l01113"></a><span class="lineno"> 1113</span> </div>
<div class="line"><a id="l01114" name="l01114"></a><span class="lineno"> 1114</span>  <span class="keyword">private</span>:</div>
<div class="line"><a id="l01115" name="l01115"></a><span class="lineno"> 1115</span>    <span class="keyword">const</span> <span class="keywordtype">void</span> *_data;</div>
<div class="line"><a id="l01116" name="l01116"></a><span class="lineno"> 1116</span>    <span class="keywordtype">size_t</span> _size;</div>
<div class="line"><a id="l01117" name="l01117"></a><span class="lineno"> 1117</span>};</div>
<div class="line"><a id="l01118" name="l01118"></a><span class="lineno"> 1118</span> </div>
<div class="line"><a id="l01119" name="l01119"></a><span class="lineno"> 1119</span><span class="keyword">inline</span> const_buffer operator+(<span class="keyword">const</span> const_buffer &amp;cb, <span class="keywordtype">size_t</span> n) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01120" name="l01120"></a><span class="lineno"> 1120</span>{</div>
<div class="line"><a id="l01121" name="l01121"></a><span class="lineno"> 1121</span>    <span class="keywordflow">return</span> const_buffer(<span class="keyword">static_cast&lt;</span><span class="keyword">const </span><span class="keywordtype">char</span> *<span class="keyword">&gt;</span>(cb.data())</div>
<div class="line"><a id="l01122" name="l01122"></a><span class="lineno"> 1122</span>                          + (std::min)(n, cb.size()),</div>
<div class="line"><a id="l01123" name="l01123"></a><span class="lineno"> 1123</span>                        cb.size() - (std::min)(n, cb.size()));</div>
<div class="line"><a id="l01124" name="l01124"></a><span class="lineno"> 1124</span>}</div>
<div class="line"><a id="l01125" name="l01125"></a><span class="lineno"> 1125</span><span class="keyword">inline</span> const_buffer operator+(<span class="keywordtype">size_t</span> n, <span class="keyword">const</span> const_buffer &amp;cb) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01126" name="l01126"></a><span class="lineno"> 1126</span>{</div>
<div class="line"><a id="l01127" name="l01127"></a><span class="lineno"> 1127</span>    <span class="keywordflow">return</span> cb + n;</div>
<div class="line"><a id="l01128" name="l01128"></a><span class="lineno"> 1128</span>}</div>
<div class="line"><a id="l01129" name="l01129"></a><span class="lineno"> 1129</span> </div>
<div class="line"><a id="l01130" name="l01130"></a><span class="lineno"> 1130</span><span class="comment">// buffer creation</span></div>
<div class="line"><a id="l01131" name="l01131"></a><span class="lineno"> 1131</span> </div>
<div class="line"><a id="l01132" name="l01132"></a><span class="lineno"> 1132</span><span class="keyword">constexpr</span> mutable_buffer buffer(<span class="keywordtype">void</span> *p, <span class="keywordtype">size_t</span> n) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01133" name="l01133"></a><span class="lineno"> 1133</span>{</div>
<div class="line"><a id="l01134" name="l01134"></a><span class="lineno"> 1134</span>    <span class="keywordflow">return</span> mutable_buffer(p, n);</div>
<div class="line"><a id="l01135" name="l01135"></a><span class="lineno"> 1135</span>}</div>
<div class="line"><a id="l01136" name="l01136"></a><span class="lineno"> 1136</span><span class="keyword">constexpr</span> const_buffer buffer(<span class="keyword">const</span> <span class="keywordtype">void</span> *p, <span class="keywordtype">size_t</span> n) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01137" name="l01137"></a><span class="lineno"> 1137</span>{</div>
<div class="line"><a id="l01138" name="l01138"></a><span class="lineno"> 1138</span>    <span class="keywordflow">return</span> const_buffer(p, n);</div>
<div class="line"><a id="l01139" name="l01139"></a><span class="lineno"> 1139</span>}</div>
<div class="line"><a id="l01140" name="l01140"></a><span class="lineno"> 1140</span><span class="keyword">constexpr</span> mutable_buffer buffer(<span class="keyword">const</span> mutable_buffer &amp;mb) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01141" name="l01141"></a><span class="lineno"> 1141</span>{</div>
<div class="line"><a id="l01142" name="l01142"></a><span class="lineno"> 1142</span>    <span class="keywordflow">return</span> mb;</div>
<div class="line"><a id="l01143" name="l01143"></a><span class="lineno"> 1143</span>}</div>
<div class="line"><a id="l01144" name="l01144"></a><span class="lineno"> 1144</span><span class="keyword">inline</span> mutable_buffer buffer(<span class="keyword">const</span> mutable_buffer &amp;mb, <span class="keywordtype">size_t</span> n) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01145" name="l01145"></a><span class="lineno"> 1145</span>{</div>
<div class="line"><a id="l01146" name="l01146"></a><span class="lineno"> 1146</span>    <span class="keywordflow">return</span> mutable_buffer(mb.data(), (std::min)(mb.size(), n));</div>
<div class="line"><a id="l01147" name="l01147"></a><span class="lineno"> 1147</span>}</div>
<div class="line"><a id="l01148" name="l01148"></a><span class="lineno"> 1148</span><span class="keyword">constexpr</span> const_buffer buffer(<span class="keyword">const</span> const_buffer &amp;cb) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01149" name="l01149"></a><span class="lineno"> 1149</span>{</div>
<div class="line"><a id="l01150" name="l01150"></a><span class="lineno"> 1150</span>    <span class="keywordflow">return</span> cb;</div>
<div class="line"><a id="l01151" name="l01151"></a><span class="lineno"> 1151</span>}</div>
<div class="line"><a id="l01152" name="l01152"></a><span class="lineno"> 1152</span><span class="keyword">inline</span> const_buffer buffer(<span class="keyword">const</span> const_buffer &amp;cb, <span class="keywordtype">size_t</span> n) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01153" name="l01153"></a><span class="lineno"> 1153</span>{</div>
<div class="line"><a id="l01154" name="l01154"></a><span class="lineno"> 1154</span>    <span class="keywordflow">return</span> const_buffer(cb.data(), (std::min)(cb.size(), n));</div>
<div class="line"><a id="l01155" name="l01155"></a><span class="lineno"> 1155</span>}</div>
<div class="line"><a id="l01156" name="l01156"></a><span class="lineno"> 1156</span> </div>
<div class="line"><a id="l01157" name="l01157"></a><span class="lineno"> 1157</span><span class="keyword">namespace </span>detail</div>
<div class="line"><a id="l01158" name="l01158"></a><span class="lineno"> 1158</span>{</div>
<div class="line"><a id="l01159" name="l01159"></a><span class="lineno"> 1159</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">struct </span>is_buffer</div>
<div class="line"><a id="l01160" name="l01160"></a><span class="lineno"> 1160</span>{</div>
<div class="line"><a id="l01161" name="l01161"></a><span class="lineno"> 1161</span>    <span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="keywordtype">bool</span> value =</div>
<div class="line"><a id="l01162" name="l01162"></a><span class="lineno"> 1162</span>      std::is_same&lt;T, const_buffer&gt;::value || std::is_same&lt;T, mutable_buffer&gt;::value;</div>
<div class="line"><a id="l01163" name="l01163"></a><span class="lineno"> 1163</span>};</div>
<div class="line"><a id="l01164" name="l01164"></a><span class="lineno"> 1164</span> </div>
<div class="line"><a id="l01165" name="l01165"></a><span class="lineno"> 1165</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T&gt; <span class="keyword">struct </span>is_pod_like</div>
<div class="line"><a id="l01166" name="l01166"></a><span class="lineno"> 1166</span>{</div>
<div class="line"><a id="l01167" name="l01167"></a><span class="lineno"> 1167</span>    <span class="comment">// NOTE: The networking draft N4771 section 16.11 requires</span></div>
<div class="line"><a id="l01168" name="l01168"></a><span class="lineno"> 1168</span>    <span class="comment">// T in the buffer functions below to be</span></div>
<div class="line"><a id="l01169" name="l01169"></a><span class="lineno"> 1169</span>    <span class="comment">// trivially copyable OR standard layout.</span></div>
<div class="line"><a id="l01170" name="l01170"></a><span class="lineno"> 1170</span>    <span class="comment">// Here we decide to be conservative and require both.</span></div>
<div class="line"><a id="l01171" name="l01171"></a><span class="lineno"> 1171</span>    <span class="keyword">static</span> <span class="keyword">constexpr</span> <span class="keywordtype">bool</span> value =</div>
<div class="line"><a id="l01172" name="l01172"></a><span class="lineno"> 1172</span>      ZMQ_IS_TRIVIALLY_COPYABLE(T) &amp;&amp; std::is_standard_layout&lt;T&gt;::value;</div>
<div class="line"><a id="l01173" name="l01173"></a><span class="lineno"> 1173</span>};</div>
<div class="line"><a id="l01174" name="l01174"></a><span class="lineno"> 1174</span> </div>
<div class="line"><a id="l01175" name="l01175"></a><span class="lineno"> 1175</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> C&gt; <span class="keyword">constexpr</span> <span class="keyword">auto</span> seq_size(<span class="keyword">const</span> C &amp;c) <span class="keyword">noexcept</span> -&gt; <span class="keyword">decltype</span>(c.size())</div>
<div class="line"><a id="l01176" name="l01176"></a><span class="lineno"> 1176</span>{</div>
<div class="line"><a id="l01177" name="l01177"></a><span class="lineno"> 1177</span>    <span class="keywordflow">return</span> c.size();</div>
<div class="line"><a id="l01178" name="l01178"></a><span class="lineno"> 1178</span>}</div>
<div class="line"><a id="l01179" name="l01179"></a><span class="lineno"> 1179</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt;</div>
<div class="line"><a id="l01180" name="l01180"></a><span class="lineno"> 1180</span><span class="keyword">constexpr</span> <span class="keywordtype">size_t</span> seq_size(<span class="keyword">const</span> T (&amp;<span class="comment">/*array*/</span>)[N]) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01181" name="l01181"></a><span class="lineno"> 1181</span>{</div>
<div class="line"><a id="l01182" name="l01182"></a><span class="lineno"> 1182</span>    <span class="keywordflow">return</span> N;</div>
<div class="line"><a id="l01183" name="l01183"></a><span class="lineno"> 1183</span>}</div>
<div class="line"><a id="l01184" name="l01184"></a><span class="lineno"> 1184</span> </div>
<div class="line"><a id="l01185" name="l01185"></a><span class="lineno"> 1185</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> Seq&gt;</div>
<div class="line"><a id="l01186" name="l01186"></a><span class="lineno"> 1186</span><span class="keyword">auto</span> buffer_contiguous_sequence(Seq &amp;&amp;seq) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01187" name="l01187"></a><span class="lineno"> 1187</span>  -&gt; <span class="keyword">decltype</span>(buffer(std::addressof(*std::begin(seq)), <span class="keywordtype">size_t</span>{}))</div>
<div class="line"><a id="l01188" name="l01188"></a><span class="lineno"> 1188</span>{</div>
<div class="line"><a id="l01189" name="l01189"></a><span class="lineno"> 1189</span>    <span class="keyword">using </span>T = <span class="keyword">typename</span> std::remove_cv&lt;</div>
<div class="line"><a id="l01190" name="l01190"></a><span class="lineno"> 1190</span>      <span class="keyword">typename</span> std::remove_reference&lt;<span class="keyword">decltype</span>(*std::begin(seq))&gt;::type&gt;::type;</div>
<div class="line"><a id="l01191" name="l01191"></a><span class="lineno"> 1191</span>    <span class="keyword">static_assert</span>(detail::is_pod_like&lt;T&gt;::value, <span class="stringliteral">&quot;T must be POD&quot;</span>);</div>
<div class="line"><a id="l01192" name="l01192"></a><span class="lineno"> 1192</span> </div>
<div class="line"><a id="l01193" name="l01193"></a><span class="lineno"> 1193</span>    <span class="keyword">const</span> <span class="keyword">auto</span> size = seq_size(seq);</div>
<div class="line"><a id="l01194" name="l01194"></a><span class="lineno"> 1194</span>    <span class="keywordflow">return</span> buffer(size != 0u ? std::addressof(*std::begin(seq)) : <span class="keyword">nullptr</span>,</div>
<div class="line"><a id="l01195" name="l01195"></a><span class="lineno"> 1195</span>                  size * <span class="keyword">sizeof</span>(T));</div>
<div class="line"><a id="l01196" name="l01196"></a><span class="lineno"> 1196</span>}</div>
<div class="line"><a id="l01197" name="l01197"></a><span class="lineno"> 1197</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> Seq&gt;</div>
<div class="line"><a id="l01198" name="l01198"></a><span class="lineno"> 1198</span><span class="keyword">auto</span> buffer_contiguous_sequence(Seq &amp;&amp;seq, <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01199" name="l01199"></a><span class="lineno"> 1199</span>  -&gt; <span class="keyword">decltype</span>(buffer_contiguous_sequence(seq))</div>
<div class="line"><a id="l01200" name="l01200"></a><span class="lineno"> 1200</span>{</div>
<div class="line"><a id="l01201" name="l01201"></a><span class="lineno"> 1201</span>    <span class="keyword">using </span>T = <span class="keyword">typename</span> std::remove_cv&lt;</div>
<div class="line"><a id="l01202" name="l01202"></a><span class="lineno"> 1202</span>      <span class="keyword">typename</span> std::remove_reference&lt;<span class="keyword">decltype</span>(*std::begin(seq))&gt;::type&gt;::type;</div>
<div class="line"><a id="l01203" name="l01203"></a><span class="lineno"> 1203</span>    <span class="keyword">static_assert</span>(detail::is_pod_like&lt;T&gt;::value, <span class="stringliteral">&quot;T must be POD&quot;</span>);</div>
<div class="line"><a id="l01204" name="l01204"></a><span class="lineno"> 1204</span> </div>
<div class="line"><a id="l01205" name="l01205"></a><span class="lineno"> 1205</span>    <span class="keyword">const</span> <span class="keyword">auto</span> size = seq_size(seq);</div>
<div class="line"><a id="l01206" name="l01206"></a><span class="lineno"> 1206</span>    <span class="keywordflow">return</span> buffer(size != 0u ? std::addressof(*std::begin(seq)) : <span class="keyword">nullptr</span>,</div>
<div class="line"><a id="l01207" name="l01207"></a><span class="lineno"> 1207</span>                  (std::min)(size * <span class="keyword">sizeof</span>(T), n_bytes));</div>
<div class="line"><a id="l01208" name="l01208"></a><span class="lineno"> 1208</span>}</div>
<div class="line"><a id="l01209" name="l01209"></a><span class="lineno"> 1209</span> </div>
<div class="line"><a id="l01210" name="l01210"></a><span class="lineno"> 1210</span>} <span class="comment">// namespace detail</span></div>
<div class="line"><a id="l01211" name="l01211"></a><span class="lineno"> 1211</span> </div>
<div class="line"><a id="l01212" name="l01212"></a><span class="lineno"> 1212</span><span class="comment">// C array</span></div>
<div class="line"><a id="l01213" name="l01213"></a><span class="lineno"> 1213</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt; mutable_buffer buffer(T (&amp;data)[N]) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01214" name="l01214"></a><span class="lineno"> 1214</span>{</div>
<div class="line"><a id="l01215" name="l01215"></a><span class="lineno"> 1215</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data);</div>
<div class="line"><a id="l01216" name="l01216"></a><span class="lineno"> 1216</span>}</div>
<div class="line"><a id="l01217" name="l01217"></a><span class="lineno"> 1217</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt;</div>
<div class="line"><a id="l01218" name="l01218"></a><span class="lineno"> 1218</span>mutable_buffer buffer(T (&amp;data)[N], <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01219" name="l01219"></a><span class="lineno"> 1219</span>{</div>
<div class="line"><a id="l01220" name="l01220"></a><span class="lineno"> 1220</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data, n_bytes);</div>
<div class="line"><a id="l01221" name="l01221"></a><span class="lineno"> 1221</span>}</div>
<div class="line"><a id="l01222" name="l01222"></a><span class="lineno"> 1222</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt; const_buffer buffer(<span class="keyword">const</span> T (&amp;data)[N]) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01223" name="l01223"></a><span class="lineno"> 1223</span>{</div>
<div class="line"><a id="l01224" name="l01224"></a><span class="lineno"> 1224</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data);</div>
<div class="line"><a id="l01225" name="l01225"></a><span class="lineno"> 1225</span>}</div>
<div class="line"><a id="l01226" name="l01226"></a><span class="lineno"> 1226</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt;</div>
<div class="line"><a id="l01227" name="l01227"></a><span class="lineno"> 1227</span>const_buffer buffer(<span class="keyword">const</span> T (&amp;data)[N], <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01228" name="l01228"></a><span class="lineno"> 1228</span>{</div>
<div class="line"><a id="l01229" name="l01229"></a><span class="lineno"> 1229</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data, n_bytes);</div>
<div class="line"><a id="l01230" name="l01230"></a><span class="lineno"> 1230</span>}</div>
<div class="line"><a id="l01231" name="l01231"></a><span class="lineno"> 1231</span><span class="comment">// std::array</span></div>
<div class="line"><a id="l01232" name="l01232"></a><span class="lineno"> 1232</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt; mutable_buffer buffer(std::array&lt;T, N&gt; &amp;data) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01233" name="l01233"></a><span class="lineno"> 1233</span>{</div>
<div class="line"><a id="l01234" name="l01234"></a><span class="lineno"> 1234</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data);</div>
<div class="line"><a id="l01235" name="l01235"></a><span class="lineno"> 1235</span>}</div>
<div class="line"><a id="l01236" name="l01236"></a><span class="lineno"> 1236</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt;</div>
<div class="line"><a id="l01237" name="l01237"></a><span class="lineno"> 1237</span>mutable_buffer buffer(std::array&lt;T, N&gt; &amp;data, <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01238" name="l01238"></a><span class="lineno"> 1238</span>{</div>
<div class="line"><a id="l01239" name="l01239"></a><span class="lineno"> 1239</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data, n_bytes);</div>
<div class="line"><a id="l01240" name="l01240"></a><span class="lineno"> 1240</span>}</div>
<div class="line"><a id="l01241" name="l01241"></a><span class="lineno"> 1241</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt;</div>
<div class="line"><a id="l01242" name="l01242"></a><span class="lineno"> 1242</span>const_buffer buffer(std::array&lt;const T, N&gt; &amp;data) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01243" name="l01243"></a><span class="lineno"> 1243</span>{</div>
<div class="line"><a id="l01244" name="l01244"></a><span class="lineno"> 1244</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data);</div>
<div class="line"><a id="l01245" name="l01245"></a><span class="lineno"> 1245</span>}</div>
<div class="line"><a id="l01246" name="l01246"></a><span class="lineno"> 1246</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt;</div>
<div class="line"><a id="l01247" name="l01247"></a><span class="lineno"> 1247</span>const_buffer buffer(std::array&lt;const T, N&gt; &amp;data, <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01248" name="l01248"></a><span class="lineno"> 1248</span>{</div>
<div class="line"><a id="l01249" name="l01249"></a><span class="lineno"> 1249</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data, n_bytes);</div>
<div class="line"><a id="l01250" name="l01250"></a><span class="lineno"> 1250</span>}</div>
<div class="line"><a id="l01251" name="l01251"></a><span class="lineno"> 1251</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt;</div>
<div class="line"><a id="l01252" name="l01252"></a><span class="lineno"> 1252</span>const_buffer buffer(<span class="keyword">const</span> std::array&lt;T, N&gt; &amp;data) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01253" name="l01253"></a><span class="lineno"> 1253</span>{</div>
<div class="line"><a id="l01254" name="l01254"></a><span class="lineno"> 1254</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data);</div>
<div class="line"><a id="l01255" name="l01255"></a><span class="lineno"> 1255</span>}</div>
<div class="line"><a id="l01256" name="l01256"></a><span class="lineno"> 1256</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keywordtype">size_t</span> N&gt;</div>
<div class="line"><a id="l01257" name="l01257"></a><span class="lineno"> 1257</span>const_buffer buffer(<span class="keyword">const</span> std::array&lt;T, N&gt; &amp;data, <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01258" name="l01258"></a><span class="lineno"> 1258</span>{</div>
<div class="line"><a id="l01259" name="l01259"></a><span class="lineno"> 1259</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data, n_bytes);</div>
<div class="line"><a id="l01260" name="l01260"></a><span class="lineno"> 1260</span>}</div>
<div class="line"><a id="l01261" name="l01261"></a><span class="lineno"> 1261</span><span class="comment">// std::vector</span></div>
<div class="line"><a id="l01262" name="l01262"></a><span class="lineno"> 1262</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> Allocator&gt;</div>
<div class="line"><a id="l01263" name="l01263"></a><span class="lineno"> 1263</span>mutable_buffer buffer(std::vector&lt;T, Allocator&gt; &amp;data) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01264" name="l01264"></a><span class="lineno"> 1264</span>{</div>
<div class="line"><a id="l01265" name="l01265"></a><span class="lineno"> 1265</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data);</div>
<div class="line"><a id="l01266" name="l01266"></a><span class="lineno"> 1266</span>}</div>
<div class="line"><a id="l01267" name="l01267"></a><span class="lineno"> 1267</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> Allocator&gt;</div>
<div class="line"><a id="l01268" name="l01268"></a><span class="lineno"> 1268</span>mutable_buffer buffer(std::vector&lt;T, Allocator&gt; &amp;data, <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01269" name="l01269"></a><span class="lineno"> 1269</span>{</div>
<div class="line"><a id="l01270" name="l01270"></a><span class="lineno"> 1270</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data, n_bytes);</div>
<div class="line"><a id="l01271" name="l01271"></a><span class="lineno"> 1271</span>}</div>
<div class="line"><a id="l01272" name="l01272"></a><span class="lineno"> 1272</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> Allocator&gt;</div>
<div class="line"><a id="l01273" name="l01273"></a><span class="lineno"> 1273</span>const_buffer buffer(<span class="keyword">const</span> std::vector&lt;T, Allocator&gt; &amp;data) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01274" name="l01274"></a><span class="lineno"> 1274</span>{</div>
<div class="line"><a id="l01275" name="l01275"></a><span class="lineno"> 1275</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data);</div>
<div class="line"><a id="l01276" name="l01276"></a><span class="lineno"> 1276</span>}</div>
<div class="line"><a id="l01277" name="l01277"></a><span class="lineno"> 1277</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> Allocator&gt;</div>
<div class="line"><a id="l01278" name="l01278"></a><span class="lineno"> 1278</span>const_buffer buffer(<span class="keyword">const</span> std::vector&lt;T, Allocator&gt; &amp;data, <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01279" name="l01279"></a><span class="lineno"> 1279</span>{</div>
<div class="line"><a id="l01280" name="l01280"></a><span class="lineno"> 1280</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data, n_bytes);</div>
<div class="line"><a id="l01281" name="l01281"></a><span class="lineno"> 1281</span>}</div>
<div class="line"><a id="l01282" name="l01282"></a><span class="lineno"> 1282</span><span class="comment">// std::basic_string</span></div>
<div class="line"><a id="l01283" name="l01283"></a><span class="lineno"> 1283</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> Traits, <span class="keyword">class</span> Allocator&gt;</div>
<div class="line"><a id="l01284" name="l01284"></a><span class="lineno"> 1284</span>mutable_buffer buffer(std::basic_string&lt;T, Traits, Allocator&gt; &amp;data) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01285" name="l01285"></a><span class="lineno"> 1285</span>{</div>
<div class="line"><a id="l01286" name="l01286"></a><span class="lineno"> 1286</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data);</div>
<div class="line"><a id="l01287" name="l01287"></a><span class="lineno"> 1287</span>}</div>
<div class="line"><a id="l01288" name="l01288"></a><span class="lineno"> 1288</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> Traits, <span class="keyword">class</span> Allocator&gt;</div>
<div class="line"><a id="l01289" name="l01289"></a><span class="lineno"> 1289</span>mutable_buffer buffer(std::basic_string&lt;T, Traits, Allocator&gt; &amp;data,</div>
<div class="line"><a id="l01290" name="l01290"></a><span class="lineno"> 1290</span>                      <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01291" name="l01291"></a><span class="lineno"> 1291</span>{</div>
<div class="line"><a id="l01292" name="l01292"></a><span class="lineno"> 1292</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data, n_bytes);</div>
<div class="line"><a id="l01293" name="l01293"></a><span class="lineno"> 1293</span>}</div>
<div class="line"><a id="l01294" name="l01294"></a><span class="lineno"> 1294</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> Traits, <span class="keyword">class</span> Allocator&gt;</div>
<div class="line"><a id="l01295" name="l01295"></a><span class="lineno"> 1295</span>const_buffer buffer(<span class="keyword">const</span> std::basic_string&lt;T, Traits, Allocator&gt; &amp;data) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01296" name="l01296"></a><span class="lineno"> 1296</span>{</div>
<div class="line"><a id="l01297" name="l01297"></a><span class="lineno"> 1297</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data);</div>
<div class="line"><a id="l01298" name="l01298"></a><span class="lineno"> 1298</span>}</div>
<div class="line"><a id="l01299" name="l01299"></a><span class="lineno"> 1299</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> Traits, <span class="keyword">class</span> Allocator&gt;</div>
<div class="line"><a id="l01300" name="l01300"></a><span class="lineno"> 1300</span>const_buffer buffer(<span class="keyword">const</span> std::basic_string&lt;T, Traits, Allocator&gt; &amp;data,</div>
<div class="line"><a id="l01301" name="l01301"></a><span class="lineno"> 1301</span>                    <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01302" name="l01302"></a><span class="lineno"> 1302</span>{</div>
<div class="line"><a id="l01303" name="l01303"></a><span class="lineno"> 1303</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data, n_bytes);</div>
<div class="line"><a id="l01304" name="l01304"></a><span class="lineno"> 1304</span>}</div>
<div class="line"><a id="l01305" name="l01305"></a><span class="lineno"> 1305</span> </div>
<div class="line"><a id="l01306" name="l01306"></a><span class="lineno"> 1306</span><span class="preprocessor">#if CPPZMQ_HAS_STRING_VIEW</span></div>
<div class="line"><a id="l01307" name="l01307"></a><span class="lineno"> 1307</span><span class="comment">// std::basic_string_view</span></div>
<div class="line"><a id="l01308" name="l01308"></a><span class="lineno"> 1308</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> Traits&gt;</div>
<div class="line"><a id="l01309" name="l01309"></a><span class="lineno"> 1309</span>const_buffer buffer(std::basic_string_view&lt;T, Traits&gt; data) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01310" name="l01310"></a><span class="lineno"> 1310</span>{</div>
<div class="line"><a id="l01311" name="l01311"></a><span class="lineno"> 1311</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data);</div>
<div class="line"><a id="l01312" name="l01312"></a><span class="lineno"> 1312</span>}</div>
<div class="line"><a id="l01313" name="l01313"></a><span class="lineno"> 1313</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T, <span class="keyword">class</span> Traits&gt;</div>
<div class="line"><a id="l01314" name="l01314"></a><span class="lineno"> 1314</span>const_buffer buffer(std::basic_string_view&lt;T, Traits&gt; data, <span class="keywordtype">size_t</span> n_bytes) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01315" name="l01315"></a><span class="lineno"> 1315</span>{</div>
<div class="line"><a id="l01316" name="l01316"></a><span class="lineno"> 1316</span>    <span class="keywordflow">return</span> detail::buffer_contiguous_sequence(data, n_bytes);</div>
<div class="line"><a id="l01317" name="l01317"></a><span class="lineno"> 1317</span>}</div>
<div class="line"><a id="l01318" name="l01318"></a><span class="lineno"> 1318</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01319" name="l01319"></a><span class="lineno"> 1319</span> </div>
<div class="line"><a id="l01320" name="l01320"></a><span class="lineno"> 1320</span><span class="comment">// Buffer for a string literal (null terminated)</span></div>
<div class="line"><a id="l01321" name="l01321"></a><span class="lineno"> 1321</span><span class="comment">// where the buffer size excludes the terminating character.</span></div>
<div class="line"><a id="l01322" name="l01322"></a><span class="lineno"> 1322</span><span class="comment">// Equivalent to zmq::buffer(std::string_view(&quot;...&quot;)).</span></div>
<div class="line"><a id="l01323" name="l01323"></a><span class="lineno"> 1323</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> Char, <span class="keywordtype">size_t</span> N&gt;</div>
<div class="line"><a id="l01324" name="l01324"></a><span class="lineno"> 1324</span><span class="keyword">constexpr</span> const_buffer str_buffer(<span class="keyword">const</span> Char (&amp;data)[N]) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01325" name="l01325"></a><span class="lineno"> 1325</span>{</div>
<div class="line"><a id="l01326" name="l01326"></a><span class="lineno"> 1326</span>    <span class="keyword">static_assert</span>(detail::is_pod_like&lt;Char&gt;::value, <span class="stringliteral">&quot;Char must be POD&quot;</span>);</div>
<div class="line"><a id="l01327" name="l01327"></a><span class="lineno"> 1327</span><span class="preprocessor">#ifdef ZMQ_EXTENDED_CONSTEXPR</span></div>
<div class="line"><a id="l01328" name="l01328"></a><span class="lineno"> 1328</span>    assert(data[N - 1] == Char{0});</div>
<div class="line"><a id="l01329" name="l01329"></a><span class="lineno"> 1329</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01330" name="l01330"></a><span class="lineno"> 1330</span>    <span class="keywordflow">return</span> const_buffer(<span class="keyword">static_cast&lt;</span><span class="keyword">const </span>Char *<span class="keyword">&gt;</span>(data), (N - 1) * <span class="keyword">sizeof</span>(Char));</div>
<div class="line"><a id="l01331" name="l01331"></a><span class="lineno"> 1331</span>}</div>
<div class="line"><a id="l01332" name="l01332"></a><span class="lineno"> 1332</span> </div>
<div class="line"><a id="l01333" name="l01333"></a><span class="lineno"> 1333</span><span class="keyword">namespace </span>literals</div>
<div class="line"><a id="l01334" name="l01334"></a><span class="lineno"> 1334</span>{</div>
<div class="line"><a id="l01335" name="l01335"></a><span class="lineno"> 1335</span><span class="keyword">constexpr</span> const_buffer <span class="keyword">operator</span><span class="stringliteral">&quot;&quot;</span> _zbuf(<span class="keyword">const</span> <span class="keywordtype">char</span> *str, <span class="keywordtype">size_t</span> len) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01336" name="l01336"></a><span class="lineno"> 1336</span>{</div>
<div class="line"><a id="l01337" name="l01337"></a><span class="lineno"> 1337</span>    <span class="keywordflow">return</span> const_buffer(str, len * <span class="keyword">sizeof</span>(<span class="keywordtype">char</span>));</div>
<div class="line"><a id="l01338" name="l01338"></a><span class="lineno"> 1338</span>}</div>
<div class="line"><a id="l01339" name="l01339"></a><span class="lineno"> 1339</span><span class="keyword">constexpr</span> const_buffer <span class="keyword">operator</span><span class="stringliteral">&quot;&quot;</span> _zbuf(<span class="keyword">const</span> <span class="keywordtype">wchar_t</span> *str, <span class="keywordtype">size_t</span> len) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01340" name="l01340"></a><span class="lineno"> 1340</span>{</div>
<div class="line"><a id="l01341" name="l01341"></a><span class="lineno"> 1341</span>    <span class="keywordflow">return</span> const_buffer(str, len * <span class="keyword">sizeof</span>(<span class="keywordtype">wchar_t</span>));</div>
<div class="line"><a id="l01342" name="l01342"></a><span class="lineno"> 1342</span>}</div>
<div class="line"><a id="l01343" name="l01343"></a><span class="lineno"> 1343</span><span class="keyword">constexpr</span> const_buffer <span class="keyword">operator</span><span class="stringliteral">&quot;&quot;</span> _zbuf(<span class="keyword">const</span> <span class="keywordtype">char16_t</span> *str, <span class="keywordtype">size_t</span> len) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01344" name="l01344"></a><span class="lineno"> 1344</span>{</div>
<div class="line"><a id="l01345" name="l01345"></a><span class="lineno"> 1345</span>    <span class="keywordflow">return</span> const_buffer(str, len * <span class="keyword">sizeof</span>(<span class="keywordtype">char16_t</span>));</div>
<div class="line"><a id="l01346" name="l01346"></a><span class="lineno"> 1346</span>}</div>
<div class="line"><a id="l01347" name="l01347"></a><span class="lineno"> 1347</span><span class="keyword">constexpr</span> const_buffer <span class="keyword">operator</span><span class="stringliteral">&quot;&quot;</span> _zbuf(<span class="keyword">const</span> <span class="keywordtype">char32_t</span> *str, <span class="keywordtype">size_t</span> len) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l01348" name="l01348"></a><span class="lineno"> 1348</span>{</div>
<div class="line"><a id="l01349" name="l01349"></a><span class="lineno"> 1349</span>    <span class="keywordflow">return</span> const_buffer(str, len * <span class="keyword">sizeof</span>(<span class="keywordtype">char32_t</span>));</div>
<div class="line"><a id="l01350" name="l01350"></a><span class="lineno"> 1350</span>}</div>
<div class="line"><a id="l01351" name="l01351"></a><span class="lineno"> 1351</span>}</div>
<div class="line"><a id="l01352" name="l01352"></a><span class="lineno"> 1352</span> </div>
<div class="line"><a id="l01353" name="l01353"></a><span class="lineno"> 1353</span><span class="preprocessor">#endif </span><span class="comment">// ZMQ_CPP11</span></div>
<div class="line"><a id="l01354" name="l01354"></a><span class="lineno"> 1354</span> </div>
<div class="line"><a id="l01355" name="l01355"></a><span class="lineno"> 1355</span> </div>
<div class="line"><a id="l01356" name="l01356"></a><span class="lineno"> 1356</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l01357" name="l01357"></a><span class="lineno"> 1357</span><span class="keyword">namespace </span>sockopt</div>
<div class="line"><a id="l01358" name="l01358"></a><span class="lineno"> 1358</span>{</div>
<div class="line"><a id="l01359" name="l01359"></a><span class="lineno"> 1359</span><span class="comment">// There are two types of options,</span></div>
<div class="line"><a id="l01360" name="l01360"></a><span class="lineno"> 1360</span><span class="comment">// integral type with known compiler time size (int, bool, int64_t, uint64_t)</span></div>
<div class="line"><a id="l01361" name="l01361"></a><span class="lineno"> 1361</span><span class="comment">// and arrays with dynamic size (strings, binary data).</span></div>
<div class="line"><a id="l01362" name="l01362"></a><span class="lineno"> 1362</span> </div>
<div class="line"><a id="l01363" name="l01363"></a><span class="lineno"> 1363</span><span class="comment">// BoolUnit: if true accepts values of type bool (but passed as T into libzmq)</span></div>
<div class="line"><a id="l01364" name="l01364"></a><span class="lineno"> 1364</span><span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keyword">class</span> T, <span class="keywordtype">bool</span> BoolUnit = false&gt; <span class="keyword">struct </span>integral_option</div>
<div class="line"><a id="l01365" name="l01365"></a><span class="lineno"> 1365</span>{</div>
<div class="line"><a id="l01366" name="l01366"></a><span class="lineno"> 1366</span>};</div>
<div class="line"><a id="l01367" name="l01367"></a><span class="lineno"> 1367</span> </div>
<div class="line"><a id="l01368" name="l01368"></a><span class="lineno"> 1368</span><span class="comment">// NullTerm:</span></div>
<div class="line"><a id="l01369" name="l01369"></a><span class="lineno"> 1369</span><span class="comment">// 0: binary data</span></div>
<div class="line"><a id="l01370" name="l01370"></a><span class="lineno"> 1370</span><span class="comment">// 1: null-terminated string (`getsockopt` size includes null)</span></div>
<div class="line"><a id="l01371" name="l01371"></a><span class="lineno"> 1371</span><span class="comment">// 2: binary (size 32) or Z85 encoder string of size 41 (null included)</span></div>
<div class="line"><a id="l01372" name="l01372"></a><span class="lineno"> 1372</span><span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keywordtype">int</span> NullTerm = 1&gt; <span class="keyword">struct </span>array_option</div>
<div class="line"><a id="l01373" name="l01373"></a><span class="lineno"> 1373</span>{</div>
<div class="line"><a id="l01374" name="l01374"></a><span class="lineno"> 1374</span>};</div>
<div class="line"><a id="l01375" name="l01375"></a><span class="lineno"> 1375</span> </div>
<div class="line"><a id="l01376" name="l01376"></a><span class="lineno"> 1376</span><span class="preprocessor">#define ZMQ_DEFINE_INTEGRAL_OPT(OPT, NAME, TYPE)                                    \</span></div>
<div class="line"><a id="l01377" name="l01377"></a><span class="lineno"> 1377</span><span class="preprocessor">    using NAME##_t = integral_option&lt;OPT, TYPE, false&gt;;                             \</span></div>
<div class="line"><a id="l01378" name="l01378"></a><span class="lineno"> 1378</span><span class="preprocessor">    ZMQ_INLINE_VAR ZMQ_CONSTEXPR_VAR NAME##_t NAME {}</span></div>
<div class="line"><a id="l01379" name="l01379"></a><span class="lineno"> 1379</span><span class="preprocessor">#define ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(OPT, NAME, TYPE)                          \</span></div>
<div class="line"><a id="l01380" name="l01380"></a><span class="lineno"> 1380</span><span class="preprocessor">    using NAME##_t = integral_option&lt;OPT, TYPE, true&gt;;                              \</span></div>
<div class="line"><a id="l01381" name="l01381"></a><span class="lineno"> 1381</span><span class="preprocessor">    ZMQ_INLINE_VAR ZMQ_CONSTEXPR_VAR NAME##_t NAME {}</span></div>
<div class="line"><a id="l01382" name="l01382"></a><span class="lineno"> 1382</span><span class="preprocessor">#define ZMQ_DEFINE_ARRAY_OPT(OPT, NAME)                                             \</span></div>
<div class="line"><a id="l01383" name="l01383"></a><span class="lineno"> 1383</span><span class="preprocessor">    using NAME##_t = array_option&lt;OPT&gt;;                                             \</span></div>
<div class="line"><a id="l01384" name="l01384"></a><span class="lineno"> 1384</span><span class="preprocessor">    ZMQ_INLINE_VAR ZMQ_CONSTEXPR_VAR NAME##_t NAME {}</span></div>
<div class="line"><a id="l01385" name="l01385"></a><span class="lineno"> 1385</span><span class="preprocessor">#define ZMQ_DEFINE_ARRAY_OPT_BINARY(OPT, NAME)                                      \</span></div>
<div class="line"><a id="l01386" name="l01386"></a><span class="lineno"> 1386</span><span class="preprocessor">    using NAME##_t = array_option&lt;OPT, 0&gt;;                                          \</span></div>
<div class="line"><a id="l01387" name="l01387"></a><span class="lineno"> 1387</span><span class="preprocessor">    ZMQ_INLINE_VAR ZMQ_CONSTEXPR_VAR NAME##_t NAME {}</span></div>
<div class="line"><a id="l01388" name="l01388"></a><span class="lineno"> 1388</span><span class="preprocessor">#define ZMQ_DEFINE_ARRAY_OPT_BIN_OR_Z85(OPT, NAME)                                  \</span></div>
<div class="line"><a id="l01389" name="l01389"></a><span class="lineno"> 1389</span><span class="preprocessor">    using NAME##_t = array_option&lt;OPT, 2&gt;;                                          \</span></div>
<div class="line"><a id="l01390" name="l01390"></a><span class="lineno"> 1390</span><span class="preprocessor">    ZMQ_INLINE_VAR ZMQ_CONSTEXPR_VAR NAME##_t NAME {}</span></div>
<div class="line"><a id="l01391" name="l01391"></a><span class="lineno"> 1391</span> </div>
<div class="line"><a id="l01392" name="l01392"></a><span class="lineno"> 1392</span><span class="comment">// duplicate definition from libzmq 4.3.3</span></div>
<div class="line"><a id="l01393" name="l01393"></a><span class="lineno"> 1393</span><span class="preprocessor">#if defined _WIN32</span></div>
<div class="line"><a id="l01394" name="l01394"></a><span class="lineno"> 1394</span><span class="preprocessor">#if defined _WIN64</span></div>
<div class="line"><a id="l01395" name="l01395"></a><span class="lineno"> 1395</span><span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> __int64 cppzmq_fd_t;</div>
<div class="line"><a id="l01396" name="l01396"></a><span class="lineno"> 1396</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l01397" name="l01397"></a><span class="lineno"> 1397</span><span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> cppzmq_fd_t;</div>
<div class="line"><a id="l01398" name="l01398"></a><span class="lineno"> 1398</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01399" name="l01399"></a><span class="lineno"> 1399</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l01400" name="l01400"></a><span class="lineno"> 1400</span><span class="keyword">typedef</span> <span class="keywordtype">int</span> cppzmq_fd_t;</div>
<div class="line"><a id="l01401" name="l01401"></a><span class="lineno"> 1401</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01402" name="l01402"></a><span class="lineno"> 1402</span> </div>
<div class="line"><a id="l01403" name="l01403"></a><span class="lineno"> 1403</span><span class="preprocessor">#ifdef ZMQ_AFFINITY</span></div>
<div class="line"><a id="l01404" name="l01404"></a><span class="lineno"> 1404</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_AFFINITY, affinity, uint64_t);</div>
<div class="line"><a id="l01405" name="l01405"></a><span class="lineno"> 1405</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01406" name="l01406"></a><span class="lineno"> 1406</span><span class="preprocessor">#ifdef ZMQ_BACKLOG</span></div>
<div class="line"><a id="l01407" name="l01407"></a><span class="lineno"> 1407</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_BACKLOG, backlog, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01408" name="l01408"></a><span class="lineno"> 1408</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01409" name="l01409"></a><span class="lineno"> 1409</span><span class="preprocessor">#ifdef ZMQ_BINDTODEVICE</span></div>
<div class="line"><a id="l01410" name="l01410"></a><span class="lineno"> 1410</span>ZMQ_DEFINE_ARRAY_OPT_BINARY(ZMQ_BINDTODEVICE, bindtodevice);</div>
<div class="line"><a id="l01411" name="l01411"></a><span class="lineno"> 1411</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01412" name="l01412"></a><span class="lineno"> 1412</span><span class="preprocessor">#ifdef ZMQ_CONFLATE</span></div>
<div class="line"><a id="l01413" name="l01413"></a><span class="lineno"> 1413</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_CONFLATE, conflate, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01414" name="l01414"></a><span class="lineno"> 1414</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01415" name="l01415"></a><span class="lineno"> 1415</span><span class="preprocessor">#ifdef ZMQ_CONNECT_ROUTING_ID</span></div>
<div class="line"><a id="l01416" name="l01416"></a><span class="lineno"> 1416</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_CONNECT_ROUTING_ID, connect_routing_id);</div>
<div class="line"><a id="l01417" name="l01417"></a><span class="lineno"> 1417</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01418" name="l01418"></a><span class="lineno"> 1418</span><span class="preprocessor">#ifdef ZMQ_CONNECT_TIMEOUT</span></div>
<div class="line"><a id="l01419" name="l01419"></a><span class="lineno"> 1419</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_CONNECT_TIMEOUT, connect_timeout, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01420" name="l01420"></a><span class="lineno"> 1420</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01421" name="l01421"></a><span class="lineno"> 1421</span><span class="preprocessor">#ifdef ZMQ_CURVE_PUBLICKEY</span></div>
<div class="line"><a id="l01422" name="l01422"></a><span class="lineno"> 1422</span>ZMQ_DEFINE_ARRAY_OPT_BIN_OR_Z85(ZMQ_CURVE_PUBLICKEY, curve_publickey);</div>
<div class="line"><a id="l01423" name="l01423"></a><span class="lineno"> 1423</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01424" name="l01424"></a><span class="lineno"> 1424</span><span class="preprocessor">#ifdef ZMQ_CURVE_SECRETKEY</span></div>
<div class="line"><a id="l01425" name="l01425"></a><span class="lineno"> 1425</span>ZMQ_DEFINE_ARRAY_OPT_BIN_OR_Z85(ZMQ_CURVE_SECRETKEY, curve_secretkey);</div>
<div class="line"><a id="l01426" name="l01426"></a><span class="lineno"> 1426</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01427" name="l01427"></a><span class="lineno"> 1427</span><span class="preprocessor">#ifdef ZMQ_CURVE_SERVER</span></div>
<div class="line"><a id="l01428" name="l01428"></a><span class="lineno"> 1428</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_CURVE_SERVER, curve_server, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01429" name="l01429"></a><span class="lineno"> 1429</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01430" name="l01430"></a><span class="lineno"> 1430</span><span class="preprocessor">#ifdef ZMQ_CURVE_SERVERKEY</span></div>
<div class="line"><a id="l01431" name="l01431"></a><span class="lineno"> 1431</span>ZMQ_DEFINE_ARRAY_OPT_BIN_OR_Z85(ZMQ_CURVE_SERVERKEY, curve_serverkey);</div>
<div class="line"><a id="l01432" name="l01432"></a><span class="lineno"> 1432</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01433" name="l01433"></a><span class="lineno"> 1433</span><span class="preprocessor">#ifdef ZMQ_EVENTS</span></div>
<div class="line"><a id="l01434" name="l01434"></a><span class="lineno"> 1434</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_EVENTS, events, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01435" name="l01435"></a><span class="lineno"> 1435</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01436" name="l01436"></a><span class="lineno"> 1436</span><span class="preprocessor">#ifdef ZMQ_FD</span></div>
<div class="line"><a id="l01437" name="l01437"></a><span class="lineno"> 1437</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_FD, fd, cppzmq_fd_t);</div>
<div class="line"><a id="l01438" name="l01438"></a><span class="lineno"> 1438</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01439" name="l01439"></a><span class="lineno"> 1439</span><span class="preprocessor">#ifdef ZMQ_GSSAPI_PLAINTEXT</span></div>
<div class="line"><a id="l01440" name="l01440"></a><span class="lineno"> 1440</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_GSSAPI_PLAINTEXT, gssapi_plaintext, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01441" name="l01441"></a><span class="lineno"> 1441</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01442" name="l01442"></a><span class="lineno"> 1442</span><span class="preprocessor">#ifdef ZMQ_GSSAPI_SERVER</span></div>
<div class="line"><a id="l01443" name="l01443"></a><span class="lineno"> 1443</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_GSSAPI_SERVER, gssapi_server, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01444" name="l01444"></a><span class="lineno"> 1444</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01445" name="l01445"></a><span class="lineno"> 1445</span><span class="preprocessor">#ifdef ZMQ_GSSAPI_SERVICE_PRINCIPAL</span></div>
<div class="line"><a id="l01446" name="l01446"></a><span class="lineno"> 1446</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_GSSAPI_SERVICE_PRINCIPAL, gssapi_service_principal);</div>
<div class="line"><a id="l01447" name="l01447"></a><span class="lineno"> 1447</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01448" name="l01448"></a><span class="lineno"> 1448</span><span class="preprocessor">#ifdef ZMQ_GSSAPI_SERVICE_PRINCIPAL_NAMETYPE</span></div>
<div class="line"><a id="l01449" name="l01449"></a><span class="lineno"> 1449</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_GSSAPI_SERVICE_PRINCIPAL_NAMETYPE,</div>
<div class="line"><a id="l01450" name="l01450"></a><span class="lineno"> 1450</span>                        gssapi_service_principal_nametype,</div>
<div class="line"><a id="l01451" name="l01451"></a><span class="lineno"> 1451</span>                        <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01452" name="l01452"></a><span class="lineno"> 1452</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01453" name="l01453"></a><span class="lineno"> 1453</span><span class="preprocessor">#ifdef ZMQ_GSSAPI_PRINCIPAL</span></div>
<div class="line"><a id="l01454" name="l01454"></a><span class="lineno"> 1454</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_GSSAPI_PRINCIPAL, gssapi_principal);</div>
<div class="line"><a id="l01455" name="l01455"></a><span class="lineno"> 1455</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01456" name="l01456"></a><span class="lineno"> 1456</span><span class="preprocessor">#ifdef ZMQ_GSSAPI_PRINCIPAL_NAMETYPE</span></div>
<div class="line"><a id="l01457" name="l01457"></a><span class="lineno"> 1457</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_GSSAPI_PRINCIPAL_NAMETYPE,</div>
<div class="line"><a id="l01458" name="l01458"></a><span class="lineno"> 1458</span>                        gssapi_principal_nametype,</div>
<div class="line"><a id="l01459" name="l01459"></a><span class="lineno"> 1459</span>                        <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01460" name="l01460"></a><span class="lineno"> 1460</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01461" name="l01461"></a><span class="lineno"> 1461</span><span class="preprocessor">#ifdef ZMQ_HANDSHAKE_IVL</span></div>
<div class="line"><a id="l01462" name="l01462"></a><span class="lineno"> 1462</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_HANDSHAKE_IVL, handshake_ivl, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01463" name="l01463"></a><span class="lineno"> 1463</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01464" name="l01464"></a><span class="lineno"> 1464</span><span class="preprocessor">#ifdef ZMQ_HEARTBEAT_IVL</span></div>
<div class="line"><a id="l01465" name="l01465"></a><span class="lineno"> 1465</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_HEARTBEAT_IVL, heartbeat_ivl, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01466" name="l01466"></a><span class="lineno"> 1466</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01467" name="l01467"></a><span class="lineno"> 1467</span><span class="preprocessor">#ifdef ZMQ_HEARTBEAT_TIMEOUT</span></div>
<div class="line"><a id="l01468" name="l01468"></a><span class="lineno"> 1468</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_HEARTBEAT_TIMEOUT, heartbeat_timeout, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01469" name="l01469"></a><span class="lineno"> 1469</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01470" name="l01470"></a><span class="lineno"> 1470</span><span class="preprocessor">#ifdef ZMQ_HEARTBEAT_TTL</span></div>
<div class="line"><a id="l01471" name="l01471"></a><span class="lineno"> 1471</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_HEARTBEAT_TTL, heartbeat_ttl, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01472" name="l01472"></a><span class="lineno"> 1472</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01473" name="l01473"></a><span class="lineno"> 1473</span><span class="preprocessor">#ifdef ZMQ_IMMEDIATE</span></div>
<div class="line"><a id="l01474" name="l01474"></a><span class="lineno"> 1474</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_IMMEDIATE, immediate, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01475" name="l01475"></a><span class="lineno"> 1475</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01476" name="l01476"></a><span class="lineno"> 1476</span><span class="preprocessor">#ifdef ZMQ_INVERT_MATCHING</span></div>
<div class="line"><a id="l01477" name="l01477"></a><span class="lineno"> 1477</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_INVERT_MATCHING, invert_matching, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01478" name="l01478"></a><span class="lineno"> 1478</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01479" name="l01479"></a><span class="lineno"> 1479</span><span class="preprocessor">#ifdef ZMQ_IPV6</span></div>
<div class="line"><a id="l01480" name="l01480"></a><span class="lineno"> 1480</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_IPV6, ipv6, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01481" name="l01481"></a><span class="lineno"> 1481</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01482" name="l01482"></a><span class="lineno"> 1482</span><span class="preprocessor">#ifdef ZMQ_LAST_ENDPOINT</span></div>
<div class="line"><a id="l01483" name="l01483"></a><span class="lineno"> 1483</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_LAST_ENDPOINT, last_endpoint);</div>
<div class="line"><a id="l01484" name="l01484"></a><span class="lineno"> 1484</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01485" name="l01485"></a><span class="lineno"> 1485</span><span class="preprocessor">#ifdef ZMQ_LINGER</span></div>
<div class="line"><a id="l01486" name="l01486"></a><span class="lineno"> 1486</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_LINGER, linger, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01487" name="l01487"></a><span class="lineno"> 1487</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01488" name="l01488"></a><span class="lineno"> 1488</span><span class="preprocessor">#ifdef ZMQ_MAXMSGSIZE</span></div>
<div class="line"><a id="l01489" name="l01489"></a><span class="lineno"> 1489</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_MAXMSGSIZE, maxmsgsize, int64_t);</div>
<div class="line"><a id="l01490" name="l01490"></a><span class="lineno"> 1490</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01491" name="l01491"></a><span class="lineno"> 1491</span><span class="preprocessor">#ifdef ZMQ_MECHANISM</span></div>
<div class="line"><a id="l01492" name="l01492"></a><span class="lineno"> 1492</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_MECHANISM, mechanism, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01493" name="l01493"></a><span class="lineno"> 1493</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01494" name="l01494"></a><span class="lineno"> 1494</span><span class="preprocessor">#ifdef ZMQ_METADATA</span></div>
<div class="line"><a id="l01495" name="l01495"></a><span class="lineno"> 1495</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_METADATA, metadata);</div>
<div class="line"><a id="l01496" name="l01496"></a><span class="lineno"> 1496</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01497" name="l01497"></a><span class="lineno"> 1497</span><span class="preprocessor">#ifdef ZMQ_MULTICAST_HOPS</span></div>
<div class="line"><a id="l01498" name="l01498"></a><span class="lineno"> 1498</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_MULTICAST_HOPS, multicast_hops, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01499" name="l01499"></a><span class="lineno"> 1499</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01500" name="l01500"></a><span class="lineno"> 1500</span><span class="preprocessor">#ifdef ZMQ_MULTICAST_LOOP</span></div>
<div class="line"><a id="l01501" name="l01501"></a><span class="lineno"> 1501</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_MULTICAST_LOOP, multicast_loop, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01502" name="l01502"></a><span class="lineno"> 1502</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01503" name="l01503"></a><span class="lineno"> 1503</span><span class="preprocessor">#ifdef ZMQ_MULTICAST_MAXTPDU</span></div>
<div class="line"><a id="l01504" name="l01504"></a><span class="lineno"> 1504</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_MULTICAST_MAXTPDU, multicast_maxtpdu, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01505" name="l01505"></a><span class="lineno"> 1505</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01506" name="l01506"></a><span class="lineno"> 1506</span><span class="preprocessor">#ifdef ZMQ_PLAIN_SERVER</span></div>
<div class="line"><a id="l01507" name="l01507"></a><span class="lineno"> 1507</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_PLAIN_SERVER, plain_server, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01508" name="l01508"></a><span class="lineno"> 1508</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01509" name="l01509"></a><span class="lineno"> 1509</span><span class="preprocessor">#ifdef ZMQ_PLAIN_PASSWORD</span></div>
<div class="line"><a id="l01510" name="l01510"></a><span class="lineno"> 1510</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_PLAIN_PASSWORD, plain_password);</div>
<div class="line"><a id="l01511" name="l01511"></a><span class="lineno"> 1511</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01512" name="l01512"></a><span class="lineno"> 1512</span><span class="preprocessor">#ifdef ZMQ_PLAIN_USERNAME</span></div>
<div class="line"><a id="l01513" name="l01513"></a><span class="lineno"> 1513</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_PLAIN_USERNAME, plain_username);</div>
<div class="line"><a id="l01514" name="l01514"></a><span class="lineno"> 1514</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01515" name="l01515"></a><span class="lineno"> 1515</span><span class="preprocessor">#ifdef ZMQ_USE_FD</span></div>
<div class="line"><a id="l01516" name="l01516"></a><span class="lineno"> 1516</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_USE_FD, use_fd, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01517" name="l01517"></a><span class="lineno"> 1517</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01518" name="l01518"></a><span class="lineno"> 1518</span><span class="preprocessor">#ifdef ZMQ_PROBE_ROUTER</span></div>
<div class="line"><a id="l01519" name="l01519"></a><span class="lineno"> 1519</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_PROBE_ROUTER, probe_router, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01520" name="l01520"></a><span class="lineno"> 1520</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01521" name="l01521"></a><span class="lineno"> 1521</span><span class="preprocessor">#ifdef ZMQ_RATE</span></div>
<div class="line"><a id="l01522" name="l01522"></a><span class="lineno"> 1522</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RATE, rate, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01523" name="l01523"></a><span class="lineno"> 1523</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01524" name="l01524"></a><span class="lineno"> 1524</span><span class="preprocessor">#ifdef ZMQ_RCVBUF</span></div>
<div class="line"><a id="l01525" name="l01525"></a><span class="lineno"> 1525</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RCVBUF, rcvbuf, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01526" name="l01526"></a><span class="lineno"> 1526</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01527" name="l01527"></a><span class="lineno"> 1527</span><span class="preprocessor">#ifdef ZMQ_RCVHWM</span></div>
<div class="line"><a id="l01528" name="l01528"></a><span class="lineno"> 1528</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RCVHWM, rcvhwm, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01529" name="l01529"></a><span class="lineno"> 1529</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01530" name="l01530"></a><span class="lineno"> 1530</span><span class="preprocessor">#ifdef ZMQ_RCVMORE</span></div>
<div class="line"><a id="l01531" name="l01531"></a><span class="lineno"> 1531</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_RCVMORE, rcvmore, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01532" name="l01532"></a><span class="lineno"> 1532</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01533" name="l01533"></a><span class="lineno"> 1533</span><span class="preprocessor">#ifdef ZMQ_RCVTIMEO</span></div>
<div class="line"><a id="l01534" name="l01534"></a><span class="lineno"> 1534</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RCVTIMEO, rcvtimeo, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01535" name="l01535"></a><span class="lineno"> 1535</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01536" name="l01536"></a><span class="lineno"> 1536</span><span class="preprocessor">#ifdef ZMQ_RECONNECT_IVL</span></div>
<div class="line"><a id="l01537" name="l01537"></a><span class="lineno"> 1537</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RECONNECT_IVL, reconnect_ivl, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01538" name="l01538"></a><span class="lineno"> 1538</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01539" name="l01539"></a><span class="lineno"> 1539</span><span class="preprocessor">#ifdef ZMQ_RECONNECT_IVL_MAX</span></div>
<div class="line"><a id="l01540" name="l01540"></a><span class="lineno"> 1540</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RECONNECT_IVL_MAX, reconnect_ivl_max, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01541" name="l01541"></a><span class="lineno"> 1541</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01542" name="l01542"></a><span class="lineno"> 1542</span><span class="preprocessor">#ifdef ZMQ_RECOVERY_IVL</span></div>
<div class="line"><a id="l01543" name="l01543"></a><span class="lineno"> 1543</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_RECOVERY_IVL, recovery_ivl, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01544" name="l01544"></a><span class="lineno"> 1544</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01545" name="l01545"></a><span class="lineno"> 1545</span><span class="preprocessor">#ifdef ZMQ_REQ_CORRELATE</span></div>
<div class="line"><a id="l01546" name="l01546"></a><span class="lineno"> 1546</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_REQ_CORRELATE, req_correlate, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01547" name="l01547"></a><span class="lineno"> 1547</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01548" name="l01548"></a><span class="lineno"> 1548</span><span class="preprocessor">#ifdef ZMQ_REQ_RELAXED</span></div>
<div class="line"><a id="l01549" name="l01549"></a><span class="lineno"> 1549</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_REQ_RELAXED, req_relaxed, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01550" name="l01550"></a><span class="lineno"> 1550</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01551" name="l01551"></a><span class="lineno"> 1551</span><span class="preprocessor">#ifdef ZMQ_ROUTER_HANDOVER</span></div>
<div class="line"><a id="l01552" name="l01552"></a><span class="lineno"> 1552</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_ROUTER_HANDOVER, router_handover, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01553" name="l01553"></a><span class="lineno"> 1553</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01554" name="l01554"></a><span class="lineno"> 1554</span><span class="preprocessor">#ifdef ZMQ_ROUTER_MANDATORY</span></div>
<div class="line"><a id="l01555" name="l01555"></a><span class="lineno"> 1555</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_ROUTER_MANDATORY, router_mandatory, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01556" name="l01556"></a><span class="lineno"> 1556</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01557" name="l01557"></a><span class="lineno"> 1557</span><span class="preprocessor">#ifdef ZMQ_ROUTER_NOTIFY</span></div>
<div class="line"><a id="l01558" name="l01558"></a><span class="lineno"> 1558</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_ROUTER_NOTIFY, router_notify, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01559" name="l01559"></a><span class="lineno"> 1559</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01560" name="l01560"></a><span class="lineno"> 1560</span><span class="preprocessor">#ifdef ZMQ_ROUTING_ID</span></div>
<div class="line"><a id="l01561" name="l01561"></a><span class="lineno"> 1561</span>ZMQ_DEFINE_ARRAY_OPT_BINARY(ZMQ_ROUTING_ID, routing_id);</div>
<div class="line"><a id="l01562" name="l01562"></a><span class="lineno"> 1562</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01563" name="l01563"></a><span class="lineno"> 1563</span><span class="preprocessor">#ifdef ZMQ_SNDBUF</span></div>
<div class="line"><a id="l01564" name="l01564"></a><span class="lineno"> 1564</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_SNDBUF, sndbuf, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01565" name="l01565"></a><span class="lineno"> 1565</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01566" name="l01566"></a><span class="lineno"> 1566</span><span class="preprocessor">#ifdef ZMQ_SNDHWM</span></div>
<div class="line"><a id="l01567" name="l01567"></a><span class="lineno"> 1567</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_SNDHWM, sndhwm, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01568" name="l01568"></a><span class="lineno"> 1568</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01569" name="l01569"></a><span class="lineno"> 1569</span><span class="preprocessor">#ifdef ZMQ_SNDTIMEO</span></div>
<div class="line"><a id="l01570" name="l01570"></a><span class="lineno"> 1570</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_SNDTIMEO, sndtimeo, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01571" name="l01571"></a><span class="lineno"> 1571</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01572" name="l01572"></a><span class="lineno"> 1572</span><span class="preprocessor">#ifdef ZMQ_SOCKS_PROXY</span></div>
<div class="line"><a id="l01573" name="l01573"></a><span class="lineno"> 1573</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_SOCKS_PROXY, socks_proxy);</div>
<div class="line"><a id="l01574" name="l01574"></a><span class="lineno"> 1574</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01575" name="l01575"></a><span class="lineno"> 1575</span><span class="preprocessor">#ifdef ZMQ_STREAM_NOTIFY</span></div>
<div class="line"><a id="l01576" name="l01576"></a><span class="lineno"> 1576</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_STREAM_NOTIFY, stream_notify, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01577" name="l01577"></a><span class="lineno"> 1577</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01578" name="l01578"></a><span class="lineno"> 1578</span><span class="preprocessor">#ifdef ZMQ_SUBSCRIBE</span></div>
<div class="line"><a id="l01579" name="l01579"></a><span class="lineno"> 1579</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_SUBSCRIBE, subscribe);</div>
<div class="line"><a id="l01580" name="l01580"></a><span class="lineno"> 1580</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01581" name="l01581"></a><span class="lineno"> 1581</span><span class="preprocessor">#ifdef ZMQ_TCP_KEEPALIVE</span></div>
<div class="line"><a id="l01582" name="l01582"></a><span class="lineno"> 1582</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TCP_KEEPALIVE, tcp_keepalive, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01583" name="l01583"></a><span class="lineno"> 1583</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01584" name="l01584"></a><span class="lineno"> 1584</span><span class="preprocessor">#ifdef ZMQ_TCP_KEEPALIVE_CNT</span></div>
<div class="line"><a id="l01585" name="l01585"></a><span class="lineno"> 1585</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TCP_KEEPALIVE_CNT, tcp_keepalive_cnt, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01586" name="l01586"></a><span class="lineno"> 1586</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01587" name="l01587"></a><span class="lineno"> 1587</span><span class="preprocessor">#ifdef ZMQ_TCP_KEEPALIVE_IDLE</span></div>
<div class="line"><a id="l01588" name="l01588"></a><span class="lineno"> 1588</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TCP_KEEPALIVE_IDLE, tcp_keepalive_idle, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01589" name="l01589"></a><span class="lineno"> 1589</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01590" name="l01590"></a><span class="lineno"> 1590</span><span class="preprocessor">#ifdef ZMQ_TCP_KEEPALIVE_INTVL</span></div>
<div class="line"><a id="l01591" name="l01591"></a><span class="lineno"> 1591</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TCP_KEEPALIVE_INTVL, tcp_keepalive_intvl, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01592" name="l01592"></a><span class="lineno"> 1592</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01593" name="l01593"></a><span class="lineno"> 1593</span><span class="preprocessor">#ifdef ZMQ_TCP_MAXRT</span></div>
<div class="line"><a id="l01594" name="l01594"></a><span class="lineno"> 1594</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TCP_MAXRT, tcp_maxrt, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01595" name="l01595"></a><span class="lineno"> 1595</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01596" name="l01596"></a><span class="lineno"> 1596</span><span class="preprocessor">#ifdef ZMQ_THREAD_SAFE</span></div>
<div class="line"><a id="l01597" name="l01597"></a><span class="lineno"> 1597</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_THREAD_SAFE, thread_safe, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01598" name="l01598"></a><span class="lineno"> 1598</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01599" name="l01599"></a><span class="lineno"> 1599</span><span class="preprocessor">#ifdef ZMQ_TOS</span></div>
<div class="line"><a id="l01600" name="l01600"></a><span class="lineno"> 1600</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TOS, tos, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01601" name="l01601"></a><span class="lineno"> 1601</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01602" name="l01602"></a><span class="lineno"> 1602</span><span class="preprocessor">#ifdef ZMQ_TYPE</span></div>
<div class="line"><a id="l01603" name="l01603"></a><span class="lineno"> 1603</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_TYPE, type, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01604" name="l01604"></a><span class="lineno"> 1604</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01605" name="l01605"></a><span class="lineno"> 1605</span><span class="preprocessor">#ifdef ZMQ_UNSUBSCRIBE</span></div>
<div class="line"><a id="l01606" name="l01606"></a><span class="lineno"> 1606</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_UNSUBSCRIBE, unsubscribe);</div>
<div class="line"><a id="l01607" name="l01607"></a><span class="lineno"> 1607</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01608" name="l01608"></a><span class="lineno"> 1608</span><span class="preprocessor">#ifdef ZMQ_VMCI_BUFFER_SIZE</span></div>
<div class="line"><a id="l01609" name="l01609"></a><span class="lineno"> 1609</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_VMCI_BUFFER_SIZE, vmci_buffer_size, uint64_t);</div>
<div class="line"><a id="l01610" name="l01610"></a><span class="lineno"> 1610</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01611" name="l01611"></a><span class="lineno"> 1611</span><span class="preprocessor">#ifdef ZMQ_VMCI_BUFFER_MIN_SIZE</span></div>
<div class="line"><a id="l01612" name="l01612"></a><span class="lineno"> 1612</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_VMCI_BUFFER_MIN_SIZE, vmci_buffer_min_size, uint64_t);</div>
<div class="line"><a id="l01613" name="l01613"></a><span class="lineno"> 1613</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01614" name="l01614"></a><span class="lineno"> 1614</span><span class="preprocessor">#ifdef ZMQ_VMCI_BUFFER_MAX_SIZE</span></div>
<div class="line"><a id="l01615" name="l01615"></a><span class="lineno"> 1615</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_VMCI_BUFFER_MAX_SIZE, vmci_buffer_max_size, uint64_t);</div>
<div class="line"><a id="l01616" name="l01616"></a><span class="lineno"> 1616</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01617" name="l01617"></a><span class="lineno"> 1617</span><span class="preprocessor">#ifdef ZMQ_VMCI_CONNECT_TIMEOUT</span></div>
<div class="line"><a id="l01618" name="l01618"></a><span class="lineno"> 1618</span>ZMQ_DEFINE_INTEGRAL_OPT(ZMQ_VMCI_CONNECT_TIMEOUT, vmci_connect_timeout, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01619" name="l01619"></a><span class="lineno"> 1619</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01620" name="l01620"></a><span class="lineno"> 1620</span><span class="preprocessor">#ifdef ZMQ_XPUB_VERBOSE</span></div>
<div class="line"><a id="l01621" name="l01621"></a><span class="lineno"> 1621</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_XPUB_VERBOSE, xpub_verbose, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01622" name="l01622"></a><span class="lineno"> 1622</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01623" name="l01623"></a><span class="lineno"> 1623</span><span class="preprocessor">#ifdef ZMQ_XPUB_VERBOSER</span></div>
<div class="line"><a id="l01624" name="l01624"></a><span class="lineno"> 1624</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_XPUB_VERBOSER, xpub_verboser, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01625" name="l01625"></a><span class="lineno"> 1625</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01626" name="l01626"></a><span class="lineno"> 1626</span><span class="preprocessor">#ifdef ZMQ_XPUB_MANUAL</span></div>
<div class="line"><a id="l01627" name="l01627"></a><span class="lineno"> 1627</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_XPUB_MANUAL, xpub_manual, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01628" name="l01628"></a><span class="lineno"> 1628</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01629" name="l01629"></a><span class="lineno"> 1629</span><span class="preprocessor">#ifdef ZMQ_XPUB_NODROP</span></div>
<div class="line"><a id="l01630" name="l01630"></a><span class="lineno"> 1630</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_XPUB_NODROP, xpub_nodrop, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01631" name="l01631"></a><span class="lineno"> 1631</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01632" name="l01632"></a><span class="lineno"> 1632</span><span class="preprocessor">#ifdef ZMQ_XPUB_WELCOME_MSG</span></div>
<div class="line"><a id="l01633" name="l01633"></a><span class="lineno"> 1633</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_XPUB_WELCOME_MSG, xpub_welcome_msg);</div>
<div class="line"><a id="l01634" name="l01634"></a><span class="lineno"> 1634</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01635" name="l01635"></a><span class="lineno"> 1635</span><span class="preprocessor">#ifdef ZMQ_ZAP_ENFORCE_DOMAIN</span></div>
<div class="line"><a id="l01636" name="l01636"></a><span class="lineno"> 1636</span>ZMQ_DEFINE_INTEGRAL_BOOL_UNIT_OPT(ZMQ_ZAP_ENFORCE_DOMAIN, zap_enforce_domain, <span class="keywordtype">int</span>);</div>
<div class="line"><a id="l01637" name="l01637"></a><span class="lineno"> 1637</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01638" name="l01638"></a><span class="lineno"> 1638</span><span class="preprocessor">#ifdef ZMQ_ZAP_DOMAIN</span></div>
<div class="line"><a id="l01639" name="l01639"></a><span class="lineno"> 1639</span>ZMQ_DEFINE_ARRAY_OPT(ZMQ_ZAP_DOMAIN, zap_domain);</div>
<div class="line"><a id="l01640" name="l01640"></a><span class="lineno"> 1640</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01641" name="l01641"></a><span class="lineno"> 1641</span> </div>
<div class="line"><a id="l01642" name="l01642"></a><span class="lineno"> 1642</span>} <span class="comment">// namespace sockopt</span></div>
<div class="line"><a id="l01643" name="l01643"></a><span class="lineno"> 1643</span><span class="preprocessor">#endif </span><span class="comment">// ZMQ_CPP11</span></div>
<div class="line"><a id="l01644" name="l01644"></a><span class="lineno"> 1644</span> </div>
<div class="line"><a id="l01645" name="l01645"></a><span class="lineno"> 1645</span> </div>
<div class="line"><a id="l01646" name="l01646"></a><span class="lineno"> 1646</span><span class="keyword">namespace </span>detail</div>
<div class="line"><a id="l01647" name="l01647"></a><span class="lineno"> 1647</span>{</div>
<div class="foldopen" id="foldopen01648" data-start="{" data-end="};">
<div class="line"><a id="l01648" name="l01648"></a><span class="lineno"><a class="line" href="classzmq_1_1detail_1_1socket__base.html"> 1648</a></span><span class="keyword">class </span>socket_base</div>
<div class="line"><a id="l01649" name="l01649"></a><span class="lineno"> 1649</span>{</div>
<div class="line"><a id="l01650" name="l01650"></a><span class="lineno"> 1650</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l01651" name="l01651"></a><span class="lineno"> 1651</span>    socket_base() ZMQ_NOTHROW : _handle(ZMQ_NULLPTR) {}</div>
<div class="line"><a id="l01652" name="l01652"></a><span class="lineno"> 1652</span>    ZMQ_EXPLICIT socket_base(<span class="keywordtype">void</span> *handle) ZMQ_NOTHROW : _handle(handle) {}</div>
<div class="line"><a id="l01653" name="l01653"></a><span class="lineno"> 1653</span> </div>
<div class="line"><a id="l01654" name="l01654"></a><span class="lineno"> 1654</span>    <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a id="l01655" name="l01655"></a><span class="lineno"> 1655</span>    ZMQ_CPP11_DEPRECATED(<span class="stringliteral">&quot;from 4.7.0, use `set` taking option from zmq::sockopt&quot;</span>)</div>
<div class="line"><a id="l01656" name="l01656"></a><span class="lineno"> 1656</span>    <span class="keywordtype">void</span> setsockopt(<span class="keywordtype">int</span> option_, T <span class="keyword">const</span> &amp;optval)</div>
<div class="line"><a id="l01657" name="l01657"></a><span class="lineno"> 1657</span>    {</div>
<div class="line"><a id="l01658" name="l01658"></a><span class="lineno"> 1658</span>        setsockopt(option_, &amp;optval, <span class="keyword">sizeof</span>(T));</div>
<div class="line"><a id="l01659" name="l01659"></a><span class="lineno"> 1659</span>    }</div>
<div class="line"><a id="l01660" name="l01660"></a><span class="lineno"> 1660</span> </div>
<div class="line"><a id="l01661" name="l01661"></a><span class="lineno"> 1661</span>    ZMQ_CPP11_DEPRECATED(<span class="stringliteral">&quot;from 4.7.0, use `set` taking option from zmq::sockopt&quot;</span>)</div>
<div class="line"><a id="l01662" name="l01662"></a><span class="lineno"> 1662</span>    <span class="keywordtype">void</span> setsockopt(<span class="keywordtype">int</span> option_, <span class="keyword">const</span> <span class="keywordtype">void</span> *optval_, <span class="keywordtype">size_t</span> optvallen_)</div>
<div class="line"><a id="l01663" name="l01663"></a><span class="lineno"> 1663</span>    {</div>
<div class="line"><a id="l01664" name="l01664"></a><span class="lineno"> 1664</span>        <span class="keywordtype">int</span> rc = zmq_setsockopt(_handle, option_, optval_, optvallen_);</div>
<div class="line"><a id="l01665" name="l01665"></a><span class="lineno"> 1665</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l01666" name="l01666"></a><span class="lineno"> 1666</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01667" name="l01667"></a><span class="lineno"> 1667</span>    }</div>
<div class="line"><a id="l01668" name="l01668"></a><span class="lineno"> 1668</span> </div>
<div class="line"><a id="l01669" name="l01669"></a><span class="lineno"> 1669</span>    ZMQ_CPP11_DEPRECATED(<span class="stringliteral">&quot;from 4.7.0, use `get` taking option from zmq::sockopt&quot;</span>)</div>
<div class="line"><a id="l01670" name="l01670"></a><span class="lineno"> 1670</span>    <span class="keywordtype">void</span> getsockopt(<span class="keywordtype">int</span> option_, <span class="keywordtype">void</span> *optval_, <span class="keywordtype">size_t</span> *optvallen_)<span class="keyword"> const</span></div>
<div class="line"><a id="l01671" name="l01671"></a><span class="lineno"> 1671</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l01672" name="l01672"></a><span class="lineno"> 1672</span>        <span class="keywordtype">int</span> rc = zmq_getsockopt(_handle, option_, optval_, optvallen_);</div>
<div class="line"><a id="l01673" name="l01673"></a><span class="lineno"> 1673</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l01674" name="l01674"></a><span class="lineno"> 1674</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01675" name="l01675"></a><span class="lineno"> 1675</span>    }</div>
<div class="line"><a id="l01676" name="l01676"></a><span class="lineno"> 1676</span> </div>
<div class="line"><a id="l01677" name="l01677"></a><span class="lineno"> 1677</span>    <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a id="l01678" name="l01678"></a><span class="lineno"> 1678</span>    ZMQ_CPP11_DEPRECATED(<span class="stringliteral">&quot;from 4.7.0, use `get` taking option from zmq::sockopt&quot;</span>)</div>
<div class="line"><a id="l01679" name="l01679"></a><span class="lineno"> 1679</span>    T getsockopt(<span class="keywordtype">int</span> option_)<span class="keyword"> const</span></div>
<div class="line"><a id="l01680" name="l01680"></a><span class="lineno"> 1680</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l01681" name="l01681"></a><span class="lineno"> 1681</span>        T optval;</div>
<div class="line"><a id="l01682" name="l01682"></a><span class="lineno"> 1682</span>        <span class="keywordtype">size_t</span> optlen = <span class="keyword">sizeof</span>(T);</div>
<div class="line"><a id="l01683" name="l01683"></a><span class="lineno"> 1683</span>        getsockopt(option_, &amp;optval, &amp;optlen);</div>
<div class="line"><a id="l01684" name="l01684"></a><span class="lineno"> 1684</span>        <span class="keywordflow">return</span> optval;</div>
<div class="line"><a id="l01685" name="l01685"></a><span class="lineno"> 1685</span>    }</div>
<div class="line"><a id="l01686" name="l01686"></a><span class="lineno"> 1686</span> </div>
<div class="line"><a id="l01687" name="l01687"></a><span class="lineno"> 1687</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l01688" name="l01688"></a><span class="lineno"> 1688</span>    <span class="comment">// Set integral socket option, e.g.</span></div>
<div class="line"><a id="l01689" name="l01689"></a><span class="lineno"> 1689</span>    <span class="comment">// `socket.set(zmq::sockopt::linger, 0)`</span></div>
<div class="line"><a id="l01690" name="l01690"></a><span class="lineno"> 1690</span>    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keyword">class</span> T, <span class="keywordtype">bool</span> BoolUnit&gt;</div>
<div class="line"><a id="l01691" name="l01691"></a><span class="lineno"> 1691</span>    <span class="keywordtype">void</span> set(sockopt::integral_option&lt;Opt, T, BoolUnit&gt;, <span class="keyword">const</span> T &amp;val)</div>
<div class="line"><a id="l01692" name="l01692"></a><span class="lineno"> 1692</span>    {</div>
<div class="line"><a id="l01693" name="l01693"></a><span class="lineno"> 1693</span>        <span class="keyword">static_assert</span>(std::is_integral&lt;T&gt;::value, <span class="stringliteral">&quot;T must be integral&quot;</span>);</div>
<div class="line"><a id="l01694" name="l01694"></a><span class="lineno"> 1694</span>        set_option(Opt, &amp;val, <span class="keyword">sizeof</span> val);</div>
<div class="line"><a id="l01695" name="l01695"></a><span class="lineno"> 1695</span>    }</div>
<div class="line"><a id="l01696" name="l01696"></a><span class="lineno"> 1696</span> </div>
<div class="line"><a id="l01697" name="l01697"></a><span class="lineno"> 1697</span>    <span class="comment">// Set integral socket option from boolean, e.g.</span></div>
<div class="line"><a id="l01698" name="l01698"></a><span class="lineno"> 1698</span>    <span class="comment">// `socket.set(zmq::sockopt::immediate, false)`</span></div>
<div class="line"><a id="l01699" name="l01699"></a><span class="lineno"> 1699</span>    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keyword">class</span> T&gt;</div>
<div class="line"><a id="l01700" name="l01700"></a><span class="lineno"> 1700</span>    <span class="keywordtype">void</span> set(sockopt::integral_option&lt;Opt, T, true&gt;, <span class="keywordtype">bool</span> val)</div>
<div class="line"><a id="l01701" name="l01701"></a><span class="lineno"> 1701</span>    {</div>
<div class="line"><a id="l01702" name="l01702"></a><span class="lineno"> 1702</span>        <span class="keyword">static_assert</span>(std::is_integral&lt;T&gt;::value, <span class="stringliteral">&quot;T must be integral&quot;</span>);</div>
<div class="line"><a id="l01703" name="l01703"></a><span class="lineno"> 1703</span>        T rep_val = val;</div>
<div class="line"><a id="l01704" name="l01704"></a><span class="lineno"> 1704</span>        set_option(Opt, &amp;rep_val, <span class="keyword">sizeof</span> rep_val);</div>
<div class="line"><a id="l01705" name="l01705"></a><span class="lineno"> 1705</span>    }</div>
<div class="line"><a id="l01706" name="l01706"></a><span class="lineno"> 1706</span> </div>
<div class="line"><a id="l01707" name="l01707"></a><span class="lineno"> 1707</span>    <span class="comment">// Set array socket option, e.g.</span></div>
<div class="line"><a id="l01708" name="l01708"></a><span class="lineno"> 1708</span>    <span class="comment">// `socket.set(zmq::sockopt::plain_username, &quot;foo123&quot;)`</span></div>
<div class="line"><a id="l01709" name="l01709"></a><span class="lineno"> 1709</span>    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keywordtype">int</span> NullTerm&gt;</div>
<div class="line"><a id="l01710" name="l01710"></a><span class="lineno"> 1710</span>    <span class="keywordtype">void</span> set(sockopt::array_option&lt;Opt, NullTerm&gt;, <span class="keyword">const</span> <span class="keywordtype">char</span> *buf)</div>
<div class="line"><a id="l01711" name="l01711"></a><span class="lineno"> 1711</span>    {</div>
<div class="line"><a id="l01712" name="l01712"></a><span class="lineno"> 1712</span>        set_option(Opt, buf, std::strlen(buf));</div>
<div class="line"><a id="l01713" name="l01713"></a><span class="lineno"> 1713</span>    }</div>
<div class="line"><a id="l01714" name="l01714"></a><span class="lineno"> 1714</span> </div>
<div class="line"><a id="l01715" name="l01715"></a><span class="lineno"> 1715</span>    <span class="comment">// Set array socket option, e.g.</span></div>
<div class="line"><a id="l01716" name="l01716"></a><span class="lineno"> 1716</span>    <span class="comment">// `socket.set(zmq::sockopt::routing_id, zmq::buffer(id))`</span></div>
<div class="line"><a id="l01717" name="l01717"></a><span class="lineno"> 1717</span>    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keywordtype">int</span> NullTerm&gt;</div>
<div class="line"><a id="l01718" name="l01718"></a><span class="lineno"> 1718</span>    <span class="keywordtype">void</span> set(sockopt::array_option&lt;Opt, NullTerm&gt;, const_buffer buf)</div>
<div class="line"><a id="l01719" name="l01719"></a><span class="lineno"> 1719</span>    {</div>
<div class="line"><a id="l01720" name="l01720"></a><span class="lineno"> 1720</span>        set_option(Opt, buf.data(), buf.size());</div>
<div class="line"><a id="l01721" name="l01721"></a><span class="lineno"> 1721</span>    }</div>
<div class="line"><a id="l01722" name="l01722"></a><span class="lineno"> 1722</span> </div>
<div class="line"><a id="l01723" name="l01723"></a><span class="lineno"> 1723</span>    <span class="comment">// Set array socket option, e.g.</span></div>
<div class="line"><a id="l01724" name="l01724"></a><span class="lineno"> 1724</span>    <span class="comment">// `socket.set(zmq::sockopt::routing_id, id_str)`</span></div>
<div class="line"><a id="l01725" name="l01725"></a><span class="lineno"> 1725</span>    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keywordtype">int</span> NullTerm&gt;</div>
<div class="line"><a id="l01726" name="l01726"></a><span class="lineno"> 1726</span>    <span class="keywordtype">void</span> set(sockopt::array_option&lt;Opt, NullTerm&gt;, <span class="keyword">const</span> std::string &amp;buf)</div>
<div class="line"><a id="l01727" name="l01727"></a><span class="lineno"> 1727</span>    {</div>
<div class="line"><a id="l01728" name="l01728"></a><span class="lineno"> 1728</span>        set_option(Opt, buf.data(), buf.size());</div>
<div class="line"><a id="l01729" name="l01729"></a><span class="lineno"> 1729</span>    }</div>
<div class="line"><a id="l01730" name="l01730"></a><span class="lineno"> 1730</span> </div>
<div class="line"><a id="l01731" name="l01731"></a><span class="lineno"> 1731</span><span class="preprocessor">#if CPPZMQ_HAS_STRING_VIEW</span></div>
<div class="line"><a id="l01732" name="l01732"></a><span class="lineno"> 1732</span>    <span class="comment">// Set array socket option, e.g.</span></div>
<div class="line"><a id="l01733" name="l01733"></a><span class="lineno"> 1733</span>    <span class="comment">// `socket.set(zmq::sockopt::routing_id, id_str)`</span></div>
<div class="line"><a id="l01734" name="l01734"></a><span class="lineno"> 1734</span>    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keywordtype">int</span> NullTerm&gt;</div>
<div class="line"><a id="l01735" name="l01735"></a><span class="lineno"> 1735</span>    <span class="keywordtype">void</span> set(sockopt::array_option&lt;Opt, NullTerm&gt;, std::string_view buf)</div>
<div class="line"><a id="l01736" name="l01736"></a><span class="lineno"> 1736</span>    {</div>
<div class="line"><a id="l01737" name="l01737"></a><span class="lineno"> 1737</span>        set_option(Opt, buf.data(), buf.size());</div>
<div class="line"><a id="l01738" name="l01738"></a><span class="lineno"> 1738</span>    }</div>
<div class="line"><a id="l01739" name="l01739"></a><span class="lineno"> 1739</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01740" name="l01740"></a><span class="lineno"> 1740</span> </div>
<div class="line"><a id="l01741" name="l01741"></a><span class="lineno"> 1741</span>    <span class="comment">// Get scalar socket option, e.g.</span></div>
<div class="line"><a id="l01742" name="l01742"></a><span class="lineno"> 1742</span>    <span class="comment">// `auto opt = socket.get(zmq::sockopt::linger)`</span></div>
<div class="line"><a id="l01743" name="l01743"></a><span class="lineno"> 1743</span>    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keyword">class</span> T, <span class="keywordtype">bool</span> BoolUnit&gt;</div>
<div class="line"><a id="l01744" name="l01744"></a><span class="lineno"> 1744</span>    ZMQ_NODISCARD T get(sockopt::integral_option&lt;Opt, T, BoolUnit&gt;)<span class="keyword"> const</span></div>
<div class="line"><a id="l01745" name="l01745"></a><span class="lineno"> 1745</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l01746" name="l01746"></a><span class="lineno"> 1746</span>        <span class="keyword">static_assert</span>(std::is_integral&lt;T&gt;::value, <span class="stringliteral">&quot;T must be integral&quot;</span>);</div>
<div class="line"><a id="l01747" name="l01747"></a><span class="lineno"> 1747</span>        T val;</div>
<div class="line"><a id="l01748" name="l01748"></a><span class="lineno"> 1748</span>        <span class="keywordtype">size_t</span> size = <span class="keyword">sizeof</span> val;</div>
<div class="line"><a id="l01749" name="l01749"></a><span class="lineno"> 1749</span>        get_option(Opt, &amp;val, &amp;size);</div>
<div class="line"><a id="l01750" name="l01750"></a><span class="lineno"> 1750</span>        assert(size == <span class="keyword">sizeof</span> val);</div>
<div class="line"><a id="l01751" name="l01751"></a><span class="lineno"> 1751</span>        <span class="keywordflow">return</span> val;</div>
<div class="line"><a id="l01752" name="l01752"></a><span class="lineno"> 1752</span>    }</div>
<div class="line"><a id="l01753" name="l01753"></a><span class="lineno"> 1753</span> </div>
<div class="line"><a id="l01754" name="l01754"></a><span class="lineno"> 1754</span>    <span class="comment">// Get array socket option, writes to buf, returns option size in bytes, e.g.</span></div>
<div class="line"><a id="l01755" name="l01755"></a><span class="lineno"> 1755</span>    <span class="comment">// `size_t optsize = socket.get(zmq::sockopt::routing_id, zmq::buffer(id))`</span></div>
<div class="line"><a id="l01756" name="l01756"></a><span class="lineno"> 1756</span>    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keywordtype">int</span> NullTerm&gt;</div>
<div class="line"><a id="l01757" name="l01757"></a><span class="lineno"> 1757</span>    ZMQ_NODISCARD <span class="keywordtype">size_t</span> get(sockopt::array_option&lt;Opt, NullTerm&gt;,</div>
<div class="line"><a id="l01758" name="l01758"></a><span class="lineno"> 1758</span>                             mutable_buffer buf)<span class="keyword"> const</span></div>
<div class="line"><a id="l01759" name="l01759"></a><span class="lineno"> 1759</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l01760" name="l01760"></a><span class="lineno"> 1760</span>        <span class="keywordtype">size_t</span> size = buf.size();</div>
<div class="line"><a id="l01761" name="l01761"></a><span class="lineno"> 1761</span>        get_option(Opt, buf.data(), &amp;size);</div>
<div class="line"><a id="l01762" name="l01762"></a><span class="lineno"> 1762</span>        <span class="keywordflow">return</span> size;</div>
<div class="line"><a id="l01763" name="l01763"></a><span class="lineno"> 1763</span>    }</div>
<div class="line"><a id="l01764" name="l01764"></a><span class="lineno"> 1764</span> </div>
<div class="line"><a id="l01765" name="l01765"></a><span class="lineno"> 1765</span>    <span class="comment">// Get array socket option as string (initializes the string buffer size to init_size) e.g.</span></div>
<div class="line"><a id="l01766" name="l01766"></a><span class="lineno"> 1766</span>    <span class="comment">// `auto s = socket.get(zmq::sockopt::routing_id)`</span></div>
<div class="line"><a id="l01767" name="l01767"></a><span class="lineno"> 1767</span>    <span class="comment">// Note: removes the null character from null-terminated string options,</span></div>
<div class="line"><a id="l01768" name="l01768"></a><span class="lineno"> 1768</span>    <span class="comment">// i.e. the string size excludes the null character.</span></div>
<div class="line"><a id="l01769" name="l01769"></a><span class="lineno"> 1769</span>    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> Opt, <span class="keywordtype">int</span> NullTerm&gt;</div>
<div class="line"><a id="l01770" name="l01770"></a><span class="lineno"> 1770</span>    ZMQ_NODISCARD std::string get(sockopt::array_option&lt;Opt, NullTerm&gt;,</div>
<div class="line"><a id="l01771" name="l01771"></a><span class="lineno"> 1771</span>                                  <span class="keywordtype">size_t</span> init_size = 1024)<span class="keyword"> const</span></div>
<div class="line"><a id="l01772" name="l01772"></a><span class="lineno"> 1772</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l01773" name="l01773"></a><span class="lineno"> 1773</span>        <span class="keywordflow">if</span> (NullTerm == 2 &amp;&amp; init_size == 1024) {</div>
<div class="line"><a id="l01774" name="l01774"></a><span class="lineno"> 1774</span>            init_size = 41; <span class="comment">// get as Z85 string</span></div>
<div class="line"><a id="l01775" name="l01775"></a><span class="lineno"> 1775</span>        }</div>
<div class="line"><a id="l01776" name="l01776"></a><span class="lineno"> 1776</span>        std::string str(init_size, <span class="charliteral">&#39;\0&#39;</span>);</div>
<div class="line"><a id="l01777" name="l01777"></a><span class="lineno"> 1777</span>        <span class="keywordtype">size_t</span> size = get(sockopt::array_option&lt;Opt&gt;{}, <a class="code hl_struct" href="structbuffer.html">buffer</a>(str));</div>
<div class="line"><a id="l01778" name="l01778"></a><span class="lineno"> 1778</span>        <span class="keywordflow">if</span> (NullTerm == 1) {</div>
<div class="line"><a id="l01779" name="l01779"></a><span class="lineno"> 1779</span>            <span class="keywordflow">if</span> (size &gt; 0) {</div>
<div class="line"><a id="l01780" name="l01780"></a><span class="lineno"> 1780</span>                assert(str[size - 1] == <span class="charliteral">&#39;\0&#39;</span>);</div>
<div class="line"><a id="l01781" name="l01781"></a><span class="lineno"> 1781</span>                --size;</div>
<div class="line"><a id="l01782" name="l01782"></a><span class="lineno"> 1782</span>            }</div>
<div class="line"><a id="l01783" name="l01783"></a><span class="lineno"> 1783</span>        } <span class="keywordflow">else</span> <span class="keywordflow">if</span> (NullTerm == 2) {</div>
<div class="line"><a id="l01784" name="l01784"></a><span class="lineno"> 1784</span>            assert(size == 32 || size == 41);</div>
<div class="line"><a id="l01785" name="l01785"></a><span class="lineno"> 1785</span>            <span class="keywordflow">if</span> (size == 41) {</div>
<div class="line"><a id="l01786" name="l01786"></a><span class="lineno"> 1786</span>                assert(str[size - 1] == <span class="charliteral">&#39;\0&#39;</span>);</div>
<div class="line"><a id="l01787" name="l01787"></a><span class="lineno"> 1787</span>                --size;</div>
<div class="line"><a id="l01788" name="l01788"></a><span class="lineno"> 1788</span>            }</div>
<div class="line"><a id="l01789" name="l01789"></a><span class="lineno"> 1789</span>        }</div>
<div class="line"><a id="l01790" name="l01790"></a><span class="lineno"> 1790</span>        str.resize(size);</div>
<div class="line"><a id="l01791" name="l01791"></a><span class="lineno"> 1791</span>        <span class="keywordflow">return</span> str;</div>
<div class="line"><a id="l01792" name="l01792"></a><span class="lineno"> 1792</span>    }</div>
<div class="line"><a id="l01793" name="l01793"></a><span class="lineno"> 1793</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01794" name="l01794"></a><span class="lineno"> 1794</span> </div>
<div class="line"><a id="l01795" name="l01795"></a><span class="lineno"> 1795</span>    <span class="keywordtype">void</span> bind(std::string <span class="keyword">const</span> &amp;addr) { bind(addr.c_str()); }</div>
<div class="line"><a id="l01796" name="l01796"></a><span class="lineno"> 1796</span> </div>
<div class="line"><a id="l01797" name="l01797"></a><span class="lineno"> 1797</span>    <span class="keywordtype">void</span> bind(<span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l01798" name="l01798"></a><span class="lineno"> 1798</span>    {</div>
<div class="line"><a id="l01799" name="l01799"></a><span class="lineno"> 1799</span>        <span class="keywordtype">int</span> rc = zmq_bind(_handle, addr_);</div>
<div class="line"><a id="l01800" name="l01800"></a><span class="lineno"> 1800</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l01801" name="l01801"></a><span class="lineno"> 1801</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01802" name="l01802"></a><span class="lineno"> 1802</span>    }</div>
<div class="line"><a id="l01803" name="l01803"></a><span class="lineno"> 1803</span> </div>
<div class="line"><a id="l01804" name="l01804"></a><span class="lineno"> 1804</span>    <span class="keywordtype">void</span> unbind(std::string <span class="keyword">const</span> &amp;addr) { unbind(addr.c_str()); }</div>
<div class="line"><a id="l01805" name="l01805"></a><span class="lineno"> 1805</span> </div>
<div class="line"><a id="l01806" name="l01806"></a><span class="lineno"> 1806</span>    <span class="keywordtype">void</span> unbind(<span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l01807" name="l01807"></a><span class="lineno"> 1807</span>    {</div>
<div class="line"><a id="l01808" name="l01808"></a><span class="lineno"> 1808</span>        <span class="keywordtype">int</span> rc = zmq_unbind(_handle, addr_);</div>
<div class="line"><a id="l01809" name="l01809"></a><span class="lineno"> 1809</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l01810" name="l01810"></a><span class="lineno"> 1810</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01811" name="l01811"></a><span class="lineno"> 1811</span>    }</div>
<div class="line"><a id="l01812" name="l01812"></a><span class="lineno"> 1812</span> </div>
<div class="line"><a id="l01813" name="l01813"></a><span class="lineno"> 1813</span>    <span class="keywordtype">void</span> connect(std::string <span class="keyword">const</span> &amp;addr) { connect(addr.c_str()); }</div>
<div class="line"><a id="l01814" name="l01814"></a><span class="lineno"> 1814</span> </div>
<div class="line"><a id="l01815" name="l01815"></a><span class="lineno"> 1815</span>    <span class="keywordtype">void</span> connect(<span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l01816" name="l01816"></a><span class="lineno"> 1816</span>    {</div>
<div class="line"><a id="l01817" name="l01817"></a><span class="lineno"> 1817</span>        <span class="keywordtype">int</span> rc = zmq_connect(_handle, addr_);</div>
<div class="line"><a id="l01818" name="l01818"></a><span class="lineno"> 1818</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l01819" name="l01819"></a><span class="lineno"> 1819</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01820" name="l01820"></a><span class="lineno"> 1820</span>    }</div>
<div class="line"><a id="l01821" name="l01821"></a><span class="lineno"> 1821</span> </div>
<div class="line"><a id="l01822" name="l01822"></a><span class="lineno"> 1822</span>    <span class="keywordtype">void</span> disconnect(std::string <span class="keyword">const</span> &amp;addr) { disconnect(addr.c_str()); }</div>
<div class="line"><a id="l01823" name="l01823"></a><span class="lineno"> 1823</span> </div>
<div class="line"><a id="l01824" name="l01824"></a><span class="lineno"> 1824</span>    <span class="keywordtype">void</span> disconnect(<span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l01825" name="l01825"></a><span class="lineno"> 1825</span>    {</div>
<div class="line"><a id="l01826" name="l01826"></a><span class="lineno"> 1826</span>        <span class="keywordtype">int</span> rc = zmq_disconnect(_handle, addr_);</div>
<div class="line"><a id="l01827" name="l01827"></a><span class="lineno"> 1827</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l01828" name="l01828"></a><span class="lineno"> 1828</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01829" name="l01829"></a><span class="lineno"> 1829</span>    }</div>
<div class="line"><a id="l01830" name="l01830"></a><span class="lineno"> 1830</span> </div>
<div class="line"><a id="l01831" name="l01831"></a><span class="lineno"> 1831</span>    <span class="keywordtype">bool</span> connected() <span class="keyword">const</span> ZMQ_NOTHROW { <span class="keywordflow">return</span> (_handle != ZMQ_NULLPTR); }</div>
<div class="line"><a id="l01832" name="l01832"></a><span class="lineno"> 1832</span> </div>
<div class="line"><a id="l01833" name="l01833"></a><span class="lineno"> 1833</span>    ZMQ_CPP11_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use send taking a const_buffer and send_flags&quot;</span>)</div>
<div class="line"><a id="l01834" name="l01834"></a><span class="lineno"> 1834</span>    <span class="keywordtype">size_t</span> send(<span class="keyword">const</span> <span class="keywordtype">void</span> *buf_, <span class="keywordtype">size_t</span> len_, <span class="keywordtype">int</span> flags_ = 0)</div>
<div class="line"><a id="l01835" name="l01835"></a><span class="lineno"> 1835</span>    {</div>
<div class="line"><a id="l01836" name="l01836"></a><span class="lineno"> 1836</span>        <span class="keywordtype">int</span> nbytes = zmq_send(_handle, buf_, len_, flags_);</div>
<div class="line"><a id="l01837" name="l01837"></a><span class="lineno"> 1837</span>        <span class="keywordflow">if</span> (nbytes &gt;= 0)</div>
<div class="line"><a id="l01838" name="l01838"></a><span class="lineno"> 1838</span>            <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(nbytes);</div>
<div class="line"><a id="l01839" name="l01839"></a><span class="lineno"> 1839</span>        <span class="keywordflow">if</span> (zmq_errno() == EAGAIN)</div>
<div class="line"><a id="l01840" name="l01840"></a><span class="lineno"> 1840</span>            <span class="keywordflow">return</span> 0;</div>
<div class="line"><a id="l01841" name="l01841"></a><span class="lineno"> 1841</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01842" name="l01842"></a><span class="lineno"> 1842</span>    }</div>
<div class="line"><a id="l01843" name="l01843"></a><span class="lineno"> 1843</span> </div>
<div class="line"><a id="l01844" name="l01844"></a><span class="lineno"> 1844</span>    ZMQ_CPP11_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use send taking message_t and send_flags&quot;</span>)</div>
<div class="line"><a id="l01845" name="l01845"></a><span class="lineno"> 1845</span>    <span class="keywordtype">bool</span> send(<a class="code hl_class" href="classzmq_1_1message__t.html">message_t</a> &amp;msg_,</div>
<div class="line"><a id="l01846" name="l01846"></a><span class="lineno"> 1846</span>              <span class="keywordtype">int</span> flags_ = 0) <span class="comment">// default until removed</span></div>
<div class="line"><a id="l01847" name="l01847"></a><span class="lineno"> 1847</span>    {</div>
<div class="line"><a id="l01848" name="l01848"></a><span class="lineno"> 1848</span>        <span class="keywordtype">int</span> nbytes = zmq_msg_send(msg_.handle(), _handle, flags_);</div>
<div class="line"><a id="l01849" name="l01849"></a><span class="lineno"> 1849</span>        <span class="keywordflow">if</span> (nbytes &gt;= 0)</div>
<div class="line"><a id="l01850" name="l01850"></a><span class="lineno"> 1850</span>            <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a id="l01851" name="l01851"></a><span class="lineno"> 1851</span>        <span class="keywordflow">if</span> (zmq_errno() == EAGAIN)</div>
<div class="line"><a id="l01852" name="l01852"></a><span class="lineno"> 1852</span>            <span class="keywordflow">return</span> <span class="keyword">false</span>;</div>
<div class="line"><a id="l01853" name="l01853"></a><span class="lineno"> 1853</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01854" name="l01854"></a><span class="lineno"> 1854</span>    }</div>
<div class="line"><a id="l01855" name="l01855"></a><span class="lineno"> 1855</span> </div>
<div class="line"><a id="l01856" name="l01856"></a><span class="lineno"> 1856</span>    <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a id="l01857" name="l01857"></a><span class="lineno"> 1857</span>    ZMQ_CPP11_DEPRECATED(</div>
<div class="line"><a id="l01858" name="l01858"></a><span class="lineno"> 1858</span>      <span class="stringliteral">&quot;from 4.4.1, use send taking message_t or buffer (for contiguous &quot;</span></div>
<div class="line"><a id="l01859" name="l01859"></a><span class="lineno"> 1859</span>      <span class="stringliteral">&quot;ranges), and send_flags&quot;</span>)</div>
<div class="line"><a id="l01860" name="l01860"></a><span class="lineno"> 1860</span>    <span class="keywordtype">bool</span> send(T first, T last, <span class="keywordtype">int</span> flags_ = 0)</div>
<div class="line"><a id="l01861" name="l01861"></a><span class="lineno"> 1861</span>    {</div>
<div class="line"><a id="l01862" name="l01862"></a><span class="lineno"> 1862</span>        <a class="code hl_class" href="classzmq_1_1message__t.html">zmq::message_t</a> msg(first, last);</div>
<div class="line"><a id="l01863" name="l01863"></a><span class="lineno"> 1863</span>        <span class="keywordtype">int</span> nbytes = zmq_msg_send(msg.handle(), _handle, flags_);</div>
<div class="line"><a id="l01864" name="l01864"></a><span class="lineno"> 1864</span>        <span class="keywordflow">if</span> (nbytes &gt;= 0)</div>
<div class="line"><a id="l01865" name="l01865"></a><span class="lineno"> 1865</span>            <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a id="l01866" name="l01866"></a><span class="lineno"> 1866</span>        <span class="keywordflow">if</span> (zmq_errno() == EAGAIN)</div>
<div class="line"><a id="l01867" name="l01867"></a><span class="lineno"> 1867</span>            <span class="keywordflow">return</span> <span class="keyword">false</span>;</div>
<div class="line"><a id="l01868" name="l01868"></a><span class="lineno"> 1868</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01869" name="l01869"></a><span class="lineno"> 1869</span>    }</div>
<div class="line"><a id="l01870" name="l01870"></a><span class="lineno"> 1870</span> </div>
<div class="line"><a id="l01871" name="l01871"></a><span class="lineno"> 1871</span><span class="preprocessor">#ifdef ZMQ_HAS_RVALUE_REFS</span></div>
<div class="line"><a id="l01872" name="l01872"></a><span class="lineno"> 1872</span>    ZMQ_CPP11_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use send taking message_t and send_flags&quot;</span>)</div>
<div class="line"><a id="l01873" name="l01873"></a><span class="lineno"> 1873</span>    <span class="keywordtype">bool</span> send(<a class="code hl_class" href="classzmq_1_1message__t.html">message_t</a> &amp;&amp;msg_,</div>
<div class="line"><a id="l01874" name="l01874"></a><span class="lineno"> 1874</span>              <span class="keywordtype">int</span> flags_ = 0) <span class="comment">// default until removed</span></div>
<div class="line"><a id="l01875" name="l01875"></a><span class="lineno"> 1875</span>    {</div>
<div class="line"><a id="l01876" name="l01876"></a><span class="lineno"> 1876</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l01877" name="l01877"></a><span class="lineno"> 1877</span>        <span class="keywordflow">return</span> send(msg_, <span class="keyword">static_cast&lt;</span>send_flags<span class="keyword">&gt;</span>(flags_)).has_value();</div>
<div class="line"><a id="l01878" name="l01878"></a><span class="lineno"> 1878</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l01879" name="l01879"></a><span class="lineno"> 1879</span>        <span class="keywordflow">return</span> send(msg_, flags_);</div>
<div class="line"><a id="l01880" name="l01880"></a><span class="lineno"> 1880</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01881" name="l01881"></a><span class="lineno"> 1881</span>    }</div>
<div class="line"><a id="l01882" name="l01882"></a><span class="lineno"> 1882</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01883" name="l01883"></a><span class="lineno"> 1883</span> </div>
<div class="line"><a id="l01884" name="l01884"></a><span class="lineno"> 1884</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l01885" name="l01885"></a><span class="lineno"> 1885</span>    send_result_t send(const_buffer buf, send_flags flags = send_flags::none)</div>
<div class="line"><a id="l01886" name="l01886"></a><span class="lineno"> 1886</span>    {</div>
<div class="line"><a id="l01887" name="l01887"></a><span class="lineno"> 1887</span>        <span class="keyword">const</span> <span class="keywordtype">int</span> nbytes =</div>
<div class="line"><a id="l01888" name="l01888"></a><span class="lineno"> 1888</span>          zmq_send(_handle, buf.data(), buf.size(), <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(flags));</div>
<div class="line"><a id="l01889" name="l01889"></a><span class="lineno"> 1889</span>        <span class="keywordflow">if</span> (nbytes &gt;= 0)</div>
<div class="line"><a id="l01890" name="l01890"></a><span class="lineno"> 1890</span>            <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(nbytes);</div>
<div class="line"><a id="l01891" name="l01891"></a><span class="lineno"> 1891</span>        <span class="keywordflow">if</span> (zmq_errno() == EAGAIN)</div>
<div class="line"><a id="l01892" name="l01892"></a><span class="lineno"> 1892</span>            <span class="keywordflow">return</span> {};</div>
<div class="line"><a id="l01893" name="l01893"></a><span class="lineno"> 1893</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01894" name="l01894"></a><span class="lineno"> 1894</span>    }</div>
<div class="line"><a id="l01895" name="l01895"></a><span class="lineno"> 1895</span> </div>
<div class="line"><a id="l01896" name="l01896"></a><span class="lineno"> 1896</span>    send_result_t send(<a class="code hl_class" href="classzmq_1_1message__t.html">message_t</a> &amp;msg, send_flags flags)</div>
<div class="line"><a id="l01897" name="l01897"></a><span class="lineno"> 1897</span>    {</div>
<div class="line"><a id="l01898" name="l01898"></a><span class="lineno"> 1898</span>        <span class="keywordtype">int</span> nbytes = zmq_msg_send(msg.handle(), _handle, <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(flags));</div>
<div class="line"><a id="l01899" name="l01899"></a><span class="lineno"> 1899</span>        <span class="keywordflow">if</span> (nbytes &gt;= 0)</div>
<div class="line"><a id="l01900" name="l01900"></a><span class="lineno"> 1900</span>            <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(nbytes);</div>
<div class="line"><a id="l01901" name="l01901"></a><span class="lineno"> 1901</span>        <span class="keywordflow">if</span> (zmq_errno() == EAGAIN)</div>
<div class="line"><a id="l01902" name="l01902"></a><span class="lineno"> 1902</span>            <span class="keywordflow">return</span> {};</div>
<div class="line"><a id="l01903" name="l01903"></a><span class="lineno"> 1903</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01904" name="l01904"></a><span class="lineno"> 1904</span>    }</div>
<div class="line"><a id="l01905" name="l01905"></a><span class="lineno"> 1905</span> </div>
<div class="line"><a id="l01906" name="l01906"></a><span class="lineno"> 1906</span>    send_result_t send(<a class="code hl_class" href="classzmq_1_1message__t.html">message_t</a> &amp;&amp;msg, send_flags flags)</div>
<div class="line"><a id="l01907" name="l01907"></a><span class="lineno"> 1907</span>    {</div>
<div class="line"><a id="l01908" name="l01908"></a><span class="lineno"> 1908</span>        <span class="keywordflow">return</span> send(msg, flags);</div>
<div class="line"><a id="l01909" name="l01909"></a><span class="lineno"> 1909</span>    }</div>
<div class="line"><a id="l01910" name="l01910"></a><span class="lineno"> 1910</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01911" name="l01911"></a><span class="lineno"> 1911</span> </div>
<div class="line"><a id="l01912" name="l01912"></a><span class="lineno"> 1912</span>    ZMQ_CPP11_DEPRECATED(</div>
<div class="line"><a id="l01913" name="l01913"></a><span class="lineno"> 1913</span>      <span class="stringliteral">&quot;from 4.3.1, use recv taking a mutable_buffer and recv_flags&quot;</span>)</div>
<div class="line"><a id="l01914" name="l01914"></a><span class="lineno"> 1914</span>    <span class="keywordtype">size_t</span> recv(<span class="keywordtype">void</span> *buf_, <span class="keywordtype">size_t</span> len_, <span class="keywordtype">int</span> flags_ = 0)</div>
<div class="line"><a id="l01915" name="l01915"></a><span class="lineno"> 1915</span>    {</div>
<div class="line"><a id="l01916" name="l01916"></a><span class="lineno"> 1916</span>        <span class="keywordtype">int</span> nbytes = zmq_recv(_handle, buf_, len_, flags_);</div>
<div class="line"><a id="l01917" name="l01917"></a><span class="lineno"> 1917</span>        <span class="keywordflow">if</span> (nbytes &gt;= 0)</div>
<div class="line"><a id="l01918" name="l01918"></a><span class="lineno"> 1918</span>            <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(nbytes);</div>
<div class="line"><a id="l01919" name="l01919"></a><span class="lineno"> 1919</span>        <span class="keywordflow">if</span> (zmq_errno() == EAGAIN)</div>
<div class="line"><a id="l01920" name="l01920"></a><span class="lineno"> 1920</span>            <span class="keywordflow">return</span> 0;</div>
<div class="line"><a id="l01921" name="l01921"></a><span class="lineno"> 1921</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01922" name="l01922"></a><span class="lineno"> 1922</span>    }</div>
<div class="line"><a id="l01923" name="l01923"></a><span class="lineno"> 1923</span> </div>
<div class="line"><a id="l01924" name="l01924"></a><span class="lineno"> 1924</span>    ZMQ_CPP11_DEPRECATED(</div>
<div class="line"><a id="l01925" name="l01925"></a><span class="lineno"> 1925</span>      <span class="stringliteral">&quot;from 4.3.1, use recv taking a reference to message_t and recv_flags&quot;</span>)</div>
<div class="line"><a id="l01926" name="l01926"></a><span class="lineno"> 1926</span>    <span class="keywordtype">bool</span> recv(<a class="code hl_class" href="classzmq_1_1message__t.html">message_t</a> *msg_, <span class="keywordtype">int</span> flags_ = 0)</div>
<div class="line"><a id="l01927" name="l01927"></a><span class="lineno"> 1927</span>    {</div>
<div class="line"><a id="l01928" name="l01928"></a><span class="lineno"> 1928</span>        <span class="keywordtype">int</span> nbytes = zmq_msg_recv(msg_-&gt;handle(), _handle, flags_);</div>
<div class="line"><a id="l01929" name="l01929"></a><span class="lineno"> 1929</span>        <span class="keywordflow">if</span> (nbytes &gt;= 0)</div>
<div class="line"><a id="l01930" name="l01930"></a><span class="lineno"> 1930</span>            <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a id="l01931" name="l01931"></a><span class="lineno"> 1931</span>        <span class="keywordflow">if</span> (zmq_errno() == EAGAIN)</div>
<div class="line"><a id="l01932" name="l01932"></a><span class="lineno"> 1932</span>            <span class="keywordflow">return</span> <span class="keyword">false</span>;</div>
<div class="line"><a id="l01933" name="l01933"></a><span class="lineno"> 1933</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01934" name="l01934"></a><span class="lineno"> 1934</span>    }</div>
<div class="line"><a id="l01935" name="l01935"></a><span class="lineno"> 1935</span> </div>
<div class="line"><a id="l01936" name="l01936"></a><span class="lineno"> 1936</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l01937" name="l01937"></a><span class="lineno"> 1937</span>    ZMQ_NODISCARD</div>
<div class="line"><a id="l01938" name="l01938"></a><span class="lineno"> 1938</span>    recv_buffer_result_t recv(mutable_buffer buf,</div>
<div class="line"><a id="l01939" name="l01939"></a><span class="lineno"> 1939</span>                              recv_flags flags = recv_flags::none)</div>
<div class="line"><a id="l01940" name="l01940"></a><span class="lineno"> 1940</span>    {</div>
<div class="line"><a id="l01941" name="l01941"></a><span class="lineno"> 1941</span>        <span class="keyword">const</span> <span class="keywordtype">int</span> nbytes =</div>
<div class="line"><a id="l01942" name="l01942"></a><span class="lineno"> 1942</span>          zmq_recv(_handle, buf.data(), buf.size(), <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(flags));</div>
<div class="line"><a id="l01943" name="l01943"></a><span class="lineno"> 1943</span>        <span class="keywordflow">if</span> (nbytes &gt;= 0) {</div>
<div class="line"><a id="l01944" name="l01944"></a><span class="lineno"> 1944</span>            <span class="keywordflow">return</span> recv_buffer_size{</div>
<div class="line"><a id="l01945" name="l01945"></a><span class="lineno"> 1945</span>              (std::min)(<span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(nbytes), buf.size()),</div>
<div class="line"><a id="l01946" name="l01946"></a><span class="lineno"> 1946</span>              <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(nbytes)};</div>
<div class="line"><a id="l01947" name="l01947"></a><span class="lineno"> 1947</span>        }</div>
<div class="line"><a id="l01948" name="l01948"></a><span class="lineno"> 1948</span>        <span class="keywordflow">if</span> (zmq_errno() == EAGAIN)</div>
<div class="line"><a id="l01949" name="l01949"></a><span class="lineno"> 1949</span>            <span class="keywordflow">return</span> {};</div>
<div class="line"><a id="l01950" name="l01950"></a><span class="lineno"> 1950</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01951" name="l01951"></a><span class="lineno"> 1951</span>    }</div>
<div class="line"><a id="l01952" name="l01952"></a><span class="lineno"> 1952</span> </div>
<div class="line"><a id="l01953" name="l01953"></a><span class="lineno"> 1953</span>    ZMQ_NODISCARD</div>
<div class="line"><a id="l01954" name="l01954"></a><span class="lineno"> 1954</span>    recv_result_t recv(<a class="code hl_class" href="classzmq_1_1message__t.html">message_t</a> &amp;msg, recv_flags flags = recv_flags::none)</div>
<div class="line"><a id="l01955" name="l01955"></a><span class="lineno"> 1955</span>    {</div>
<div class="line"><a id="l01956" name="l01956"></a><span class="lineno"> 1956</span>        <span class="keyword">const</span> <span class="keywordtype">int</span> nbytes =</div>
<div class="line"><a id="l01957" name="l01957"></a><span class="lineno"> 1957</span>          zmq_msg_recv(msg.handle(), _handle, <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(flags));</div>
<div class="line"><a id="l01958" name="l01958"></a><span class="lineno"> 1958</span>        <span class="keywordflow">if</span> (nbytes &gt;= 0) {</div>
<div class="line"><a id="l01959" name="l01959"></a><span class="lineno"> 1959</span>            assert(msg.size() == <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(nbytes));</div>
<div class="line"><a id="l01960" name="l01960"></a><span class="lineno"> 1960</span>            <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(nbytes);</div>
<div class="line"><a id="l01961" name="l01961"></a><span class="lineno"> 1961</span>        }</div>
<div class="line"><a id="l01962" name="l01962"></a><span class="lineno"> 1962</span>        <span class="keywordflow">if</span> (zmq_errno() == EAGAIN)</div>
<div class="line"><a id="l01963" name="l01963"></a><span class="lineno"> 1963</span>            <span class="keywordflow">return</span> {};</div>
<div class="line"><a id="l01964" name="l01964"></a><span class="lineno"> 1964</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01965" name="l01965"></a><span class="lineno"> 1965</span>    }</div>
<div class="line"><a id="l01966" name="l01966"></a><span class="lineno"> 1966</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01967" name="l01967"></a><span class="lineno"> 1967</span> </div>
<div class="line"><a id="l01968" name="l01968"></a><span class="lineno"> 1968</span><span class="preprocessor">#if defined(ZMQ_BUILD_DRAFT_API) &amp;&amp; ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(4, 2, 0)</span></div>
<div class="line"><a id="l01969" name="l01969"></a><span class="lineno"> 1969</span>    <span class="keywordtype">void</span> join(<span class="keyword">const</span> <span class="keywordtype">char</span> *group)</div>
<div class="line"><a id="l01970" name="l01970"></a><span class="lineno"> 1970</span>    {</div>
<div class="line"><a id="l01971" name="l01971"></a><span class="lineno"> 1971</span>        <span class="keywordtype">int</span> rc = zmq_join(_handle, group);</div>
<div class="line"><a id="l01972" name="l01972"></a><span class="lineno"> 1972</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l01973" name="l01973"></a><span class="lineno"> 1973</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01974" name="l01974"></a><span class="lineno"> 1974</span>    }</div>
<div class="line"><a id="l01975" name="l01975"></a><span class="lineno"> 1975</span> </div>
<div class="line"><a id="l01976" name="l01976"></a><span class="lineno"> 1976</span>    <span class="keywordtype">void</span> leave(<span class="keyword">const</span> <span class="keywordtype">char</span> *group)</div>
<div class="line"><a id="l01977" name="l01977"></a><span class="lineno"> 1977</span>    {</div>
<div class="line"><a id="l01978" name="l01978"></a><span class="lineno"> 1978</span>        <span class="keywordtype">int</span> rc = zmq_leave(_handle, group);</div>
<div class="line"><a id="l01979" name="l01979"></a><span class="lineno"> 1979</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l01980" name="l01980"></a><span class="lineno"> 1980</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l01981" name="l01981"></a><span class="lineno"> 1981</span>    }</div>
<div class="line"><a id="l01982" name="l01982"></a><span class="lineno"> 1982</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l01983" name="l01983"></a><span class="lineno"> 1983</span> </div>
<div class="line"><a id="l01984" name="l01984"></a><span class="lineno"> 1984</span>    ZMQ_NODISCARD <span class="keywordtype">void</span> *handle() ZMQ_NOTHROW { <span class="keywordflow">return</span> _handle; }</div>
<div class="line"><a id="l01985" name="l01985"></a><span class="lineno"> 1985</span>    ZMQ_NODISCARD <span class="keyword">const</span> <span class="keywordtype">void</span> *handle() <span class="keyword">const</span> ZMQ_NOTHROW { <span class="keywordflow">return</span> _handle; }</div>
<div class="line"><a id="l01986" name="l01986"></a><span class="lineno"> 1986</span> </div>
<div class="line"><a id="l01987" name="l01987"></a><span class="lineno"> 1987</span>    ZMQ_EXPLICIT <span class="keyword">operator</span> bool() <span class="keyword">const</span> ZMQ_NOTHROW { <span class="keywordflow">return</span> _handle != ZMQ_NULLPTR; }</div>
<div class="line"><a id="l01988" name="l01988"></a><span class="lineno"> 1988</span>    <span class="comment">// note: non-const operator bool can be removed once</span></div>
<div class="line"><a id="l01989" name="l01989"></a><span class="lineno"> 1989</span>    <span class="comment">// operator void* is removed from socket_t</span></div>
<div class="line"><a id="l01990" name="l01990"></a><span class="lineno"> 1990</span>    ZMQ_EXPLICIT <span class="keyword">operator</span> bool() ZMQ_NOTHROW { <span class="keywordflow">return</span> _handle != ZMQ_NULLPTR; }</div>
<div class="line"><a id="l01991" name="l01991"></a><span class="lineno"> 1991</span> </div>
<div class="line"><a id="l01992" name="l01992"></a><span class="lineno"> 1992</span>  <span class="keyword">protected</span>:</div>
<div class="line"><a id="l01993" name="l01993"></a><span class="lineno"> 1993</span>    <span class="keywordtype">void</span> *_handle;</div>
<div class="line"><a id="l01994" name="l01994"></a><span class="lineno"> 1994</span> </div>
<div class="line"><a id="l01995" name="l01995"></a><span class="lineno"> 1995</span>  <span class="keyword">private</span>:</div>
<div class="line"><a id="l01996" name="l01996"></a><span class="lineno"> 1996</span>    <span class="keywordtype">void</span> set_option(<span class="keywordtype">int</span> option_, <span class="keyword">const</span> <span class="keywordtype">void</span> *optval_, <span class="keywordtype">size_t</span> optvallen_)</div>
<div class="line"><a id="l01997" name="l01997"></a><span class="lineno"> 1997</span>    {</div>
<div class="line"><a id="l01998" name="l01998"></a><span class="lineno"> 1998</span>        <span class="keywordtype">int</span> rc = zmq_setsockopt(_handle, option_, optval_, optvallen_);</div>
<div class="line"><a id="l01999" name="l01999"></a><span class="lineno"> 1999</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l02000" name="l02000"></a><span class="lineno"> 2000</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l02001" name="l02001"></a><span class="lineno"> 2001</span>    }</div>
<div class="line"><a id="l02002" name="l02002"></a><span class="lineno"> 2002</span> </div>
<div class="line"><a id="l02003" name="l02003"></a><span class="lineno"> 2003</span>    <span class="keywordtype">void</span> get_option(<span class="keywordtype">int</span> option_, <span class="keywordtype">void</span> *optval_, <span class="keywordtype">size_t</span> *optvallen_)<span class="keyword"> const</span></div>
<div class="line"><a id="l02004" name="l02004"></a><span class="lineno"> 2004</span><span class="keyword">    </span>{</div>
<div class="line"><a id="l02005" name="l02005"></a><span class="lineno"> 2005</span>        <span class="keywordtype">int</span> rc = zmq_getsockopt(_handle, option_, optval_, optvallen_);</div>
<div class="line"><a id="l02006" name="l02006"></a><span class="lineno"> 2006</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l02007" name="l02007"></a><span class="lineno"> 2007</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l02008" name="l02008"></a><span class="lineno"> 2008</span>    }</div>
<div class="line"><a id="l02009" name="l02009"></a><span class="lineno"> 2009</span>};</div>
</div>
<div class="line"><a id="l02010" name="l02010"></a><span class="lineno"> 2010</span>} <span class="comment">// namespace detail</span></div>
<div class="line"><a id="l02011" name="l02011"></a><span class="lineno"> 2011</span> </div>
<div class="line"><a id="l02012" name="l02012"></a><span class="lineno"> 2012</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l02013" name="l02013"></a><span class="lineno"> 2013</span><span class="keyword">enum class</span> socket_type : <span class="keywordtype">int</span></div>
<div class="line"><a id="l02014" name="l02014"></a><span class="lineno"> 2014</span>{</div>
<div class="line"><a id="l02015" name="l02015"></a><span class="lineno"> 2015</span>    req = ZMQ_REQ,</div>
<div class="line"><a id="l02016" name="l02016"></a><span class="lineno"> 2016</span>    rep = ZMQ_REP,</div>
<div class="line"><a id="l02017" name="l02017"></a><span class="lineno"> 2017</span>    dealer = ZMQ_DEALER,</div>
<div class="line"><a id="l02018" name="l02018"></a><span class="lineno"> 2018</span>    router = ZMQ_ROUTER,</div>
<div class="line"><a id="l02019" name="l02019"></a><span class="lineno"> 2019</span>    pub = ZMQ_PUB,</div>
<div class="line"><a id="l02020" name="l02020"></a><span class="lineno"> 2020</span>    sub = ZMQ_SUB,</div>
<div class="line"><a id="l02021" name="l02021"></a><span class="lineno"> 2021</span>    xpub = ZMQ_XPUB,</div>
<div class="line"><a id="l02022" name="l02022"></a><span class="lineno"> 2022</span>    xsub = ZMQ_XSUB,</div>
<div class="line"><a id="l02023" name="l02023"></a><span class="lineno"> 2023</span>    push = ZMQ_PUSH,</div>
<div class="line"><a id="l02024" name="l02024"></a><span class="lineno"> 2024</span>    pull = ZMQ_PULL,</div>
<div class="line"><a id="l02025" name="l02025"></a><span class="lineno"> 2025</span><span class="preprocessor">#if defined(ZMQ_BUILD_DRAFT_API) &amp;&amp; ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(4, 2, 0)</span></div>
<div class="line"><a id="l02026" name="l02026"></a><span class="lineno"> 2026</span>    server = ZMQ_SERVER,</div>
<div class="line"><a id="l02027" name="l02027"></a><span class="lineno"> 2027</span>    client = ZMQ_CLIENT,</div>
<div class="line"><a id="l02028" name="l02028"></a><span class="lineno"> 2028</span>    radio = ZMQ_RADIO,</div>
<div class="line"><a id="l02029" name="l02029"></a><span class="lineno"> 2029</span>    dish = ZMQ_DISH,</div>
<div class="line"><a id="l02030" name="l02030"></a><span class="lineno"> 2030</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02031" name="l02031"></a><span class="lineno"> 2031</span><span class="preprocessor">#if ZMQ_VERSION_MAJOR &gt;= 4</span></div>
<div class="line"><a id="l02032" name="l02032"></a><span class="lineno"> 2032</span>    stream = ZMQ_STREAM,</div>
<div class="line"><a id="l02033" name="l02033"></a><span class="lineno"> 2033</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02034" name="l02034"></a><span class="lineno"> 2034</span>    pair = ZMQ_PAIR</div>
<div class="line"><a id="l02035" name="l02035"></a><span class="lineno"> 2035</span>};</div>
<div class="line"><a id="l02036" name="l02036"></a><span class="lineno"> 2036</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02037" name="l02037"></a><span class="lineno"> 2037</span> </div>
<div class="foldopen" id="foldopen02038" data-start="{" data-end="};">
<div class="line"><a id="l02038" name="l02038"></a><span class="lineno"><a class="line" href="structzmq_1_1from__handle__t.html"> 2038</a></span><span class="keyword">struct </span>from_handle_t</div>
<div class="line"><a id="l02039" name="l02039"></a><span class="lineno"> 2039</span>{</div>
<div class="foldopen" id="foldopen02040" data-start="{" data-end="};">
<div class="line"><a id="l02040" name="l02040"></a><span class="lineno"><a class="line" href="structzmq_1_1from__handle__t_1_1__private.html"> 2040</a></span>    <span class="keyword">struct </span><a class="code hl_struct" href="structzmq_1_1from__handle__t_1_1__private.html">_private</a></div>
<div class="line"><a id="l02041" name="l02041"></a><span class="lineno"> 2041</span>    {</div>
<div class="line"><a id="l02042" name="l02042"></a><span class="lineno"> 2042</span>    }; <span class="comment">// disabling use other than with from_handle</span></div>
</div>
<div class="line"><a id="l02043" name="l02043"></a><span class="lineno"> 2043</span>    ZMQ_CONSTEXPR_FN ZMQ_EXPLICIT from_handle_t(<a class="code hl_struct" href="structzmq_1_1from__handle__t_1_1__private.html">_private</a> <span class="comment">/*p*/</span>) ZMQ_NOTHROW {}</div>
<div class="line"><a id="l02044" name="l02044"></a><span class="lineno"> 2044</span>};</div>
</div>
<div class="line"><a id="l02045" name="l02045"></a><span class="lineno"> 2045</span> </div>
<div class="line"><a id="l02046" name="l02046"></a><span class="lineno"> 2046</span>ZMQ_CONSTEXPR_VAR from_handle_t from_handle =</div>
<div class="line"><a id="l02047" name="l02047"></a><span class="lineno"> 2047</span>  from_handle_t(from_handle_t::_private());</div>
<div class="line"><a id="l02048" name="l02048"></a><span class="lineno"> 2048</span> </div>
<div class="line"><a id="l02049" name="l02049"></a><span class="lineno"> 2049</span><span class="comment">// A non-owning nullable reference to a socket.</span></div>
<div class="line"><a id="l02050" name="l02050"></a><span class="lineno"> 2050</span><span class="comment">// The reference is invalidated on socket close or destruction.</span></div>
<div class="foldopen" id="foldopen02051" data-start="{" data-end="};">
<div class="line"><a id="l02051" name="l02051"></a><span class="lineno"><a class="line" href="classzmq_1_1socket__ref.html"> 2051</a></span><span class="keyword">class </span>socket_ref : <span class="keyword">public</span> <a class="code hl_class" href="classzmq_1_1detail_1_1socket__base.html">detail::socket_base</a></div>
<div class="line"><a id="l02052" name="l02052"></a><span class="lineno"> 2052</span>{</div>
<div class="line"><a id="l02053" name="l02053"></a><span class="lineno"> 2053</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l02054" name="l02054"></a><span class="lineno"> 2054</span>    socket_ref() ZMQ_NOTHROW : <a class="code hl_class" href="classzmq_1_1detail_1_1socket__base.html">detail::socket_base</a>() {}</div>
<div class="line"><a id="l02055" name="l02055"></a><span class="lineno"> 2055</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l02056" name="l02056"></a><span class="lineno"> 2056</span>    socket_ref(std::nullptr_t) ZMQ_NOTHROW : <a class="code hl_class" href="classzmq_1_1detail_1_1socket__base.html">detail::socket_base</a>() {}</div>
<div class="line"><a id="l02057" name="l02057"></a><span class="lineno"> 2057</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02058" name="l02058"></a><span class="lineno"> 2058</span>    socket_ref(<a class="code hl_struct" href="structzmq_1_1from__handle__t.html">from_handle_t</a> <span class="comment">/*fh*/</span>, <span class="keywordtype">void</span> *handle) ZMQ_NOTHROW</div>
<div class="line"><a id="l02059" name="l02059"></a><span class="lineno"> 2059</span>        : <a class="code hl_class" href="classzmq_1_1detail_1_1socket__base.html">detail::socket_base</a>(handle)</div>
<div class="line"><a id="l02060" name="l02060"></a><span class="lineno"> 2060</span>    {</div>
<div class="line"><a id="l02061" name="l02061"></a><span class="lineno"> 2061</span>    }</div>
<div class="line"><a id="l02062" name="l02062"></a><span class="lineno"> 2062</span>};</div>
</div>
<div class="line"><a id="l02063" name="l02063"></a><span class="lineno"> 2063</span> </div>
<div class="line"><a id="l02064" name="l02064"></a><span class="lineno"> 2064</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l02065" name="l02065"></a><span class="lineno"> 2065</span><span class="keyword">inline</span> <span class="keywordtype">bool</span> operator==(<a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> sr, std::nullptr_t <span class="comment">/*p*/</span>) ZMQ_NOTHROW</div>
<div class="line"><a id="l02066" name="l02066"></a><span class="lineno"> 2066</span>{</div>
<div class="line"><a id="l02067" name="l02067"></a><span class="lineno"> 2067</span>    <span class="keywordflow">return</span> sr.handle() == <span class="keyword">nullptr</span>;</div>
<div class="line"><a id="l02068" name="l02068"></a><span class="lineno"> 2068</span>}</div>
<div class="line"><a id="l02069" name="l02069"></a><span class="lineno"> 2069</span><span class="keyword">inline</span> <span class="keywordtype">bool</span> operator==(std::nullptr_t <span class="comment">/*p*/</span>, socket_ref sr) ZMQ_NOTHROW</div>
<div class="line"><a id="l02070" name="l02070"></a><span class="lineno"> 2070</span>{</div>
<div class="line"><a id="l02071" name="l02071"></a><span class="lineno"> 2071</span>    <span class="keywordflow">return</span> sr.handle() == <span class="keyword">nullptr</span>;</div>
<div class="line"><a id="l02072" name="l02072"></a><span class="lineno"> 2072</span>}</div>
<div class="line"><a id="l02073" name="l02073"></a><span class="lineno"> 2073</span><span class="keyword">inline</span> <span class="keywordtype">bool</span> operator!=(<a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> sr, std::nullptr_t <span class="comment">/*p*/</span>) ZMQ_NOTHROW</div>
<div class="line"><a id="l02074" name="l02074"></a><span class="lineno"> 2074</span>{</div>
<div class="line"><a id="l02075" name="l02075"></a><span class="lineno"> 2075</span>    <span class="keywordflow">return</span> !(sr == <span class="keyword">nullptr</span>);</div>
<div class="line"><a id="l02076" name="l02076"></a><span class="lineno"> 2076</span>}</div>
<div class="line"><a id="l02077" name="l02077"></a><span class="lineno"> 2077</span><span class="keyword">inline</span> <span class="keywordtype">bool</span> operator!=(std::nullptr_t <span class="comment">/*p*/</span>, <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> sr) ZMQ_NOTHROW</div>
<div class="line"><a id="l02078" name="l02078"></a><span class="lineno"> 2078</span>{</div>
<div class="line"><a id="l02079" name="l02079"></a><span class="lineno"> 2079</span>    <span class="keywordflow">return</span> !(sr == <span class="keyword">nullptr</span>);</div>
<div class="line"><a id="l02080" name="l02080"></a><span class="lineno"> 2080</span>}</div>
<div class="line"><a id="l02081" name="l02081"></a><span class="lineno"> 2081</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02082" name="l02082"></a><span class="lineno"> 2082</span> </div>
<div class="line"><a id="l02083" name="l02083"></a><span class="lineno"> 2083</span><span class="keyword">inline</span> <span class="keywordtype">bool</span> operator==(<a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> a, <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> b) ZMQ_NOTHROW</div>
<div class="line"><a id="l02084" name="l02084"></a><span class="lineno"> 2084</span>{</div>
<div class="line"><a id="l02085" name="l02085"></a><span class="lineno"> 2085</span>    <span class="keywordflow">return</span> std::equal_to&lt;void *&gt;()(a.handle(), b.handle());</div>
<div class="line"><a id="l02086" name="l02086"></a><span class="lineno"> 2086</span>}</div>
<div class="line"><a id="l02087" name="l02087"></a><span class="lineno"> 2087</span><span class="keyword">inline</span> <span class="keywordtype">bool</span> operator!=(<a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> a, <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> b) ZMQ_NOTHROW</div>
<div class="line"><a id="l02088" name="l02088"></a><span class="lineno"> 2088</span>{</div>
<div class="line"><a id="l02089" name="l02089"></a><span class="lineno"> 2089</span>    <span class="keywordflow">return</span> !(a == b);</div>
<div class="line"><a id="l02090" name="l02090"></a><span class="lineno"> 2090</span>}</div>
<div class="line"><a id="l02091" name="l02091"></a><span class="lineno"> 2091</span><span class="keyword">inline</span> <span class="keywordtype">bool</span> operator&lt;(<a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> a, <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> b) ZMQ_NOTHROW</div>
<div class="line"><a id="l02092" name="l02092"></a><span class="lineno"> 2092</span>{</div>
<div class="line"><a id="l02093" name="l02093"></a><span class="lineno"> 2093</span>    <span class="keywordflow">return</span> std::less&lt;void *&gt;()(a.handle(), b.handle());</div>
<div class="line"><a id="l02094" name="l02094"></a><span class="lineno"> 2094</span>}</div>
<div class="line"><a id="l02095" name="l02095"></a><span class="lineno"> 2095</span><span class="keyword">inline</span> <span class="keywordtype">bool</span> operator&gt;(<a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> a, <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> b) ZMQ_NOTHROW</div>
<div class="line"><a id="l02096" name="l02096"></a><span class="lineno"> 2096</span>{</div>
<div class="line"><a id="l02097" name="l02097"></a><span class="lineno"> 2097</span>    <span class="keywordflow">return</span> b &lt; a;</div>
<div class="line"><a id="l02098" name="l02098"></a><span class="lineno"> 2098</span>}</div>
<div class="line"><a id="l02099" name="l02099"></a><span class="lineno"> 2099</span><span class="keyword">inline</span> <span class="keywordtype">bool</span> operator&lt;=(<a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> a, <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> b) ZMQ_NOTHROW</div>
<div class="line"><a id="l02100" name="l02100"></a><span class="lineno"> 2100</span>{</div>
<div class="line"><a id="l02101" name="l02101"></a><span class="lineno"> 2101</span>    <span class="keywordflow">return</span> !(a &gt; b);</div>
<div class="line"><a id="l02102" name="l02102"></a><span class="lineno"> 2102</span>}</div>
<div class="line"><a id="l02103" name="l02103"></a><span class="lineno"> 2103</span><span class="keyword">inline</span> <span class="keywordtype">bool</span> operator&gt;=(<a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> a, <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> b) ZMQ_NOTHROW</div>
<div class="line"><a id="l02104" name="l02104"></a><span class="lineno"> 2104</span>{</div>
<div class="line"><a id="l02105" name="l02105"></a><span class="lineno"> 2105</span>    <span class="keywordflow">return</span> !(a &lt; b);</div>
<div class="line"><a id="l02106" name="l02106"></a><span class="lineno"> 2106</span>}</div>
<div class="line"><a id="l02107" name="l02107"></a><span class="lineno"> 2107</span> </div>
<div class="line"><a id="l02108" name="l02108"></a><span class="lineno"> 2108</span>} <span class="comment">// namespace zmq</span></div>
<div class="line"><a id="l02109" name="l02109"></a><span class="lineno"> 2109</span> </div>
<div class="line"><a id="l02110" name="l02110"></a><span class="lineno"> 2110</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l02111" name="l02111"></a><span class="lineno"> 2111</span><span class="keyword">namespace </span>std</div>
<div class="line"><a id="l02112" name="l02112"></a><span class="lineno"> 2112</span>{</div>
<div class="line"><a id="l02113" name="l02113"></a><span class="lineno"> 2113</span><span class="keyword">template</span>&lt;&gt; <span class="keyword">struct </span>hash&lt;zmq::socket_ref&gt;</div>
<div class="line"><a id="l02114" name="l02114"></a><span class="lineno"> 2114</span>{</div>
<div class="line"><a id="l02115" name="l02115"></a><span class="lineno"> 2115</span>    <span class="keywordtype">size_t</span> operator()(zmq::socket_ref sr) <span class="keyword">const</span> ZMQ_NOTHROW</div>
<div class="line"><a id="l02116" name="l02116"></a><span class="lineno"> 2116</span>    {</div>
<div class="line"><a id="l02117" name="l02117"></a><span class="lineno"> 2117</span>        <span class="keywordflow">return</span> hash&lt;void *&gt;()(sr.handle());</div>
<div class="line"><a id="l02118" name="l02118"></a><span class="lineno"> 2118</span>    }</div>
<div class="line"><a id="l02119" name="l02119"></a><span class="lineno"> 2119</span>};</div>
<div class="line"><a id="l02120" name="l02120"></a><span class="lineno"> 2120</span>} <span class="comment">// namespace std</span></div>
<div class="line"><a id="l02121" name="l02121"></a><span class="lineno"> 2121</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02122" name="l02122"></a><span class="lineno"> 2122</span> </div>
<div class="line"><a id="l02123" name="l02123"></a><span class="lineno"> 2123</span><span class="keyword">namespace </span>zmq</div>
<div class="line"><a id="l02124" name="l02124"></a><span class="lineno"> 2124</span>{</div>
<div class="foldopen" id="foldopen02125" data-start="{" data-end="};">
<div class="line"><a id="l02125" name="l02125"></a><span class="lineno"><a class="line" href="classzmq_1_1socket__t.html"> 2125</a></span><span class="keyword">class </span>socket_t : <span class="keyword">public</span> <a class="code hl_class" href="classzmq_1_1detail_1_1socket__base.html">detail::socket_base</a></div>
<div class="line"><a id="l02126" name="l02126"></a><span class="lineno"> 2126</span>{</div>
<div class="line"><a id="l02127" name="l02127"></a><span class="lineno"> 2127</span>    <span class="keyword">friend</span> <span class="keyword">class </span>monitor_t;</div>
<div class="line"><a id="l02128" name="l02128"></a><span class="lineno"> 2128</span> </div>
<div class="line"><a id="l02129" name="l02129"></a><span class="lineno"> 2129</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l02130" name="l02130"></a><span class="lineno"> 2130</span>    socket_t() ZMQ_NOTHROW : <a class="code hl_class" href="classzmq_1_1detail_1_1socket__base.html">detail::socket_base</a>(ZMQ_NULLPTR), ctxptr(ZMQ_NULLPTR) {}</div>
<div class="line"><a id="l02131" name="l02131"></a><span class="lineno"> 2131</span> </div>
<div class="line"><a id="l02132" name="l02132"></a><span class="lineno"> 2132</span>    socket_t(<a class="code hl_class" href="classzmq_1_1context__t.html">context_t</a> &amp;context_, <span class="keywordtype">int</span> type_) :</div>
<div class="line"><a id="l02133" name="l02133"></a><span class="lineno"> 2133</span>        <a class="code hl_class" href="classzmq_1_1detail_1_1socket__base.html">detail::socket_base</a>(zmq_socket(context_.handle(), type_)),</div>
<div class="line"><a id="l02134" name="l02134"></a><span class="lineno"> 2134</span>        ctxptr(context_.handle())</div>
<div class="line"><a id="l02135" name="l02135"></a><span class="lineno"> 2135</span>    {</div>
<div class="line"><a id="l02136" name="l02136"></a><span class="lineno"> 2136</span>        <span class="keywordflow">if</span> (_handle == ZMQ_NULLPTR)</div>
<div class="line"><a id="l02137" name="l02137"></a><span class="lineno"> 2137</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l02138" name="l02138"></a><span class="lineno"> 2138</span>    }</div>
<div class="line"><a id="l02139" name="l02139"></a><span class="lineno"> 2139</span> </div>
<div class="line"><a id="l02140" name="l02140"></a><span class="lineno"> 2140</span><span class="preprocessor">#ifdef ZMQ_CPP11</span></div>
<div class="line"><a id="l02141" name="l02141"></a><span class="lineno"> 2141</span>    socket_t(<a class="code hl_class" href="classzmq_1_1context__t.html">context_t</a> &amp;context_, socket_type type_) :</div>
<div class="line"><a id="l02142" name="l02142"></a><span class="lineno"> 2142</span>        socket_t(context_, <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(type_))</div>
<div class="line"><a id="l02143" name="l02143"></a><span class="lineno"> 2143</span>    {</div>
<div class="line"><a id="l02144" name="l02144"></a><span class="lineno"> 2144</span>    }</div>
<div class="line"><a id="l02145" name="l02145"></a><span class="lineno"> 2145</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02146" name="l02146"></a><span class="lineno"> 2146</span> </div>
<div class="line"><a id="l02147" name="l02147"></a><span class="lineno"> 2147</span><span class="preprocessor">#ifdef ZMQ_HAS_RVALUE_REFS</span></div>
<div class="line"><a id="l02148" name="l02148"></a><span class="lineno"> 2148</span>    socket_t(socket_t &amp;&amp;rhs) ZMQ_NOTHROW : <a class="code hl_class" href="classzmq_1_1detail_1_1socket__base.html">detail::socket_base</a>(rhs._handle),</div>
<div class="line"><a id="l02149" name="l02149"></a><span class="lineno"> 2149</span>                                           ctxptr(rhs.ctxptr)</div>
<div class="line"><a id="l02150" name="l02150"></a><span class="lineno"> 2150</span>    {</div>
<div class="line"><a id="l02151" name="l02151"></a><span class="lineno"> 2151</span>        rhs._handle = ZMQ_NULLPTR;</div>
<div class="line"><a id="l02152" name="l02152"></a><span class="lineno"> 2152</span>        rhs.ctxptr = ZMQ_NULLPTR;</div>
<div class="line"><a id="l02153" name="l02153"></a><span class="lineno"> 2153</span>    }</div>
<div class="line"><a id="l02154" name="l02154"></a><span class="lineno"> 2154</span>    socket_t &amp;operator=(socket_t &amp;&amp;rhs) ZMQ_NOTHROW</div>
<div class="line"><a id="l02155" name="l02155"></a><span class="lineno"> 2155</span>    {</div>
<div class="line"><a id="l02156" name="l02156"></a><span class="lineno"> 2156</span>        close();</div>
<div class="line"><a id="l02157" name="l02157"></a><span class="lineno"> 2157</span>        std::swap(_handle, rhs._handle);</div>
<div class="line"><a id="l02158" name="l02158"></a><span class="lineno"> 2158</span>        std::swap(ctxptr, rhs.ctxptr);</div>
<div class="line"><a id="l02159" name="l02159"></a><span class="lineno"> 2159</span>        <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a id="l02160" name="l02160"></a><span class="lineno"> 2160</span>    }</div>
<div class="line"><a id="l02161" name="l02161"></a><span class="lineno"> 2161</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02162" name="l02162"></a><span class="lineno"> 2162</span> </div>
<div class="line"><a id="l02163" name="l02163"></a><span class="lineno"> 2163</span>    ~socket_t() ZMQ_NOTHROW { close(); }</div>
<div class="line"><a id="l02164" name="l02164"></a><span class="lineno"> 2164</span> </div>
<div class="line"><a id="l02165" name="l02165"></a><span class="lineno"> 2165</span>    <span class="keyword">operator</span> <span class="keywordtype">void</span> *() ZMQ_NOTHROW { <span class="keywordflow">return</span> _handle; }</div>
<div class="line"><a id="l02166" name="l02166"></a><span class="lineno"> 2166</span> </div>
<div class="line"><a id="l02167" name="l02167"></a><span class="lineno"> 2167</span>    <span class="keyword">operator</span> <span class="keywordtype">void</span> <span class="keyword">const</span> *() <span class="keyword">const</span> ZMQ_NOTHROW { <span class="keywordflow">return</span> _handle; }</div>
<div class="line"><a id="l02168" name="l02168"></a><span class="lineno"> 2168</span> </div>
<div class="line"><a id="l02169" name="l02169"></a><span class="lineno"> 2169</span>    <span class="keywordtype">void</span> close() ZMQ_NOTHROW</div>
<div class="line"><a id="l02170" name="l02170"></a><span class="lineno"> 2170</span>    {</div>
<div class="line"><a id="l02171" name="l02171"></a><span class="lineno"> 2171</span>        <span class="keywordflow">if</span> (_handle == ZMQ_NULLPTR)</div>
<div class="line"><a id="l02172" name="l02172"></a><span class="lineno"> 2172</span>            <span class="comment">// already closed</span></div>
<div class="line"><a id="l02173" name="l02173"></a><span class="lineno"> 2173</span>            <span class="keywordflow">return</span>;</div>
<div class="line"><a id="l02174" name="l02174"></a><span class="lineno"> 2174</span>        <span class="keywordtype">int</span> rc = zmq_close(_handle);</div>
<div class="line"><a id="l02175" name="l02175"></a><span class="lineno"> 2175</span>        ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l02176" name="l02176"></a><span class="lineno"> 2176</span>        _handle = ZMQ_NULLPTR;</div>
<div class="line"><a id="l02177" name="l02177"></a><span class="lineno"> 2177</span>        ctxptr = ZMQ_NULLPTR;</div>
<div class="line"><a id="l02178" name="l02178"></a><span class="lineno"> 2178</span>    }</div>
<div class="line"><a id="l02179" name="l02179"></a><span class="lineno"> 2179</span> </div>
<div class="line"><a id="l02180" name="l02180"></a><span class="lineno"> 2180</span>    <span class="keywordtype">void</span> swap(socket_t &amp;other) ZMQ_NOTHROW</div>
<div class="line"><a id="l02181" name="l02181"></a><span class="lineno"> 2181</span>    {</div>
<div class="line"><a id="l02182" name="l02182"></a><span class="lineno"> 2182</span>        std::swap(_handle, other._handle);</div>
<div class="line"><a id="l02183" name="l02183"></a><span class="lineno"> 2183</span>        std::swap(ctxptr, other.ctxptr);</div>
<div class="line"><a id="l02184" name="l02184"></a><span class="lineno"> 2184</span>    }</div>
<div class="line"><a id="l02185" name="l02185"></a><span class="lineno"> 2185</span> </div>
<div class="line"><a id="l02186" name="l02186"></a><span class="lineno"> 2186</span>    <span class="keyword">operator</span> <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a>() ZMQ_NOTHROW { <span class="keywordflow">return</span> <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a>(from_handle, _handle); }</div>
<div class="line"><a id="l02187" name="l02187"></a><span class="lineno"> 2187</span> </div>
<div class="line"><a id="l02188" name="l02188"></a><span class="lineno"> 2188</span>  <span class="keyword">private</span>:</div>
<div class="line"><a id="l02189" name="l02189"></a><span class="lineno"> 2189</span>    <span class="keywordtype">void</span> *ctxptr;</div>
<div class="line"><a id="l02190" name="l02190"></a><span class="lineno"> 2190</span> </div>
<div class="line"><a id="l02191" name="l02191"></a><span class="lineno"> 2191</span>    socket_t(<span class="keyword">const</span> socket_t &amp;) ZMQ_DELETED_FUNCTION;</div>
<div class="line"><a id="l02192" name="l02192"></a><span class="lineno"> 2192</span>    <span class="keywordtype">void</span> operator=(<span class="keyword">const</span> socket_t &amp;) ZMQ_DELETED_FUNCTION;</div>
<div class="line"><a id="l02193" name="l02193"></a><span class="lineno"> 2193</span> </div>
<div class="line"><a id="l02194" name="l02194"></a><span class="lineno"> 2194</span>    <span class="comment">// used by monitor_t</span></div>
<div class="line"><a id="l02195" name="l02195"></a><span class="lineno"> 2195</span>    socket_t(<span class="keywordtype">void</span> *context_, <span class="keywordtype">int</span> type_) :</div>
<div class="line"><a id="l02196" name="l02196"></a><span class="lineno"> 2196</span>        <a class="code hl_class" href="classzmq_1_1detail_1_1socket__base.html">detail::socket_base</a>(zmq_socket(context_, type_)),</div>
<div class="line"><a id="l02197" name="l02197"></a><span class="lineno"> 2197</span>        ctxptr(context_)</div>
<div class="line"><a id="l02198" name="l02198"></a><span class="lineno"> 2198</span>    {</div>
<div class="line"><a id="l02199" name="l02199"></a><span class="lineno"> 2199</span>        <span class="keywordflow">if</span> (_handle == ZMQ_NULLPTR)</div>
<div class="line"><a id="l02200" name="l02200"></a><span class="lineno"> 2200</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l02201" name="l02201"></a><span class="lineno"> 2201</span>        <span class="keywordflow">if</span> (ctxptr == ZMQ_NULLPTR)</div>
<div class="line"><a id="l02202" name="l02202"></a><span class="lineno"> 2202</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l02203" name="l02203"></a><span class="lineno"> 2203</span>    }</div>
<div class="line"><a id="l02204" name="l02204"></a><span class="lineno"> 2204</span>};</div>
</div>
<div class="line"><a id="l02205" name="l02205"></a><span class="lineno"> 2205</span> </div>
<div class="line"><a id="l02206" name="l02206"></a><span class="lineno"> 2206</span><span class="keyword">inline</span> <span class="keywordtype">void</span> swap(<a class="code hl_class" href="classzmq_1_1socket__t.html">socket_t</a> &amp;a, <a class="code hl_class" href="classzmq_1_1socket__t.html">socket_t</a> &amp;b) ZMQ_NOTHROW</div>
<div class="line"><a id="l02207" name="l02207"></a><span class="lineno"> 2207</span>{</div>
<div class="line"><a id="l02208" name="l02208"></a><span class="lineno"> 2208</span>    a.swap(b);</div>
<div class="line"><a id="l02209" name="l02209"></a><span class="lineno"> 2209</span>}</div>
<div class="line"><a id="l02210" name="l02210"></a><span class="lineno"> 2210</span> </div>
<div class="line"><a id="l02211" name="l02211"></a><span class="lineno"> 2211</span>ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use proxy taking socket_t objects&quot;</span>)</div>
<div class="line"><a id="l02212" name="l02212"></a><span class="lineno"> 2212</span>inline <span class="keywordtype">void</span> proxy(<span class="keywordtype">void</span> *frontend, <span class="keywordtype">void</span> *backend, <span class="keywordtype">void</span> *capture)</div>
<div class="line"><a id="l02213" name="l02213"></a><span class="lineno"> 2213</span>{</div>
<div class="line"><a id="l02214" name="l02214"></a><span class="lineno"> 2214</span>    <span class="keywordtype">int</span> rc = zmq_proxy(frontend, backend, capture);</div>
<div class="line"><a id="l02215" name="l02215"></a><span class="lineno"> 2215</span>    <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l02216" name="l02216"></a><span class="lineno"> 2216</span>        <span class="keywordflow">throw</span> error_t();</div>
<div class="line"><a id="l02217" name="l02217"></a><span class="lineno"> 2217</span>}</div>
<div class="line"><a id="l02218" name="l02218"></a><span class="lineno"> 2218</span> </div>
<div class="line"><a id="l02219" name="l02219"></a><span class="lineno"> 2219</span><span class="keyword">inline</span> <span class="keywordtype">void</span></div>
<div class="line"><a id="l02220" name="l02220"></a><span class="lineno"> 2220</span>proxy(<a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> frontend, <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> backend, <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> capture = <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a>())</div>
<div class="line"><a id="l02221" name="l02221"></a><span class="lineno"> 2221</span>{</div>
<div class="line"><a id="l02222" name="l02222"></a><span class="lineno"> 2222</span>    <span class="keywordtype">int</span> rc = zmq_proxy(frontend.handle(), backend.handle(), capture.handle());</div>
<div class="line"><a id="l02223" name="l02223"></a><span class="lineno"> 2223</span>    <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l02224" name="l02224"></a><span class="lineno"> 2224</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l02225" name="l02225"></a><span class="lineno"> 2225</span>}</div>
<div class="line"><a id="l02226" name="l02226"></a><span class="lineno"> 2226</span> </div>
<div class="line"><a id="l02227" name="l02227"></a><span class="lineno"> 2227</span><span class="preprocessor">#ifdef ZMQ_HAS_PROXY_STEERABLE</span></div>
<div class="line"><a id="l02228" name="l02228"></a><span class="lineno"> 2228</span>ZMQ_DEPRECATED(<span class="stringliteral">&quot;from 4.3.1, use proxy_steerable taking socket_t objects&quot;</span>)</div>
<div class="line"><a id="l02229" name="l02229"></a><span class="lineno"> 2229</span>inline <span class="keywordtype">void</span></div>
<div class="line"><a id="l02230" name="l02230"></a><span class="lineno"> 2230</span>proxy_steerable(<span class="keywordtype">void</span> *frontend, <span class="keywordtype">void</span> *backend, <span class="keywordtype">void</span> *capture, <span class="keywordtype">void</span> *control)</div>
<div class="line"><a id="l02231" name="l02231"></a><span class="lineno"> 2231</span>{</div>
<div class="line"><a id="l02232" name="l02232"></a><span class="lineno"> 2232</span>    <span class="keywordtype">int</span> rc = zmq_proxy_steerable(frontend, backend, capture, control);</div>
<div class="line"><a id="l02233" name="l02233"></a><span class="lineno"> 2233</span>    <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l02234" name="l02234"></a><span class="lineno"> 2234</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l02235" name="l02235"></a><span class="lineno"> 2235</span>}</div>
<div class="line"><a id="l02236" name="l02236"></a><span class="lineno"> 2236</span> </div>
<div class="line"><a id="l02237" name="l02237"></a><span class="lineno"> 2237</span><span class="keyword">inline</span> <span class="keywordtype">void</span> proxy_steerable(<a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> frontend,</div>
<div class="line"><a id="l02238" name="l02238"></a><span class="lineno"> 2238</span>                            <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> backend,</div>
<div class="line"><a id="l02239" name="l02239"></a><span class="lineno"> 2239</span>                            <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> capture,</div>
<div class="line"><a id="l02240" name="l02240"></a><span class="lineno"> 2240</span>                            <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> control)</div>
<div class="line"><a id="l02241" name="l02241"></a><span class="lineno"> 2241</span>{</div>
<div class="line"><a id="l02242" name="l02242"></a><span class="lineno"> 2242</span>    <span class="keywordtype">int</span> rc = zmq_proxy_steerable(frontend.handle(), backend.handle(),</div>
<div class="line"><a id="l02243" name="l02243"></a><span class="lineno"> 2243</span>                                 capture.handle(), control.handle());</div>
<div class="line"><a id="l02244" name="l02244"></a><span class="lineno"> 2244</span>    <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l02245" name="l02245"></a><span class="lineno"> 2245</span>        <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l02246" name="l02246"></a><span class="lineno"> 2246</span>}</div>
<div class="line"><a id="l02247" name="l02247"></a><span class="lineno"> 2247</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02248" name="l02248"></a><span class="lineno"> 2248</span> </div>
<div class="foldopen" id="foldopen02249" data-start="{" data-end="};">
<div class="line"><a id="l02249" name="l02249"></a><span class="lineno"><a class="line" href="classzmq_1_1monitor__t.html"> 2249</a></span><span class="keyword">class </span>monitor_t</div>
<div class="line"><a id="l02250" name="l02250"></a><span class="lineno"> 2250</span>{</div>
<div class="line"><a id="l02251" name="l02251"></a><span class="lineno"> 2251</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l02252" name="l02252"></a><span class="lineno"> 2252</span>    monitor_t() : _socket(), _monitor_socket() {}</div>
<div class="line"><a id="l02253" name="l02253"></a><span class="lineno"> 2253</span> </div>
<div class="line"><a id="l02254" name="l02254"></a><span class="lineno"> 2254</span>    <span class="keyword">virtual</span> ~monitor_t() { close(); }</div>
<div class="line"><a id="l02255" name="l02255"></a><span class="lineno"> 2255</span> </div>
<div class="line"><a id="l02256" name="l02256"></a><span class="lineno"> 2256</span><span class="preprocessor">#ifdef ZMQ_HAS_RVALUE_REFS</span></div>
<div class="line"><a id="l02257" name="l02257"></a><span class="lineno"> 2257</span>    monitor_t(monitor_t &amp;&amp;rhs) ZMQ_NOTHROW : _socket(), _monitor_socket()</div>
<div class="line"><a id="l02258" name="l02258"></a><span class="lineno"> 2258</span>    {</div>
<div class="line"><a id="l02259" name="l02259"></a><span class="lineno"> 2259</span>        std::swap(_socket, rhs._socket);</div>
<div class="line"><a id="l02260" name="l02260"></a><span class="lineno"> 2260</span>        std::swap(_monitor_socket, rhs._monitor_socket);</div>
<div class="line"><a id="l02261" name="l02261"></a><span class="lineno"> 2261</span>    }</div>
<div class="line"><a id="l02262" name="l02262"></a><span class="lineno"> 2262</span> </div>
<div class="line"><a id="l02263" name="l02263"></a><span class="lineno"> 2263</span>    monitor_t &amp;operator=(monitor_t &amp;&amp;rhs) ZMQ_NOTHROW</div>
<div class="line"><a id="l02264" name="l02264"></a><span class="lineno"> 2264</span>    {</div>
<div class="line"><a id="l02265" name="l02265"></a><span class="lineno"> 2265</span>        close();</div>
<div class="line"><a id="l02266" name="l02266"></a><span class="lineno"> 2266</span>        _socket = <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a>();</div>
<div class="line"><a id="l02267" name="l02267"></a><span class="lineno"> 2267</span>        std::swap(_socket, rhs._socket);</div>
<div class="line"><a id="l02268" name="l02268"></a><span class="lineno"> 2268</span>        std::swap(_monitor_socket, rhs._monitor_socket);</div>
<div class="line"><a id="l02269" name="l02269"></a><span class="lineno"> 2269</span>        <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a id="l02270" name="l02270"></a><span class="lineno"> 2270</span>    }</div>
<div class="line"><a id="l02271" name="l02271"></a><span class="lineno"> 2271</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02272" name="l02272"></a><span class="lineno"> 2272</span> </div>
<div class="line"><a id="l02273" name="l02273"></a><span class="lineno"> 2273</span> </div>
<div class="line"><a id="l02274" name="l02274"></a><span class="lineno"> 2274</span>    <span class="keywordtype">void</span></div>
<div class="line"><a id="l02275" name="l02275"></a><span class="lineno"> 2275</span>    monitor(<a class="code hl_class" href="classzmq_1_1socket__t.html">socket_t</a> &amp;socket, std::string <span class="keyword">const</span> &amp;addr, <span class="keywordtype">int</span> events = ZMQ_EVENT_ALL)</div>
<div class="line"><a id="l02276" name="l02276"></a><span class="lineno"> 2276</span>    {</div>
<div class="line"><a id="l02277" name="l02277"></a><span class="lineno"> 2277</span>        monitor(socket, addr.c_str(), events);</div>
<div class="line"><a id="l02278" name="l02278"></a><span class="lineno"> 2278</span>    }</div>
<div class="line"><a id="l02279" name="l02279"></a><span class="lineno"> 2279</span> </div>
<div class="line"><a id="l02280" name="l02280"></a><span class="lineno"> 2280</span>    <span class="keywordtype">void</span> monitor(<a class="code hl_class" href="classzmq_1_1socket__t.html">socket_t</a> &amp;socket, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_, <span class="keywordtype">int</span> events = ZMQ_EVENT_ALL)</div>
<div class="line"><a id="l02281" name="l02281"></a><span class="lineno"> 2281</span>    {</div>
<div class="line"><a id="l02282" name="l02282"></a><span class="lineno"> 2282</span>        init(socket, addr_, events);</div>
<div class="line"><a id="l02283" name="l02283"></a><span class="lineno"> 2283</span>        <span class="keywordflow">while</span> (<span class="keyword">true</span>) {</div>
<div class="line"><a id="l02284" name="l02284"></a><span class="lineno"> 2284</span>            check_event(-1);</div>
<div class="line"><a id="l02285" name="l02285"></a><span class="lineno"> 2285</span>        }</div>
<div class="line"><a id="l02286" name="l02286"></a><span class="lineno"> 2286</span>    }</div>
<div class="line"><a id="l02287" name="l02287"></a><span class="lineno"> 2287</span> </div>
<div class="line"><a id="l02288" name="l02288"></a><span class="lineno"> 2288</span>    <span class="keywordtype">void</span> init(<a class="code hl_class" href="classzmq_1_1socket__t.html">socket_t</a> &amp;socket, std::string <span class="keyword">const</span> &amp;addr, <span class="keywordtype">int</span> events = ZMQ_EVENT_ALL)</div>
<div class="line"><a id="l02289" name="l02289"></a><span class="lineno"> 2289</span>    {</div>
<div class="line"><a id="l02290" name="l02290"></a><span class="lineno"> 2290</span>        init(socket, addr.c_str(), events);</div>
<div class="line"><a id="l02291" name="l02291"></a><span class="lineno"> 2291</span>    }</div>
<div class="line"><a id="l02292" name="l02292"></a><span class="lineno"> 2292</span> </div>
<div class="line"><a id="l02293" name="l02293"></a><span class="lineno"> 2293</span>    <span class="keywordtype">void</span> init(<a class="code hl_class" href="classzmq_1_1socket__t.html">socket_t</a> &amp;socket, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_, <span class="keywordtype">int</span> events = ZMQ_EVENT_ALL)</div>
<div class="line"><a id="l02294" name="l02294"></a><span class="lineno"> 2294</span>    {</div>
<div class="line"><a id="l02295" name="l02295"></a><span class="lineno"> 2295</span>        <span class="keywordtype">int</span> rc = zmq_socket_monitor(socket.handle(), addr_, events);</div>
<div class="line"><a id="l02296" name="l02296"></a><span class="lineno"> 2296</span>        <span class="keywordflow">if</span> (rc != 0)</div>
<div class="line"><a id="l02297" name="l02297"></a><span class="lineno"> 2297</span>            <span class="keywordflow">throw</span> <a class="code hl_class" href="classzmq_1_1error__t.html">error_t</a>();</div>
<div class="line"><a id="l02298" name="l02298"></a><span class="lineno"> 2298</span> </div>
<div class="line"><a id="l02299" name="l02299"></a><span class="lineno"> 2299</span>        _socket = socket;</div>
<div class="line"><a id="l02300" name="l02300"></a><span class="lineno"> 2300</span>        _monitor_socket = <a class="code hl_class" href="classzmq_1_1socket__t.html">socket_t</a>(socket.ctxptr, ZMQ_PAIR);</div>
<div class="line"><a id="l02301" name="l02301"></a><span class="lineno"> 2301</span>        _monitor_socket.connect(addr_);</div>
<div class="line"><a id="l02302" name="l02302"></a><span class="lineno"> 2302</span> </div>
<div class="line"><a id="l02303" name="l02303"></a><span class="lineno"> 2303</span>        on_monitor_started();</div>
<div class="line"><a id="l02304" name="l02304"></a><span class="lineno"> 2304</span>    }</div>
<div class="line"><a id="l02305" name="l02305"></a><span class="lineno"> 2305</span> </div>
<div class="line"><a id="l02306" name="l02306"></a><span class="lineno"> 2306</span>    <span class="keywordtype">bool</span> check_event(<span class="keywordtype">int</span> timeout = 0)</div>
<div class="line"><a id="l02307" name="l02307"></a><span class="lineno"> 2307</span>    {</div>
<div class="line"><a id="l02308" name="l02308"></a><span class="lineno"> 2308</span>        assert(_monitor_socket);</div>
<div class="line"><a id="l02309" name="l02309"></a><span class="lineno"> 2309</span> </div>
<div class="line"><a id="l02310" name="l02310"></a><span class="lineno"> 2310</span>        zmq_msg_t eventMsg;</div>
<div class="line"><a id="l02311" name="l02311"></a><span class="lineno"> 2311</span>        zmq_msg_init(&amp;eventMsg);</div>
<div class="line"><a id="l02312" name="l02312"></a><span class="lineno"> 2312</span> </div>
<div class="line"><a id="l02313" name="l02313"></a><span class="lineno"> 2313</span>        zmq::pollitem_t items[] = {</div>
<div class="line"><a id="l02314" name="l02314"></a><span class="lineno"> 2314</span>          {_monitor_socket.handle(), 0, ZMQ_POLLIN, 0},</div>
<div class="line"><a id="l02315" name="l02315"></a><span class="lineno"> 2315</span>        };</div>
<div class="line"><a id="l02316" name="l02316"></a><span class="lineno"> 2316</span> </div>
<div class="line"><a id="l02317" name="l02317"></a><span class="lineno"> 2317</span>        zmq::poll(&amp;items[0], 1, timeout);</div>
<div class="line"><a id="l02318" name="l02318"></a><span class="lineno"> 2318</span> </div>
<div class="line"><a id="l02319" name="l02319"></a><span class="lineno"> 2319</span>        <span class="keywordflow">if</span> (items[0].revents &amp; ZMQ_POLLIN) {</div>
<div class="line"><a id="l02320" name="l02320"></a><span class="lineno"> 2320</span>            <span class="keywordtype">int</span> rc = zmq_msg_recv(&amp;eventMsg, _monitor_socket.handle(), 0);</div>
<div class="line"><a id="l02321" name="l02321"></a><span class="lineno"> 2321</span>            <span class="keywordflow">if</span> (rc == -1 &amp;&amp; zmq_errno() == ETERM)</div>
<div class="line"><a id="l02322" name="l02322"></a><span class="lineno"> 2322</span>                <span class="keywordflow">return</span> <span class="keyword">false</span>;</div>
<div class="line"><a id="l02323" name="l02323"></a><span class="lineno"> 2323</span>            assert(rc != -1);</div>
<div class="line"><a id="l02324" name="l02324"></a><span class="lineno"> 2324</span> </div>
<div class="line"><a id="l02325" name="l02325"></a><span class="lineno"> 2325</span>        } <span class="keywordflow">else</span> {</div>
<div class="line"><a id="l02326" name="l02326"></a><span class="lineno"> 2326</span>            zmq_msg_close(&amp;eventMsg);</div>
<div class="line"><a id="l02327" name="l02327"></a><span class="lineno"> 2327</span>            <span class="keywordflow">return</span> <span class="keyword">false</span>;</div>
<div class="line"><a id="l02328" name="l02328"></a><span class="lineno"> 2328</span>        }</div>
<div class="line"><a id="l02329" name="l02329"></a><span class="lineno"> 2329</span> </div>
<div class="line"><a id="l02330" name="l02330"></a><span class="lineno"> 2330</span><span class="preprocessor">#if ZMQ_VERSION_MAJOR &gt;= 4</span></div>
<div class="line"><a id="l02331" name="l02331"></a><span class="lineno"> 2331</span>        <span class="keyword">const</span> <span class="keywordtype">char</span> *data = <span class="keyword">static_cast&lt;</span><span class="keyword">const </span><span class="keywordtype">char</span> *<span class="keyword">&gt;</span>(zmq_msg_data(&amp;eventMsg));</div>
<div class="line"><a id="l02332" name="l02332"></a><span class="lineno"> 2332</span>        <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> msgEvent;</div>
<div class="line"><a id="l02333" name="l02333"></a><span class="lineno"> 2333</span>        memcpy(&amp;msgEvent.event, data, <span class="keyword">sizeof</span>(uint16_t));</div>
<div class="line"><a id="l02334" name="l02334"></a><span class="lineno"> 2334</span>        data += <span class="keyword">sizeof</span>(uint16_t);</div>
<div class="line"><a id="l02335" name="l02335"></a><span class="lineno"> 2335</span>        memcpy(&amp;msgEvent.value, data, <span class="keyword">sizeof</span>(int32_t));</div>
<div class="line"><a id="l02336" name="l02336"></a><span class="lineno"> 2336</span>        <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> *<span class="keyword">event</span> = &amp;msgEvent;</div>
<div class="line"><a id="l02337" name="l02337"></a><span class="lineno"> 2337</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l02338" name="l02338"></a><span class="lineno"> 2338</span>        <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> *<span class="keyword">event</span> = <span class="keyword">static_cast&lt;</span><a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> *<span class="keyword">&gt;</span>(zmq_msg_data(&amp;eventMsg));</div>
<div class="line"><a id="l02339" name="l02339"></a><span class="lineno"> 2339</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02340" name="l02340"></a><span class="lineno"> 2340</span> </div>
<div class="line"><a id="l02341" name="l02341"></a><span class="lineno"> 2341</span><span class="preprocessor">#ifdef ZMQ_NEW_MONITOR_EVENT_LAYOUT</span></div>
<div class="line"><a id="l02342" name="l02342"></a><span class="lineno"> 2342</span>        zmq_msg_t addrMsg;</div>
<div class="line"><a id="l02343" name="l02343"></a><span class="lineno"> 2343</span>        zmq_msg_init(&amp;addrMsg);</div>
<div class="line"><a id="l02344" name="l02344"></a><span class="lineno"> 2344</span>        <span class="keywordtype">int</span> rc = zmq_msg_recv(&amp;addrMsg, _monitor_socket.handle(), 0);</div>
<div class="line"><a id="l02345" name="l02345"></a><span class="lineno"> 2345</span>        <span class="keywordflow">if</span> (rc == -1 &amp;&amp; zmq_errno() == ETERM) {</div>
<div class="line"><a id="l02346" name="l02346"></a><span class="lineno"> 2346</span>            zmq_msg_close(&amp;eventMsg);</div>
<div class="line"><a id="l02347" name="l02347"></a><span class="lineno"> 2347</span>            <span class="keywordflow">return</span> <span class="keyword">false</span>;</div>
<div class="line"><a id="l02348" name="l02348"></a><span class="lineno"> 2348</span>        }</div>
<div class="line"><a id="l02349" name="l02349"></a><span class="lineno"> 2349</span> </div>
<div class="line"><a id="l02350" name="l02350"></a><span class="lineno"> 2350</span>        assert(rc != -1);</div>
<div class="line"><a id="l02351" name="l02351"></a><span class="lineno"> 2351</span>        <span class="keyword">const</span> <span class="keywordtype">char</span> *str = <span class="keyword">static_cast&lt;</span><span class="keyword">const </span><span class="keywordtype">char</span> *<span class="keyword">&gt;</span>(zmq_msg_data(&amp;addrMsg));</div>
<div class="line"><a id="l02352" name="l02352"></a><span class="lineno"> 2352</span>        std::string address(str, str + zmq_msg_size(&amp;addrMsg));</div>
<div class="line"><a id="l02353" name="l02353"></a><span class="lineno"> 2353</span>        zmq_msg_close(&amp;addrMsg);</div>
<div class="line"><a id="l02354" name="l02354"></a><span class="lineno"> 2354</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l02355" name="l02355"></a><span class="lineno"> 2355</span>        <span class="comment">// Bit of a hack, but all events in the zmq_event_t union have the same layout so this will work for all event types.</span></div>
<div class="line"><a id="l02356" name="l02356"></a><span class="lineno"> 2356</span>        std::string address = <span class="keyword">event</span>-&gt;data.connected.addr;</div>
<div class="line"><a id="l02357" name="l02357"></a><span class="lineno"> 2357</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02358" name="l02358"></a><span class="lineno"> 2358</span> </div>
<div class="line"><a id="l02359" name="l02359"></a><span class="lineno"> 2359</span><span class="preprocessor">#ifdef ZMQ_EVENT_MONITOR_STOPPED</span></div>
<div class="line"><a id="l02360" name="l02360"></a><span class="lineno"> 2360</span>        <span class="keywordflow">if</span> (event-&gt;event == ZMQ_EVENT_MONITOR_STOPPED) {</div>
<div class="line"><a id="l02361" name="l02361"></a><span class="lineno"> 2361</span>            zmq_msg_close(&amp;eventMsg);</div>
<div class="line"><a id="l02362" name="l02362"></a><span class="lineno"> 2362</span>            <span class="keywordflow">return</span> <span class="keyword">false</span>;</div>
<div class="line"><a id="l02363" name="l02363"></a><span class="lineno"> 2363</span>        }</div>
<div class="line"><a id="l02364" name="l02364"></a><span class="lineno"> 2364</span> </div>
<div class="line"><a id="l02365" name="l02365"></a><span class="lineno"> 2365</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02366" name="l02366"></a><span class="lineno"> 2366</span> </div>
<div class="line"><a id="l02367" name="l02367"></a><span class="lineno"> 2367</span>        <span class="keywordflow">switch</span> (event-&gt;event) {</div>
<div class="line"><a id="l02368" name="l02368"></a><span class="lineno"> 2368</span>            <span class="keywordflow">case</span> ZMQ_EVENT_CONNECTED:</div>
<div class="line"><a id="l02369" name="l02369"></a><span class="lineno"> 2369</span>                on_event_connected(*event, address.c_str());</div>
<div class="line"><a id="l02370" name="l02370"></a><span class="lineno"> 2370</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02371" name="l02371"></a><span class="lineno"> 2371</span>            <span class="keywordflow">case</span> ZMQ_EVENT_CONNECT_DELAYED:</div>
<div class="line"><a id="l02372" name="l02372"></a><span class="lineno"> 2372</span>                on_event_connect_delayed(*event, address.c_str());</div>
<div class="line"><a id="l02373" name="l02373"></a><span class="lineno"> 2373</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02374" name="l02374"></a><span class="lineno"> 2374</span>            <span class="keywordflow">case</span> ZMQ_EVENT_CONNECT_RETRIED:</div>
<div class="line"><a id="l02375" name="l02375"></a><span class="lineno"> 2375</span>                on_event_connect_retried(*event, address.c_str());</div>
<div class="line"><a id="l02376" name="l02376"></a><span class="lineno"> 2376</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02377" name="l02377"></a><span class="lineno"> 2377</span>            <span class="keywordflow">case</span> ZMQ_EVENT_LISTENING:</div>
<div class="line"><a id="l02378" name="l02378"></a><span class="lineno"> 2378</span>                on_event_listening(*event, address.c_str());</div>
<div class="line"><a id="l02379" name="l02379"></a><span class="lineno"> 2379</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02380" name="l02380"></a><span class="lineno"> 2380</span>            <span class="keywordflow">case</span> ZMQ_EVENT_BIND_FAILED:</div>
<div class="line"><a id="l02381" name="l02381"></a><span class="lineno"> 2381</span>                on_event_bind_failed(*event, address.c_str());</div>
<div class="line"><a id="l02382" name="l02382"></a><span class="lineno"> 2382</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02383" name="l02383"></a><span class="lineno"> 2383</span>            <span class="keywordflow">case</span> ZMQ_EVENT_ACCEPTED:</div>
<div class="line"><a id="l02384" name="l02384"></a><span class="lineno"> 2384</span>                on_event_accepted(*event, address.c_str());</div>
<div class="line"><a id="l02385" name="l02385"></a><span class="lineno"> 2385</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02386" name="l02386"></a><span class="lineno"> 2386</span>            <span class="keywordflow">case</span> ZMQ_EVENT_ACCEPT_FAILED:</div>
<div class="line"><a id="l02387" name="l02387"></a><span class="lineno"> 2387</span>                on_event_accept_failed(*event, address.c_str());</div>
<div class="line"><a id="l02388" name="l02388"></a><span class="lineno"> 2388</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02389" name="l02389"></a><span class="lineno"> 2389</span>            <span class="keywordflow">case</span> ZMQ_EVENT_CLOSED:</div>
<div class="line"><a id="l02390" name="l02390"></a><span class="lineno"> 2390</span>                on_event_closed(*event, address.c_str());</div>
<div class="line"><a id="l02391" name="l02391"></a><span class="lineno"> 2391</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02392" name="l02392"></a><span class="lineno"> 2392</span>            <span class="keywordflow">case</span> ZMQ_EVENT_CLOSE_FAILED:</div>
<div class="line"><a id="l02393" name="l02393"></a><span class="lineno"> 2393</span>                on_event_close_failed(*event, address.c_str());</div>
<div class="line"><a id="l02394" name="l02394"></a><span class="lineno"> 2394</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02395" name="l02395"></a><span class="lineno"> 2395</span>            <span class="keywordflow">case</span> ZMQ_EVENT_DISCONNECTED:</div>
<div class="line"><a id="l02396" name="l02396"></a><span class="lineno"> 2396</span>                on_event_disconnected(*event, address.c_str());</div>
<div class="line"><a id="l02397" name="l02397"></a><span class="lineno"> 2397</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02398" name="l02398"></a><span class="lineno"> 2398</span><span class="preprocessor">#ifdef ZMQ_BUILD_DRAFT_API</span></div>
<div class="line"><a id="l02399" name="l02399"></a><span class="lineno"> 2399</span><span class="preprocessor">#if ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(4, 2, 3)</span></div>
<div class="line"><a id="l02400" name="l02400"></a><span class="lineno"> 2400</span>            <span class="keywordflow">case</span> ZMQ_EVENT_HANDSHAKE_FAILED_NO_DETAIL:</div>
<div class="line"><a id="l02401" name="l02401"></a><span class="lineno"> 2401</span>                on_event_handshake_failed_no_detail(*event, address.c_str());</div>
<div class="line"><a id="l02402" name="l02402"></a><span class="lineno"> 2402</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02403" name="l02403"></a><span class="lineno"> 2403</span>            <span class="keywordflow">case</span> ZMQ_EVENT_HANDSHAKE_FAILED_PROTOCOL:</div>
<div class="line"><a id="l02404" name="l02404"></a><span class="lineno"> 2404</span>                on_event_handshake_failed_protocol(*event, address.c_str());</div>
<div class="line"><a id="l02405" name="l02405"></a><span class="lineno"> 2405</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02406" name="l02406"></a><span class="lineno"> 2406</span>            <span class="keywordflow">case</span> ZMQ_EVENT_HANDSHAKE_FAILED_AUTH:</div>
<div class="line"><a id="l02407" name="l02407"></a><span class="lineno"> 2407</span>                on_event_handshake_failed_auth(*event, address.c_str());</div>
<div class="line"><a id="l02408" name="l02408"></a><span class="lineno"> 2408</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02409" name="l02409"></a><span class="lineno"> 2409</span>            <span class="keywordflow">case</span> ZMQ_EVENT_HANDSHAKE_SUCCEEDED:</div>
<div class="line"><a id="l02410" name="l02410"></a><span class="lineno"> 2410</span>                on_event_handshake_succeeded(*event, address.c_str());</div>
<div class="line"><a id="l02411" name="l02411"></a><span class="lineno"> 2411</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02412" name="l02412"></a><span class="lineno"> 2412</span><span class="preprocessor">#elif ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(4, 2, 1)</span></div>
<div class="line"><a id="l02413" name="l02413"></a><span class="lineno"> 2413</span>            <span class="keywordflow">case</span> ZMQ_EVENT_HANDSHAKE_FAILED:</div>
<div class="line"><a id="l02414" name="l02414"></a><span class="lineno"> 2414</span>                on_event_handshake_failed(*event, address.c_str());</div>
<div class="line"><a id="l02415" name="l02415"></a><span class="lineno"> 2415</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02416" name="l02416"></a><span class="lineno"> 2416</span>            <span class="keywordflow">case</span> ZMQ_EVENT_HANDSHAKE_SUCCEED:</div>
<div class="line"><a id="l02417" name="l02417"></a><span class="lineno"> 2417</span>                on_event_handshake_succeed(*event, address.c_str());</div>
<div class="line"><a id="l02418" name="l02418"></a><span class="lineno"> 2418</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02419" name="l02419"></a><span class="lineno"> 2419</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02420" name="l02420"></a><span class="lineno"> 2420</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02421" name="l02421"></a><span class="lineno"> 2421</span>            <span class="keywordflow">default</span>:</div>
<div class="line"><a id="l02422" name="l02422"></a><span class="lineno"> 2422</span>                on_event_unknown(*event, address.c_str());</div>
<div class="line"><a id="l02423" name="l02423"></a><span class="lineno"> 2423</span>                <span class="keywordflow">break</span>;</div>
<div class="line"><a id="l02424" name="l02424"></a><span class="lineno"> 2424</span>        }</div>
<div class="line"><a id="l02425" name="l02425"></a><span class="lineno"> 2425</span>        zmq_msg_close(&amp;eventMsg);</div>
<div class="line"><a id="l02426" name="l02426"></a><span class="lineno"> 2426</span> </div>
<div class="line"><a id="l02427" name="l02427"></a><span class="lineno"> 2427</span>        <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a id="l02428" name="l02428"></a><span class="lineno"> 2428</span>    }</div>
<div class="line"><a id="l02429" name="l02429"></a><span class="lineno"> 2429</span> </div>
<div class="line"><a id="l02430" name="l02430"></a><span class="lineno"> 2430</span><span class="preprocessor">#ifdef ZMQ_EVENT_MONITOR_STOPPED</span></div>
<div class="line"><a id="l02431" name="l02431"></a><span class="lineno"> 2431</span>    <span class="keywordtype">void</span> abort()</div>
<div class="line"><a id="l02432" name="l02432"></a><span class="lineno"> 2432</span>    {</div>
<div class="line"><a id="l02433" name="l02433"></a><span class="lineno"> 2433</span>        <span class="keywordflow">if</span> (_socket)</div>
<div class="line"><a id="l02434" name="l02434"></a><span class="lineno"> 2434</span>            zmq_socket_monitor(_socket.handle(), ZMQ_NULLPTR, 0);</div>
<div class="line"><a id="l02435" name="l02435"></a><span class="lineno"> 2435</span> </div>
<div class="line"><a id="l02436" name="l02436"></a><span class="lineno"> 2436</span>        _socket = <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a>();</div>
<div class="line"><a id="l02437" name="l02437"></a><span class="lineno"> 2437</span>    }</div>
<div class="line"><a id="l02438" name="l02438"></a><span class="lineno"> 2438</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02439" name="l02439"></a><span class="lineno"> 2439</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_monitor_started() {}</div>
<div class="line"><a id="l02440" name="l02440"></a><span class="lineno"> 2440</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_connected(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02441" name="l02441"></a><span class="lineno"> 2441</span>    {</div>
<div class="line"><a id="l02442" name="l02442"></a><span class="lineno"> 2442</span>        (void) event_;</div>
<div class="line"><a id="l02443" name="l02443"></a><span class="lineno"> 2443</span>        (void) addr_;</div>
<div class="line"><a id="l02444" name="l02444"></a><span class="lineno"> 2444</span>    }</div>
<div class="line"><a id="l02445" name="l02445"></a><span class="lineno"> 2445</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_connect_delayed(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_,</div>
<div class="line"><a id="l02446" name="l02446"></a><span class="lineno"> 2446</span>                                          <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02447" name="l02447"></a><span class="lineno"> 2447</span>    {</div>
<div class="line"><a id="l02448" name="l02448"></a><span class="lineno"> 2448</span>        (void) event_;</div>
<div class="line"><a id="l02449" name="l02449"></a><span class="lineno"> 2449</span>        (void) addr_;</div>
<div class="line"><a id="l02450" name="l02450"></a><span class="lineno"> 2450</span>    }</div>
<div class="line"><a id="l02451" name="l02451"></a><span class="lineno"> 2451</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_connect_retried(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_,</div>
<div class="line"><a id="l02452" name="l02452"></a><span class="lineno"> 2452</span>                                          <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02453" name="l02453"></a><span class="lineno"> 2453</span>    {</div>
<div class="line"><a id="l02454" name="l02454"></a><span class="lineno"> 2454</span>        (void) event_;</div>
<div class="line"><a id="l02455" name="l02455"></a><span class="lineno"> 2455</span>        (void) addr_;</div>
<div class="line"><a id="l02456" name="l02456"></a><span class="lineno"> 2456</span>    }</div>
<div class="line"><a id="l02457" name="l02457"></a><span class="lineno"> 2457</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_listening(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02458" name="l02458"></a><span class="lineno"> 2458</span>    {</div>
<div class="line"><a id="l02459" name="l02459"></a><span class="lineno"> 2459</span>        (void) event_;</div>
<div class="line"><a id="l02460" name="l02460"></a><span class="lineno"> 2460</span>        (void) addr_;</div>
<div class="line"><a id="l02461" name="l02461"></a><span class="lineno"> 2461</span>    }</div>
<div class="line"><a id="l02462" name="l02462"></a><span class="lineno"> 2462</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_bind_failed(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02463" name="l02463"></a><span class="lineno"> 2463</span>    {</div>
<div class="line"><a id="l02464" name="l02464"></a><span class="lineno"> 2464</span>        (void) event_;</div>
<div class="line"><a id="l02465" name="l02465"></a><span class="lineno"> 2465</span>        (void) addr_;</div>
<div class="line"><a id="l02466" name="l02466"></a><span class="lineno"> 2466</span>    }</div>
<div class="line"><a id="l02467" name="l02467"></a><span class="lineno"> 2467</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_accepted(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02468" name="l02468"></a><span class="lineno"> 2468</span>    {</div>
<div class="line"><a id="l02469" name="l02469"></a><span class="lineno"> 2469</span>        (void) event_;</div>
<div class="line"><a id="l02470" name="l02470"></a><span class="lineno"> 2470</span>        (void) addr_;</div>
<div class="line"><a id="l02471" name="l02471"></a><span class="lineno"> 2471</span>    }</div>
<div class="line"><a id="l02472" name="l02472"></a><span class="lineno"> 2472</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_accept_failed(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02473" name="l02473"></a><span class="lineno"> 2473</span>    {</div>
<div class="line"><a id="l02474" name="l02474"></a><span class="lineno"> 2474</span>        (void) event_;</div>
<div class="line"><a id="l02475" name="l02475"></a><span class="lineno"> 2475</span>        (void) addr_;</div>
<div class="line"><a id="l02476" name="l02476"></a><span class="lineno"> 2476</span>    }</div>
<div class="line"><a id="l02477" name="l02477"></a><span class="lineno"> 2477</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_closed(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02478" name="l02478"></a><span class="lineno"> 2478</span>    {</div>
<div class="line"><a id="l02479" name="l02479"></a><span class="lineno"> 2479</span>        (void) event_;</div>
<div class="line"><a id="l02480" name="l02480"></a><span class="lineno"> 2480</span>        (void) addr_;</div>
<div class="line"><a id="l02481" name="l02481"></a><span class="lineno"> 2481</span>    }</div>
<div class="line"><a id="l02482" name="l02482"></a><span class="lineno"> 2482</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_close_failed(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02483" name="l02483"></a><span class="lineno"> 2483</span>    {</div>
<div class="line"><a id="l02484" name="l02484"></a><span class="lineno"> 2484</span>        (void) event_;</div>
<div class="line"><a id="l02485" name="l02485"></a><span class="lineno"> 2485</span>        (void) addr_;</div>
<div class="line"><a id="l02486" name="l02486"></a><span class="lineno"> 2486</span>    }</div>
<div class="line"><a id="l02487" name="l02487"></a><span class="lineno"> 2487</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_disconnected(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02488" name="l02488"></a><span class="lineno"> 2488</span>    {</div>
<div class="line"><a id="l02489" name="l02489"></a><span class="lineno"> 2489</span>        (void) event_;</div>
<div class="line"><a id="l02490" name="l02490"></a><span class="lineno"> 2490</span>        (void) addr_;</div>
<div class="line"><a id="l02491" name="l02491"></a><span class="lineno"> 2491</span>    }</div>
<div class="line"><a id="l02492" name="l02492"></a><span class="lineno"> 2492</span><span class="preprocessor">#if ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(4, 2, 3)</span></div>
<div class="line"><a id="l02493" name="l02493"></a><span class="lineno"> 2493</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_handshake_failed_no_detail(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_,</div>
<div class="line"><a id="l02494" name="l02494"></a><span class="lineno"> 2494</span>                                                     <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02495" name="l02495"></a><span class="lineno"> 2495</span>    {</div>
<div class="line"><a id="l02496" name="l02496"></a><span class="lineno"> 2496</span>        (void) event_;</div>
<div class="line"><a id="l02497" name="l02497"></a><span class="lineno"> 2497</span>        (void) addr_;</div>
<div class="line"><a id="l02498" name="l02498"></a><span class="lineno"> 2498</span>    }</div>
<div class="line"><a id="l02499" name="l02499"></a><span class="lineno"> 2499</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_handshake_failed_protocol(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_,</div>
<div class="line"><a id="l02500" name="l02500"></a><span class="lineno"> 2500</span>                                                    <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02501" name="l02501"></a><span class="lineno"> 2501</span>    {</div>
<div class="line"><a id="l02502" name="l02502"></a><span class="lineno"> 2502</span>        (void) event_;</div>
<div class="line"><a id="l02503" name="l02503"></a><span class="lineno"> 2503</span>        (void) addr_;</div>
<div class="line"><a id="l02504" name="l02504"></a><span class="lineno"> 2504</span>    }</div>
<div class="line"><a id="l02505" name="l02505"></a><span class="lineno"> 2505</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_handshake_failed_auth(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_,</div>
<div class="line"><a id="l02506" name="l02506"></a><span class="lineno"> 2506</span>                                                <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02507" name="l02507"></a><span class="lineno"> 2507</span>    {</div>
<div class="line"><a id="l02508" name="l02508"></a><span class="lineno"> 2508</span>        (void) event_;</div>
<div class="line"><a id="l02509" name="l02509"></a><span class="lineno"> 2509</span>        (void) addr_;</div>
<div class="line"><a id="l02510" name="l02510"></a><span class="lineno"> 2510</span>    }</div>
<div class="line"><a id="l02511" name="l02511"></a><span class="lineno"> 2511</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_handshake_succeeded(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_,</div>
<div class="line"><a id="l02512" name="l02512"></a><span class="lineno"> 2512</span>                                              <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02513" name="l02513"></a><span class="lineno"> 2513</span>    {</div>
<div class="line"><a id="l02514" name="l02514"></a><span class="lineno"> 2514</span>        (void) event_;</div>
<div class="line"><a id="l02515" name="l02515"></a><span class="lineno"> 2515</span>        (void) addr_;</div>
<div class="line"><a id="l02516" name="l02516"></a><span class="lineno"> 2516</span>    }</div>
<div class="line"><a id="l02517" name="l02517"></a><span class="lineno"> 2517</span><span class="preprocessor">#elif ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(4, 2, 1)</span></div>
<div class="line"><a id="l02518" name="l02518"></a><span class="lineno"> 2518</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_handshake_failed(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_,</div>
<div class="line"><a id="l02519" name="l02519"></a><span class="lineno"> 2519</span>                                           <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02520" name="l02520"></a><span class="lineno"> 2520</span>    {</div>
<div class="line"><a id="l02521" name="l02521"></a><span class="lineno"> 2521</span>        (void) event_;</div>
<div class="line"><a id="l02522" name="l02522"></a><span class="lineno"> 2522</span>        (void) addr_;</div>
<div class="line"><a id="l02523" name="l02523"></a><span class="lineno"> 2523</span>    }</div>
<div class="line"><a id="l02524" name="l02524"></a><span class="lineno"> 2524</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_handshake_succeed(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_,</div>
<div class="line"><a id="l02525" name="l02525"></a><span class="lineno"> 2525</span>                                            <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02526" name="l02526"></a><span class="lineno"> 2526</span>    {</div>
<div class="line"><a id="l02527" name="l02527"></a><span class="lineno"> 2527</span>        (void) event_;</div>
<div class="line"><a id="l02528" name="l02528"></a><span class="lineno"> 2528</span>        (void) addr_;</div>
<div class="line"><a id="l02529" name="l02529"></a><span class="lineno"> 2529</span>    }</div>
<div class="line"><a id="l02530" name="l02530"></a><span class="lineno"> 2530</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02531" name="l02531"></a><span class="lineno"> 2531</span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> on_event_unknown(<span class="keyword">const</span> <a class="code hl_struct" href="structzmq__event__t.html">zmq_event_t</a> &amp;event_, <span class="keyword">const</span> <span class="keywordtype">char</span> *addr_)</div>
<div class="line"><a id="l02532" name="l02532"></a><span class="lineno"> 2532</span>    {</div>
<div class="line"><a id="l02533" name="l02533"></a><span class="lineno"> 2533</span>        (void) event_;</div>
<div class="line"><a id="l02534" name="l02534"></a><span class="lineno"> 2534</span>        (void) addr_;</div>
<div class="line"><a id="l02535" name="l02535"></a><span class="lineno"> 2535</span>    }</div>
<div class="line"><a id="l02536" name="l02536"></a><span class="lineno"> 2536</span> </div>
<div class="line"><a id="l02537" name="l02537"></a><span class="lineno"> 2537</span>  <span class="keyword">private</span>:</div>
<div class="line"><a id="l02538" name="l02538"></a><span class="lineno"> 2538</span>    monitor_t(<span class="keyword">const</span> monitor_t &amp;) ZMQ_DELETED_FUNCTION;</div>
<div class="line"><a id="l02539" name="l02539"></a><span class="lineno"> 2539</span>    <span class="keywordtype">void</span> operator=(<span class="keyword">const</span> monitor_t &amp;) ZMQ_DELETED_FUNCTION;</div>
<div class="line"><a id="l02540" name="l02540"></a><span class="lineno"> 2540</span> </div>
<div class="line"><a id="l02541" name="l02541"></a><span class="lineno"> 2541</span>    <a class="code hl_class" href="classzmq_1_1socket__ref.html">socket_ref</a> _socket;</div>
<div class="line"><a id="l02542" name="l02542"></a><span class="lineno"> 2542</span>    <a class="code hl_class" href="classzmq_1_1socket__t.html">socket_t</a> _monitor_socket;</div>
<div class="line"><a id="l02543" name="l02543"></a><span class="lineno"> 2543</span> </div>
<div class="line"><a id="l02544" name="l02544"></a><span class="lineno"> 2544</span>    <span class="keywordtype">void</span> close() ZMQ_NOTHROW</div>
<div class="line"><a id="l02545" name="l02545"></a><span class="lineno"> 2545</span>    {</div>
<div class="line"><a id="l02546" name="l02546"></a><span class="lineno"> 2546</span>        <span class="keywordflow">if</span> (_socket)</div>
<div class="line"><a id="l02547" name="l02547"></a><span class="lineno"> 2547</span>            zmq_socket_monitor(_socket.handle(), ZMQ_NULLPTR, 0);</div>
<div class="line"><a id="l02548" name="l02548"></a><span class="lineno"> 2548</span>        _monitor_socket.close();</div>
<div class="line"><a id="l02549" name="l02549"></a><span class="lineno"> 2549</span>    }</div>
<div class="line"><a id="l02550" name="l02550"></a><span class="lineno"> 2550</span>};</div>
</div>
<div class="line"><a id="l02551" name="l02551"></a><span class="lineno"> 2551</span> </div>
<div class="line"><a id="l02552" name="l02552"></a><span class="lineno"> 2552</span><span class="preprocessor">#if defined(ZMQ_BUILD_DRAFT_API) &amp;&amp; defined(ZMQ_CPP11) &amp;&amp; defined(ZMQ_HAVE_POLLER)</span></div>
<div class="line"><a id="l02553" name="l02553"></a><span class="lineno"> 2553</span> </div>
<div class="line"><a id="l02554" name="l02554"></a><span class="lineno"> 2554</span><span class="comment">// polling events</span></div>
<div class="line"><a id="l02555" name="l02555"></a><span class="lineno"> 2555</span><span class="keyword">enum class</span> event_flags : <span class="keywordtype">short</span></div>
<div class="line"><a id="l02556" name="l02556"></a><span class="lineno"> 2556</span>{</div>
<div class="line"><a id="l02557" name="l02557"></a><span class="lineno"> 2557</span>    none = 0,</div>
<div class="line"><a id="l02558" name="l02558"></a><span class="lineno"> 2558</span>    pollin = ZMQ_POLLIN,</div>
<div class="line"><a id="l02559" name="l02559"></a><span class="lineno"> 2559</span>    pollout = ZMQ_POLLOUT,</div>
<div class="line"><a id="l02560" name="l02560"></a><span class="lineno"> 2560</span>    pollerr = ZMQ_POLLERR,</div>
<div class="line"><a id="l02561" name="l02561"></a><span class="lineno"> 2561</span>    pollpri = ZMQ_POLLPRI</div>
<div class="line"><a id="l02562" name="l02562"></a><span class="lineno"> 2562</span>};</div>
<div class="line"><a id="l02563" name="l02563"></a><span class="lineno"> 2563</span> </div>
<div class="line"><a id="l02564" name="l02564"></a><span class="lineno"> 2564</span><span class="keyword">constexpr</span> event_flags operator|(event_flags a, event_flags b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l02565" name="l02565"></a><span class="lineno"> 2565</span>{</div>
<div class="line"><a id="l02566" name="l02566"></a><span class="lineno"> 2566</span>    <span class="keywordflow">return</span> detail::enum_bit_or(a, b);</div>
<div class="line"><a id="l02567" name="l02567"></a><span class="lineno"> 2567</span>}</div>
<div class="line"><a id="l02568" name="l02568"></a><span class="lineno"> 2568</span><span class="keyword">constexpr</span> event_flags operator&amp;(event_flags a, event_flags b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l02569" name="l02569"></a><span class="lineno"> 2569</span>{</div>
<div class="line"><a id="l02570" name="l02570"></a><span class="lineno"> 2570</span>    <span class="keywordflow">return</span> detail::enum_bit_and(a, b);</div>
<div class="line"><a id="l02571" name="l02571"></a><span class="lineno"> 2571</span>}</div>
<div class="line"><a id="l02572" name="l02572"></a><span class="lineno"> 2572</span><span class="keyword">constexpr</span> event_flags operator^(event_flags a, event_flags b) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l02573" name="l02573"></a><span class="lineno"> 2573</span>{</div>
<div class="line"><a id="l02574" name="l02574"></a><span class="lineno"> 2574</span>    <span class="keywordflow">return</span> detail::enum_bit_xor(a, b);</div>
<div class="line"><a id="l02575" name="l02575"></a><span class="lineno"> 2575</span>}</div>
<div class="line"><a id="l02576" name="l02576"></a><span class="lineno"> 2576</span><span class="keyword">constexpr</span> event_flags operator~(event_flags a) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l02577" name="l02577"></a><span class="lineno"> 2577</span>{</div>
<div class="line"><a id="l02578" name="l02578"></a><span class="lineno"> 2578</span>    <span class="keywordflow">return</span> detail::enum_bit_not(a);</div>
<div class="line"><a id="l02579" name="l02579"></a><span class="lineno"> 2579</span>}</div>
<div class="line"><a id="l02580" name="l02580"></a><span class="lineno"> 2580</span> </div>
<div class="line"><a id="l02581" name="l02581"></a><span class="lineno"> 2581</span><span class="keyword">struct </span>no_user_data;</div>
<div class="line"><a id="l02582" name="l02582"></a><span class="lineno"> 2582</span> </div>
<div class="line"><a id="l02583" name="l02583"></a><span class="lineno"> 2583</span><span class="comment">// layout compatible with zmq_poller_event_t</span></div>
<div class="line"><a id="l02584" name="l02584"></a><span class="lineno"> 2584</span><span class="keyword">template</span>&lt;<span class="keyword">class</span> T = no_user_data&gt; <span class="keyword">struct </span>poller_event</div>
<div class="line"><a id="l02585" name="l02585"></a><span class="lineno"> 2585</span>{</div>
<div class="line"><a id="l02586" name="l02586"></a><span class="lineno"> 2586</span>    socket_ref socket;</div>
<div class="line"><a id="l02587" name="l02587"></a><span class="lineno"> 2587</span><span class="preprocessor">#ifdef _WIN32</span></div>
<div class="line"><a id="l02588" name="l02588"></a><span class="lineno"> 2588</span>    SOCKET fd;</div>
<div class="line"><a id="l02589" name="l02589"></a><span class="lineno"> 2589</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l02590" name="l02590"></a><span class="lineno"> 2590</span>    <span class="keywordtype">int</span> fd;</div>
<div class="line"><a id="l02591" name="l02591"></a><span class="lineno"> 2591</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02592" name="l02592"></a><span class="lineno"> 2592</span>    T *user_data;</div>
<div class="line"><a id="l02593" name="l02593"></a><span class="lineno"> 2593</span>    event_flags events;</div>
<div class="line"><a id="l02594" name="l02594"></a><span class="lineno"> 2594</span>};</div>
<div class="line"><a id="l02595" name="l02595"></a><span class="lineno"> 2595</span> </div>
<div class="line"><a id="l02596" name="l02596"></a><span class="lineno"> 2596</span><span class="keyword">template</span>&lt;<span class="keyword">typename</span> T = no_user_data&gt; <span class="keyword">class </span>poller_t</div>
<div class="line"><a id="l02597" name="l02597"></a><span class="lineno"> 2597</span>{</div>
<div class="line"><a id="l02598" name="l02598"></a><span class="lineno"> 2598</span>  <span class="keyword">public</span>:</div>
<div class="line"><a id="l02599" name="l02599"></a><span class="lineno"> 2599</span>    <span class="keyword">using </span>event_type = poller_event&lt;T&gt;;</div>
<div class="line"><a id="l02600" name="l02600"></a><span class="lineno"> 2600</span> </div>
<div class="line"><a id="l02601" name="l02601"></a><span class="lineno"> 2601</span>    poller_t() : poller_ptr(zmq_poller_new())</div>
<div class="line"><a id="l02602" name="l02602"></a><span class="lineno"> 2602</span>    {</div>
<div class="line"><a id="l02603" name="l02603"></a><span class="lineno"> 2603</span>        <span class="keywordflow">if</span> (!poller_ptr)</div>
<div class="line"><a id="l02604" name="l02604"></a><span class="lineno"> 2604</span>            <span class="keywordflow">throw</span> error_t();</div>
<div class="line"><a id="l02605" name="l02605"></a><span class="lineno"> 2605</span>    }</div>
<div class="line"><a id="l02606" name="l02606"></a><span class="lineno"> 2606</span> </div>
<div class="line"><a id="l02607" name="l02607"></a><span class="lineno"> 2607</span>    <span class="keyword">template</span>&lt;</div>
<div class="line"><a id="l02608" name="l02608"></a><span class="lineno"> 2608</span>      <span class="keyword">typename</span> Dummy = void,</div>
<div class="line"><a id="l02609" name="l02609"></a><span class="lineno"> 2609</span>      <span class="keyword">typename</span> =</div>
<div class="line"><a id="l02610" name="l02610"></a><span class="lineno"> 2610</span>        <span class="keyword">typename</span> std::enable_if&lt;!std::is_same&lt;T, no_user_data&gt;::value, Dummy&gt;::type&gt;</div>
<div class="line"><a id="l02611" name="l02611"></a><span class="lineno"> 2611</span>    <span class="keywordtype">void</span> add(zmq::socket_ref socket, event_flags events, T *user_data)</div>
<div class="line"><a id="l02612" name="l02612"></a><span class="lineno"> 2612</span>    {</div>
<div class="line"><a id="l02613" name="l02613"></a><span class="lineno"> 2613</span>        add_impl(socket, events, user_data);</div>
<div class="line"><a id="l02614" name="l02614"></a><span class="lineno"> 2614</span>    }</div>
<div class="line"><a id="l02615" name="l02615"></a><span class="lineno"> 2615</span> </div>
<div class="line"><a id="l02616" name="l02616"></a><span class="lineno"> 2616</span>    <span class="keywordtype">void</span> add(zmq::socket_ref socket, event_flags events)</div>
<div class="line"><a id="l02617" name="l02617"></a><span class="lineno"> 2617</span>    {</div>
<div class="line"><a id="l02618" name="l02618"></a><span class="lineno"> 2618</span>        add_impl(socket, events, <span class="keyword">nullptr</span>);</div>
<div class="line"><a id="l02619" name="l02619"></a><span class="lineno"> 2619</span>    }</div>
<div class="line"><a id="l02620" name="l02620"></a><span class="lineno"> 2620</span> </div>
<div class="line"><a id="l02621" name="l02621"></a><span class="lineno"> 2621</span>    <span class="keywordtype">void</span> remove(zmq::socket_ref socket)</div>
<div class="line"><a id="l02622" name="l02622"></a><span class="lineno"> 2622</span>    {</div>
<div class="line"><a id="l02623" name="l02623"></a><span class="lineno"> 2623</span>        <span class="keywordflow">if</span> (0 != zmq_poller_remove(poller_ptr.get(), socket.handle())) {</div>
<div class="line"><a id="l02624" name="l02624"></a><span class="lineno"> 2624</span>            <span class="keywordflow">throw</span> error_t();</div>
<div class="line"><a id="l02625" name="l02625"></a><span class="lineno"> 2625</span>        }</div>
<div class="line"><a id="l02626" name="l02626"></a><span class="lineno"> 2626</span>    }</div>
<div class="line"><a id="l02627" name="l02627"></a><span class="lineno"> 2627</span> </div>
<div class="line"><a id="l02628" name="l02628"></a><span class="lineno"> 2628</span>    <span class="keywordtype">void</span> modify(zmq::socket_ref socket, event_flags events)</div>
<div class="line"><a id="l02629" name="l02629"></a><span class="lineno"> 2629</span>    {</div>
<div class="line"><a id="l02630" name="l02630"></a><span class="lineno"> 2630</span>        <span class="keywordflow">if</span> (0</div>
<div class="line"><a id="l02631" name="l02631"></a><span class="lineno"> 2631</span>            != zmq_poller_modify(poller_ptr.get(), socket.handle(),</div>
<div class="line"><a id="l02632" name="l02632"></a><span class="lineno"> 2632</span>                                 <span class="keyword">static_cast&lt;</span><span class="keywordtype">short</span><span class="keyword">&gt;</span>(events))) {</div>
<div class="line"><a id="l02633" name="l02633"></a><span class="lineno"> 2633</span>            <span class="keywordflow">throw</span> error_t();</div>
<div class="line"><a id="l02634" name="l02634"></a><span class="lineno"> 2634</span>        }</div>
<div class="line"><a id="l02635" name="l02635"></a><span class="lineno"> 2635</span>    }</div>
<div class="line"><a id="l02636" name="l02636"></a><span class="lineno"> 2636</span> </div>
<div class="line"><a id="l02637" name="l02637"></a><span class="lineno"> 2637</span>    <span class="keywordtype">size_t</span> wait_all(std::vector&lt;event_type&gt; &amp;poller_events,</div>
<div class="line"><a id="l02638" name="l02638"></a><span class="lineno"> 2638</span>                    <span class="keyword">const</span> std::chrono::milliseconds timeout)</div>
<div class="line"><a id="l02639" name="l02639"></a><span class="lineno"> 2639</span>    {</div>
<div class="line"><a id="l02640" name="l02640"></a><span class="lineno"> 2640</span>        <span class="keywordtype">int</span> rc = zmq_poller_wait_all(</div>
<div class="line"><a id="l02641" name="l02641"></a><span class="lineno"> 2641</span>          poller_ptr.get(),</div>
<div class="line"><a id="l02642" name="l02642"></a><span class="lineno"> 2642</span>          <span class="keyword">reinterpret_cast&lt;</span>zmq_poller_event_t *<span class="keyword">&gt;</span>(poller_events.data()),</div>
<div class="line"><a id="l02643" name="l02643"></a><span class="lineno"> 2643</span>          <span class="keyword">static_cast&lt;</span><span class="keywordtype">int</span><span class="keyword">&gt;</span>(poller_events.size()),</div>
<div class="line"><a id="l02644" name="l02644"></a><span class="lineno"> 2644</span>          <span class="keyword">static_cast&lt;</span><span class="keywordtype">long</span><span class="keyword">&gt;</span>(timeout.count()));</div>
<div class="line"><a id="l02645" name="l02645"></a><span class="lineno"> 2645</span>        <span class="keywordflow">if</span> (rc &gt; 0)</div>
<div class="line"><a id="l02646" name="l02646"></a><span class="lineno"> 2646</span>            <span class="keywordflow">return</span> <span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(rc);</div>
<div class="line"><a id="l02647" name="l02647"></a><span class="lineno"> 2647</span> </div>
<div class="line"><a id="l02648" name="l02648"></a><span class="lineno"> 2648</span><span class="preprocessor">#if ZMQ_VERSION &gt;= ZMQ_MAKE_VERSION(4, 2, 3)</span></div>
<div class="line"><a id="l02649" name="l02649"></a><span class="lineno"> 2649</span>        <span class="keywordflow">if</span> (zmq_errno() == EAGAIN)</div>
<div class="line"><a id="l02650" name="l02650"></a><span class="lineno"> 2650</span><span class="preprocessor">#else</span></div>
<div class="line"><a id="l02651" name="l02651"></a><span class="lineno"> 2651</span>        <span class="keywordflow">if</span> (zmq_errno() == ETIMEDOUT)</div>
<div class="line"><a id="l02652" name="l02652"></a><span class="lineno"> 2652</span><span class="preprocessor">#endif</span></div>
<div class="line"><a id="l02653" name="l02653"></a><span class="lineno"> 2653</span>            <span class="keywordflow">return</span> 0;</div>
<div class="line"><a id="l02654" name="l02654"></a><span class="lineno"> 2654</span> </div>
<div class="line"><a id="l02655" name="l02655"></a><span class="lineno"> 2655</span>        <span class="keywordflow">throw</span> error_t();</div>
<div class="line"><a id="l02656" name="l02656"></a><span class="lineno"> 2656</span>    }</div>
<div class="line"><a id="l02657" name="l02657"></a><span class="lineno"> 2657</span> </div>
<div class="line"><a id="l02658" name="l02658"></a><span class="lineno"> 2658</span>  <span class="keyword">private</span>:</div>
<div class="line"><a id="l02659" name="l02659"></a><span class="lineno"> 2659</span>    <span class="keyword">struct </span>destroy_poller_t</div>
<div class="line"><a id="l02660" name="l02660"></a><span class="lineno"> 2660</span>    {</div>
<div class="line"><a id="l02661" name="l02661"></a><span class="lineno"> 2661</span>        <span class="keywordtype">void</span> operator()(<span class="keywordtype">void</span> *ptr) <span class="keyword">noexcept</span></div>
<div class="line"><a id="l02662" name="l02662"></a><span class="lineno"> 2662</span>        {</div>
<div class="line"><a id="l02663" name="l02663"></a><span class="lineno"> 2663</span>            <span class="keywordtype">int</span> rc = zmq_poller_destroy(&amp;ptr);</div>
<div class="line"><a id="l02664" name="l02664"></a><span class="lineno"> 2664</span>            ZMQ_ASSERT(rc == 0);</div>
<div class="line"><a id="l02665" name="l02665"></a><span class="lineno"> 2665</span>        }</div>
<div class="line"><a id="l02666" name="l02666"></a><span class="lineno"> 2666</span>    };</div>
<div class="line"><a id="l02667" name="l02667"></a><span class="lineno"> 2667</span> </div>
<div class="line"><a id="l02668" name="l02668"></a><span class="lineno"> 2668</span>    std::unique_ptr&lt;void, destroy_poller_t&gt; poller_ptr;</div>
<div class="line"><a id="l02669" name="l02669"></a><span class="lineno"> 2669</span> </div>
<div class="line"><a id="l02670" name="l02670"></a><span class="lineno"> 2670</span>    <span class="keywordtype">void</span> add_impl(zmq::socket_ref socket, event_flags events, T *user_data)</div>
<div class="line"><a id="l02671" name="l02671"></a><span class="lineno"> 2671</span>    {</div>
<div class="line"><a id="l02672" name="l02672"></a><span class="lineno"> 2672</span>        <span class="keywordflow">if</span> (0</div>
<div class="line"><a id="l02673" name="l02673"></a><span class="lineno"> 2673</span>            != zmq_poller_add(poller_ptr.get(), socket.handle(), user_data,</div>
<div class="line"><a id="l02674" name="l02674"></a><span class="lineno"> 2674</span>                              <span class="keyword">static_cast&lt;</span><span class="keywordtype">short</span><span class="keyword">&gt;</span>(events))) {</div>
<div class="line"><a id="l02675" name="l02675"></a><span class="lineno"> 2675</span>            <span class="keywordflow">throw</span> error_t();</div>
<div class="line"><a id="l02676" name="l02676"></a><span class="lineno"> 2676</span>        }</div>
<div class="line"><a id="l02677" name="l02677"></a><span class="lineno"> 2677</span>    }</div>
<div class="line"><a id="l02678" name="l02678"></a><span class="lineno"> 2678</span>};</div>
<div class="line"><a id="l02679" name="l02679"></a><span class="lineno"> 2679</span><span class="preprocessor">#endif </span><span class="comment">//  defined(ZMQ_BUILD_DRAFT_API) &amp;&amp; defined(ZMQ_CPP11) &amp;&amp; defined(ZMQ_HAVE_POLLER)</span></div>
<div class="line"><a id="l02680" name="l02680"></a><span class="lineno"> 2680</span> </div>
<div class="line"><a id="l02681" name="l02681"></a><span class="lineno"> 2681</span><span class="keyword">inline</span> std::ostream &amp;operator&lt;&lt;(std::ostream &amp;os, <span class="keyword">const</span> <a class="code hl_class" href="classzmq_1_1message__t.html">message_t</a> &amp;msg)</div>
<div class="line"><a id="l02682" name="l02682"></a><span class="lineno"> 2682</span>{</div>
<div class="line"><a id="l02683" name="l02683"></a><span class="lineno"> 2683</span>    <span class="keywordflow">return</span> os &lt;&lt; msg.str();</div>
<div class="line"><a id="l02684" name="l02684"></a><span class="lineno"> 2684</span>}</div>
<div class="line"><a id="l02685" name="l02685"></a><span class="lineno"> 2685</span> </div>
<div class="line"><a id="l02686" name="l02686"></a><span class="lineno"> 2686</span>} <span class="comment">// namespace zmq</span></div>
<div class="line"><a id="l02687" name="l02687"></a><span class="lineno"> 2687</span> </div>
<div class="line"><a id="l02688" name="l02688"></a><span class="lineno"> 2688</span><span class="preprocessor">#endif </span><span class="comment">// __ZMQ_HPP_INCLUDED__</span></div>
<div class="ttc" id="aclasszmq_1_1context__t_html"><div class="ttname"><a href="classzmq_1_1context__t.html">zmq::context_t</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:767</div></div>
<div class="ttc" id="aclasszmq_1_1detail_1_1socket__base_html"><div class="ttname"><a href="classzmq_1_1detail_1_1socket__base.html">zmq::detail::socket_base</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:1649</div></div>
<div class="ttc" id="aclasszmq_1_1error__t_html"><div class="ttname"><a href="classzmq_1_1error__t.html">zmq::error_t</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:275</div></div>
<div class="ttc" id="aclasszmq_1_1message__t_html"><div class="ttname"><a href="classzmq_1_1message__t.html">zmq::message_t</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:382</div></div>
<div class="ttc" id="aclasszmq_1_1message__t_html_aed6502a922a73cda7f47d47689617d03"><div class="ttname"><a href="classzmq_1_1message__t.html#aed6502a922a73cda7f47d47689617d03">zmq::message_t::str</a></div><div class="ttdeci">std::string str() const</div><div class="ttdef"><b>Definition</b> zmq.hpp:661</div></div>
<div class="ttc" id="aclasszmq_1_1socket__ref_html"><div class="ttname"><a href="classzmq_1_1socket__ref.html">zmq::socket_ref</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:2052</div></div>
<div class="ttc" id="aclasszmq_1_1socket__t_html"><div class="ttname"><a href="classzmq_1_1socket__t.html">zmq::socket_t</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:2126</div></div>
<div class="ttc" id="astructbuffer_html"><div class="ttname"><a href="structbuffer.html">buffer</a></div><div class="ttdef"><b>Definition</b> DtnUtil.h:35</div></div>
<div class="ttc" id="astructzmq_1_1from__handle__t_1_1__private_html"><div class="ttname"><a href="structzmq_1_1from__handle__t_1_1__private.html">zmq::from_handle_t::_private</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:2041</div></div>
<div class="ttc" id="astructzmq_1_1from__handle__t_html"><div class="ttname"><a href="structzmq_1_1from__handle__t.html">zmq::from_handle_t</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:2039</div></div>
<div class="ttc" id="astructzmq__event__t_html"><div class="ttname"><a href="structzmq__event__t.html">zmq_event_t</a></div><div class="ttdef"><b>Definition</b> zmq.hpp:204</div></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_bdd9a5d540de89e9fe90efdfc6973a4f.html">common</a></li><li class="navelem"><a class="el" href="dir_f7cbf1dfc4945ce4e61a395e67c06604.html">util</a></li><li class="navelem"><a class="el" href="dir_d8cd0b0498ce5da3d465b26b60aa31cc.html">include</a></li><li class="navelem"><b>zmq.hpp</b></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.13.2 </li>
  </ul>
</div>
</body>
</html>
